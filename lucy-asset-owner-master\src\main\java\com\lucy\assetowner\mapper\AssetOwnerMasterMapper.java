package com.lucy.assetowner.mapper;

import java.util.List;
import com.lucy.assetowner.domain.AssetOwnerMaster;

/**
 * 资产所属主体主 Asset owner master dataMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface AssetOwnerMasterMapper 
{
    /**
     * 查询资产所属主体主 Asset owner master data
     * 
     * @param id 资产所属主体主 Asset owner master data主键
     * @return 资产所属主体主 Asset owner master data
     */
    public AssetOwnerMaster selectAssetOwnerMasterById(Long id);

    /**
     * 查询资产所属主体主 Asset owner master data列表
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 资产所属主体主 Asset owner master data集合
     */
    public List<AssetOwnerMaster> selectAssetOwnerMasterList(AssetOwnerMaster assetOwnerMaster);

    /**
     * 新增资产所属主体主 Asset owner master data
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 结果
     */
    public int insertAssetOwnerMaster(AssetOwnerMaster assetOwnerMaster);

    /**
     * 修改资产所属主体主 Asset owner master data
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 结果
     */
    public int updateAssetOwnerMaster(AssetOwnerMaster assetOwnerMaster);

    /**
     * 删除资产所属主体主 Asset owner master data
     * 
     * @param id 资产所属主体主 Asset owner master data主键
     * @return 结果
     */
    public int deleteAssetOwnerMasterById(Long id);

    /**
     * 批量删除资产所属主体主 Asset owner master data
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAssetOwnerMasterByIds(Long[] ids);
}
