package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasEmployee;
import com.lucy.erp.service.IBasEmployeeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_员工
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/system/employee")
public class BasEmployeeController extends BaseController
{
    @Autowired
    private IBasEmployeeService basEmployeeService;

    /**
     * 查询基础_员工
列表
     */
    @PreAuthorize("@ss.hasPermi('system:employee:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasEmployee basEmployee)
    {
        startPage();
        List<BasEmployee> list = basEmployeeService.selectBasEmployeeList(basEmployee);
        return getDataTable(list);
    }

    /**
     * 导出基础_员工
列表
     */
    @PreAuthorize("@ss.hasPermi('system:employee:export')")
    @Log(title = "基础_员工 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasEmployee basEmployee)
    {
        List<BasEmployee> list = basEmployeeService.selectBasEmployeeList(basEmployee);
        ExcelUtil<BasEmployee> util = new ExcelUtil<BasEmployee>(BasEmployee.class);
        util.exportExcel(response, list, "基础_员工 数据");
    }

    /**
     * 获取基础_员工
详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:employee:query')")
    @GetMapping(value = "/{beNo}")
    public AjaxResult getInfo(@PathVariable("beNo") String beNo)
    {
        return success(basEmployeeService.selectBasEmployeeByBeNo(beNo));
    }

    /**
     * 新增基础_员工

     */
    @PreAuthorize("@ss.hasPermi('system:employee:add')")
    @Log(title = "基础_员工 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasEmployee basEmployee)
    {
        return toAjax(basEmployeeService.insertBasEmployee(basEmployee));
    }

    /**
     * 修改基础_员工

     */
    @PreAuthorize("@ss.hasPermi('system:employee:edit')")
    @Log(title = "基础_员工 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasEmployee basEmployee)
    {
        return toAjax(basEmployeeService.updateBasEmployee(basEmployee));
    }

    /**
     * 删除基础_员工

     */
    @PreAuthorize("@ss.hasPermi('system:employee:remove')")
    @Log(title = "基础_员工 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{beNos}")
    public AjaxResult remove(@PathVariable String[] beNos)
    {
        return toAjax(basEmployeeService.deleteBasEmployeeByBeNos(beNos));
    }
}
