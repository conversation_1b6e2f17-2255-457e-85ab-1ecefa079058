package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogOsPlan;
import com.lucy.erp.service.ILogOsPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 物流_出库_计划
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/system/osplan")
public class LogOsPlanController extends BaseController
{
    @Autowired
    private ILogOsPlanService logOsPlanService;

    /**
     * 查询物流_出库_计划
列表
     */
    @PreAuthorize("@ss.hasPermi('system:osplan:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogOsPlan logOsPlan)
    {
        startPage();
        List<LogOsPlan> list = logOsPlanService.selectLogOsPlanList(logOsPlan);
        return getDataTable(list);
    }

    /**
     * 导出物流_出库_计划
列表
     */
    @PreAuthorize("@ss.hasPermi('system:osplan:export')")
    @Log(title = "物流_出库_计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogOsPlan logOsPlan)
    {
        List<LogOsPlan> list = logOsPlanService.selectLogOsPlanList(logOsPlan);
        ExcelUtil<LogOsPlan> util = new ExcelUtil<LogOsPlan>(LogOsPlan.class);
        util.exportExcel(response, list, "物流_出库_计划数据");
    }

    /**
     * 获取物流_出库_计划
详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:osplan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(String id)
    {
        return success(logOsPlanService.selectLogOsPlanById(id));
    }

    /**
     * 新增物流_出库_计划

     */
    @PreAuthorize("@ss.hasPermi('system:osplan:add')")
    @Log(title = "物流_出库_计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogOsPlan logOsPlan)
    {
        return toAjax(logOsPlanService.insertLogOsPlan(logOsPlan));
    }

    /**
     * 修改物流_出库_计划

     */
    @PreAuthorize("@ss.hasPermi('system:osplan:edit')")
    @Log(title = "物流_出库_计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogOsPlan logOsPlan)
    {
        return toAjax(logOsPlanService.updateLogOsPlan(logOsPlan));
    }

    /**
     * 删除物流_出库_计划

     */
    @PreAuthorize("@ss.hasPermi('system:osplan:remove')")
    @Log(title = "物流_出库_计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(logOsPlanService.deleteLogOsPlanByIds(ids));
    }
}
