package com.lucy.inventoryoe.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 会计科目 Accounting subject master对象 accounting_subject
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public class AccountingSubject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 科目代码, Account code */
    private String code;

    /** 科目名称, Account name */
    @Excel(name = "科目名称, Account name")
    private String name;

    /** 科目类别, Account category */
    @Excel(name = "科目类别, Account category")
    private String category;

    /** 科目层级(1-5), Account level */
    @Excel(name = "科目层级(1-5), Account level")
    private Integer level;

    /** 上级科目代码, Parent account code */
    @Excel(name = "上级科目代码, Parent account code")
    private String parentCode;

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }

    public void setLevel(Integer level) 
    {
        this.level = level;
    }

    public Integer getLevel() 
    {
        return level;
    }

    public void setParentCode(String parentCode) 
    {
        this.parentCode = parentCode;
    }

    public String getParentCode() 
    {
        return parentCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("name", getName())
            .append("category", getCategory())
            .append("level", getLevel())
            .append("parentCode", getParentCode())
            .toString();
    }
}
