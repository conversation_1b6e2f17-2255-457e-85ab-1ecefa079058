package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产成品对象 log_repack_result
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public class LogRepackResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 产成品表id */
    private Long id;

    /** 投料单编号 */
    private String lrrCd;

    /** 分装类型 */
    @Excel(name = "分装类型")
    private String lrrTyp;

    /** 工单号 */
    @Excel(name = "工单号")
    private String lrrPsn;

    /** 分装明细编号 */
    @Excel(name = "分装明细编号")
    private String lrrPsnCd;

    /** 产品包装sku编号 */
    @Excel(name = "产品包装sku编号")
    private String lrrMainSkuCd;

    /** 产成品批次 */
    @Excel(name = "产成品批次")
    private String lrrMainBatch;

    /** 产成品数量 */
    @Excel(name = "产成品数量")
    private BigDecimal lrrMainSkuQty;

    /** 产成品总金额 */
    @Excel(name = "产成品总金额")
    private BigDecimal lrrMainTotalPrc;

    /** 产成品产成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "产成品产成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lrrMainDate;

    /** 批号 */
    @Excel(name = "批号")
    private String lrrBn;

    /** 加密批号 */
    @Excel(name = "加密批号")
    private String lrrBnEncrypt;

    /** 仓库 */
    @Excel(name = "仓库")
    private String lrrDmcd;

    /** 库位 */
    @Excel(name = "库位")
    private String lrrSlcd;

    /** 库位类型 */
    @Excel(name = "库位类型")
    private String lrrTcd;

    /** 资产所属组织 */
    @Excel(name = "资产所属组织")
    private String lrrOrgmcd;

    /** 逆向数据标记 */
    @Excel(name = "逆向数据标记")
    private Integer flag;

    /** r_cre_kid */
    @Excel(name = "r_cre_kid")
    private String rCreKid;

    /** r_cre_dt */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "r_cre_dt", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** r_upd_kid */
    @Excel(name = "r_upd_kid")
    private String rUpdKid;

    /** r_upd_dt */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "r_upd_dt", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** r_v */
    @Excel(name = "r_v")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    private String startDate;
    private String endDate;
    private String lrpurdMcd;
    private String lrpurdSkuCd;
    private BigDecimal lrpurdTotalPrc;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLrrCd(String lrrCd) 
    {
        this.lrrCd = lrrCd;
    }

    public String getLrrCd() 
    {
        return lrrCd;
    }

    public void setLrrTyp(String lrrTyp) 
    {
        this.lrrTyp = lrrTyp;
    }

    public String getLrrTyp() 
    {
        return lrrTyp;
    }

    public void setLrrPsn(String lrrPsn) 
    {
        this.lrrPsn = lrrPsn;
    }

    public String getLrrPsn() 
    {
        return lrrPsn;
    }

    public void setLrrPsnCd(String lrrPsnCd) 
    {
        this.lrrPsnCd = lrrPsnCd;
    }

    public String getLrrPsnCd() 
    {
        return lrrPsnCd;
    }

    public void setLrrMainSkuCd(String lrrMainSkuCd) 
    {
        this.lrrMainSkuCd = lrrMainSkuCd;
    }

    public String getLrrMainSkuCd() 
    {
        return lrrMainSkuCd;
    }

    public void setLrrMainBatch(String lrrMainBatch) 
    {
        this.lrrMainBatch = lrrMainBatch;
    }

    public String getLrrMainBatch() 
    {
        return lrrMainBatch;
    }

    public void setLrrMainSkuQty(BigDecimal lrrMainSkuQty) 
    {
        this.lrrMainSkuQty = lrrMainSkuQty;
    }

    public BigDecimal getLrrMainSkuQty() 
    {
        return lrrMainSkuQty;
    }

    public void setLrrMainTotalPrc(BigDecimal lrrMainTotalPrc) 
    {
        this.lrrMainTotalPrc = lrrMainTotalPrc;
    }

    public BigDecimal getLrrMainTotalPrc() 
    {
        return lrrMainTotalPrc;
    }

    public void setLrrMainDate(Date lrrMainDate) 
    {
        this.lrrMainDate = lrrMainDate;
    }

    public Date getLrrMainDate() 
    {
        return lrrMainDate;
    }

    public void setLrrBn(String lrrBn) 
    {
        this.lrrBn = lrrBn;
    }

    public String getLrrBn() 
    {
        return lrrBn;
    }

    public void setLrrBnEncrypt(String lrrBnEncrypt) 
    {
        this.lrrBnEncrypt = lrrBnEncrypt;
    }

    public String getLrrBnEncrypt() 
    {
        return lrrBnEncrypt;
    }

    public void setLrrDmcd(String lrrDmcd) 
    {
        this.lrrDmcd = lrrDmcd;
    }

    public String getLrrDmcd() 
    {
        return lrrDmcd;
    }

    public void setLrrSlcd(String lrrSlcd) 
    {
        this.lrrSlcd = lrrSlcd;
    }

    public String getLrrSlcd() 
    {
        return lrrSlcd;
    }

    public void setLrrTcd(String lrrTcd) 
    {
        this.lrrTcd = lrrTcd;
    }

    public String getLrrTcd() 
    {
        return lrrTcd;
    }

    public void setLrrOrgmcd(String lrrOrgmcd) 
    {
        this.lrrOrgmcd = lrrOrgmcd;
    }

    public String getLrrOrgmcd() 
    {
        return lrrOrgmcd;
    }

    public void setFlag(Integer flag) 
    {
        this.flag = flag;
    }

    public Integer getFlag() 
    {
        return flag;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getLrpurdMcd() {
        return lrpurdMcd;
    }

    public void setLrpurdMcd(String lrpurdMcd) {
        this.lrpurdMcd = lrpurdMcd;
    }

    public String getLrpurdSkuCd() {
        return lrpurdSkuCd;
    }

    public void setLrpurdSkuCd(String lrpurdSkuCd) {
        this.lrpurdSkuCd = lrpurdSkuCd;
    }

    public BigDecimal getLrpurdTotalPrc() {
        return lrpurdTotalPrc;
    }

    public void setLrpurdTotalPrc(BigDecimal lrpurdTotalPrc) {
        this.lrpurdTotalPrc = lrpurdTotalPrc;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lrrCd", getLrrCd())
            .append("lrrTyp", getLrrTyp())
            .append("lrrPsn", getLrrPsn())
            .append("lrrPsnCd", getLrrPsnCd())
            .append("lrrMainSkuCd", getLrrMainSkuCd())
            .append("lrrMainBatch", getLrrMainBatch())
            .append("lrrMainSkuQty", getLrrMainSkuQty())
            .append("lrrMainTotalPrc", getLrrMainTotalPrc())
            .append("lrrMainDate", getLrrMainDate())
            .append("lrrBn", getLrrBn())
            .append("lrrBnEncrypt", getLrrBnEncrypt())
            .append("lrrDmcd", getLrrDmcd())
            .append("lrrSlcd", getLrrSlcd())
            .append("lrrTcd", getLrrTcd())
            .append("lrrOrgmcd", getLrrOrgmcd())
            .append("flag", getFlag())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
