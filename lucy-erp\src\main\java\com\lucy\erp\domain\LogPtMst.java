package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 物流_生产跟踪_主
对象 log_pt_mst
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class LogPtMst extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 生产工单号 */
    private String lpmCd;

    /** 生产优先级默认0,置为1时表示优先生产 */
    @Excel(name = "生产优先级默认0,置为1时表示优先生产")
    private Integer lpmPri;

    /** 批号 */
    @Excel(name = "批号")
    private String lpmBn;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String lpmPcd;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String lpmPpcd;

    /** 库位 */
    @Excel(name = "库位")
    private String lpmSlcd;

    /** 是否分装 */
    @Excel(name = "是否分装")
    private String lpmIsrp;

    /** 分装是否完成 */
    @Excel(name = "分装是否完成")
    private String lpmRpwt;

    /** 现货封装质检原库位 */
    @Excel(name = "现货封装质检原库位")
    private String lpmZxwtslcd;

    /** 是否质检 */
    @Excel(name = "是否质检")
    private String lpmIsqc;

    /** 质检是否通过 */
    @Excel(name = "质检是否通过")
    private String lpmQcwt;

    /** 质检放行时间 */
    @Excel(name = "质检放行时间")
    private String lpmQcwtdt;

    /** 状态 */
    @Excel(name = "状态")
    private String lpmSts;

    /** 到货类型 */
    @Excel(name = "到货类型")
    private String lpmTyp;

    /** 备注 */
    @Excel(name = "备注")
    private String lpmRmk;

    /** 产品批次 */
    @Excel(name = "产品批次")
    private String lpmLocPbn;

    /** 供应商批次 */
    @Excel(name = "供应商批次")
    private String lpmLocSbn;

    /** 所在资产所属组织 */
    @Excel(name = "所在资产所属组织")
    private String lpmLocOrgmcd;

    /** 所在仓库 */
    @Excel(name = "所在仓库")
    private String lpmLocDmcd;

    /** 所在库位类型 */
    @Excel(name = "所在库位类型")
    private String lpmLocTcd;

    /** 所在库位 */
    @Excel(name = "所在库位")
    private String lpmLocSlcd;

    /** 加密批号 */
    @Excel(name = "加密批号")
    private String lpmLocBnEncrypt;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal lpmLocQty;

    /** 含税单位成本 */
    @Excel(name = "含税单位成本")
    private BigDecimal lpmLocUnitp;

    /** 是否价税分离 */
    @Excel(name = "是否价税分离")
    private Integer lpmLocIspart;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal lpmLocBit;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 质检完成入分装仓库 */
    @Excel(name = "质检完成入分装仓库")
    private String lpmDmcdfz;

    /** 质检入分装库是否完成 */
    @Excel(name = "质检入分装库是否完成")
    private String lpmDmcdfzpc;

    /** 来源类型(00为生物打印标签) */
    @Excel(name = "来源类型(00为生物打印标签)")
    private String lpmSourcetype;

    /** 原生产工单号 */
    @Excel(name = "原生产工单号")
    private String lpmOriginally;

    /** 供应商生产日期 */
    @Excel(name = "供应商生产日期")
    private String lpmSupplierDt;

    /** 入不良数量 */
    @Excel(name = "入不良数量")
    private BigDecimal lpmReqty;

    /** 分装流程改造后新旧数据标记 */
    @Excel(name = "分装流程改造后新旧数据标记")
    private String lpmFlag;

    /** 分装出库的库位类型 */
    @Excel(name = "分装出库的库位类型")
    private String lpmOutTcd;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLpmCd(String lpmCd) 
    {
        this.lpmCd = lpmCd;
    }

    public String getLpmCd() 
    {
        return lpmCd;
    }

    public void setLpmPri(Integer lpmPri) 
    {
        this.lpmPri = lpmPri;
    }

    public Integer getLpmPri() 
    {
        return lpmPri;
    }

    public void setLpmBn(String lpmBn) 
    {
        this.lpmBn = lpmBn;
    }

    public String getLpmBn() 
    {
        return lpmBn;
    }

    public void setLpmPcd(String lpmPcd) 
    {
        this.lpmPcd = lpmPcd;
    }

    public String getLpmPcd() 
    {
        return lpmPcd;
    }

    public void setLpmPpcd(String lpmPpcd) 
    {
        this.lpmPpcd = lpmPpcd;
    }

    public String getLpmPpcd() 
    {
        return lpmPpcd;
    }

    public void setLpmSlcd(String lpmSlcd) 
    {
        this.lpmSlcd = lpmSlcd;
    }

    public String getLpmSlcd() 
    {
        return lpmSlcd;
    }

    public void setLpmIsrp(String lpmIsrp) 
    {
        this.lpmIsrp = lpmIsrp;
    }

    public String getLpmIsrp() 
    {
        return lpmIsrp;
    }

    public void setLpmRpwt(String lpmRpwt) 
    {
        this.lpmRpwt = lpmRpwt;
    }

    public String getLpmRpwt() 
    {
        return lpmRpwt;
    }

    public void setLpmZxwtslcd(String lpmZxwtslcd) 
    {
        this.lpmZxwtslcd = lpmZxwtslcd;
    }

    public String getLpmZxwtslcd() 
    {
        return lpmZxwtslcd;
    }

    public void setLpmIsqc(String lpmIsqc) 
    {
        this.lpmIsqc = lpmIsqc;
    }

    public String getLpmIsqc() 
    {
        return lpmIsqc;
    }

    public void setLpmQcwt(String lpmQcwt) 
    {
        this.lpmQcwt = lpmQcwt;
    }

    public String getLpmQcwt() 
    {
        return lpmQcwt;
    }

    public void setLpmQcwtdt(String lpmQcwtdt) 
    {
        this.lpmQcwtdt = lpmQcwtdt;
    }

    public String getLpmQcwtdt() 
    {
        return lpmQcwtdt;
    }

    public void setLpmSts(String lpmSts) 
    {
        this.lpmSts = lpmSts;
    }

    public String getLpmSts() 
    {
        return lpmSts;
    }

    public void setLpmTyp(String lpmTyp) 
    {
        this.lpmTyp = lpmTyp;
    }

    public String getLpmTyp() 
    {
        return lpmTyp;
    }

    public void setLpmRmk(String lpmRmk) 
    {
        this.lpmRmk = lpmRmk;
    }

    public String getLpmRmk() 
    {
        return lpmRmk;
    }

    public void setLpmLocPbn(String lpmLocPbn) 
    {
        this.lpmLocPbn = lpmLocPbn;
    }

    public String getLpmLocPbn() 
    {
        return lpmLocPbn;
    }

    public void setLpmLocSbn(String lpmLocSbn) 
    {
        this.lpmLocSbn = lpmLocSbn;
    }

    public String getLpmLocSbn() 
    {
        return lpmLocSbn;
    }

    public void setLpmLocOrgmcd(String lpmLocOrgmcd) 
    {
        this.lpmLocOrgmcd = lpmLocOrgmcd;
    }

    public String getLpmLocOrgmcd() 
    {
        return lpmLocOrgmcd;
    }

    public void setLpmLocDmcd(String lpmLocDmcd) 
    {
        this.lpmLocDmcd = lpmLocDmcd;
    }

    public String getLpmLocDmcd() 
    {
        return lpmLocDmcd;
    }

    public void setLpmLocTcd(String lpmLocTcd) 
    {
        this.lpmLocTcd = lpmLocTcd;
    }

    public String getLpmLocTcd() 
    {
        return lpmLocTcd;
    }

    public void setLpmLocSlcd(String lpmLocSlcd) 
    {
        this.lpmLocSlcd = lpmLocSlcd;
    }

    public String getLpmLocSlcd() 
    {
        return lpmLocSlcd;
    }

    public void setLpmLocBnEncrypt(String lpmLocBnEncrypt) 
    {
        this.lpmLocBnEncrypt = lpmLocBnEncrypt;
    }

    public String getLpmLocBnEncrypt() 
    {
        return lpmLocBnEncrypt;
    }

    public void setLpmLocQty(BigDecimal lpmLocQty) 
    {
        this.lpmLocQty = lpmLocQty;
    }

    public BigDecimal getLpmLocQty() 
    {
        return lpmLocQty;
    }

    public void setLpmLocUnitp(BigDecimal lpmLocUnitp) 
    {
        this.lpmLocUnitp = lpmLocUnitp;
    }

    public BigDecimal getLpmLocUnitp() 
    {
        return lpmLocUnitp;
    }

    public void setLpmLocIspart(Integer lpmLocIspart) 
    {
        this.lpmLocIspart = lpmLocIspart;
    }

    public Integer getLpmLocIspart() 
    {
        return lpmLocIspart;
    }

    public void setLpmLocBit(BigDecimal lpmLocBit) 
    {
        this.lpmLocBit = lpmLocBit;
    }

    public BigDecimal getLpmLocBit() 
    {
        return lpmLocBit;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setLpmDmcdfz(String lpmDmcdfz) 
    {
        this.lpmDmcdfz = lpmDmcdfz;
    }

    public String getLpmDmcdfz() 
    {
        return lpmDmcdfz;
    }

    public void setLpmDmcdfzpc(String lpmDmcdfzpc) 
    {
        this.lpmDmcdfzpc = lpmDmcdfzpc;
    }

    public String getLpmDmcdfzpc() 
    {
        return lpmDmcdfzpc;
    }

    public void setLpmSourcetype(String lpmSourcetype) 
    {
        this.lpmSourcetype = lpmSourcetype;
    }

    public String getLpmSourcetype() 
    {
        return lpmSourcetype;
    }

    public void setLpmOriginally(String lpmOriginally) 
    {
        this.lpmOriginally = lpmOriginally;
    }

    public String getLpmOriginally() 
    {
        return lpmOriginally;
    }

    public void setLpmSupplierDt(String lpmSupplierDt) 
    {
        this.lpmSupplierDt = lpmSupplierDt;
    }

    public String getLpmSupplierDt() 
    {
        return lpmSupplierDt;
    }

    public void setLpmReqty(BigDecimal lpmReqty) 
    {
        this.lpmReqty = lpmReqty;
    }

    public BigDecimal getLpmReqty() 
    {
        return lpmReqty;
    }

    public void setLpmFlag(String lpmFlag) 
    {
        this.lpmFlag = lpmFlag;
    }

    public String getLpmFlag() 
    {
        return lpmFlag;
    }

    public void setLpmOutTcd(String lpmOutTcd) 
    {
        this.lpmOutTcd = lpmOutTcd;
    }

    public String getLpmOutTcd() 
    {
        return lpmOutTcd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lpmCd", getLpmCd())
            .append("lpmPri", getLpmPri())
            .append("lpmBn", getLpmBn())
            .append("lpmPcd", getLpmPcd())
            .append("lpmPpcd", getLpmPpcd())
            .append("lpmSlcd", getLpmSlcd())
            .append("lpmIsrp", getLpmIsrp())
            .append("lpmRpwt", getLpmRpwt())
            .append("lpmZxwtslcd", getLpmZxwtslcd())
            .append("lpmIsqc", getLpmIsqc())
            .append("lpmQcwt", getLpmQcwt())
            .append("lpmQcwtdt", getLpmQcwtdt())
            .append("lpmSts", getLpmSts())
            .append("lpmTyp", getLpmTyp())
            .append("lpmRmk", getLpmRmk())
            .append("lpmLocPbn", getLpmLocPbn())
            .append("lpmLocSbn", getLpmLocSbn())
            .append("lpmLocOrgmcd", getLpmLocOrgmcd())
            .append("lpmLocDmcd", getLpmLocDmcd())
            .append("lpmLocTcd", getLpmLocTcd())
            .append("lpmLocSlcd", getLpmLocSlcd())
            .append("lpmLocBnEncrypt", getLpmLocBnEncrypt())
            .append("lpmLocQty", getLpmLocQty())
            .append("lpmLocUnitp", getLpmLocUnitp())
            .append("lpmLocIspart", getLpmLocIspart())
            .append("lpmLocBit", getLpmLocBit())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("lpmDmcdfz", getLpmDmcdfz())
            .append("lpmDmcdfzpc", getLpmDmcdfzpc())
            .append("lpmSourcetype", getLpmSourcetype())
            .append("lpmOriginally", getLpmOriginally())
            .append("lpmSupplierDt", getLpmSupplierDt())
            .append("lpmReqty", getLpmReqty())
            .append("lpmFlag", getLpmFlag())
            .append("lpmOutTcd", getLpmOutTcd())
            .toString();
    }
}
