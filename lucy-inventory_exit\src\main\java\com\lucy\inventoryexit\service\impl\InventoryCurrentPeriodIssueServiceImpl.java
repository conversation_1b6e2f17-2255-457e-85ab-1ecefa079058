package com.lucy.inventoryexit.service.impl;

import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.lucy.customer.service.ICustomerCompanyService;
import com.lucy.customer.service.ICustomerMasterService;
import com.lucy.customer.service.IDepartmentGroupService;
import com.lucy.erp.domain.*;
import com.lucy.erp.service.*;
import com.lucy.inventoryexit.domain.InventoryCurrentPeriodIssue;
import com.lucy.inventoryexit.mapper.InventoryCurrentPeriodIssueMapper;
import com.lucy.inventoryexit.service.IInventoryCurrentPeriodIssueService;
import com.lucy.inventoryoe.domain.CostComposition;
import com.lucy.inventoryoe.service.ICostCompositionService;
import com.lucy.skumaster.service.ISkuMasterService;
import com.lucy.supplier.service.ISupplierMasterService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.WXUtils;
import com.ruoyi.repackage.domain.ConversionRule;
import com.ruoyi.repackage.domain.ConversionRuleDetail;
import com.ruoyi.repackage.service.IConversionDependencyGraphService;
import com.ruoyi.repackage.service.IConversionRuleDetailService;
import com.ruoyi.repackage.service.IConversionRuleService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 本期出库明细 Current period inventory issuesService业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class InventoryCurrentPeriodIssueServiceImpl implements IInventoryCurrentPeriodIssueService {
    private static final Logger log = LoggerFactory.getLogger(InventoryCurrentPeriodIssueServiceImpl.class);

    private static final String TABLE_NAME = "inventory_current_period_issue";

    // 批量插入的批次大小，避免内存溢出
    private static final int BATCH_SIZE = 1000;

    // 线程池用于并行处理load方法
    private final ExecutorService executorService = createOptimizedThreadPool("LoadProcessor");

    @Autowired
    private InventoryCurrentPeriodIssueMapper inventoryCurrentPeriodIssueMapper;

    @Autowired
    private ILogInventoryIoTransactionService logInventoryIoTransactionService;

    @Autowired
    private IBasProdPkgService basProdPkgService;

    @Autowired
    private IBasProdMstService basProdMstService;

    @Autowired
    private ISalesContractMstService salesContractMstService;

    @Autowired
    private ISalesContractDtlService salesContractDtlService;

    @Autowired
    private ISalesContractInvcService salesContractInvcService;

    @Autowired
    private IFinProdCodeService finProdCodeService;

    @Autowired
    private IBasInvoiceTypService basInvoiceTypService;

    @Autowired
    private IBasCustorgMstService basCustorgMstService;

    @Autowired
    private IBasCompanyService basCompanyService;

    @Autowired
    private ISystemUserService systemUserService;

    @Autowired
    private IBasEmployeeService basEmployeeService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IPurContractDtlService purContractDtlService;

    @Autowired
    private IPurContractMstService purContractMstService;

    @Autowired
    private ICostCompositionService costCompositionService;

    @Autowired
    private ILabPickingDtlService labPickingDtlService;

    @Autowired
    private ILabProjectService labProjectService;

    @Autowired
    private IFinAccAvgCostService finAccAvgCostService;

    @Autowired
    private IFinAccMasterRecordsUniqService finAccMasterRecordsUniqService;

    @Autowired
    private ILogRepackPackageUsePlanService logRepackPackageUsePlanService;

    @Autowired
    private ILogPtMstService logPtMstService;

    @Autowired
    private ILogRadDeveloComposeService logRadDeveloComposeService;

    @Autowired
    private ICustomerMasterService customerMasterService;

    @Autowired
    private ICustomerCompanyService customerCompanyService;

    @Autowired
    private IDepartmentGroupService departmentGroupService;

    @Autowired
    private ISkuMasterService skuMasterService;

    @Autowired
    private ISupplierMasterService supplierMasterService;

    @Autowired
    private ILogRepackResultService logRepackResultService;

    @Autowired
    private ILogRepackPackageUseResultDtlService logRepackPackageUseResultDtlService;

    @Autowired
    private IConversionRuleService conversionRuleService;

    @Autowired
    private IConversionRuleDetailService conversionRuleDetailService;

    @Autowired
    private IConversionDependencyGraphService conversionDependencyGraphService;

    // 数据处理专用线程池，用于并行处理
    private final ExecutorService dataProcessorExecutor = createOptimizedThreadPool("DataProcessor");

    // 转换数据处理专用线程池，用于loadConversionData方法
    private final ExecutorService conversionDataExecutor = createOptimizedThreadPool("ConversionData");

    private final Map<String, BasProdMst> basProdMstMap = new ConcurrentHashMap<>();

    private final Map<String, BasProdPkg> basProdPkgMap = new ConcurrentHashMap<>();

    /**
     * 查询本期出库明细 Current period inventory issues
     *
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 本期出库明细 Current period inventory issues
     */
    @Override
    public InventoryCurrentPeriodIssue selectInventoryCurrentPeriodIssueById(Long id) {
        return inventoryCurrentPeriodIssueMapper.selectInventoryCurrentPeriodIssueById(id);
    }

    /**
     * 查询本期出库明细 Current period inventory issues列表
     *
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 本期出库明细 Current period inventory issues
     */
    @Override
    public List<InventoryCurrentPeriodIssue> selectInventoryCurrentPeriodIssueList(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue) {
        return inventoryCurrentPeriodIssueMapper.selectInventoryCurrentPeriodIssueList(inventoryCurrentPeriodIssue);
    }

    /**
     * 新增本期出库明细 Current period inventory issues
     *
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    @Override
    public int insertInventoryCurrentPeriodIssue(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue) {
        return inventoryCurrentPeriodIssueMapper.insertInventoryCurrentPeriodIssue(inventoryCurrentPeriodIssue);
    }

    /**
     * 批量新增本期出库明细 Current period inventory issues
     * 支持大数据量分批处理，避免内存溢出
     *
     * @param inventoryCurrentPeriodIssues 本期出库明细 Current period inventory issues列表
     * @return 结果
     */
    @Override
    public int batchInsertInventoryCurrentPeriodIssue(List<InventoryCurrentPeriodIssue> inventoryCurrentPeriodIssues) {
        if (inventoryCurrentPeriodIssues == null || inventoryCurrentPeriodIssues.isEmpty()) {
            return 0;
        }

        int totalInserted = 0;
        int size = inventoryCurrentPeriodIssues.size();

        // 分批处理，避免内存溢出和SQL语句过长
        for (int i = 0; i < size; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, size);
            List<InventoryCurrentPeriodIssue> batch = inventoryCurrentPeriodIssues.subList(i, endIndex);

            try {
                int inserted = inventoryCurrentPeriodIssueMapper.batchInsertInventoryCurrentPeriodIssue(batch);
                totalInserted += inserted;
            } catch (Exception e) {
                throw new RuntimeException("批量插入数据时发生异常" + e.getMessage(), e);
            }
        }

        return totalInserted;
    }

    /**
     * 修改本期出库明细 Current period inventory issues
     *
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    @Override
    public int updateInventoryCurrentPeriodIssue(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue) {
        return inventoryCurrentPeriodIssueMapper.updateInventoryCurrentPeriodIssue(inventoryCurrentPeriodIssue);
    }

    /**
     * 批量删除本期出库明细 Current period inventory issues
     *
     * @param ids 需要删除的本期出库明细 Current period inventory issues主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodIssueByIds(Long[] ids) {
        return inventoryCurrentPeriodIssueMapper.deleteInventoryCurrentPeriodIssueByIds(ids);
    }

    /**
     * 删除本期出库明细 Current period inventory issues信息
     *
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodIssueById(Long id) {
        return inventoryCurrentPeriodIssueMapper.deleteInventoryCurrentPeriodIssueById(id);
    }

    @Override
    public void loadBQCK(String dateStart, String dateEnd, AssetOwner assetOwner) {
        // 解析日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(dateStart, formatter);
        LocalDate endDate = LocalDate.parse(dateEnd, formatter);

        // 按月分组处理
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // 计算当前月的开始和结束日期
            LocalDate monthStart = currentDate.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate monthEnd = currentDate.with(TemporalAdjusters.lastDayOfMonth());

            // 确保不超出原始日期范围
            LocalDate rangeStart = monthStart.isBefore(startDate) ? startDate : monthStart;
            LocalDate rangeEnd = monthEnd.isAfter(endDate) ? endDate : monthEnd;

            // 转换为字符串格式
            String monthStartStr = rangeStart.format(formatter);
            String monthEndStr = rangeEnd.format(formatter);
            String currentMonth = rangeStart.format(DateTimeFormatter.ofPattern("yyyy-MM"));

            // 处理当前月的数据
            processMonthlyData(monthStartStr, monthEndStr, assetOwner, currentMonth);

            // 移动到下一个月
            currentDate = currentDate.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        }
    }

    /**
     * 格式化耗时显示
     *
     * @param duration 耗时（毫秒）
     * @return 格式化的耗时字符串
     */
    private String formatDuration(long duration) {
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.2fs", duration / 1000.0);
        } else {
            long minutes = duration / 60000;
            long seconds = (duration % 60000) / 1000;
            return String.format("%dm%ds", minutes, seconds);
        }
    }

    /**
     * 处理单月数据
     *
     * @param dateStart 开始日期
     * @param dateEnd   结束日期
     * @param month     月份 yyyy-MM
     */
    public void processMonthlyData(String dateStart, String dateEnd, AssetOwner assetOwner, String month) {
        // 使用并行处理提高性能
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> futures = new ArrayList<>();
        // 记录整体处理开始时间
        long overallStartTime = System.currentTimeMillis();

        // 创建异步任务
        futures.add(CompletableFuture.supplyAsync(() -> loadXSCK(dateStart, dateEnd, assetOwner), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadCGTH(dateStart, dateEnd, assetOwner), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadYFSCLY(dateStart, dateEnd, assetOwner), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadHCLY(dateStart, dateEnd, assetOwner, month), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadCCDTZ(dateStart, dateEnd, assetOwner), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadBF(dateStart, dateEnd, assetOwner, month), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadPK(dateStart, dateEnd, assetOwner, month), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadCB(dateStart, dateEnd, assetOwner), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadFZ(dateStart, dateEnd, assetOwner), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadXYSC(dateStart, dateEnd, assetOwner), executorService));
        futures.add(CompletableFuture.supplyAsync(() -> loadHHH(dateStart, dateEnd, assetOwner, month), executorService));

        List<InventoryCurrentPeriodIssue> total = new ArrayList<>();
        for (CompletableFuture<List<InventoryCurrentPeriodIssue>> future : futures) {
            try {
                total.addAll(future.get());
            } catch (Exception e) {
                log.error("并行处理load方法时发生异常", e);
            }
        }

        // 批量插入数据，提高性能
        if (!total.isEmpty()) {
            batchInsertInventoryCurrentPeriodIssue(total);
            total.clear();
        }

        // 计算整体耗时
        long overallEndTime = System.currentTimeMillis();
        long overallDuration = overallEndTime - overallStartTime;
        String overallDurationStr = formatDuration(overallDuration);

        // 构建处理结果摘要
        String resultSummary = String.format("出库数据同步完成，总耗时：%s", overallDurationStr);
        log.info(resultSummary);

        WXUtils.invokeWorkNotification("***********", String.format("[%s] %s %s，日期：%s至%s",
                new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                assetOwner.entity().getName(), resultSummary, dateStart, dateEnd));
    }

    private void handleInventoryCurrentPeriodIssue(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue, List<InventoryCurrentPeriodIssue> resultList) {
        InventoryCurrentPeriodIssue query = new InventoryCurrentPeriodIssue();
        query.setSourceTable(inventoryCurrentPeriodIssue.getSourceTable());
        query.setSourceRecordId(inventoryCurrentPeriodIssue.getSourceRecordId());
        List<InventoryCurrentPeriodIssue> inventoryCurrentPeriodIssues = inventoryCurrentPeriodIssueMapper
                .selectInventoryCurrentPeriodIssueList(query);
        if (!inventoryCurrentPeriodIssues.isEmpty()) {
            CostComposition costComposition = new CostComposition();
            costComposition.setStructureName(TABLE_NAME);
            costComposition.setParentStructureId(inventoryCurrentPeriodIssues.get(0).getId());
            costComposition.setStatus("X");
            costComposition.setTotalAmount(inventoryCurrentPeriodIssue.getAmount());
            costComposition.setGenerationContext("");
            costComposition.setCreatedBy("");
            costComposition.setCreatedTime(new Date());
            costComposition.setUpdatedBy("");
            costComposition.setUpdatedTime(new Date());
            costComposition.setVersion(0);
            costComposition.setIsDeleted(0);

            costCompositionService.insertCostComposition(costComposition);
        } else {
            resultList.add(inventoryCurrentPeriodIssue);
        }
    }

    /**
     * 加载销售出库数据
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param assetOwner 资产主体
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadXSCK(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "销售出库(XSCK)";

        List<InventoryCurrentPeriodIssue> xsck = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("销售出库", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();
            exceptCusts.add("KH10116772");
            exceptCusts.add("KH10129596");

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("100101");
            liot_typs.add("110102");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("销售出库数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                xsck = processDataInParallel(logInventoryIoTransactions, "XSCK", assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("销售出库数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                xsck = processXSCKSequentially(logInventoryIoTransactions, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!xsck.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, xsck.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return xsck;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理销售出库数据时发生异常", e);
            return xsck;
        }
    }

    /**
     * 通用的并行数据处理方法
     *
     * @param allData      所有数据
     * @param businessType 业务类型（XSCK, CGTH, YFSCLY等）
     * @param assetOwner   资产主体
     * @param exceptCusts  排除的客户列表
     * @return 处理结果
     */
    private List<InventoryCurrentPeriodIssue> processDataInParallel(
            List<LogInventoryIoTransaction> allData,
            String businessType,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理{}数据，总数据量：{}，分片数：{}，每片大小：{}",
                businessType, allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processChunk(chunk, businessType, assetOwner, exceptCusts), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodIssue> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodIssue>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("{}数据并行处理完成，结果数量：{}", businessType, result.size());
        return result;
    }

    /**
     * 处理单个数据分片
     */
    private List<InventoryCurrentPeriodIssue> processChunk(
            List<LogInventoryIoTransaction> chunk,
            String businessType,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodIssue result = processSingleItem(item, businessType, assetOwner, exceptCusts);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条{}数据时发生异常，数据ID：{}", businessType, item.getId(), e);
            }
        }

        return chunkResult;
    }

    /**
     * 处理单条数据的通用方法
     */
    private InventoryCurrentPeriodIssue processSingleItem(
            LogInventoryIoTransaction item,
            String businessType,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        // 根据业务类型调用不同的处理方法
        switch (businessType) {
            case "XSCK":
                return processXSCKSingleItem(item, assetOwner, exceptCusts);
            case "CGTH":
                return processCGTHSingleItem(item, assetOwner, exceptCusts);
            case "YFSCLY":
                return processYFSCLYSingleItem(item, assetOwner, exceptCusts);
            case "CCDTZ":
                return processCCDTZSingleItem(item, assetOwner, exceptCusts);
            case "CB":
                return processCBSingleItem(item, assetOwner, exceptCusts);
            default:
                log.warn("未知的业务类型：{}", businessType);
                return null;
        }
    }

    /**
     * 计算最优分片数量
     */
    private int calculateOptimalChunkCount(int dataSize) {
        if (dataSize <= 500) return 1;
        if (dataSize <= 2000) return 2;
        if (dataSize <= 10000) return 4;
        if (dataSize <= 50000) return 8;
        return Math.min(16, Runtime.getRuntime().availableProcessors() * 2);
    }

    /**
     * 将列表分片
     */
    private <T> List<List<T>> partitionList(List<T> list, int chunkSize) {
        List<List<T>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            chunks.add(list.subList(i, Math.min(i + chunkSize, list.size())));
        }
        return chunks;
    }

    /**
     * 串行处理销售出库数据
     */
    private List<InventoryCurrentPeriodIssue> processXSCKSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> xsck = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processXSCKSingleItem(item, assetOwner, exceptCusts);
                if (result != null && StringUtils.isNotBlank(result.getDocumentType())) {
                    handleInventoryCurrentPeriodIssue(result, xsck);
                }
            } catch (Exception e) {
                log.error("处理销售出库数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return xsck;
    }

    /**
     * 处理单条销售出库数据
     */
    private InventoryCurrentPeriodIssue processXSCKSingleItem(
            LogInventoryIoTransaction item,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        try {
            // 验证必要字段
            if (item.getLiotPpcd() == null || item.getLiotRmk1() == null) {
                return null;
            }

            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            // 销售合同明细
            SalesContractDtl salesContractDtl = salesContractDtlService.selectSalesContractDtlByScdcd(item.getLiotRmk1());
            if (salesContractDtl == null) {
                return null;
            }
            SalesContractMst salesContractMst = salesContractMstService.selectSalesContractMstByScmCd(salesContractDtl.getScdMcd());
            if (salesContractMst == null) {
                return null;
            }
            // 排除研发领用
            if (exceptCusts.contains(salesContractMst.getScmCustcd())) {
                return null;
            }

            skuMasterService.insertSkuMasterIfNotExist(basProdPkg.getBppCd());
            SalesContractInvc salesContractInvc = salesContractInvcService.selectSalesContractInvcBySciMcd(salesContractDtl.getScdMcd());
            BasInvoiceTyp basInvoiceTyp = basInvoiceTypService.selectBasInvoiceTypByBitCd(salesContractInvc.getSciTyp());
            BasCustorgMst basCustorgMst = basCustorgMstService.selectBasCustorgMstByBcmCd(salesContractMst.getScmFrcocd());
            BasCompany basCompany = basCompanyService.selectBasCompanyByBcCd(salesContractMst.getScmPocpcd());

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodIssue.setQuantity(item.getLiotQty());
            // 金额计算 - 添加空值检查
            BigDecimal scd_prc;
            if (salesContractDtl.getScdQty() == null || salesContractDtl.getScdQty().compareTo(BigDecimal.ZERO) == 0) {
                // 如果数量为0或null，使用默认价格0
                scd_prc = BigDecimal.ZERO;
            } else {
                BigDecimal scdTotprc = salesContractDtl.getScdTotprc();
                if (scdTotprc == null) {
                    scd_prc = BigDecimal.ZERO;
                } else {
                    scd_prc = scdTotprc.divide(salesContractDtl.getScdQty(), 2, RoundingMode.HALF_UP);
                }
            }
            BigDecimal liotQty = item.getLiotQty();
            if (liotQty == null) {
                liotQty = BigDecimal.ZERO;
            }
            BigDecimal scd_totprc = scd_prc.multiply(liotQty);
            // 不含税总价
            BigDecimal amount;
            BigDecimal taxDivisor = basInvoiceTyp.getBitTr().add(new BigDecimal(1));
            if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                // 如果税率计算结果为0，直接使用含税总价
                amount = scd_totprc;
            } else {
                amount = scd_totprc.divide(taxDivisor, 2, RoundingMode.HALF_UP);
            }
            inventoryCurrentPeriodIssue.setAmount(amount);
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            inventoryCurrentPeriodIssue.setCustomerCode(salesContractMst.getScmFrcocd());
            inventoryCurrentPeriodIssue.setCustomerName(basCustorgMst.getBcmNm());
            customerMasterService.insertCustomerMasterIfNotExist(salesContractMst.getScmFrcocd(), basCompany.getBcCd());
            SystemUser systemUser = systemUserService.selectSystemUserByLogno(salesContractMst.getScmSmcd());
            inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
            inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(item.getrCreKid());
            inventoryCurrentPeriodIssue.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodIssue.setDocumentType("销售出库");
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(salesContractMst.getScmSmcd());
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("SAL");
            // 部门审核人
            SystemUser manager = systemUserService.selectSystemUserManager(salesContractMst.getScmSmcd());
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartment(manager.getDept());
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(salesContractMst.getScmShdt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(item.getrCreKid());
            inventoryCurrentPeriodIssue.setUpdatedBy(item.getrCreKid());

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理销售出库数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    private InventoryCurrentPeriodIssue processCGTHSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            PurContractDtl purContractDtl = purContractDtlService.selectPurContractDtlByPcdCd(item.getLiotRmk1());
            if (purContractDtl == null) {
                return null;
            }
            PurContractMst purContractMst = purContractMstService.selectPurContractMstByPcmCd(purContractDtl.getPcdMcd());
            if (purContractMst == null) {
                return null;
            }

            // 排除研发领用
            if (exceptCusts.contains(purContractMst.getPcmCd())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodIssue.setQuantity(item.getLiotQty());
            // 金额
            BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
            BigDecimal amount;
            if (item.getLiotIspart().equals(1L)) {
                BigDecimal taxDivisor = item.getLiotBit().add(new BigDecimal(1));
                if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                    // 如果税率计算结果为0，直接使用含税价格
                    amount = price;
                } else {
                    amount = price.divide(taxDivisor, 2, RoundingMode.HALF_UP);
                }
            } else {
                amount = price;
            }
            inventoryCurrentPeriodIssue.setAmount(amount);
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            String sourceType = "01".equals(purContractMst.getPcmIsImportPur()) ? "DOMESTIC" : "INTERNATIONAL";
            inventoryCurrentPeriodIssue.setPurchaseScope(sourceType);
            inventoryCurrentPeriodIssue.setCustomerCode(purContractMst.getPcmLmcd());
            inventoryCurrentPeriodIssue.setCustomerName(purContractMst.getPcmLmnm());
            SystemUser systemUser = systemUserService.selectSystemUserByLogno(purContractMst.getPcmUcd());
            inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
            inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            supplierMasterService.insertSupplierMasterIfNotExist(purContractMst.getPcmScd());
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(item.getrCreKid());
            inventoryCurrentPeriodIssue.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodIssue.setDocumentType("采购退货");
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(purContractMst.getPcmUcd());
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("PUR");
            // 部门审核人
            SystemUser manager = systemUserService.selectSystemUserManager(purContractMst.getPcmUcd());
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartment(manager.getDept());
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(item.getrCreKid());
            inventoryCurrentPeriodIssue.setUpdatedBy(item.getrCreKid());

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理采购退货数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理采购退货数据
     */
    private List<InventoryCurrentPeriodIssue> processCGTHSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> cgth = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processCGTHSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, cgth);
                }
            } catch (Exception e) {
                log.error("处理采购退货数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return cgth;
    }

    private InventoryCurrentPeriodIssue processYFSCLYSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            LabPickingDtl labPickingDtl = labPickingDtlService.selectLabPickingDtlByLpdCd(item.getLiotRmk1());
            if (labPickingDtl == null) {
                return null;
            }
            LabProject labProject = labProjectService.selectLabProjectByLpCd(labPickingDtl.getLpdPcd());
            if (labProject == null) {
                return null;
            }

            // 记账标记
            if (!"6".equals(labPickingDtl.getLpdFinFlag())) {
                return null;
            }

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            BigDecimal liot_qty = item.getLiotQty().abs();
            BigDecimal quantity = item.getLiotTyp() == 500206
                    ? liot_qty.multiply(new BigDecimal(-1))
                    : liot_qty;
            inventoryCurrentPeriodIssue.setQuantity(quantity);
            inventoryCurrentPeriodIssue.setAmount(labPickingDtl.getLpdTotalPrc());
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            String type = "00".equals(labProject.getLpSource()) ? "研发领用" : "生产领用";
            inventoryCurrentPeriodIssue.setDocumentType(type);
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("R&D");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setProjectCode(labProject.getLpCd());
            inventoryCurrentPeriodIssue.setProjectName(labProject.getLpNm());
            String rd_id = labPickingDtl.getLpdRdId() != null
                    ? labPickingDtl.getLpdRdId().toString()
                    : "";
            inventoryCurrentPeriodIssue.setRdSystemId(rd_id);
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理研发生产领用数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理研发生产领用数据
     */
    private List<InventoryCurrentPeriodIssue> processYFSCLYSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> yfly = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processYFSCLYSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, yfly);
                }
            } catch (Exception e) {
                log.error("处理研发生产领用数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return yfly;
    }

    private InventoryCurrentPeriodIssue processHCLYSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts, String month) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinAccAvgCost finAccAvgCost = new FinAccAvgCost();
            finAccAvgCost.setFaacId(finAccMasterRecordsUniqService.getFaacId(assetOwner.code(), month));
            finAccAvgCost.setSku(basProdPkg.getBppCd());
            finAccAvgCost = finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost);

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodIssue.setQuantity(item.getLiotQty());
            BigDecimal amount;
            if ("03".equals(basProdMst.getBpmSort())) {
                amount = item.getLiotQty().multiply(finAccAvgCost.getFaacCost());
            } else {
                BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
                if (item.getLiotIspart().equals(1L)) {
                    BigDecimal taxDivisor = item.getLiotBit().add(new BigDecimal(1));
                    if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                        // 如果税率计算结果为0，直接使用含税价格
                        amount = price;
                    } else {
                        amount = price.divide(taxDivisor, 2, RoundingMode.HALF_UP);
                    }
                } else {
                    amount = price;
                }
            }
            inventoryCurrentPeriodIssue.setAmount(amount);
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodIssue.setDocumentType("耗材领用");
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("R&D");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理耗材领用数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理耗材领用数据
     */
    private List<InventoryCurrentPeriodIssue> processHCLYSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodIssue> hcly = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processHCLYSingleItem(item, assetOwner, exceptCusts, month);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, hcly);
                }
            } catch (Exception e) {
                log.error("处理耗材领用数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return hcly;
    }

    /**
     * 并行处理耗材领用数据
     */
    private List<InventoryCurrentPeriodIssue> processHCLYDataInParallel(
            List<LogInventoryIoTransaction> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理耗材领用数据，总数据量：{}，分片数：{}，每片大小：{}",
                allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processHCLYChunk(chunk, assetOwner, exceptCusts, month), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodIssue> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理耗材领用数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodIssue>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("耗材领用数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 处理耗材领用数据分片
     */
    private List<InventoryCurrentPeriodIssue> processHCLYChunk(
            List<LogInventoryIoTransaction> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodIssue> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodIssue result = processHCLYSingleItem(item, assetOwner, exceptCusts, month);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条耗材领用数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    private InventoryCurrentPeriodIssue processCCDTZSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts) {
        try {
            if (!item.getLiotSlcd().contains("poor-999")) {
                return null;
            }
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodIssue.setQuantity(new BigDecimal(0));
            BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
            BigDecimal amount;
            if (item.getLiotIspart().equals(1L)) {
                BigDecimal taxDivisor = item.getLiotBit().add(new BigDecimal(1));
                if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                    // 如果税率计算结果为0，直接使用含税价格
                    amount = price;
                } else {
                    amount = price.divide(taxDivisor, 2, RoundingMode.HALF_UP);
                }
            } else {
                amount = price;
            }
            inventoryCurrentPeriodIssue.setAmount(amount);
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodIssue.setDocumentType("差错单调整");
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("PUR");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理差错单调整数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理差错单调整数据
     */
    private List<InventoryCurrentPeriodIssue> processCCDTZSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> ccdtz = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processCCDTZSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, ccdtz);
                }
            } catch (Exception e) {
                log.error("处理差错单调整数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return ccdtz;
    }

    private InventoryCurrentPeriodIssue processBFSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts, String month) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinAccAvgCost finAccAvgCost = new FinAccAvgCost();
            finAccAvgCost.setFaacId(finAccMasterRecordsUniqService.getFaacId(assetOwner.code(), month));
            finAccAvgCost.setSku(basProdPkg.getBppCd());
            finAccAvgCost = finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost);

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodIssue.setQuantity(item.getLiotQty());
            BigDecimal amount;
            BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
            BigDecimal amt;
            if (item.getLiotIspart().equals(1L)) {
                BigDecimal taxDivisor = item.getLiotBit().add(new BigDecimal(1));
                if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                    // 如果税率计算结果为0，直接使用含税价格
                    amt = price;
                } else {
                    amt = price.divide(taxDivisor, 2, RoundingMode.HALF_UP);
                }
            } else {
                amt = price;
            }
            if (basProdMst.getBpmSort().equals("01") || basProdMst.getBpmSort().equals("02")) {
                amount = item.getLiotQty().multiply(finAccAvgCost.getFaacCost());
                if (amount.compareTo(new BigDecimal(0)) == 0) {
                    amount = amt;
                }
            } else {
                amount = amt;
            }
            inventoryCurrentPeriodIssue.setAmount(amount);
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodIssue.setDocumentType("报废");
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("LOG");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理报废数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理报废数据
     */
    private List<InventoryCurrentPeriodIssue> processBFSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodIssue> bf = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processBFSingleItem(item, assetOwner, exceptCusts, month);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, bf);
                }
            } catch (Exception e) {
                log.error("处理报废数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return bf;
    }

    /**
     * 并行处理报废数据
     */
    private List<InventoryCurrentPeriodIssue> processBFDataInParallel(
            List<LogInventoryIoTransaction> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理报废数据，总数据量：{}，分片数：{}，每片大小：{}",
                allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processBFChunk(chunk, assetOwner, exceptCusts, month), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodIssue> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理报废数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodIssue>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("报废数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 处理报废数据分片
     */
    private List<InventoryCurrentPeriodIssue> processBFChunk(
            List<LogInventoryIoTransaction> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodIssue> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodIssue result = processBFSingleItem(item, assetOwner, exceptCusts, month);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条报废数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    private InventoryCurrentPeriodIssue processPKSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts, String month) {
        try {
            // 添加空值检查
            if (!validateInventoryTransaction(item)) {
                return null;
            }

            if (item.getLiotTyp() == 500109 && safeGetString(item.getLiotSlcd()).contains("poor-999")) {
                return null;
            }
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinAccAvgCost finAccAvgCost = new FinAccAvgCost();
            finAccAvgCost.setFaacId(finAccMasterRecordsUniqService.getFaacId(assetOwner.code(), month));
            finAccAvgCost.setSku(basProdPkg.getBppCd());
            finAccAvgCost = finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost);

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            BigDecimal liotQty = safeGetBigDecimal(item.getLiotQty());
            BigDecimal quantity = item.getLiotTyp() == 500109 ? new BigDecimal(0) : liotQty;
            inventoryCurrentPeriodIssue.setQuantity(quantity);
            BigDecimal amount;
            BigDecimal liotUnitp = safeGetBigDecimal(item.getLiotUnitp());
            BigDecimal price = liotQty.multiply(liotUnitp);
            BigDecimal amt;
            if (safeGetLong(item.getLiotIspart()).equals(1L)) {
                BigDecimal liotBit = safeGetBigDecimal(item.getLiotBit());
                BigDecimal taxDivisor = liotBit.add(new BigDecimal(1));
                if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                    // 如果税率计算结果为0，直接使用含税价格
                    amt = price;
                } else {
                    amt = price.divide(taxDivisor, 2, RoundingMode.HALF_UP);
                }
            } else {
                amt = price;
            }
            if (basProdMst.getBpmSort().equals("01") || basProdMst.getBpmSort().equals("02")) {
                BigDecimal faacCost = finAccAvgCost != null ? safeGetBigDecimal(finAccAvgCost.getFaacCost()) : BigDecimal.ZERO;
                amount = liotQty.multiply(faacCost);
                if (amount.compareTo(new BigDecimal(0)) == 0) {
                    amount = amt;
                }
            } else {
                amount = amt;
            }
            if (item.getLiotTyp() == 500109) {
                inventoryCurrentPeriodIssue.setAmount(amt);
            } else {
                inventoryCurrentPeriodIssue.setAmount(amount);
            }
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            String documentType = item.getLiotTyp() == 500102 ? "盘亏" : "差额盘亏";
            inventoryCurrentPeriodIssue.setDocumentType(documentType);
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("LOG");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理盘亏和差额盘亏数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理盘亏和差额盘亏数据
     */
    private List<InventoryCurrentPeriodIssue> processPKSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodIssue> pk = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processPKSingleItem(item, assetOwner, exceptCusts, month);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, pk);
                }
            } catch (Exception e) {
                log.error("处理盘亏和差额盘亏数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return pk;
    }

    /**
     * 并行处理盘亏和差额盘亏数据
     */
    private List<InventoryCurrentPeriodIssue> processPKDataInParallel(
            List<LogInventoryIoTransaction> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理盘亏和差额盘亏数据，总数据量：{}，分片数：{}，每片大小：{}",
                allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processPKChunk(chunk, assetOwner, exceptCusts, month), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodIssue> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理盘亏和差额盘亏数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodIssue>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("盘亏和差额盘亏数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 处理盘亏和差额盘亏数据分片
     */
    private List<InventoryCurrentPeriodIssue> processPKChunk(
            List<LogInventoryIoTransaction> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodIssue> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodIssue result = processPKSingleItem(item, assetOwner, exceptCusts, month);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条盘亏和差额盘亏数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    private InventoryCurrentPeriodIssue processCBSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodIssue.setQuantity(new BigDecimal(0));
            BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
            BigDecimal amount;
            if (item.getLiotIspart().equals(1L)) {
                BigDecimal taxDivisor = item.getLiotBit().add(new BigDecimal(1));
                if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                    // 如果税率计算结果为0，直接使用含税价格
                    amount = price;
                } else {
                    amount = price.divide(taxDivisor, 2, RoundingMode.HALF_UP);
                }
            } else {
                amount = price;
            }
            inventoryCurrentPeriodIssue.setAmount(amount);
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(item.getLiotOrgmcd());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodIssue.setDocumentType("拆包出");
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("LOG");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理拆包数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理拆包数据
     */
    private List<InventoryCurrentPeriodIssue> processCBSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> cb = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodIssue result = processCBSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, cb);
                }
            } catch (Exception e) {
                log.error("处理拆包数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return cb;
    }


    private InventoryCurrentPeriodIssue processFZSingleItem(LogRepackPackageUsePlan item, AssetOwner assetOwner, List<String> exceptCusts) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLrpupSkuCd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(basProdPkg.getBppCd());

            LogPtMst logPtMst = logPtMstService.selectLogPtMstByLpmCd(item.getLrpupPsn());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLrpupSkuCd());
            inventoryCurrentPeriodIssue.setQuantity(item.getLrpupSkuTotalQty());
            inventoryCurrentPeriodIssue.setAmount(item.getLrpupSkuTotalPrc());
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(logPtMst.getLpmLocOrgmcd());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(logPtMst.getLpmBn());
            inventoryCurrentPeriodIssue.setWarehouse(logPtMst.getLpmLocDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(logPtMst.getLpmLocSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(logPtMst.getLpmLocTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_repack_package_use_plan");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(logPtMst.getLpmLocBnEncrypt());
            if (StringUtils.isBlank(item.getLrpupPcd())) {
                inventoryCurrentPeriodIssue.setDocumentType("分装领料出库");
            } else if (item.getFlag() == 6 && StringUtils.isNotBlank(item.getLrpupPcd())) {
                inventoryCurrentPeriodIssue.setDocumentType("分装领料退库");
            }
            inventoryCurrentPeriodIssue.setAssetOwner(logPtMst.getLpmLocOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("LOG");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            // 只有当文档类型不为空时才返回
            if (StringUtils.isNotBlank(inventoryCurrentPeriodIssue.getDocumentType())) {
                return inventoryCurrentPeriodIssue;
            }
            return null;
        } catch (Exception e) {
            log.error("处理分装数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理分装数据
     */
    private List<InventoryCurrentPeriodIssue> processFZSequentially(
            List<LogRepackPackageUsePlan> logRepackPackageUsePlans,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> fz = new ArrayList<>();

        for (LogRepackPackageUsePlan item : logRepackPackageUsePlans) {
            try {
                InventoryCurrentPeriodIssue result = processFZSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, fz);
                }
            } catch (Exception e) {
                log.error("处理分装数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return fz;
    }

    /**
     * 并行处理分装数据
     */
    private List<InventoryCurrentPeriodIssue> processFZDataInParallel(
            List<LogRepackPackageUsePlan> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理分装数据，总数据量：{}，分片数：{}，每片大小：{}",
                allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogRepackPackageUsePlan>> chunks = partitionFZList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processFZChunk(chunk, assetOwner, exceptCusts), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodIssue> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理分装数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodIssue>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("分装数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 处理分装数据分片
     */
    private List<InventoryCurrentPeriodIssue> processFZChunk(
            List<LogRepackPackageUsePlan> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> chunkResult = new ArrayList<>();

        for (LogRepackPackageUsePlan item : chunk) {
            try {
                InventoryCurrentPeriodIssue result = processFZSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条分装数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    /**
     * 将LogRepackPackageUsePlan列表分片
     */
    private List<List<LogRepackPackageUsePlan>> partitionFZList(List<LogRepackPackageUsePlan> list, int chunkSize) {
        List<List<LogRepackPackageUsePlan>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            chunks.add(list.subList(i, Math.min(i + chunkSize, list.size())));
        }
        return chunks;
    }

    private InventoryCurrentPeriodIssue processXYSCSingleItem(LogRepackPackageUsePlan item, AssetOwner assetOwner, List<String> exceptCusts) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLrpupSkuCd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(basProdPkg.getBppCd());
            LogRadDeveloCompose logRadDeveloCompose = logRadDeveloComposeService.selectLogRadDeveloComposeByLrdcCd(item.getLrpupPsnCd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLrpupSkuCd());
            inventoryCurrentPeriodIssue.setQuantity(item.getLrpupSkuTotalQty());
            inventoryCurrentPeriodIssue.setAmount(item.getLrpupSkuTotalPrc());
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(logRadDeveloCompose.getLrdcBn());
            inventoryCurrentPeriodIssue.setWarehouse(logRadDeveloCompose.getLrdcDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(logRadDeveloCompose.getLrdcSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(logRadDeveloCompose.getLrdcTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_repack_package_use_plan");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(logRadDeveloCompose.getLrdcBnEncrypt());
            inventoryCurrentPeriodIssue.setDocumentType("小样生产领料");
            inventoryCurrentPeriodIssue.setAssetOwner(logRadDeveloCompose.getLrdcOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("R&D");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理小样生产数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理小样生产数据
     */
    private List<InventoryCurrentPeriodIssue> processXYSCSequentially(
            List<LogRepackPackageUsePlan> logRepackPackageUsePlans,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> xysc = new ArrayList<>();

        for (LogRepackPackageUsePlan item : logRepackPackageUsePlans) {
            try {
                InventoryCurrentPeriodIssue result = processXYSCSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, xysc);
                }
            } catch (Exception e) {
                log.error("处理小样生产数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return xysc;
    }

    /**
     * 并行处理小样生产数据
     */
    private List<InventoryCurrentPeriodIssue> processXYSCDataInParallel(
            List<LogRepackPackageUsePlan> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理小样生产数据，总数据量：{}，分片数：{}，每片大小：{}",
                allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogRepackPackageUsePlan>> chunks = partitionFZList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processXYSCChunk(chunk, assetOwner, exceptCusts), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodIssue> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理小样生产数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodIssue>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("小样生产数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 处理小样生产数据分片
     */
    private List<InventoryCurrentPeriodIssue> processXYSCChunk(
            List<LogRepackPackageUsePlan> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodIssue> chunkResult = new ArrayList<>();

        for (LogRepackPackageUsePlan item : chunk) {
            try {
                InventoryCurrentPeriodIssue result = processXYSCSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条小样生产数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    private InventoryCurrentPeriodIssue processHHHSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts, String month, Map<String, BigDecimal> outMap) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinAccAvgCost finAccAvgCost = new FinAccAvgCost();
            finAccAvgCost.setFaacId(finAccMasterRecordsUniqService.getFaacId(assetOwner.code(), month));
            finAccAvgCost.setSku(basProdPkg.getBppCd());
            finAccAvgCost = finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost);

            if (outMap.containsKey(item.getLiotPpcd())
                    && finAccAvgCost.getFaacQty().compareTo(outMap.get(item.getLiotPpcd())) < 0) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            // 封装数据
            InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue = new InventoryCurrentPeriodIssue();
            inventoryCurrentPeriodIssue.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodIssue.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodIssue.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodIssue.setQuantity(item.getLiotQty());
            BigDecimal amount;
            BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
            BigDecimal amt;
            if (item.getLiotIspart().equals(1L)) {
                BigDecimal taxDivisor = item.getLiotBit().add(new BigDecimal(1));
                if (taxDivisor.compareTo(BigDecimal.ZERO) == 0) {
                    // 如果税率计算结果为0，直接使用含税价格
                    amt = price;
                } else {
                    amt = price.divide(taxDivisor, 2, RoundingMode.HALF_UP);
                }
            } else {
                amt = price;
            }
            if (!"03".equals(basProdMst.getBpmSort())) {
                amount = item.getLiotQty().multiply(finAccAvgCost.getFaacCost());
                if (amount.compareTo(new BigDecimal(0)) == 0) {
                    amount = amt;
                }
            } else {
                amount = amt;
            }
            if (item.getLiotTyp() == 500603) {
                inventoryCurrentPeriodIssue.setAmount(finAccAvgCost.getFaacCost().multiply(item.getLiotQty()));
            } else {
                inventoryCurrentPeriodIssue.setAmount(amount);
            }
            // 外采
            inventoryCurrentPeriodIssue.setSourceType("PURCHASED");
            // 国内采购
            inventoryCurrentPeriodIssue.setPurchaseScope("DOMESTIC");
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            if (StringUtils.isBlank(rCreKid)) {
                inventoryCurrentPeriodIssue.setCustomerCode("");
                inventoryCurrentPeriodIssue.setCustomerName("");
                inventoryCurrentPeriodIssue.setDepartmentGroupCode("");
                inventoryCurrentPeriodIssue.setDepartmentGroupName("");
            } else {
                SystemUser systemUser = systemUserService.selectSystemUserByLogno(rCreKid);
                inventoryCurrentPeriodIssue.setCustomerCode(systemUser.getSuLogno());
                inventoryCurrentPeriodIssue.setCustomerName(systemUser.getSuEnm());
                inventoryCurrentPeriodIssue.setDepartmentGroupCode(systemUser.getDept());
                inventoryCurrentPeriodIssue.setDepartmentGroupName(systemUser.getDeptName());
            }
            inventoryCurrentPeriodIssue.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodIssue.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodIssue.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodIssue.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodIssue.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodIssue.setSourceRecordId(item.getId());
            inventoryCurrentPeriodIssue.setIssueTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setOperator(rCreKid);
            inventoryCurrentPeriodIssue.setAuditor(rCreKid);
            inventoryCurrentPeriodIssue.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setEncryptedBatch(item.getLiotBnEncrypt());
            String documentType = item.getLiotTyp() == 500601 ? "换货号生产出" : "换货号出";
            inventoryCurrentPeriodIssue.setDocumentType(documentType);
            inventoryCurrentPeriodIssue.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodIssue.setProcessor(rCreKid);
            inventoryCurrentPeriodIssue.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodIssue.setDepartment("LOG");
            // 部门审核人
            SystemUser manager = null;
            if (StringUtils.isNotBlank(rCreKid)) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodIssue.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodIssue.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodIssue.setCreatedBy(rCreKid);
            inventoryCurrentPeriodIssue.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodIssue;
        } catch (Exception e) {
            log.error("处理换货号数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 串行处理换货号数据
     */
    private List<InventoryCurrentPeriodIssue> processHHHSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodIssue> hhh = new ArrayList<>();

        // 分类换货号出 和 换货号入
        List<LogInventoryIoTransaction> outList = logInventoryIoTransactions.stream()
                .filter(t -> t.getLiotTyp() == 500601 || t.getLiotTyp() == 500603)
                .collect(Collectors.toList());
        List<LogInventoryIoTransaction> inList = logInventoryIoTransactions.stream()
                .filter(t -> t.getLiotTyp() == 500602 || t.getLiotTyp() == 500604)
                .collect(Collectors.toList());

        // 找出要排除的
        Set<String> excludeRmk1 = outList.stream()
                .filter(out -> inList.stream().anyMatch(in ->
                        Objects.equals(out.getLiotRmk1(), in.getLiotRmk1()) &&
                                Objects.equals(out.getLiotPpcd(), in.getLiotPpcd()) &&
                                (out.getLiotQty().compareTo(in.getLiotQty()) == 0
                                        || in.getLiotQty().compareTo(out.getLiotQty().multiply(BigDecimal.valueOf(2))) == 0)))
                .map(LogInventoryIoTransaction::getLiotRmk1)
                .collect(Collectors.toSet());

        Map<String, BigDecimal> outMap = outList.stream()
                .filter(out -> !excludeRmk1.contains(out.getLiotRmk1()))
                .collect(Collectors.groupingBy(LogInventoryIoTransaction::getLiotPpcd,
                        Collectors.mapping(LogInventoryIoTransaction::getLiotQty,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        for (LogInventoryIoTransaction item : outList) {
            try {
                InventoryCurrentPeriodIssue result = processHHHSingleItem(item, assetOwner, exceptCusts, month, outMap);
                if (result != null) {
                    handleInventoryCurrentPeriodIssue(result, hhh);
                }
            } catch (Exception e) {
                log.error("处理换货号数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return hhh;
    }

    /**
     * 并行处理换货号数据
     */
    private List<InventoryCurrentPeriodIssue> processHHHDataInParallel(
            List<LogInventoryIoTransaction> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 分类换货号出 和 换货号入
        List<LogInventoryIoTransaction> outList = allData.stream()
                .filter(t -> t.getLiotTyp() == 500601 || t.getLiotTyp() == 500603)
                .collect(Collectors.toList());
        List<LogInventoryIoTransaction> inList = allData.stream()
                .filter(t -> t.getLiotTyp() == 500602 || t.getLiotTyp() == 500604)
                .collect(Collectors.toList());

        // 找出要排除的
        Set<String> excludeRmk1 = outList.stream()
                .filter(out -> inList.stream().anyMatch(in ->
                        Objects.equals(out.getLiotRmk1(), in.getLiotRmk1()) &&
                                Objects.equals(out.getLiotPpcd(), in.getLiotPpcd()) &&
                                (out.getLiotQty().compareTo(in.getLiotQty()) == 0
                                        || in.getLiotQty().compareTo(out.getLiotQty().multiply(BigDecimal.valueOf(2))) == 0)))
                .map(LogInventoryIoTransaction::getLiotRmk1)
                .collect(Collectors.toSet());

        Map<String, BigDecimal> outMap = outList.stream()
                .filter(out -> !excludeRmk1.contains(out.getLiotRmk1()))
                .collect(Collectors.groupingBy(LogInventoryIoTransaction::getLiotPpcd,
                        Collectors.mapping(LogInventoryIoTransaction::getLiotQty,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(outList.size());
        int chunkSize = Math.max(1, outList.size() / chunkCount);

        log.info("开始并行处理换货号数据，总数据量：{}，分片数：{}，每片大小：{}",
                outList.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(outList, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodIssue>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processHHHChunk(chunk, assetOwner, exceptCusts, month, outMap), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodIssue> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理换货号数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodIssue>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("换货号数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 处理换货号数据分片
     */
    private List<InventoryCurrentPeriodIssue> processHHHChunk(
            List<LogInventoryIoTransaction> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month,
            Map<String, BigDecimal> outMap) {

        List<InventoryCurrentPeriodIssue> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodIssue result = processHHHSingleItem(item, assetOwner, exceptCusts, month, outMap);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条换货号数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }


    /**
     * 采购退货
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadCGTH(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "采购退货(CGTH)";

        List<InventoryCurrentPeriodIssue> cgth = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("采购退货", startDate, endDate, assetOwner);

            // 研发
            List<String> pcm_cdsr = purContractMstService.selectPurContractMstListR();
            List<String> exceptCusts = new ArrayList<>(pcm_cdsr);

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("210101");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("采购退货数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                cgth = processDataInParallel(logInventoryIoTransactions, "CGTH", assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("采购退货数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                cgth = processCGTHSequentially(logInventoryIoTransactions, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!cgth.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, cgth.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return cgth;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理采购退货数据时发生异常", e);
            return cgth;
        }
    }

    /**
     * 研发生产领用出库
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadYFSCLY(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "研发生产领用(YFSCLY)";

        List<InventoryCurrentPeriodIssue> yfly = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("研发生产领用出库", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500205");
            liot_typs.add("500206");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("研发生产领用数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                yfly = processDataInParallel(logInventoryIoTransactions, "YFSCLY", assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("研发生产领用数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                yfly = processYFSCLYSequentially(logInventoryIoTransactions, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!yfly.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, yfly.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return yfly;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理研发生产领用出库数据时发生异常", e);
            return yfly;
        }
    }

    /**
     * 耗材领用出库
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadHCLY(String startDate, String endDate, AssetOwner assetOwner, String month) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "耗材领用(HCLY)";

        List<InventoryCurrentPeriodIssue> hcly = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("耗材领用出库", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500104");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("耗材领用数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                hcly = processHCLYDataInParallel(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            } else {
                // 数据量小，使用串行处理
                log.info("耗材领用数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                hcly = processHCLYSequentially(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!hcly.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, hcly.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return hcly;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理耗材领用出库数据时发生异常", e);
            return hcly;
        }
    }

    /**
     * 差错单调整出
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadCCDTZ(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "差错单调整(CCDTZ)";

        List<InventoryCurrentPeriodIssue> ccdtz = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("差错单调整出库", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500701");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("差错单调整数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                ccdtz = processDataInParallel(logInventoryIoTransactions, "CCDTZ", assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("差错单调整数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                ccdtz = processCCDTZSequentially(logInventoryIoTransactions, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!ccdtz.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, ccdtz.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return ccdtz;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理差错单调整出库数据时发生异常", e);
            return ccdtz;
        }
    }

    /**
     * 报废
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadBF(String startDate, String endDate, AssetOwner assetOwner, String month) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "报废(BF)";

        List<InventoryCurrentPeriodIssue> bf = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("报废出库", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500105");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("报废数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                bf = processBFDataInParallel(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            } else {
                // 数据量小，使用串行处理
                log.info("报废数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                bf = processBFSequentially(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!bf.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, bf.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return bf;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理报废出库数据时发生异常", e);
            return bf;
        }
    }

    /**
     * 盘亏和差额盘亏
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadPK(String startDate, String endDate, AssetOwner assetOwner, String month) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "盘亏和差额盘亏(PK)";

        List<InventoryCurrentPeriodIssue> pk = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("盘亏和差额盘亏", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500102");
            liot_typs.add("500109");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("盘亏和差额盘亏数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                pk = processPKDataInParallel(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            } else {
                // 数据量小，使用串行处理
                log.info("盘亏和差额盘亏数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                pk = processPKSequentially(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!pk.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, pk.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return pk;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理盘亏和差额盘亏数据时发生异常", e);
            return pk;
        }
    }

    /**
     * 拆包
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadCB(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "拆包(CB)";

        List<InventoryCurrentPeriodIssue> cb = new ArrayList<>();

        try {
            AssetOwnerMaster assetOwnerMaster = assetOwner.entity();

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500605");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("拆包数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                cb = processDataInParallel(logInventoryIoTransactions, "CB", assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("拆包数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                cb = processCBSequentially(logInventoryIoTransactions, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!cb.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, cb.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return cb;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理拆包数据时发生异常", e);
            return cb;
        }
    }

    /**
     * 分装领料出
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadFZ(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "分装(FZ)";

        List<InventoryCurrentPeriodIssue> fz = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("分装领料出", startDate, endDate, assetOwner);

            AssetOwnerMaster assetOwnerMaster = assetOwner.entity();

            List<String> exceptCusts = new ArrayList<>();

            LogRepackPackageUsePlan logRepackPackageUsePlan = new LogRepackPackageUsePlan();
            logRepackPackageUsePlan.setStartDate(startDate);
            logRepackPackageUsePlan.setEndDate(endDate);
            logRepackPackageUsePlan.setZti(assetOwner.code());
            List<LogRepackPackageUsePlan> logRepackPackageUsePlans =
                    logRepackPackageUsePlanService.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);

            // 根据数据量选择处理方式
            if (logRepackPackageUsePlans.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("分装数据量较大({}条)，启用并行处理", logRepackPackageUsePlans.size());
                fz = processFZDataInParallel(logRepackPackageUsePlans, assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("分装数据量较小({}条)，使用串行处理", logRepackPackageUsePlans.size());
                fz = processFZSequentially(logRepackPackageUsePlans, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!fz.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, fz.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return fz;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理分装领料出库数据时发生异常", e);
            return fz;
        }
    }

    /**
     * 小样生产领料
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadXYSC(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "小样生产(XYSC)";

        List<InventoryCurrentPeriodIssue> xysc = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("小样生产领料出库", startDate, endDate, assetOwner);

            AssetOwnerMaster assetOwnerMaster = assetOwner.entity();

            List<String> exceptCusts = new ArrayList<>();

            LogRepackPackageUsePlan logRepackPackageUsePlan = new LogRepackPackageUsePlan();
            logRepackPackageUsePlan.setStartDate(startDate);
            logRepackPackageUsePlan.setEndDate(endDate);
            logRepackPackageUsePlan.setZti(assetOwner.code());
            logRepackPackageUsePlan.setLrpupTyp("09");
            logRepackPackageUsePlan.setFlag(8);
            List<LogRepackPackageUsePlan> logRepackPackageUsePlans =
                    logRepackPackageUsePlanService.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);

            // 根据数据量选择处理方式
            if (logRepackPackageUsePlans.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("小样生产数据量较大({}条)，启用并行处理", logRepackPackageUsePlans.size());
                xysc = processXYSCDataInParallel(logRepackPackageUsePlans, assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("小样生产数据量较小({}条)，使用串行处理", logRepackPackageUsePlans.size());
                xysc = processXYSCSequentially(logRepackPackageUsePlans, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!xysc.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, xysc.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return xysc;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理小样生产领料出库数据时发生异常", e);
            return xysc;
        }
    }


    /**
     * 换货号出
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodIssue> loadHHH(String startDate, String endDate, AssetOwner assetOwner, String month) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "换货号(HHH)";

        List<InventoryCurrentPeriodIssue> hhh = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("换货号出库", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500601");
            liot_typs.add("500602");
            liot_typs.add("500603");
            liot_typs.add("500604");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("换货号数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                hhh = processHHHDataInParallel(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            } else {
                // 数据量小，使用串行处理
                log.info("换货号数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                hhh = processHHHSequentially(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            }


            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!hhh.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, hhh.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return hhh;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理换货号出库数据时发生异常", e);
            return hhh;
        }
    }

    /**
     * 加载分装数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    @Override
    public void loadConversionData(String startDate, String endDate, AssetOwner assetOwner) {
        long startTime = System.currentTimeMillis();
        log.info("开始加载分装数据，日期范围：{} 至 {}", startDate, endDate);

        try {
            // 使用CompletableFuture并行处理两个主要任务
            CompletableFuture<Void> repackTask = CompletableFuture.runAsync(() ->
                processRepackData(startDate, endDate, assetOwner), conversionDataExecutor);

            CompletableFuture<Void> inventoryTask = CompletableFuture.runAsync(() ->
                processInventoryConversionData(startDate, endDate, assetOwner), conversionDataExecutor);

            // 等待两个任务完成
            CompletableFuture.allOf(repackTask, inventoryTask).get();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            log.info("分装数据加载完成，总耗时：{}", durationStr);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            log.error("分装数据加载失败，耗时：{}，异常信息：{}", durationStr, e.getMessage(), e);
            throw new RuntimeException("分装数据加载失败", e);
        }
    }

    @Override
    public void loadDependencyGraph(String assetOwner) {
        conversionDependencyGraphService.buildDependencyGraph(assetOwner);
    }

    /**
     * 处理分装数据（分装 + 小样生产）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void processRepackData(String startDate, String endDate, AssetOwner assetOwner) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理分装数据");

        try {
            // 查询分装结果数据
            LogRepackResult logRepackResult = new LogRepackResult();
            logRepackResult.setStartDate(startDate);
            logRepackResult.setEndDate(endDate);
            logRepackResult.setLrrOrgmcd(assetOwner.code());
            List<LogRepackResult> logRepackResults = logRepackResultService.selectLogRepackResultList(logRepackResult);

            if (logRepackResults.isEmpty()) {
                log.info("未找到分装数据");
                return;
            }

            log.info("找到{}条分装结果数据，开始并行处理", logRepackResults.size());

            // 根据数据量选择处理策略
            if (logRepackResults.size() > 100) {
                // 数据量大，使用并行处理
                processRepackDataInParallel(logRepackResults);
            } else {
                // 数据量小，使用串行处理
                processRepackDataSequentially(logRepackResults);
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.info("分装数据处理完成，耗时：{}", formatDuration(duration));

        } catch (Exception e) {
            log.error("处理分装数据时发生异常", e);
            throw new RuntimeException("处理分装数据失败", e);
        }
    }


    /**
     * 并行处理分装数据
     * @param logRepackResults 分装结果列表
     */
    private void processRepackDataInParallel(List<LogRepackResult> logRepackResults) {
        // 计算最优分片数量
        int chunkCount = calculateOptimalChunkCount(logRepackResults.size());
        int chunkSize = Math.max(1, logRepackResults.size() / chunkCount);

        log.info("并行处理分装数据，总数据量：{}，分片数：{}，每片大小：{}",
                logRepackResults.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogRepackResult>> chunks = partitionList(logRepackResults, chunkSize);

        // 创建并行任务
        List<CompletableFuture<Void>> chunkFutures = chunks.stream()
                .map(chunk -> CompletableFuture.runAsync(() ->
                    processRepackDataSequentially(chunk), dataProcessorExecutor))
                .collect(Collectors.toList());

        // 等待所有分片处理完成
        try {
            CompletableFuture.allOf(chunkFutures.toArray(new CompletableFuture[0])).get();
            log.info("分装数据并行处理完成");
        } catch (Exception e) {
            log.error("分装数据并行处理失败", e);
            throw new RuntimeException("分装数据并行处理失败", e);
        }
    }

    /**
     * 处理分装数据（支持串行和分片处理）
     * @param logRepackResults 分装结果列表
     */
    private void processRepackDataSequentially(List<LogRepackResult> logRepackResults) {
        if (logRepackResults.size() > 100) {
            log.info("处理分装数据，数据量：{}", logRepackResults.size());
        }

        for (LogRepackResult item : logRepackResults) {
            try {
                processRepackItem(item);
            } catch (Exception e) {
                log.error("处理分装数据项时发生异常，数据ID：{}", item.getLrrCd(), e);
            }
        }
    }

    /**
     * 处理单个分装数据项
     * @param item 分装结果项
     */
    private void processRepackItem(LogRepackResult item) {
        // 查询相关的包装使用结果明细
        LogRepackPackageUseResultDtl query = new LogRepackPackageUseResultDtl();
        query.setLrpurdMcd(item.getLrrCd());
        List<LogRepackPackageUseResultDtl> logRepackPackageUseResultDtls =
                logRepackPackageUseResultDtlService.selectLogRepackPackageUseResultDtlList(query);

        // 并行处理明细数据（如果明细数据较多）
        if (logRepackPackageUseResultDtls.size() > 50) {
            processRepackDetailsInParallel(logRepackPackageUseResultDtls, item);
        } else {
            processRepackDetailsSequentially(logRepackPackageUseResultDtls, item);
        }
    }

    /**
     * 并行处理分装明细数据
     * @param details 明细列表
     * @param parentItem 父级分装结果
     */
    private void processRepackDetailsInParallel(List<LogRepackPackageUseResultDtl> details, LogRepackResult parentItem) {
        List<CompletableFuture<Void>> detailFutures = details.stream()
                .map(detail -> CompletableFuture.runAsync(() ->
                    processRepackDetail(detail, parentItem), dataProcessorExecutor))
                .collect(Collectors.toList());

        try {
            CompletableFuture.allOf(detailFutures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("并行处理分装明细数据失败", e);
            throw new RuntimeException("并行处理分装明细数据失败", e);
        }
    }

    /**
     * 串行处理分装明细数据
     * @param details 明细列表
     * @param parentItem 父级分装结果
     */
    private void processRepackDetailsSequentially(List<LogRepackPackageUseResultDtl> details, LogRepackResult parentItem) {
        for (LogRepackPackageUseResultDtl detail : details) {
            try {
                processRepackDetail(detail, parentItem);
            } catch (Exception e) {
                log.error("处理分装明细数据时发生异常，明细ID：{}", detail.getLrpurdCd(), e);
            }
        }
    }

    /**
     * 处理单个分装明细
     * @param detail 分装明细
     * @param parentItem 父级分装结果
     */
    private void processRepackDetail(LogRepackPackageUseResultDtl detail, LogRepackResult parentItem) {
        String ruleCode = detail.getLrpurdSkuCd() + "__" + parentItem.getLrrMainSkuCd();

        Long ruleId = conversionRuleService.getConversionRuleId(ruleCode, parentItem.getLrrOrgmcd());
        conversionRuleDetailService.addConversionRuleDetail(
                detail.getLrpurdSkuCd(),
                parentItem.getLrrMainSkuCd(),
                detail.getLrpurdSkuBnQty().intValue(),
                parentItem.getLrrMainSkuQty().intValue(),
                ruleId);
    }

    /**
     * 处理库存转换数据（换货号出、换货号生产出、换标、批次整理）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void processInventoryConversionData(String startDate, String endDate, AssetOwner assetOwner) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理库存转换数据");

        try {
            // 查询库存事务数据：换货号出（500603）换货号生产出（500601） 换标（800601） 批次整理（500801，501001）
            LogInventoryIoTransaction logInventoryIoTransaction = new LogInventoryIoTransaction();
            logInventoryIoTransaction.setStartDate(startDate);
            logInventoryIoTransaction.setEndDate(endDate);
            logInventoryIoTransaction.setLiotOrgmcd(assetOwner.code());
            logInventoryIoTransaction.setLiotTyps(Arrays.asList("500601", "500603", "800601", "500801", "501001"));

            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            if (logInventoryIoTransactions.isEmpty()) {
                log.info("未找到库存转换数据");
                return;
            }

            log.info("找到{}条库存转换数据，开始并行处理", logInventoryIoTransactions.size());

            if (logInventoryIoTransactions.size() > 100) {
                // 数据量大，使用并行处理
                processInventoryConversionDataInParallel(logInventoryIoTransactions);
            } else {
                // 数据量小，使用串行处理
                processInventoryConversionDataSequentially(logInventoryIoTransactions);
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.info("库存转换数据处理完成，耗时：{}", formatDuration(duration));

        } catch (Exception e) {
            log.error("处理库存转换数据时发生异常", e);
            throw new RuntimeException("处理库存转换数据失败", e);
        }
    }

    /**
     * 并行处理库存转换数据
     * @param logInventoryIoTransactions 库存事务列表
     */
    private void processInventoryConversionDataInParallel(List<LogInventoryIoTransaction> logInventoryIoTransactions) {
        // 计算最优分片数量
        int chunkCount = calculateOptimalChunkCount(logInventoryIoTransactions.size());
        int chunkSize = Math.max(1, logInventoryIoTransactions.size() / chunkCount);

        log.info("并行处理库存转换数据，总数据量：{}，分片数：{}，每片大小：{}",
                logInventoryIoTransactions.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(logInventoryIoTransactions, chunkSize);

        // 创建并行任务
        List<CompletableFuture<Void>> chunkFutures = chunks.stream()
                .map(chunk -> CompletableFuture.runAsync(() ->
                    processInventoryConversionDataSequentially(chunk), dataProcessorExecutor))
                .collect(Collectors.toList());

        // 等待所有分片处理完成
        try {
            CompletableFuture.allOf(chunkFutures.toArray(new CompletableFuture[0])).get();
            log.info("库存转换数据并行处理完成");
        } catch (Exception e) {
            log.error("库存转换数据并行处理失败", e);
            throw new RuntimeException("库存转换数据并行处理失败", e);
        }
    }

    /**
     * 处理库存转换数据（支持串行和分片处理）
     * @param logInventoryIoTransactions 库存事务列表
     */
    private void processInventoryConversionDataSequentially(List<LogInventoryIoTransaction> logInventoryIoTransactions) {
        if (logInventoryIoTransactions.size() > 100) {
            log.info("串行处理库存转换数据，数据量：{}", logInventoryIoTransactions.size());
        }

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                processInventoryConversionItem(item);
            } catch (Exception e) {
                log.error("处理库存转换数据项时发生异常，数据ID：{}", item.getId(), e);
            }
        }
    }

    /**
     * 发送数据同步开始通知
     *
     * @param operationType 操作类型
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param assetOwner    资产主体
     */
    private void sendStartNotification(String operationType, String startDate, String endDate, AssetOwner assetOwner) {
        WXUtils.invokeWorkNotification("***********",
                operationType + "数据同步开始执行，日期：" + startDate + "至" + endDate + "，资产主体：" + assetOwner.code());
    }

    /**
     * 处理单个库存转换数据项
     * @param item 库存事务项
     */
    private void processInventoryConversionItem(LogInventoryIoTransaction item) {
        if (item.getLiotRmk1() == null || item.getLiotRmk1().trim().isEmpty()) {
            log.warn("liot_rmk1为空，数据ID：{}", item.getId());
            return;
        }

        // 查询相关的库存事务数据
        LogInventoryIoTransaction query = new LogInventoryIoTransaction();
        query.setLiotRmk1(item.getLiotRmk1());
        List<LogInventoryIoTransaction> queryResult = logInventoryIoTransactionService
                .selectLogInventoryIoTransactionList(query);

        if (queryResult.isEmpty()) {
            log.warn("根据liot_rmk1未找到相关的库存事务数据，数据ID：{}", item.getId());
            return;
        }

        LogInventoryIoTransaction item2 = queryResult.get(0);

        // 构建规则代码
        String ruleCode = item.getLiotPpcd() + "__" + item2.getLiotPpcd();

        try {
            Long ruleId = conversionRuleService.getConversionRuleId(ruleCode, item.getLiotOrgmcd());
            conversionRuleDetailService.addConversionRuleDetail(
                    item.getLiotPpcd(),
                    item2.getLiotPpcd(),
                    item.getLiotQty().intValue(),
                    item2.getLiotQty().intValue(),
                    ruleId);
        } catch (Exception e) {
            log.error("添加转换规则详情失败，规则代码：{}，库存事务ID：{}", ruleCode, item.getId(), e);
            throw e;
        }
    }

    /**
     * 创建库存事务查询对象
     *
     * @param liotTyps   事务类型列表
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param assetOwner 资产主体
     * @return 查询对象
     */
    private LogInventoryIoTransaction createInventoryQuery(List<String> liotTyps, String startDate, String endDate, AssetOwner assetOwner) {
        LogInventoryIoTransaction logInventoryIoTransaction = new LogInventoryIoTransaction();
        logInventoryIoTransaction.setLiotTyps(liotTyps);
        logInventoryIoTransaction.setStartDate(startDate);
        logInventoryIoTransaction.setEndDate(endDate);
        logInventoryIoTransaction.setLiotOrgmcd(assetOwner.code());
        return logInventoryIoTransaction;
    }

    /**
     * 获取产品包装信息（带缓存）
     *
     * @param packageCode 包装编号
     * @return 产品包装信息
     */
    private BasProdPkg getCachedBasProdPkg(String packageCode) {
        if (packageCode == null || packageCode.trim().isEmpty()) {
            throw new IllegalArgumentException("包装编号不能为空");
        }

        if (basProdPkgMap.containsKey(packageCode)) {
            BasProdPkg cached = basProdPkgMap.get(packageCode);
            if (cached == null) {
                throw new RuntimeException("产品包装信息不存在，包装编号：" + packageCode);
            }
            return cached;
        } else {
            BasProdPkg basProdPkg = basProdPkgService.selectBasProdPkgByBppCd(packageCode);
            if (basProdPkg == null) {
                throw new RuntimeException("产品包装信息不存在，包装编号：" + packageCode);
            }
            basProdPkgMap.put(packageCode, basProdPkg);
            return basProdPkg;
        }
    }

    /**
     * 获取产品主表信息（带缓存）
     *
     * @param productCode 产品编号
     * @return 产品主表信息
     */
    private BasProdMst getCachedBasProdMst(String productCode) {
        if (productCode == null || productCode.trim().isEmpty()) {
            throw new IllegalArgumentException("产品编号不能为空");
        }

        if (basProdMstMap.containsKey(productCode)) {
            BasProdMst cached = basProdMstMap.get(productCode);
            if (cached == null) {
                throw new RuntimeException("产品主表信息不存在，产品编号：" + productCode);
            }
            return cached;
        } else {
            BasProdMst basProdMst = basProdMstService.selectBasProdMstByBpmCd(productCode);
            if (basProdMst == null) {
                throw new RuntimeException("产品主表信息不存在，产品编号：" + productCode);
            }
            basProdMstMap.put(productCode, basProdMst);
            return basProdMst;
        }
    }

    /**
     * 验证LogInventoryIoTransaction对象的必要字段
     *
     * @param item 库存事务对象
     * @return 是否通过验证
     */
    private boolean validateInventoryTransaction(LogInventoryIoTransaction item) {
        if (item == null) {
            return false;
        }

        // 检查关键字段
        if (item.getLiotPpcd() == null || item.getLiotPpcd().trim().isEmpty()) {
            return false;
        }

        if (item.getLiotQty() == null) {
            return false;
        }

        if (item.getrCreDt() == null) {
            return false;
        }

        return true;
    }

    /**
     * 安全获取BigDecimal值，如果为null则返回ZERO
     *
     * @param value 原始值
     * @return 安全的BigDecimal值
     */
    private BigDecimal safeGetBigDecimal(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    /**
     * 安全获取Long值，如果为null则返回0L
     *
     * @param value 原始值
     * @return 安全的Long值
     */
    private Long safeGetLong(Long value) {
        return value != null ? value : 0L;
    }

    /**
     * 安全获取String值，如果为null则返回空字符串
     *
     * @param value 原始值
     * @return 安全的String值
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }

    /**
     * 创建优化的线程池
     * @param poolName 线程池名称，用于线程命名
     * @return 优化配置的线程池
     */
    private ExecutorService createOptimizedThreadPool(String poolName) {
        int corePoolSize = Math.max(4, Runtime.getRuntime().availableProcessors());
        int maximumPoolSize = corePoolSize * 2;
        long keepAliveTime = 60L;

        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> {
                    Thread t = new Thread(r, poolName + "-Worker");
                    t.setDaemon(true);
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 监控线程池状态
     * @param executor 线程池
     * @param poolName 线程池名称
     */
    private void monitorThreadPool(ExecutorService executor, String poolName) {
        if (executor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
            log.info("线程池[{}]状态 - 核心线程数：{}，最大线程数：{}，活跃线程数：{}，队列大小：{}，已完成任务数：{}",
                    poolName,
                    tpe.getCorePoolSize(),
                    tpe.getMaximumPoolSize(),
                    tpe.getActiveCount(),
                    tpe.getQueue().size(),
                    tpe.getCompletedTaskCount());
        }
    }

    /**
     * 销毁方法，关闭线程池
     */
    @PreDestroy
    public void destroy() {
        // 关闭业务处理线程池
        shutdownExecutorService(executorService, "executorService");

        // 关闭数据处理线程池
        shutdownExecutorService(dataProcessorExecutor, "dataProcessorExecutor");

        // 关闭转换数据处理线程池
        shutdownExecutorService(conversionDataExecutor, "conversionDataExecutor");
    }

    /**
     * 安全关闭线程池
     * @param executor 线程池
     * @param name 线程池名称
     */
    private void shutdownExecutorService(ExecutorService executor, String name) {
        if (executor != null && !executor.isShutdown()) {
            log.info("正在关闭线程池：{}", name);

            // 监控线程池状态
            monitorThreadPool(executor, name);

            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("线程池{}在60秒内未能正常关闭，强制关闭", name);
                    executor.shutdownNow();

                    if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                        log.error("线程池{}强制关闭失败", name);
                    }
                }
                log.info("线程池{}已成功关闭", name);
            } catch (InterruptedException e) {
                log.warn("等待线程池{}关闭时被中断，强制关闭", name);
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

}
