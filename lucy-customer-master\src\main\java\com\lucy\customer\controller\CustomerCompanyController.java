package com.lucy.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.customer.domain.CustomerCompany;
import com.lucy.customer.service.ICustomerCompanyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 部门组织结构 Department groupsController
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@RequestMapping("/customer/company")
public class CustomerCompanyController extends BaseController
{
    @Autowired
    private ICustomerCompanyService customerCompanyService;

    /**
     * 查询部门组织结构 Department groups列表
     */
    @PreAuthorize("@ss.hasPermi('customer:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(CustomerCompany customerCompany)
    {
        startPage();
        List<CustomerCompany> list = customerCompanyService.selectCustomerCompanyList(customerCompany);
        return getDataTable(list);
    }

    /**
     * 导出部门组织结构 Department groups列表
     */
    @PreAuthorize("@ss.hasPermi('customer:company:export')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CustomerCompany customerCompany)
    {
        List<CustomerCompany> list = customerCompanyService.selectCustomerCompanyList(customerCompany);
        ExcelUtil<CustomerCompany> util = new ExcelUtil<CustomerCompany>(CustomerCompany.class);
        util.exportExcel(response, list, "部门组织结构 Department groups数据");
    }

    /**
     * 获取部门组织结构 Department groups详细信息
     */
    @PreAuthorize("@ss.hasPermi('customer:company:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(customerCompanyService.selectCustomerCompanyByCode(code));
    }

    /**
     * 新增部门组织结构 Department groups
     */
    @PreAuthorize("@ss.hasPermi('customer:company:add')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CustomerCompany customerCompany)
    {
        return toAjax(customerCompanyService.insertCustomerCompany(customerCompany));
    }

    /**
     * 修改部门组织结构 Department groups
     */
    @PreAuthorize("@ss.hasPermi('customer:company:edit')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CustomerCompany customerCompany)
    {
        return toAjax(customerCompanyService.updateCustomerCompany(customerCompany));
    }

    /**
     * 删除部门组织结构 Department groups
     */
    @PreAuthorize("@ss.hasPermi('customer:company:remove')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(customerCompanyService.deleteCustomerCompanyByCodes(codes));
    }
}
