package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 研发项目对象 lab_project
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class LabProject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 项目编号 */
    private String lpCd;

    /** 项目编号（可输入） */
    @Excel(name = "项目编号", readConverterExp = "可=输入")
    private String lpPcd;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String lpNm;

    /** 项目内容 */
    @Excel(name = "项目内容")
    private String lpContents;

    /** 项目预计开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目预计开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lpPlanTimeStart;

    /** 项目预计结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目预计结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lpPlanTimeEnd;

    /** 项目耗时 */
    @Excel(name = "项目耗时")
    private String lpElapsedTime;

    /** 项目开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lpTimeStart;

    /** 项目结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lpTimeEnd;

    /** 项目状态 */
    @Excel(name = "项目状态")
    private String lpSts;

    /** 项目创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lpCreateTime;

    /** 项目创建人 */
    @Excel(name = "项目创建人")
    private String lpUser;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long rV;

    /** 删除标志 */
    @Excel(name = "删除标志")
    private Long rDel;

    /** 项目结题 */
    @Excel(name = "项目结题")
    private String lpFinalReport;

    /** 项目结题时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目结题时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lpFinalReportDt;

    /** 需方 */
    @Excel(name = "需方")
    private String lpDemander;

    /** 项目来源（00研发/01生产/02RD） */
    @Excel(name = "项目来源", readConverterExp = "0=0研发/01生产/02RD")
    private String lpSource;

    /** CAS */
    @Excel(name = "CAS")
    private String lpCas;

    /** 产品来源 */
    @Excel(name = "产品来源")
    private String lpProdSource;

    /** 需求量 */
    @Excel(name = "需求量")
    private String lpQuantityDemanded;

    /** 备注 */
    @Excel(name = "备注")
    private String lpRemark;

    /** 产品部备注 */
    @Excel(name = "产品部备注")
    private String lpProdRemark;

    /** 最终确定 */
    @Excel(name = "最终确定")
    private String lpFinalConfirm;

    /** 是否立项 */
    @Excel(name = "是否立项")
    private String lpIsApproval;

    /** 计划总产量 */
    @Excel(name = "计划总产量")
    private BigDecimal lpQty;

    /** 计划总产量计量单位 */
    @Excel(name = "计划总产量计量单位")
    private String lpQtyUnit;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLpCd(String lpCd) 
    {
        this.lpCd = lpCd;
    }

    public String getLpCd() 
    {
        return lpCd;
    }

    public void setLpPcd(String lpPcd) 
    {
        this.lpPcd = lpPcd;
    }

    public String getLpPcd() 
    {
        return lpPcd;
    }

    public void setLpNm(String lpNm) 
    {
        this.lpNm = lpNm;
    }

    public String getLpNm() 
    {
        return lpNm;
    }

    public void setLpContents(String lpContents) 
    {
        this.lpContents = lpContents;
    }

    public String getLpContents() 
    {
        return lpContents;
    }

    public void setLpPlanTimeStart(Date lpPlanTimeStart) 
    {
        this.lpPlanTimeStart = lpPlanTimeStart;
    }

    public Date getLpPlanTimeStart() 
    {
        return lpPlanTimeStart;
    }

    public void setLpPlanTimeEnd(Date lpPlanTimeEnd) 
    {
        this.lpPlanTimeEnd = lpPlanTimeEnd;
    }

    public Date getLpPlanTimeEnd() 
    {
        return lpPlanTimeEnd;
    }

    public void setLpElapsedTime(String lpElapsedTime) 
    {
        this.lpElapsedTime = lpElapsedTime;
    }

    public String getLpElapsedTime() 
    {
        return lpElapsedTime;
    }

    public void setLpTimeStart(Date lpTimeStart) 
    {
        this.lpTimeStart = lpTimeStart;
    }

    public Date getLpTimeStart() 
    {
        return lpTimeStart;
    }

    public void setLpTimeEnd(Date lpTimeEnd) 
    {
        this.lpTimeEnd = lpTimeEnd;
    }

    public Date getLpTimeEnd() 
    {
        return lpTimeEnd;
    }

    public void setLpSts(String lpSts) 
    {
        this.lpSts = lpSts;
    }

    public String getLpSts() 
    {
        return lpSts;
    }

    public void setLpCreateTime(Date lpCreateTime) 
    {
        this.lpCreateTime = lpCreateTime;
    }

    public Date getLpCreateTime() 
    {
        return lpCreateTime;
    }

    public void setLpUser(String lpUser) 
    {
        this.lpUser = lpUser;
    }

    public String getLpUser() 
    {
        return lpUser;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setLpFinalReport(String lpFinalReport) 
    {
        this.lpFinalReport = lpFinalReport;
    }

    public String getLpFinalReport() 
    {
        return lpFinalReport;
    }

    public void setLpFinalReportDt(Date lpFinalReportDt) 
    {
        this.lpFinalReportDt = lpFinalReportDt;
    }

    public Date getLpFinalReportDt() 
    {
        return lpFinalReportDt;
    }

    public void setLpDemander(String lpDemander) 
    {
        this.lpDemander = lpDemander;
    }

    public String getLpDemander() 
    {
        return lpDemander;
    }

    public void setLpSource(String lpSource) 
    {
        this.lpSource = lpSource;
    }

    public String getLpSource() 
    {
        return lpSource;
    }

    public void setLpCas(String lpCas) 
    {
        this.lpCas = lpCas;
    }

    public String getLpCas() 
    {
        return lpCas;
    }

    public void setLpProdSource(String lpProdSource) 
    {
        this.lpProdSource = lpProdSource;
    }

    public String getLpProdSource() 
    {
        return lpProdSource;
    }

    public void setLpQuantityDemanded(String lpQuantityDemanded) 
    {
        this.lpQuantityDemanded = lpQuantityDemanded;
    }

    public String getLpQuantityDemanded() 
    {
        return lpQuantityDemanded;
    }

    public void setLpRemark(String lpRemark) 
    {
        this.lpRemark = lpRemark;
    }

    public String getLpRemark() 
    {
        return lpRemark;
    }

    public void setLpProdRemark(String lpProdRemark) 
    {
        this.lpProdRemark = lpProdRemark;
    }

    public String getLpProdRemark() 
    {
        return lpProdRemark;
    }

    public void setLpFinalConfirm(String lpFinalConfirm) 
    {
        this.lpFinalConfirm = lpFinalConfirm;
    }

    public String getLpFinalConfirm() 
    {
        return lpFinalConfirm;
    }

    public void setLpIsApproval(String lpIsApproval) 
    {
        this.lpIsApproval = lpIsApproval;
    }

    public String getLpIsApproval() 
    {
        return lpIsApproval;
    }

    public void setLpQty(BigDecimal lpQty) 
    {
        this.lpQty = lpQty;
    }

    public BigDecimal getLpQty() 
    {
        return lpQty;
    }

    public void setLpQtyUnit(String lpQtyUnit) 
    {
        this.lpQtyUnit = lpQtyUnit;
    }

    public String getLpQtyUnit() 
    {
        return lpQtyUnit;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lpCd", getLpCd())
            .append("lpPcd", getLpPcd())
            .append("lpNm", getLpNm())
            .append("lpContents", getLpContents())
            .append("lpPlanTimeStart", getLpPlanTimeStart())
            .append("lpPlanTimeEnd", getLpPlanTimeEnd())
            .append("lpElapsedTime", getLpElapsedTime())
            .append("lpTimeStart", getLpTimeStart())
            .append("lpTimeEnd", getLpTimeEnd())
            .append("lpSts", getLpSts())
            .append("lpCreateTime", getLpCreateTime())
            .append("lpUser", getLpUser())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("lpFinalReport", getLpFinalReport())
            .append("lpFinalReportDt", getLpFinalReportDt())
            .append("lpDemander", getLpDemander())
            .append("lpSource", getLpSource())
            .append("lpCas", getLpCas())
            .append("lpProdSource", getLpProdSource())
            .append("lpQuantityDemanded", getLpQuantityDemanded())
            .append("lpRemark", getLpRemark())
            .append("lpProdRemark", getLpProdRemark())
            .append("lpFinalConfirm", getLpFinalConfirm())
            .append("lpIsApproval", getLpIsApproval())
            .append("lpQty", getLpQty())
            .append("lpQtyUnit", getLpQtyUnit())
            .toString();
    }
}
