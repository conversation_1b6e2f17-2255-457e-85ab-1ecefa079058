package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 物流_库存_清单对象 log_inventory_io_transaction
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class LogInventoryIoTransaction extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 仓库编号 */
    @Excel(name = "仓库编号")
    private String liotDmcd;

    /** 库位类别 */
    @Excel(name = "库位类别")
    private String liotTcd;

    /** 库位编号 */
    @Excel(name = "库位编号")
    private String liotSlcd;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String liotPpcd;

    /** 批号 */
    @Excel(name = "批号")
    private String liotBn;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String liotBnEncrypt;

    /** 产品批号 */
    @Excel(name = "产品批号")
    private String liotPbn;

    /** 供应商批号 */
    @Excel(name = "供应商批号")
    private String liotSbn;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal liotQty;

    /** 单位成本 */
    @Excel(name = "单位成本")
    private BigDecimal liotUnitp;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal liotBit;

    /** 是否价税分离 */
    @Excel(name = "是否价税分离")
    private Long liotIspart;

    /** 库存资产总金额 */
    @Excel(name = "库存资产总金额")
    private BigDecimal liotTotalValue;

    /** 资产所属组织编号 */
    @Excel(name = "资产所属组织编号")
    private String liotOrgmcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer liotTyp;

    /** 业务备注1 */
    @Excel(name = "业务备注1")
    private String liotRmk1;

    /** 业务备注2 */
    @Excel(name = "业务备注2")
    private String liotRmk2;

    /** 业务备注3 */
    @Excel(name = "业务备注3")
    private String liotRmk3;

    /** 业务备注4 */
    @Excel(name = "业务备注4")
    private String liotRmk4;

    /** 业务备注5 */
    @Excel(name = "业务备注5")
    private String liotRmk5;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String liotRmk6;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String liotRmk7;

    /** 记账标记 */
    @Excel(name = "记账标记")
    private String liotFinFlag;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;
    
    private String startDate;
    private String endDate;
    private List<String> liotTyps;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLiotDmcd(String liotDmcd) 
    {
        this.liotDmcd = liotDmcd;
    }

    public String getLiotDmcd() 
    {
        return liotDmcd;
    }

    public void setLiotTcd(String liotTcd) 
    {
        this.liotTcd = liotTcd;
    }

    public String getLiotTcd() 
    {
        return liotTcd;
    }

    public void setLiotSlcd(String liotSlcd) 
    {
        this.liotSlcd = liotSlcd;
    }

    public String getLiotSlcd() 
    {
        return liotSlcd;
    }

    public void setLiotPpcd(String liotPpcd) 
    {
        this.liotPpcd = liotPpcd;
    }

    public String getLiotPpcd() 
    {
        return liotPpcd;
    }

    public void setLiotBn(String liotBn) 
    {
        this.liotBn = liotBn;
    }

    public String getLiotBn() 
    {
        return liotBn;
    }

    public void setLiotBnEncrypt(String liotBnEncrypt) 
    {
        this.liotBnEncrypt = liotBnEncrypt;
    }

    public String getLiotBnEncrypt() 
    {
        return liotBnEncrypt;
    }

    public void setLiotPbn(String liotPbn) 
    {
        this.liotPbn = liotPbn;
    }

    public String getLiotPbn() 
    {
        return liotPbn;
    }

    public void setLiotSbn(String liotSbn) 
    {
        this.liotSbn = liotSbn;
    }

    public String getLiotSbn() 
    {
        return liotSbn;
    }

    public void setLiotQty(BigDecimal liotQty) 
    {
        this.liotQty = liotQty;
    }

    public BigDecimal getLiotQty() 
    {
        return liotQty;
    }

    public void setLiotUnitp(BigDecimal liotUnitp) 
    {
        this.liotUnitp = liotUnitp;
    }

    public BigDecimal getLiotUnitp() 
    {
        return liotUnitp;
    }

    public void setLiotBit(BigDecimal liotBit) 
    {
        this.liotBit = liotBit;
    }

    public BigDecimal getLiotBit() 
    {
        return liotBit;
    }

    public void setLiotIspart(Long liotIspart) 
    {
        this.liotIspart = liotIspart;
    }

    public Long getLiotIspart() 
    {
        return liotIspart;
    }

    public void setLiotTotalValue(BigDecimal liotTotalValue) 
    {
        this.liotTotalValue = liotTotalValue;
    }

    public BigDecimal getLiotTotalValue() 
    {
        return liotTotalValue;
    }

    public void setLiotOrgmcd(String liotOrgmcd) 
    {
        this.liotOrgmcd = liotOrgmcd;
    }

    public String getLiotOrgmcd() 
    {
        return liotOrgmcd;
    }

    public void setLiotTyp(Integer liotTyp) 
    {
        this.liotTyp = liotTyp;
    }

    public Integer getLiotTyp() 
    {
        return liotTyp;
    }

    public void setLiotRmk1(String liotRmk1) 
    {
        this.liotRmk1 = liotRmk1;
    }

    public String getLiotRmk1() 
    {
        return liotRmk1;
    }

    public void setLiotRmk2(String liotRmk2) 
    {
        this.liotRmk2 = liotRmk2;
    }

    public String getLiotRmk2() 
    {
        return liotRmk2;
    }

    public void setLiotRmk3(String liotRmk3) 
    {
        this.liotRmk3 = liotRmk3;
    }

    public String getLiotRmk3() 
    {
        return liotRmk3;
    }

    public void setLiotRmk4(String liotRmk4) 
    {
        this.liotRmk4 = liotRmk4;
    }

    public String getLiotRmk4() 
    {
        return liotRmk4;
    }

    public void setLiotRmk5(String liotRmk5) 
    {
        this.liotRmk5 = liotRmk5;
    }

    public String getLiotRmk5() 
    {
        return liotRmk5;
    }

    public void setLiotRmk6(String liotRmk6) 
    {
        this.liotRmk6 = liotRmk6;
    }

    public String getLiotRmk6() 
    {
        return liotRmk6;
    }

    public void setLiotRmk7(String liotRmk7) 
    {
        this.liotRmk7 = liotRmk7;
    }

    public String getLiotRmk7() 
    {
        return liotRmk7;
    }

    public void setLiotFinFlag(String liotFinFlag) 
    {
        this.liotFinFlag = liotFinFlag;
    }

    public String getLiotFinFlag() 
    {
        return liotFinFlag;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public List<String> getLiotTyps() {
        return liotTyps;
    }

    public void setLiotTyps(List<String> liotTyps) {
        this.liotTyps = liotTyps;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("liotDmcd", getLiotDmcd())
            .append("liotTcd", getLiotTcd())
            .append("liotSlcd", getLiotSlcd())
            .append("liotPpcd", getLiotPpcd())
            .append("liotBn", getLiotBn())
            .append("liotBnEncrypt", getLiotBnEncrypt())
            .append("liotPbn", getLiotPbn())
            .append("liotSbn", getLiotSbn())
            .append("liotQty", getLiotQty())
            .append("liotUnitp", getLiotUnitp())
            .append("liotBit", getLiotBit())
            .append("liotIspart", getLiotIspart())
            .append("liotTotalValue", getLiotTotalValue())
            .append("liotOrgmcd", getLiotOrgmcd())
            .append("liotTyp", getLiotTyp())
            .append("liotRmk1", getLiotRmk1())
            .append("liotRmk2", getLiotRmk2())
            .append("liotRmk3", getLiotRmk3())
            .append("liotRmk4", getLiotRmk4())
            .append("liotRmk5", getLiotRmk5())
            .append("liotRmk6", getLiotRmk6())
            .append("liotRmk7", getLiotRmk7())
            .append("liotFinFlag", getLiotFinFlag())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
