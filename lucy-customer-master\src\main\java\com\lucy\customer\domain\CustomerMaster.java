package com.lucy.customer.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 客户主数据 Customer master对象 customer_master
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public class CustomerMaster extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 客户编号, Customer code */
    private String code;

    /** 客户名称, Customer name */
    @Excel(name = "客户名称, Customer name")
    private String name;

    /** 信用等级, Credit rating */
    @Excel(name = "信用等级, Credit rating")
    private String creditRating;

    /** 付款条件, Payment terms */
    @Excel(name = "付款条件, Payment terms")
    private String paymentTerms;

    /** 公司编号, Group code */
    @Excel(name = "公司编号, Group code")
    private String companyCode;

    /** 客户同控合并情况 */
    @Excel(name = "客户同控合并情况")
    private String mergerSituation;

    /** 匹配客户档案信息【应用领域、省份等信息】 */
    @Excel(name = "匹配客户档案信息【应用领域、省份等信息】")
    private String profile;

    /** 客户分类-大类 */
    @Excel(name = "客户分类-大类")
    private String classificationMajor;

    /** 客户分类-小类 */
    @Excel(name = "客户分类-小类")
    private String classificationSubclass;

    /** 客户类别（直销-终端客户、贸易商、经销商） */
    @Excel(name = "客户类别", readConverterExp = "直=销-终端客户、贸易商、经销商")
    private String category;

    /** 公司名称, Group name */
    @Excel(name = "公司名称, Group name")
    private String companyName;

    /** 税号, tax no */
    @Excel(name = "税号, tax no")
    private String taxNo;

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setCreditRating(String creditRating) 
    {
        this.creditRating = creditRating;
    }

    public String getCreditRating() 
    {
        return creditRating;
    }

    public void setPaymentTerms(String paymentTerms) 
    {
        this.paymentTerms = paymentTerms;
    }

    public String getPaymentTerms() 
    {
        return paymentTerms;
    }

    public void setCompanyCode(String companyCode) 
    {
        this.companyCode = companyCode;
    }

    public String getCompanyCode() 
    {
        return companyCode;
    }

    public void setMergerSituation(String mergerSituation) 
    {
        this.mergerSituation = mergerSituation;
    }

    public String getMergerSituation() 
    {
        return mergerSituation;
    }

    public void setProfile(String profile) 
    {
        this.profile = profile;
    }

    public String getProfile() 
    {
        return profile;
    }

    public void setClassificationMajor(String classificationMajor) 
    {
        this.classificationMajor = classificationMajor;
    }

    public String getClassificationMajor() 
    {
        return classificationMajor;
    }

    public void setClassificationSubclass(String classificationSubclass) 
    {
        this.classificationSubclass = classificationSubclass;
    }

    public String getClassificationSubclass() 
    {
        return classificationSubclass;
    }

    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }

    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    public void setTaxNo(String taxNo) 
    {
        this.taxNo = taxNo;
    }

    public String getTaxNo() 
    {
        return taxNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("name", getName())
            .append("creditRating", getCreditRating())
            .append("paymentTerms", getPaymentTerms())
            .append("companyCode", getCompanyCode())
            .append("mergerSituation", getMergerSituation())
            .append("profile", getProfile())
            .append("classificationMajor", getClassificationMajor())
            .append("classificationSubclass", getClassificationSubclass())
            .append("category", getCategory())
            .append("companyName", getCompanyName())
            .append("taxNo", getTaxNo())
            .toString();
    }
}
