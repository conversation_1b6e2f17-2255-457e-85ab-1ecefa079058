package com.lucy.inventoryoe.service;

import java.util.List;
import java.util.Map;

import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.lucy.inventoryoe.domain.AccountingSubject;

/**
 * 会计科目 Accounting subject masterService接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface IAccountingSubjectService 
{
    /**
     * 查询会计科目 Accounting subject master
     * 
     * @param code 会计科目 Accounting subject master主键
     * @return 会计科目 Accounting subject master
     */
    public AccountingSubject selectAccountingSubjectByCode(String code);

    /**
     * 查询会计科目 Accounting subject master列表
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 会计科目 Accounting subject master集合
     */
    public List<AccountingSubject> selectAccountingSubjectList(AccountingSubject accountingSubject);

    /**
     * 新增会计科目 Accounting subject master
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 结果
     */
    public int insertAccountingSubject(AccountingSubject accountingSubject);

    /**
     * 修改会计科目 Accounting subject master
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 结果
     */
    public int updateAccountingSubject(AccountingSubject accountingSubject);

    /**
     * 批量删除会计科目 Accounting subject master
     * 
     * @param codes 需要删除的会计科目 Accounting subject master主键集合
     * @return 结果
     */
    public int deleteAccountingSubjectByCodes(String[] codes);

    /**
     * 删除会计科目 Accounting subject master信息
     * 
     * @param code 会计科目 Accounting subject master主键
     * @return 结果
     */
    public int deleteAccountingSubjectByCode(String code);

    public  AccountingSubject selectAccountingSubjectByName(String faiSubject);

    void loadData();

    public Map<String, AccountingSubject> fatchAll();

}
