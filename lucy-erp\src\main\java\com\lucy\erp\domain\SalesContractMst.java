package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 销售合同主表对象 sales_contract_mst
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class SalesContractMst extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 合同编号 */
    private String scmCd;

    /** 旧合同编号 */
    @Excel(name = "旧合同编号")
    private String scmCdold;

    /** 类型 */
    @Excel(name = "类型")
    private String scmTyp;

    /** 订单类别 */
    @Excel(name = "订单类别")
    private String scmOrdtyp;

    /** 打印类型 */
    @Excel(name = "打印类型")
    private String scmPrinttyp;

    /** 来源 */
    @Excel(name = "来源")
    private String scmScd;

    /** 付款方式 */
    @Excel(name = "付款方式")
    private String scmPaytyp;

    /** 是否可以发货(默认为是，当有预付款时，为否，有主管特殊审批担保时，改为是) */
    @Excel(name = "是否可以发货(默认为是，当有预付款时，为否，有主管特殊审批担保时，改为是)")
    private String scmYn;

    /** 整体发货 */
    @Excel(name = "整体发货")
    private String scmIsod;

    /** 日期 */
    @Excel(name = "日期")
    private String scmDt;

    /** 发票类别 */
    @Excel(name = "发票类别")
    private String scmInvcTyp;

    /** 预收款编号 */
    @Excel(name = "预收款编号")
    private String scmPpacd;

    /** 买方结算公司编号 */
    @Excel(name = "买方结算公司编号")
    private String scmPocpcd;

    /** 买方公司编号 */
    @Excel(name = "买方公司编号")
    private String scmCocpcd;

    /** 客户组织编号顶级 */
    @Excel(name = "客户组织编号顶级")
    private String scmPrcocd;

    /** 客户组织编号直属 */
    @Excel(name = "客户组织编号直属")
    private String scmFrcocd;

    /** 客户ID */
    @Excel(name = "客户ID")
    private Long scmCustid;

    /** 客户CD */
    @Excel(name = "客户CD")
    private String scmCustcd;

    /** 客户姓名 */
    @Excel(name = "客户姓名")
    private String scmCustnm;

    /** 客户电话 */
    @Excel(name = "客户电话")
    private String scmCusttel;

    /** 客户传真 */
    @Excel(name = "客户传真")
    private String scmCustfax;

    /** 客户电子邮箱 */
    @Excel(name = "客户电子邮箱")
    private String scmEmail;

    /** 组织公司编号 */
    @Excel(name = "组织公司编号")
    private String scmOcpcd;

    /** 组织编号 */
    @Excel(name = "组织编号")
    private String scmOcd;

    /** 销售员ID */
    @Excel(name = "销售员ID")
    private Long scmSmid;

    /** 销售员CD */
    @Excel(name = "销售员CD")
    private String scmSmcd;

    /** 销售员NM */
    @Excel(name = "销售员NM")
    private String scmSmnm;

    /** 制单人ID */
    @Excel(name = "制单人ID")
    private Long scmOuid;

    /** 制单人CD */
    @Excel(name = "制单人CD")
    private String scmOucd;

    /** 合同金额 */
    @Excel(name = "合同金额")
    private BigDecimal scmAmt;

    /** 定金 */
    @Excel(name = "定金")
    private BigDecimal scmPreamt;

    /** 积分 */
    @Excel(name = "积分")
    private Long scmPnt;

    /** 金豆 */
    @Excel(name = "金豆")
    private Long scmGold;

    /** 抵扣积分 */
    @Excel(name = "抵扣积分")
    private Long scmPntUsed;

    /** 抵扣金豆 */
    @Excel(name = "抵扣金豆")
    private Long scmGoldUsed;

    /** 是否使用积分 */
    @Excel(name = "是否使用积分")
    private String scmPntYn;

    /** 状态CD */
    @Excel(name = "状态CD")
    private String scmStscd;

    /** 状态NM */
    @Excel(name = "状态NM")
    private String scmStsnm;

    /** 备注 */
    @Excel(name = "备注")
    private String scmRmk;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scmShdt;

    /** 身份证复印件URL */
    @Excel(name = "身份证复印件URL")
    private String scmIcPicurl;

    /** 使用证明URL */
    @Excel(name = "使用证明URL")
    private String scmUseVerify;

    /** 营业执照URL */
    @Excel(name = "营业执照URL")
    private String scmLicence;

    /** 销售合同发票附件 */
    @Excel(name = "销售合同发票附件")
    private String scmInvoicePdf;

    /** 销售合同PDF */
    @Excel(name = "销售合同PDF")
    private String scmSalesPdf;

    /** 销售身份证复印件 */
    @Excel(name = "销售身份证复印件")
    private String scmSalesCard;

    /** 泽升生化易制爆使用证明 */
    @Excel(name = "泽升生化易制爆使用证明")
    private String scmAlzsVerify;

    /** 易爆品产品签收单 */
    @Excel(name = "易爆品产品签收单")
    private String scmYzbzmPdf;

    /** 票面备注 */
    @Excel(name = "票面备注")
    private String scmInvcrmk;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 开票方式 */
    @Excel(name = "开票方式")
    private String scmKp;

    /** 易涉毒标注类型 */
    @Excel(name = "易涉毒标注类型")
    private String scmSptyp;

    /** 是否推送安耐吉 */
    @Excel(name = "是否推送安耐吉")
    private String scmPush;

    /** 平台单号是否显示条形码 */
    @Excel(name = "平台单号是否显示条形码")
    private String scmBarcode;

    /** 首次点击支付的时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次点击支付的时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scmPaytime;

    /** erp端支付回调是否成功 01：成功 */
    @Excel(name = "erp端支付回调是否成功 01：成功")
    private String scmCallback;

    /** 销售类型：01国内销售，02国外销售 */
    @Excel(name = "销售类型：01国内销售，02国外销售")
    private String scmNationalType;

    /** 币种 */
    @Excel(name = "币种")
    private String scmCurrency;

    /** 是否为VMI(0:不是 1: 是) */
    @Excel(name = "是否为VMI(0:不是 1: 是)")
    private String scmVmi;

    /** 00：普通订单，01：金豆订单 */
    @Excel(name = "00：普通订单，01：金豆订单")
    private String scmJd;

    /** 其他平台单号 */
    @Excel(name = "其他平台单号")
    private String scmOpcd;

    /** vmi订单一级组织编号 */
    @Excel(name = "vmi订单一级组织编号")
    private String scmVmicd;

    /** 出口仓（订单级别） */
    @Excel(name = "出口仓", readConverterExp = "订=单级别")
    private String scmExportDmcd;

    /** 报价方式 CIF/FOB */
    @Excel(name = "报价方式 CIF/FOB")
    private String scmQumethod;

    /** PO号 */
    @Excel(name = "PO号")
    private String scmPono;

    /** 危化品订单类型 */
    @Excel(name = "危化品订单类型")
    private String scmWhpType;

    /** 买方结算公司名称 */
    @Excel(name = "买方结算公司名称")
    private String scmPocpnm;

    /** 客户组织编号直属(课题组名称) */
    @Excel(name = "客户组织编号直属(课题组名称)")
    private String scmFrconm;

    /** 海外备货类型（01期货订单 02海外备货订单） */
    @Excel(name = "海外备货类型", readConverterExp = "0=1期货订单,0=2海外备货订单")
    private String scmOsktype;

    /** 整单发货（默认为否00） */
    @Excel(name = "整单发货", readConverterExp = "默=认为否00")
    private String scmFos;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setScmCd(String scmCd) 
    {
        this.scmCd = scmCd;
    }

    public String getScmCd() 
    {
        return scmCd;
    }

    public void setScmCdold(String scmCdold) 
    {
        this.scmCdold = scmCdold;
    }

    public String getScmCdold() 
    {
        return scmCdold;
    }

    public void setScmTyp(String scmTyp) 
    {
        this.scmTyp = scmTyp;
    }

    public String getScmTyp() 
    {
        return scmTyp;
    }

    public void setScmOrdtyp(String scmOrdtyp) 
    {
        this.scmOrdtyp = scmOrdtyp;
    }

    public String getScmOrdtyp() 
    {
        return scmOrdtyp;
    }

    public void setScmPrinttyp(String scmPrinttyp) 
    {
        this.scmPrinttyp = scmPrinttyp;
    }

    public String getScmPrinttyp() 
    {
        return scmPrinttyp;
    }

    public void setScmScd(String scmScd) 
    {
        this.scmScd = scmScd;
    }

    public String getScmScd() 
    {
        return scmScd;
    }

    public void setScmPaytyp(String scmPaytyp) 
    {
        this.scmPaytyp = scmPaytyp;
    }

    public String getScmPaytyp() 
    {
        return scmPaytyp;
    }

    public void setScmYn(String scmYn) 
    {
        this.scmYn = scmYn;
    }

    public String getScmYn() 
    {
        return scmYn;
    }

    public void setScmIsod(String scmIsod) 
    {
        this.scmIsod = scmIsod;
    }

    public String getScmIsod() 
    {
        return scmIsod;
    }

    public void setScmDt(String scmDt) 
    {
        this.scmDt = scmDt;
    }

    public String getScmDt() 
    {
        return scmDt;
    }

    public void setScmInvcTyp(String scmInvcTyp) 
    {
        this.scmInvcTyp = scmInvcTyp;
    }

    public String getScmInvcTyp() 
    {
        return scmInvcTyp;
    }

    public void setScmPpacd(String scmPpacd) 
    {
        this.scmPpacd = scmPpacd;
    }

    public String getScmPpacd() 
    {
        return scmPpacd;
    }

    public void setScmPocpcd(String scmPocpcd) 
    {
        this.scmPocpcd = scmPocpcd;
    }

    public String getScmPocpcd() 
    {
        return scmPocpcd;
    }

    public void setScmCocpcd(String scmCocpcd) 
    {
        this.scmCocpcd = scmCocpcd;
    }

    public String getScmCocpcd() 
    {
        return scmCocpcd;
    }

    public void setScmPrcocd(String scmPrcocd) 
    {
        this.scmPrcocd = scmPrcocd;
    }

    public String getScmPrcocd() 
    {
        return scmPrcocd;
    }

    public void setScmFrcocd(String scmFrcocd) 
    {
        this.scmFrcocd = scmFrcocd;
    }

    public String getScmFrcocd() 
    {
        return scmFrcocd;
    }

    public void setScmCustid(Long scmCustid) 
    {
        this.scmCustid = scmCustid;
    }

    public Long getScmCustid() 
    {
        return scmCustid;
    }

    public void setScmCustcd(String scmCustcd) 
    {
        this.scmCustcd = scmCustcd;
    }

    public String getScmCustcd() 
    {
        return scmCustcd;
    }

    public void setScmCustnm(String scmCustnm) 
    {
        this.scmCustnm = scmCustnm;
    }

    public String getScmCustnm() 
    {
        return scmCustnm;
    }

    public void setScmCusttel(String scmCusttel) 
    {
        this.scmCusttel = scmCusttel;
    }

    public String getScmCusttel() 
    {
        return scmCusttel;
    }

    public void setScmCustfax(String scmCustfax) 
    {
        this.scmCustfax = scmCustfax;
    }

    public String getScmCustfax() 
    {
        return scmCustfax;
    }

    public void setScmEmail(String scmEmail) 
    {
        this.scmEmail = scmEmail;
    }

    public String getScmEmail() 
    {
        return scmEmail;
    }

    public void setScmOcpcd(String scmOcpcd) 
    {
        this.scmOcpcd = scmOcpcd;
    }

    public String getScmOcpcd() 
    {
        return scmOcpcd;
    }

    public void setScmOcd(String scmOcd) 
    {
        this.scmOcd = scmOcd;
    }

    public String getScmOcd() 
    {
        return scmOcd;
    }

    public void setScmSmid(Long scmSmid) 
    {
        this.scmSmid = scmSmid;
    }

    public Long getScmSmid() 
    {
        return scmSmid;
    }

    public void setScmSmcd(String scmSmcd) 
    {
        this.scmSmcd = scmSmcd;
    }

    public String getScmSmcd() 
    {
        return scmSmcd;
    }

    public void setScmSmnm(String scmSmnm) 
    {
        this.scmSmnm = scmSmnm;
    }

    public String getScmSmnm() 
    {
        return scmSmnm;
    }

    public void setScmOuid(Long scmOuid) 
    {
        this.scmOuid = scmOuid;
    }

    public Long getScmOuid() 
    {
        return scmOuid;
    }

    public void setScmOucd(String scmOucd) 
    {
        this.scmOucd = scmOucd;
    }

    public String getScmOucd() 
    {
        return scmOucd;
    }

    public void setScmAmt(BigDecimal scmAmt) 
    {
        this.scmAmt = scmAmt;
    }

    public BigDecimal getScmAmt() 
    {
        return scmAmt;
    }

    public void setScmPreamt(BigDecimal scmPreamt) 
    {
        this.scmPreamt = scmPreamt;
    }

    public BigDecimal getScmPreamt() 
    {
        return scmPreamt;
    }

    public void setScmPnt(Long scmPnt) 
    {
        this.scmPnt = scmPnt;
    }

    public Long getScmPnt() 
    {
        return scmPnt;
    }

    public void setScmGold(Long scmGold) 
    {
        this.scmGold = scmGold;
    }

    public Long getScmGold() 
    {
        return scmGold;
    }

    public void setScmPntUsed(Long scmPntUsed) 
    {
        this.scmPntUsed = scmPntUsed;
    }

    public Long getScmPntUsed() 
    {
        return scmPntUsed;
    }

    public void setScmGoldUsed(Long scmGoldUsed) 
    {
        this.scmGoldUsed = scmGoldUsed;
    }

    public Long getScmGoldUsed() 
    {
        return scmGoldUsed;
    }

    public void setScmPntYn(String scmPntYn) 
    {
        this.scmPntYn = scmPntYn;
    }

    public String getScmPntYn() 
    {
        return scmPntYn;
    }

    public void setScmStscd(String scmStscd) 
    {
        this.scmStscd = scmStscd;
    }

    public String getScmStscd() 
    {
        return scmStscd;
    }

    public void setScmStsnm(String scmStsnm) 
    {
        this.scmStsnm = scmStsnm;
    }

    public String getScmStsnm() 
    {
        return scmStsnm;
    }

    public void setScmRmk(String scmRmk) 
    {
        this.scmRmk = scmRmk;
    }

    public String getScmRmk() 
    {
        return scmRmk;
    }

    public void setScmShdt(Date scmShdt) 
    {
        this.scmShdt = scmShdt;
    }

    public Date getScmShdt() 
    {
        return scmShdt;
    }

    public void setScmIcPicurl(String scmIcPicurl) 
    {
        this.scmIcPicurl = scmIcPicurl;
    }

    public String getScmIcPicurl() 
    {
        return scmIcPicurl;
    }

    public void setScmUseVerify(String scmUseVerify) 
    {
        this.scmUseVerify = scmUseVerify;
    }

    public String getScmUseVerify() 
    {
        return scmUseVerify;
    }

    public void setScmLicence(String scmLicence) 
    {
        this.scmLicence = scmLicence;
    }

    public String getScmLicence() 
    {
        return scmLicence;
    }

    public void setScmInvoicePdf(String scmInvoicePdf) 
    {
        this.scmInvoicePdf = scmInvoicePdf;
    }

    public String getScmInvoicePdf() 
    {
        return scmInvoicePdf;
    }

    public void setScmSalesPdf(String scmSalesPdf) 
    {
        this.scmSalesPdf = scmSalesPdf;
    }

    public String getScmSalesPdf() 
    {
        return scmSalesPdf;
    }

    public void setScmSalesCard(String scmSalesCard) 
    {
        this.scmSalesCard = scmSalesCard;
    }

    public String getScmSalesCard() 
    {
        return scmSalesCard;
    }

    public void setScmAlzsVerify(String scmAlzsVerify) 
    {
        this.scmAlzsVerify = scmAlzsVerify;
    }

    public String getScmAlzsVerify() 
    {
        return scmAlzsVerify;
    }

    public void setScmYzbzmPdf(String scmYzbzmPdf) 
    {
        this.scmYzbzmPdf = scmYzbzmPdf;
    }

    public String getScmYzbzmPdf() 
    {
        return scmYzbzmPdf;
    }

    public void setScmInvcrmk(String scmInvcrmk) 
    {
        this.scmInvcrmk = scmInvcrmk;
    }

    public String getScmInvcrmk() 
    {
        return scmInvcrmk;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setScmKp(String scmKp) 
    {
        this.scmKp = scmKp;
    }

    public String getScmKp() 
    {
        return scmKp;
    }

    public void setScmSptyp(String scmSptyp) 
    {
        this.scmSptyp = scmSptyp;
    }

    public String getScmSptyp() 
    {
        return scmSptyp;
    }

    public void setScmPush(String scmPush) 
    {
        this.scmPush = scmPush;
    }

    public String getScmPush() 
    {
        return scmPush;
    }

    public void setScmBarcode(String scmBarcode) 
    {
        this.scmBarcode = scmBarcode;
    }

    public String getScmBarcode() 
    {
        return scmBarcode;
    }

    public void setScmPaytime(Date scmPaytime) 
    {
        this.scmPaytime = scmPaytime;
    }

    public Date getScmPaytime() 
    {
        return scmPaytime;
    }

    public void setScmCallback(String scmCallback) 
    {
        this.scmCallback = scmCallback;
    }

    public String getScmCallback() 
    {
        return scmCallback;
    }

    public void setScmNationalType(String scmNationalType) 
    {
        this.scmNationalType = scmNationalType;
    }

    public String getScmNationalType() 
    {
        return scmNationalType;
    }

    public void setScmCurrency(String scmCurrency) 
    {
        this.scmCurrency = scmCurrency;
    }

    public String getScmCurrency() 
    {
        return scmCurrency;
    }

    public void setScmVmi(String scmVmi) 
    {
        this.scmVmi = scmVmi;
    }

    public String getScmVmi() 
    {
        return scmVmi;
    }

    public void setScmJd(String scmJd) 
    {
        this.scmJd = scmJd;
    }

    public String getScmJd() 
    {
        return scmJd;
    }

    public void setScmOpcd(String scmOpcd) 
    {
        this.scmOpcd = scmOpcd;
    }

    public String getScmOpcd() 
    {
        return scmOpcd;
    }

    public void setScmVmicd(String scmVmicd) 
    {
        this.scmVmicd = scmVmicd;
    }

    public String getScmVmicd() 
    {
        return scmVmicd;
    }

    public void setScmExportDmcd(String scmExportDmcd) 
    {
        this.scmExportDmcd = scmExportDmcd;
    }

    public String getScmExportDmcd() 
    {
        return scmExportDmcd;
    }

    public void setScmQumethod(String scmQumethod) 
    {
        this.scmQumethod = scmQumethod;
    }

    public String getScmQumethod() 
    {
        return scmQumethod;
    }

    public void setScmPono(String scmPono) 
    {
        this.scmPono = scmPono;
    }

    public String getScmPono() 
    {
        return scmPono;
    }

    public void setScmWhpType(String scmWhpType) 
    {
        this.scmWhpType = scmWhpType;
    }

    public String getScmWhpType() 
    {
        return scmWhpType;
    }

    public void setScmPocpnm(String scmPocpnm) 
    {
        this.scmPocpnm = scmPocpnm;
    }

    public String getScmPocpnm() 
    {
        return scmPocpnm;
    }

    public void setScmFrconm(String scmFrconm) 
    {
        this.scmFrconm = scmFrconm;
    }

    public String getScmFrconm() 
    {
        return scmFrconm;
    }

    public void setScmOsktype(String scmOsktype) 
    {
        this.scmOsktype = scmOsktype;
    }

    public String getScmOsktype() 
    {
        return scmOsktype;
    }

    public void setScmFos(String scmFos) 
    {
        this.scmFos = scmFos;
    }

    public String getScmFos() 
    {
        return scmFos;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("scmCd", getScmCd())
            .append("scmCdold", getScmCdold())
            .append("scmTyp", getScmTyp())
            .append("scmOrdtyp", getScmOrdtyp())
            .append("scmPrinttyp", getScmPrinttyp())
            .append("scmScd", getScmScd())
            .append("scmPaytyp", getScmPaytyp())
            .append("scmYn", getScmYn())
            .append("scmIsod", getScmIsod())
            .append("scmDt", getScmDt())
            .append("scmInvcTyp", getScmInvcTyp())
            .append("scmPpacd", getScmPpacd())
            .append("scmPocpcd", getScmPocpcd())
            .append("scmCocpcd", getScmCocpcd())
            .append("scmPrcocd", getScmPrcocd())
            .append("scmFrcocd", getScmFrcocd())
            .append("scmCustid", getScmCustid())
            .append("scmCustcd", getScmCustcd())
            .append("scmCustnm", getScmCustnm())
            .append("scmCusttel", getScmCusttel())
            .append("scmCustfax", getScmCustfax())
            .append("scmEmail", getScmEmail())
            .append("scmOcpcd", getScmOcpcd())
            .append("scmOcd", getScmOcd())
            .append("scmSmid", getScmSmid())
            .append("scmSmcd", getScmSmcd())
            .append("scmSmnm", getScmSmnm())
            .append("scmOuid", getScmOuid())
            .append("scmOucd", getScmOucd())
            .append("scmAmt", getScmAmt())
            .append("scmPreamt", getScmPreamt())
            .append("scmPnt", getScmPnt())
            .append("scmGold", getScmGold())
            .append("scmPntUsed", getScmPntUsed())
            .append("scmGoldUsed", getScmGoldUsed())
            .append("scmPntYn", getScmPntYn())
            .append("scmStscd", getScmStscd())
            .append("scmStsnm", getScmStsnm())
            .append("scmRmk", getScmRmk())
            .append("scmShdt", getScmShdt())
            .append("scmIcPicurl", getScmIcPicurl())
            .append("scmUseVerify", getScmUseVerify())
            .append("scmLicence", getScmLicence())
            .append("scmInvoicePdf", getScmInvoicePdf())
            .append("scmSalesPdf", getScmSalesPdf())
            .append("scmSalesCard", getScmSalesCard())
            .append("scmAlzsVerify", getScmAlzsVerify())
            .append("scmYzbzmPdf", getScmYzbzmPdf())
            .append("scmInvcrmk", getScmInvcrmk())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("scmKp", getScmKp())
            .append("scmSptyp", getScmSptyp())
            .append("scmPush", getScmPush())
            .append("scmBarcode", getScmBarcode())
            .append("scmPaytime", getScmPaytime())
            .append("scmCallback", getScmCallback())
            .append("scmNationalType", getScmNationalType())
            .append("scmCurrency", getScmCurrency())
            .append("scmVmi", getScmVmi())
            .append("scmJd", getScmJd())
            .append("scmOpcd", getScmOpcd())
            .append("scmVmicd", getScmVmicd())
            .append("scmExportDmcd", getScmExportDmcd())
            .append("scmQumethod", getScmQumethod())
            .append("scmPono", getScmPono())
            .append("scmWhpType", getScmWhpType())
            .append("scmPocpnm", getScmPocpnm())
            .append("scmFrconm", getScmFrconm())
            .append("scmOsktype", getScmOsktype())
            .append("scmFos", getScmFos())
            .toString();
    }
}
