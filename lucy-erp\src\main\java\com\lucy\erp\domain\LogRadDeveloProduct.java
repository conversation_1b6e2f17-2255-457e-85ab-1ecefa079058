package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小样或者产成品结果对象 log_rad_develo_product
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public class LogRadDeveloProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 产成品编号 */
    private String lrdpCd;

    /** 生产编号 */
    @Excel(name = "生产编号")
    private String lrdpMcd;

    /** 类型（00研发生产/01项目生产） */
    @Excel(name = "类型", readConverterExp = "0=0研发生产/01项目生产")
    private String lrdpSource;

    /** 批号 */
    @Excel(name = "批号")
    private String lrdpBn;

    /** 加密批号 */
    @Excel(name = "加密批号")
    private String lrdpBnEncrypt;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String lrdpPcd;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String lrdpPpcd;

    /** 库位 */
    @Excel(name = "库位")
    private String lrdpSlcd;

    /** 库位类型 */
    @Excel(name = "库位类型")
    private String lrdpTcd;

    /** 仓库 */
    @Excel(name = "仓库")
    private String lrdpDmcd;

    /** 资产所属 */
    @Excel(name = "资产所属")
    private String lrdpOrgmcd;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal lrdpQty;

    /** 计划数量 */
    @Excel(name = "计划数量")
    private BigDecimal lrdpPlanqty;

    /** 实际产出数量 */
    @Excel(name = "实际产出数量")
    private BigDecimal lrdpActualqty;

    /** 不含税单价 */
    @Excel(name = "不含税单价")
    private BigDecimal lrdpUnitp;

    /** 不含税总价 */
    @Excel(name = "不含税总价")
    private BigDecimal lrdpTotprc;

    /** 记账标记 */
    @Excel(name = "记账标记")
    private Integer lrdpFinFlag;

    /** 创建人 */
    @Excel(name = "创建人")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 修改人 */
    @Excel(name = "修改人")
    private String rUpdKid;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Integer rDel;

    /** 岩峰RD系统库存事务ID */
    @Excel(name = "岩峰RD系统库存事务ID")
    private Long lrdpRdId;

    private String startDate;
    private String endDate;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLrdpCd(String lrdpCd) 
    {
        this.lrdpCd = lrdpCd;
    }

    public String getLrdpCd() 
    {
        return lrdpCd;
    }

    public void setLrdpMcd(String lrdpMcd) 
    {
        this.lrdpMcd = lrdpMcd;
    }

    public String getLrdpMcd() 
    {
        return lrdpMcd;
    }

    public void setLrdpSource(String lrdpSource) 
    {
        this.lrdpSource = lrdpSource;
    }

    public String getLrdpSource() 
    {
        return lrdpSource;
    }

    public void setLrdpBn(String lrdpBn) 
    {
        this.lrdpBn = lrdpBn;
    }

    public String getLrdpBn() 
    {
        return lrdpBn;
    }

    public void setLrdpBnEncrypt(String lrdpBnEncrypt) 
    {
        this.lrdpBnEncrypt = lrdpBnEncrypt;
    }

    public String getLrdpBnEncrypt() 
    {
        return lrdpBnEncrypt;
    }

    public void setLrdpPcd(String lrdpPcd) 
    {
        this.lrdpPcd = lrdpPcd;
    }

    public String getLrdpPcd() 
    {
        return lrdpPcd;
    }

    public void setLrdpPpcd(String lrdpPpcd) 
    {
        this.lrdpPpcd = lrdpPpcd;
    }

    public String getLrdpPpcd() 
    {
        return lrdpPpcd;
    }

    public void setLrdpSlcd(String lrdpSlcd) 
    {
        this.lrdpSlcd = lrdpSlcd;
    }

    public String getLrdpSlcd() 
    {
        return lrdpSlcd;
    }

    public void setLrdpTcd(String lrdpTcd) 
    {
        this.lrdpTcd = lrdpTcd;
    }

    public String getLrdpTcd() 
    {
        return lrdpTcd;
    }

    public void setLrdpDmcd(String lrdpDmcd) 
    {
        this.lrdpDmcd = lrdpDmcd;
    }

    public String getLrdpDmcd() 
    {
        return lrdpDmcd;
    }

    public void setLrdpOrgmcd(String lrdpOrgmcd) 
    {
        this.lrdpOrgmcd = lrdpOrgmcd;
    }

    public String getLrdpOrgmcd() 
    {
        return lrdpOrgmcd;
    }

    public void setLrdpQty(BigDecimal lrdpQty) 
    {
        this.lrdpQty = lrdpQty;
    }

    public BigDecimal getLrdpQty() 
    {
        return lrdpQty;
    }

    public void setLrdpPlanqty(BigDecimal lrdpPlanqty) 
    {
        this.lrdpPlanqty = lrdpPlanqty;
    }

    public BigDecimal getLrdpPlanqty() 
    {
        return lrdpPlanqty;
    }

    public void setLrdpActualqty(BigDecimal lrdpActualqty) 
    {
        this.lrdpActualqty = lrdpActualqty;
    }

    public BigDecimal getLrdpActualqty() 
    {
        return lrdpActualqty;
    }

    public void setLrdpUnitp(BigDecimal lrdpUnitp) 
    {
        this.lrdpUnitp = lrdpUnitp;
    }

    public BigDecimal getLrdpUnitp() 
    {
        return lrdpUnitp;
    }

    public void setLrdpTotprc(BigDecimal lrdpTotprc) 
    {
        this.lrdpTotprc = lrdpTotprc;
    }

    public BigDecimal getLrdpTotprc() 
    {
        return lrdpTotprc;
    }

    public void setLrdpFinFlag(Integer lrdpFinFlag) 
    {
        this.lrdpFinFlag = lrdpFinFlag;
    }

    public Integer getLrdpFinFlag() 
    {
        return lrdpFinFlag;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Integer rDel) 
    {
        this.rDel = rDel;
    }

    public Integer getrDel() 
    {
        return rDel;
    }

    public void setLrdpRdId(Long lrdpRdId) 
    {
        this.lrdpRdId = lrdpRdId;
    }

    public Long getLrdpRdId() 
    {
        return lrdpRdId;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lrdpCd", getLrdpCd())
            .append("lrdpMcd", getLrdpMcd())
            .append("lrdpSource", getLrdpSource())
            .append("lrdpBn", getLrdpBn())
            .append("lrdpBnEncrypt", getLrdpBnEncrypt())
            .append("lrdpPcd", getLrdpPcd())
            .append("lrdpPpcd", getLrdpPpcd())
            .append("lrdpSlcd", getLrdpSlcd())
            .append("lrdpTcd", getLrdpTcd())
            .append("lrdpDmcd", getLrdpDmcd())
            .append("lrdpOrgmcd", getLrdpOrgmcd())
            .append("lrdpQty", getLrdpQty())
            .append("lrdpPlanqty", getLrdpPlanqty())
            .append("lrdpActualqty", getLrdpActualqty())
            .append("lrdpUnitp", getLrdpUnitp())
            .append("lrdpTotprc", getLrdpTotprc())
            .append("lrdpFinFlag", getLrdpFinFlag())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("lrdpRdId", getLrdpRdId())
            .toString();
    }
}
