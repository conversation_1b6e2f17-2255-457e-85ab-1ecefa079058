<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.PurContractDtlMapper">
    
    <resultMap type="PurContractDtl" id="PurContractDtlResult">
        <result property="id"    column="id"    />
        <result property="pcdMcd"    column="pcd_mcd"    />
        <result property="pcdCd"    column="pcd_cd"    />
        <result property="pcdOpcd"    column="pcd_opcd"    />
        <result property="pcdTyp"    column="pcd_typ"    />
        <result property="pcdBr"    column="pcd_br"    />
        <result property="pcdPrecd"    column="pcd_precd"    />
        <result property="pcdPdcd"    column="pcd_pdcd"    />
        <result property="pcdPorgcd"    column="pcd_porgcd"    />
        <result property="pcdPcd"    column="pcd_pcd"    />
        <result property="pcdPnm"    column="pcd_pnm"    />
        <result property="pcdCas"    column="pcd_cas"    />
        <result property="pcdPurity"    column="pcd_purity"    />
        <result property="pcdDcls"    column="pcd_dcls"    />
        <result property="pcdSprmk"    column="pcd_sprmk"    />
        <result property="pcdSprmkPoisonod"    column="pcd_sprmk_poisonod"    />
        <result property="pcdStg"    column="pcd_stg"    />
        <result property="pcdPpcd"    column="pcd_ppcd"    />
        <result property="pcdPkgset"    column="pcd_pkgset"    />
        <result property="pcdPkgqty"    column="pcd_pkgqty"    />
        <result property="pcdPkgunit"    column="pcd_pkgunit"    />
        <result property="pcdPack"    column="pcd_pack"    />
        <result property="pcdDcd"    column="pcd_dcd"    />
        <result property="pcdDnm"    column="pcd_dnm"    />
        <result property="pcdOprc"    column="pcd_oprc"    />
        <result property="pcdDct"    column="pcd_dct"    />
        <result property="pcdUprc"    column="pcd_uprc"    />
        <result property="pcdQty"    column="pcd_qty"    />
        <result property="pcdInqty"    column="pcd_inqty"    />
        <result property="pcdIqty"    column="pcd_iqty"    />
        <result property="pcdTprc"    column="pcd_tprc"    />
        <result property="pcdTn"    column="pcd_tn"    />
        <result property="pcdStscd"    column="pcd_stscd"    />
        <result property="pcdStsnm"    column="pcd_stsnm"    />
        <result property="pcdPodt"    column="pcd_podt"    />
        <result property="pcdIsdt"    column="pcd_isdt"    />
        <result property="pcdRmk"    column="pcd_rmk"    />
        <result property="pcdTdt"    column="pcd_tdt"    />
        <result property="pcdNtdt"    column="pcd_ntdt"    />
        <result property="pcdDdend"    column="pcd_ddend"    />
        <result property="pcdExchange"    column="pcd_exchange"    />
        <result property="pcdLcUprc"    column="pcd_lc_uprc"    />
        <result property="pcdLcValue"    column="pcd_lc_value"    />
        <result property="pcdEadt"    column="pcd_eadt"    />
        <result property="pcdNatyp"    column="pcd_natyp"    />
        <result property="pcdTrmk"    column="pcd_trmk"    />
        <result property="pcdFprmk"    column="pcd_fprmk"    />
        <result property="pcdPtyp"    column="pcd_ptyp"    />
        <result property="pcdIsoem"    column="pcd_isoem"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="pcdSupPkg"    column="pcd_sup_pkg"    />
        <result property="pcdLcIncidentals"    column="pcd_lc_incidentals"    />
        <result property="pcdReturnProvince"    column="pcd_return_province"    />
        <result property="pcdReturnCity"    column="pcd_return_city"    />
        <result property="pcdReturnDistrict"    column="pcd_return_district"    />
        <result property="pcdReturnAddr"    column="pcd_return_addr"    />
        <result property="pcdReturnContacts"    column="pcd_return_contacts"    />
        <result property="pcdReturnPhone"    column="pcd_return_phone"    />
        <result property="pcdReturnDt"    column="pcd_return_dt"    />
        <result property="pcdPurcycl"    column="pcd_purcycl"    />
        <result property="pcdExpencesSts"    column="pcd_expences_sts"    />
        <result property="pcdExppassDt"    column="pcd_exppass_dt"    />
        <result property="pcdSupPbcd"    column="pcd_sup_pbcd"    />
        <result property="pcdSupPorgcd"    column="pcd_sup_porgcd"    />
        <result property="pcdSupPpcd"    column="pcd_sup_ppcd"    />
        <result property="pcdSupQty"    column="pcd_sup_qty"    />
        <result property="pcdBnEncrypt"    column="pcd_bn_encrypt"    />
    </resultMap>

    <sql id="selectPurContractDtlVo">
        select id, pcd_mcd, pcd_cd, pcd_opcd, pcd_typ, pcd_br, pcd_precd, pcd_pdcd, pcd_porgcd, pcd_pcd, pcd_pnm, pcd_cas, pcd_purity, pcd_dcls, pcd_sprmk, pcd_sprmk_poisonod, pcd_stg, pcd_ppcd, pcd_pkgset, pcd_pkgqty, pcd_pkgunit, pcd_pack, pcd_dcd, pcd_dnm, pcd_oprc, pcd_dct, pcd_uprc, pcd_qty, pcd_inqty, pcd_iqty, pcd_tprc, pcd_tn, pcd_stscd, pcd_stsnm, pcd_podt, pcd_isdt, pcd_rmk, pcd_tdt, pcd_ntdt, pcd_ddend, pcd_exchange, pcd_lc_uprc, pcd_lc_value, pcd_eadt, pcd_natyp, pcd_trmk, pcd_fprmk, pcd_ptyp, pcd_isoem, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, pcd_sup_pkg, pcd_lc_incidentals, pcd_return_province, pcd_return_city, pcd_return_district, pcd_return_addr, pcd_return_contacts, pcd_return_phone, pcd_return_dt, pcd_purcycl, pcd_expences_sts, pcd_exppass_dt, pcd_sup_pbcd, pcd_sup_porgcd, pcd_sup_ppcd, pcd_sup_qty, pcd_bn_encrypt from pur_contract_dtl
    </sql>

    <select id="selectPurContractDtlList" parameterType="PurContractDtl" resultMap="PurContractDtlResult">
        <include refid="selectPurContractDtlVo"/>
        <where>  
            <if test="pcdMcd != null  and pcdMcd != ''"> and pcd_mcd = #{pcdMcd}</if>
            <if test="pcdOpcd != null  and pcdOpcd != ''"> and pcd_opcd = #{pcdOpcd}</if>
            <if test="pcdTyp != null  and pcdTyp != ''"> and pcd_typ = #{pcdTyp}</if>
            <if test="pcdBr != null  and pcdBr != ''"> and pcd_br = #{pcdBr}</if>
            <if test="pcdPrecd != null  and pcdPrecd != ''"> and pcd_precd = #{pcdPrecd}</if>
            <if test="pcdPdcd != null  and pcdPdcd != ''"> and pcd_pdcd = #{pcdPdcd}</if>
            <if test="pcdPorgcd != null  and pcdPorgcd != ''"> and pcd_porgcd = #{pcdPorgcd}</if>
            <if test="pcdPcd != null  and pcdPcd != ''"> and pcd_pcd = #{pcdPcd}</if>
            <if test="pcdPnm != null  and pcdPnm != ''"> and pcd_pnm = #{pcdPnm}</if>
            <if test="pcdCas != null  and pcdCas != ''"> and pcd_cas = #{pcdCas}</if>
            <if test="pcdPurity != null  and pcdPurity != ''"> and pcd_purity = #{pcdPurity}</if>
            <if test="pcdDcls != null  and pcdDcls != ''"> and pcd_dcls = #{pcdDcls}</if>
            <if test="pcdSprmk != null  and pcdSprmk != ''"> and pcd_sprmk = #{pcdSprmk}</if>
            <if test="pcdSprmkPoisonod != null  and pcdSprmkPoisonod != ''"> and pcd_sprmk_poisonod = #{pcdSprmkPoisonod}</if>
            <if test="pcdStg != null  and pcdStg != ''"> and pcd_stg = #{pcdStg}</if>
            <if test="pcdPpcd != null  and pcdPpcd != ''"> and pcd_ppcd = #{pcdPpcd}</if>
            <if test="pcdPkgset != null "> and pcd_pkgset = #{pcdPkgset}</if>
            <if test="pcdPkgqty != null "> and pcd_pkgqty = #{pcdPkgqty}</if>
            <if test="pcdPkgunit != null  and pcdPkgunit != ''"> and pcd_pkgunit = #{pcdPkgunit}</if>
            <if test="pcdPack != null  and pcdPack != ''"> and pcd_pack = #{pcdPack}</if>
            <if test="pcdDcd != null  and pcdDcd != ''"> and pcd_dcd = #{pcdDcd}</if>
            <if test="pcdDnm != null  and pcdDnm != ''"> and pcd_dnm = #{pcdDnm}</if>
            <if test="pcdOprc != null "> and pcd_oprc = #{pcdOprc}</if>
            <if test="pcdDct != null "> and pcd_dct = #{pcdDct}</if>
            <if test="pcdUprc != null "> and pcd_uprc = #{pcdUprc}</if>
            <if test="pcdQty != null "> and pcd_qty = #{pcdQty}</if>
            <if test="pcdInqty != null "> and pcd_inqty = #{pcdInqty}</if>
            <if test="pcdIqty != null "> and pcd_iqty = #{pcdIqty}</if>
            <if test="pcdTprc != null "> and pcd_tprc = #{pcdTprc}</if>
            <if test="pcdTn != null  and pcdTn != ''"> and pcd_tn = #{pcdTn}</if>
            <if test="pcdStscd != null  and pcdStscd != ''"> and pcd_stscd = #{pcdStscd}</if>
            <if test="pcdStsnm != null  and pcdStsnm != ''"> and pcd_stsnm = #{pcdStsnm}</if>
            <if test="pcdPodt != null "> and pcd_podt = #{pcdPodt}</if>
            <if test="pcdIsdt != null "> and pcd_isdt = #{pcdIsdt}</if>
            <if test="pcdRmk != null  and pcdRmk != ''"> and pcd_rmk = #{pcdRmk}</if>
            <if test="pcdTdt != null  and pcdTdt != ''"> and pcd_tdt = #{pcdTdt}</if>
            <if test="pcdNtdt != null  and pcdNtdt != ''"> and pcd_ntdt = #{pcdNtdt}</if>
            <if test="pcdDdend != null  and pcdDdend != ''"> and pcd_ddend = #{pcdDdend}</if>
            <if test="pcdExchange != null "> and pcd_exchange = #{pcdExchange}</if>
            <if test="pcdLcUprc != null "> and pcd_lc_uprc = #{pcdLcUprc}</if>
            <if test="pcdLcValue != null "> and pcd_lc_value = #{pcdLcValue}</if>
            <if test="pcdEadt != null  and pcdEadt != ''"> and pcd_eadt = #{pcdEadt}</if>
            <if test="pcdNatyp != null  and pcdNatyp != ''"> and pcd_natyp = #{pcdNatyp}</if>
            <if test="pcdTrmk != null  and pcdTrmk != ''"> and pcd_trmk = #{pcdTrmk}</if>
            <if test="pcdFprmk != null  and pcdFprmk != ''"> and pcd_fprmk = #{pcdFprmk}</if>
            <if test="pcdPtyp != null  and pcdPtyp != ''"> and pcd_ptyp = #{pcdPtyp}</if>
            <if test="pcdIsoem != null  and pcdIsoem != ''"> and pcd_isoem = #{pcdIsoem}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="pcdSupPkg != null  and pcdSupPkg != ''"> and pcd_sup_pkg = #{pcdSupPkg}</if>
            <if test="pcdLcIncidentals != null "> and pcd_lc_incidentals = #{pcdLcIncidentals}</if>
            <if test="pcdReturnProvince != null  and pcdReturnProvince != ''"> and pcd_return_province = #{pcdReturnProvince}</if>
            <if test="pcdReturnCity != null  and pcdReturnCity != ''"> and pcd_return_city = #{pcdReturnCity}</if>
            <if test="pcdReturnDistrict != null  and pcdReturnDistrict != ''"> and pcd_return_district = #{pcdReturnDistrict}</if>
            <if test="pcdReturnAddr != null  and pcdReturnAddr != ''"> and pcd_return_addr = #{pcdReturnAddr}</if>
            <if test="pcdReturnContacts != null  and pcdReturnContacts != ''"> and pcd_return_contacts = #{pcdReturnContacts}</if>
            <if test="pcdReturnPhone != null  and pcdReturnPhone != ''"> and pcd_return_phone = #{pcdReturnPhone}</if>
            <if test="pcdReturnDt != null "> and pcd_return_dt = #{pcdReturnDt}</if>
            <if test="pcdPurcycl != null  and pcdPurcycl != ''"> and pcd_purcycl = #{pcdPurcycl}</if>
            <if test="pcdExpencesSts != null  and pcdExpencesSts != ''"> and pcd_expences_sts = #{pcdExpencesSts}</if>
            <if test="pcdExppassDt != null "> and pcd_exppass_dt = #{pcdExppassDt}</if>
            <if test="pcdSupPbcd != null  and pcdSupPbcd != ''"> and pcd_sup_pbcd = #{pcdSupPbcd}</if>
            <if test="pcdSupPorgcd != null  and pcdSupPorgcd != ''"> and pcd_sup_porgcd = #{pcdSupPorgcd}</if>
            <if test="pcdSupPpcd != null  and pcdSupPpcd != ''"> and pcd_sup_ppcd = #{pcdSupPpcd}</if>
            <if test="pcdSupQty != null "> and pcd_sup_qty = #{pcdSupQty}</if>
            <if test="pcdBnEncrypt != null  and pcdBnEncrypt != ''"> and pcd_bn_encrypt = #{pcdBnEncrypt}</if>
        </where>
    </select>
    
    <select id="selectPurContractDtlByPcdCd" parameterType="String" resultMap="PurContractDtlResult">
        <include refid="selectPurContractDtlVo"/>
        where pcd_cd = #{pcdCd}
    </select>

    <insert id="insertPurContractDtl" parameterType="PurContractDtl">
        insert into pur_contract_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pcdMcd != null">pcd_mcd,</if>
            <if test="pcdCd != null">pcd_cd,</if>
            <if test="pcdOpcd != null">pcd_opcd,</if>
            <if test="pcdTyp != null">pcd_typ,</if>
            <if test="pcdBr != null">pcd_br,</if>
            <if test="pcdPrecd != null">pcd_precd,</if>
            <if test="pcdPdcd != null">pcd_pdcd,</if>
            <if test="pcdPorgcd != null">pcd_porgcd,</if>
            <if test="pcdPcd != null">pcd_pcd,</if>
            <if test="pcdPnm != null">pcd_pnm,</if>
            <if test="pcdCas != null">pcd_cas,</if>
            <if test="pcdPurity != null">pcd_purity,</if>
            <if test="pcdDcls != null">pcd_dcls,</if>
            <if test="pcdSprmk != null">pcd_sprmk,</if>
            <if test="pcdSprmkPoisonod != null">pcd_sprmk_poisonod,</if>
            <if test="pcdStg != null">pcd_stg,</if>
            <if test="pcdPpcd != null">pcd_ppcd,</if>
            <if test="pcdPkgset != null">pcd_pkgset,</if>
            <if test="pcdPkgqty != null">pcd_pkgqty,</if>
            <if test="pcdPkgunit != null">pcd_pkgunit,</if>
            <if test="pcdPack != null">pcd_pack,</if>
            <if test="pcdDcd != null">pcd_dcd,</if>
            <if test="pcdDnm != null">pcd_dnm,</if>
            <if test="pcdOprc != null">pcd_oprc,</if>
            <if test="pcdDct != null">pcd_dct,</if>
            <if test="pcdUprc != null">pcd_uprc,</if>
            <if test="pcdQty != null">pcd_qty,</if>
            <if test="pcdInqty != null">pcd_inqty,</if>
            <if test="pcdIqty != null">pcd_iqty,</if>
            <if test="pcdTprc != null">pcd_tprc,</if>
            <if test="pcdTn != null">pcd_tn,</if>
            <if test="pcdStscd != null">pcd_stscd,</if>
            <if test="pcdStsnm != null">pcd_stsnm,</if>
            <if test="pcdPodt != null">pcd_podt,</if>
            <if test="pcdIsdt != null">pcd_isdt,</if>
            <if test="pcdRmk != null">pcd_rmk,</if>
            <if test="pcdTdt != null">pcd_tdt,</if>
            <if test="pcdNtdt != null">pcd_ntdt,</if>
            <if test="pcdDdend != null">pcd_ddend,</if>
            <if test="pcdExchange != null">pcd_exchange,</if>
            <if test="pcdLcUprc != null">pcd_lc_uprc,</if>
            <if test="pcdLcValue != null">pcd_lc_value,</if>
            <if test="pcdEadt != null">pcd_eadt,</if>
            <if test="pcdNatyp != null">pcd_natyp,</if>
            <if test="pcdTrmk != null">pcd_trmk,</if>
            <if test="pcdFprmk != null">pcd_fprmk,</if>
            <if test="pcdPtyp != null">pcd_ptyp,</if>
            <if test="pcdIsoem != null">pcd_isoem,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="pcdSupPkg != null">pcd_sup_pkg,</if>
            <if test="pcdLcIncidentals != null">pcd_lc_incidentals,</if>
            <if test="pcdReturnProvince != null">pcd_return_province,</if>
            <if test="pcdReturnCity != null">pcd_return_city,</if>
            <if test="pcdReturnDistrict != null">pcd_return_district,</if>
            <if test="pcdReturnAddr != null">pcd_return_addr,</if>
            <if test="pcdReturnContacts != null">pcd_return_contacts,</if>
            <if test="pcdReturnPhone != null">pcd_return_phone,</if>
            <if test="pcdReturnDt != null">pcd_return_dt,</if>
            <if test="pcdPurcycl != null">pcd_purcycl,</if>
            <if test="pcdExpencesSts != null">pcd_expences_sts,</if>
            <if test="pcdExppassDt != null">pcd_exppass_dt,</if>
            <if test="pcdSupPbcd != null">pcd_sup_pbcd,</if>
            <if test="pcdSupPorgcd != null">pcd_sup_porgcd,</if>
            <if test="pcdSupPpcd != null">pcd_sup_ppcd,</if>
            <if test="pcdSupQty != null">pcd_sup_qty,</if>
            <if test="pcdBnEncrypt != null">pcd_bn_encrypt,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pcdMcd != null">#{pcdMcd},</if>
            <if test="pcdCd != null">#{pcdCd},</if>
            <if test="pcdOpcd != null">#{pcdOpcd},</if>
            <if test="pcdTyp != null">#{pcdTyp},</if>
            <if test="pcdBr != null">#{pcdBr},</if>
            <if test="pcdPrecd != null">#{pcdPrecd},</if>
            <if test="pcdPdcd != null">#{pcdPdcd},</if>
            <if test="pcdPorgcd != null">#{pcdPorgcd},</if>
            <if test="pcdPcd != null">#{pcdPcd},</if>
            <if test="pcdPnm != null">#{pcdPnm},</if>
            <if test="pcdCas != null">#{pcdCas},</if>
            <if test="pcdPurity != null">#{pcdPurity},</if>
            <if test="pcdDcls != null">#{pcdDcls},</if>
            <if test="pcdSprmk != null">#{pcdSprmk},</if>
            <if test="pcdSprmkPoisonod != null">#{pcdSprmkPoisonod},</if>
            <if test="pcdStg != null">#{pcdStg},</if>
            <if test="pcdPpcd != null">#{pcdPpcd},</if>
            <if test="pcdPkgset != null">#{pcdPkgset},</if>
            <if test="pcdPkgqty != null">#{pcdPkgqty},</if>
            <if test="pcdPkgunit != null">#{pcdPkgunit},</if>
            <if test="pcdPack != null">#{pcdPack},</if>
            <if test="pcdDcd != null">#{pcdDcd},</if>
            <if test="pcdDnm != null">#{pcdDnm},</if>
            <if test="pcdOprc != null">#{pcdOprc},</if>
            <if test="pcdDct != null">#{pcdDct},</if>
            <if test="pcdUprc != null">#{pcdUprc},</if>
            <if test="pcdQty != null">#{pcdQty},</if>
            <if test="pcdInqty != null">#{pcdInqty},</if>
            <if test="pcdIqty != null">#{pcdIqty},</if>
            <if test="pcdTprc != null">#{pcdTprc},</if>
            <if test="pcdTn != null">#{pcdTn},</if>
            <if test="pcdStscd != null">#{pcdStscd},</if>
            <if test="pcdStsnm != null">#{pcdStsnm},</if>
            <if test="pcdPodt != null">#{pcdPodt},</if>
            <if test="pcdIsdt != null">#{pcdIsdt},</if>
            <if test="pcdRmk != null">#{pcdRmk},</if>
            <if test="pcdTdt != null">#{pcdTdt},</if>
            <if test="pcdNtdt != null">#{pcdNtdt},</if>
            <if test="pcdDdend != null">#{pcdDdend},</if>
            <if test="pcdExchange != null">#{pcdExchange},</if>
            <if test="pcdLcUprc != null">#{pcdLcUprc},</if>
            <if test="pcdLcValue != null">#{pcdLcValue},</if>
            <if test="pcdEadt != null">#{pcdEadt},</if>
            <if test="pcdNatyp != null">#{pcdNatyp},</if>
            <if test="pcdTrmk != null">#{pcdTrmk},</if>
            <if test="pcdFprmk != null">#{pcdFprmk},</if>
            <if test="pcdPtyp != null">#{pcdPtyp},</if>
            <if test="pcdIsoem != null">#{pcdIsoem},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="pcdSupPkg != null">#{pcdSupPkg},</if>
            <if test="pcdLcIncidentals != null">#{pcdLcIncidentals},</if>
            <if test="pcdReturnProvince != null">#{pcdReturnProvince},</if>
            <if test="pcdReturnCity != null">#{pcdReturnCity},</if>
            <if test="pcdReturnDistrict != null">#{pcdReturnDistrict},</if>
            <if test="pcdReturnAddr != null">#{pcdReturnAddr},</if>
            <if test="pcdReturnContacts != null">#{pcdReturnContacts},</if>
            <if test="pcdReturnPhone != null">#{pcdReturnPhone},</if>
            <if test="pcdReturnDt != null">#{pcdReturnDt},</if>
            <if test="pcdPurcycl != null">#{pcdPurcycl},</if>
            <if test="pcdExpencesSts != null">#{pcdExpencesSts},</if>
            <if test="pcdExppassDt != null">#{pcdExppassDt},</if>
            <if test="pcdSupPbcd != null">#{pcdSupPbcd},</if>
            <if test="pcdSupPorgcd != null">#{pcdSupPorgcd},</if>
            <if test="pcdSupPpcd != null">#{pcdSupPpcd},</if>
            <if test="pcdSupQty != null">#{pcdSupQty},</if>
            <if test="pcdBnEncrypt != null">#{pcdBnEncrypt},</if>
         </trim>
    </insert>

    <update id="updatePurContractDtl" parameterType="PurContractDtl">
        update pur_contract_dtl
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="pcdMcd != null">pcd_mcd = #{pcdMcd},</if>
            <if test="pcdOpcd != null">pcd_opcd = #{pcdOpcd},</if>
            <if test="pcdTyp != null">pcd_typ = #{pcdTyp},</if>
            <if test="pcdBr != null">pcd_br = #{pcdBr},</if>
            <if test="pcdPrecd != null">pcd_precd = #{pcdPrecd},</if>
            <if test="pcdPdcd != null">pcd_pdcd = #{pcdPdcd},</if>
            <if test="pcdPorgcd != null">pcd_porgcd = #{pcdPorgcd},</if>
            <if test="pcdPcd != null">pcd_pcd = #{pcdPcd},</if>
            <if test="pcdPnm != null">pcd_pnm = #{pcdPnm},</if>
            <if test="pcdCas != null">pcd_cas = #{pcdCas},</if>
            <if test="pcdPurity != null">pcd_purity = #{pcdPurity},</if>
            <if test="pcdDcls != null">pcd_dcls = #{pcdDcls},</if>
            <if test="pcdSprmk != null">pcd_sprmk = #{pcdSprmk},</if>
            <if test="pcdSprmkPoisonod != null">pcd_sprmk_poisonod = #{pcdSprmkPoisonod},</if>
            <if test="pcdStg != null">pcd_stg = #{pcdStg},</if>
            <if test="pcdPpcd != null">pcd_ppcd = #{pcdPpcd},</if>
            <if test="pcdPkgset != null">pcd_pkgset = #{pcdPkgset},</if>
            <if test="pcdPkgqty != null">pcd_pkgqty = #{pcdPkgqty},</if>
            <if test="pcdPkgunit != null">pcd_pkgunit = #{pcdPkgunit},</if>
            <if test="pcdPack != null">pcd_pack = #{pcdPack},</if>
            <if test="pcdDcd != null">pcd_dcd = #{pcdDcd},</if>
            <if test="pcdDnm != null">pcd_dnm = #{pcdDnm},</if>
            <if test="pcdOprc != null">pcd_oprc = #{pcdOprc},</if>
            <if test="pcdDct != null">pcd_dct = #{pcdDct},</if>
            <if test="pcdUprc != null">pcd_uprc = #{pcdUprc},</if>
            <if test="pcdQty != null">pcd_qty = #{pcdQty},</if>
            <if test="pcdInqty != null">pcd_inqty = #{pcdInqty},</if>
            <if test="pcdIqty != null">pcd_iqty = #{pcdIqty},</if>
            <if test="pcdTprc != null">pcd_tprc = #{pcdTprc},</if>
            <if test="pcdTn != null">pcd_tn = #{pcdTn},</if>
            <if test="pcdStscd != null">pcd_stscd = #{pcdStscd},</if>
            <if test="pcdStsnm != null">pcd_stsnm = #{pcdStsnm},</if>
            <if test="pcdPodt != null">pcd_podt = #{pcdPodt},</if>
            <if test="pcdIsdt != null">pcd_isdt = #{pcdIsdt},</if>
            <if test="pcdRmk != null">pcd_rmk = #{pcdRmk},</if>
            <if test="pcdTdt != null">pcd_tdt = #{pcdTdt},</if>
            <if test="pcdNtdt != null">pcd_ntdt = #{pcdNtdt},</if>
            <if test="pcdDdend != null">pcd_ddend = #{pcdDdend},</if>
            <if test="pcdExchange != null">pcd_exchange = #{pcdExchange},</if>
            <if test="pcdLcUprc != null">pcd_lc_uprc = #{pcdLcUprc},</if>
            <if test="pcdLcValue != null">pcd_lc_value = #{pcdLcValue},</if>
            <if test="pcdEadt != null">pcd_eadt = #{pcdEadt},</if>
            <if test="pcdNatyp != null">pcd_natyp = #{pcdNatyp},</if>
            <if test="pcdTrmk != null">pcd_trmk = #{pcdTrmk},</if>
            <if test="pcdFprmk != null">pcd_fprmk = #{pcdFprmk},</if>
            <if test="pcdPtyp != null">pcd_ptyp = #{pcdPtyp},</if>
            <if test="pcdIsoem != null">pcd_isoem = #{pcdIsoem},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="pcdSupPkg != null">pcd_sup_pkg = #{pcdSupPkg},</if>
            <if test="pcdLcIncidentals != null">pcd_lc_incidentals = #{pcdLcIncidentals},</if>
            <if test="pcdReturnProvince != null">pcd_return_province = #{pcdReturnProvince},</if>
            <if test="pcdReturnCity != null">pcd_return_city = #{pcdReturnCity},</if>
            <if test="pcdReturnDistrict != null">pcd_return_district = #{pcdReturnDistrict},</if>
            <if test="pcdReturnAddr != null">pcd_return_addr = #{pcdReturnAddr},</if>
            <if test="pcdReturnContacts != null">pcd_return_contacts = #{pcdReturnContacts},</if>
            <if test="pcdReturnPhone != null">pcd_return_phone = #{pcdReturnPhone},</if>
            <if test="pcdReturnDt != null">pcd_return_dt = #{pcdReturnDt},</if>
            <if test="pcdPurcycl != null">pcd_purcycl = #{pcdPurcycl},</if>
            <if test="pcdExpencesSts != null">pcd_expences_sts = #{pcdExpencesSts},</if>
            <if test="pcdExppassDt != null">pcd_exppass_dt = #{pcdExppassDt},</if>
            <if test="pcdSupPbcd != null">pcd_sup_pbcd = #{pcdSupPbcd},</if>
            <if test="pcdSupPorgcd != null">pcd_sup_porgcd = #{pcdSupPorgcd},</if>
            <if test="pcdSupPpcd != null">pcd_sup_ppcd = #{pcdSupPpcd},</if>
            <if test="pcdSupQty != null">pcd_sup_qty = #{pcdSupQty},</if>
            <if test="pcdBnEncrypt != null">pcd_bn_encrypt = #{pcdBnEncrypt},</if>
        </trim>
        where pcd_cd = #{pcdCd}
    </update>

    <delete id="deletePurContractDtlByPcdCd" parameterType="String">
        delete from pur_contract_dtl where pcd_cd = #{pcdCd}
    </delete>

    <delete id="deletePurContractDtlByPcdCds" parameterType="String">
        delete from pur_contract_dtl where pcd_cd in 
        <foreach item="pcdCd" collection="array" open="(" separator="," close=")">
            #{pcdCd}
        </foreach>
    </delete>
</mapper>