package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogRadDeveloCompose;
import com.lucy.erp.service.ILogRadDeveloComposeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小样工单-主料明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/log/compose")
public class LogRadDeveloComposeController extends BaseController
{
    @Autowired
    private ILogRadDeveloComposeService logRadDeveloComposeService;

    /**
     * 查询小样工单-主料明细列表
     */
    @PreAuthorize("@ss.hasPermi('log:compose:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogRadDeveloCompose logRadDeveloCompose)
    {
        startPage();
        List<LogRadDeveloCompose> list = logRadDeveloComposeService.selectLogRadDeveloComposeList(logRadDeveloCompose);
        return getDataTable(list);
    }

    /**
     * 导出小样工单-主料明细列表
     */
    @PreAuthorize("@ss.hasPermi('log:compose:export')")
    @Log(title = "小样工单-主料明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogRadDeveloCompose logRadDeveloCompose)
    {
        List<LogRadDeveloCompose> list = logRadDeveloComposeService.selectLogRadDeveloComposeList(logRadDeveloCompose);
        ExcelUtil<LogRadDeveloCompose> util = new ExcelUtil<LogRadDeveloCompose>(LogRadDeveloCompose.class);
        util.exportExcel(response, list, "小样工单-主料明细数据");
    }

    /**
     * 获取小样工单-主料明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:compose:query')")
    @GetMapping(value = "/{lrdcCd}")
    public AjaxResult getInfo(@PathVariable("lrdcCd") String lrdcCd)
    {
        return success(logRadDeveloComposeService.selectLogRadDeveloComposeByLrdcCd(lrdcCd));
    }

    /**
     * 新增小样工单-主料明细
     */
    @PreAuthorize("@ss.hasPermi('log:compose:add')")
    @Log(title = "小样工单-主料明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogRadDeveloCompose logRadDeveloCompose)
    {
        return toAjax(logRadDeveloComposeService.insertLogRadDeveloCompose(logRadDeveloCompose));
    }

    /**
     * 修改小样工单-主料明细
     */
    @PreAuthorize("@ss.hasPermi('log:compose:edit')")
    @Log(title = "小样工单-主料明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogRadDeveloCompose logRadDeveloCompose)
    {
        return toAjax(logRadDeveloComposeService.updateLogRadDeveloCompose(logRadDeveloCompose));
    }

    /**
     * 删除小样工单-主料明细
     */
    @PreAuthorize("@ss.hasPermi('log:compose:remove')")
    @Log(title = "小样工单-主料明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lrdcCds}")
    public AjaxResult remove(@PathVariable String[] lrdcCds)
    {
        return toAjax(logRadDeveloComposeService.deleteLogRadDeveloComposeByLrdcCds(lrdcCds));
    }
}
