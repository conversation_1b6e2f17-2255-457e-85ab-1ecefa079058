<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.inventoryoe.mapper.CostCompositionMapper">
    
    <resultMap type="CostComposition" id="CostCompositionResult">
        <result property="id"    column="id"    />
        <result property="structureName"    column="structure_name"    />
        <result property="parentStructureId"    column="parent_structure_id"    />
        <result property="status"    column="status"    />
        <result property="generationContext"    column="generation_context"    />
        <result property="currencyCode"    column="currency_code"    />
        <result property="baseCurrencyCode"    column="base_currency_code"    />
        <result property="exchangeRate"    column="exchange_rate"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="materialCost"    column="material_cost"    />
        <result property="laborCost"    column="labor_cost"    />
        <result property="manufacturingCost"    column="manufacturing_cost"    />
        <result property="logisticsCost"    column="logistics_cost"    />
        <result property="packagingCost"    column="packaging_cost"    />
        <result property="standardCost"    column="standard_cost"    />
        <result property="standardMaterial"    column="standard_material"    />
        <result property="standardLabor"    column="standard_labor"    />
        <result property="standardOverhead"    column="standard_overhead"    />
        <result property="standardLogistics"    column="standard_logistics"    />
        <result property="standardPackaging"    column="standard_packaging"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="version"    column="version"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectCostCompositionVo">
        select id, structure_name, parent_structure_id, status, generation_context, currency_code, base_currency_code, exchange_rate, total_amount, material_cost, labor_cost, manufacturing_cost, logistics_cost, packaging_cost, standard_cost, standard_material, standard_labor, standard_overhead, standard_logistics, standard_packaging, created_by, created_time, updated_by, updated_time, version, is_deleted from cost_composition
    </sql>

    <select id="selectCostCompositionList" parameterType="CostComposition" resultMap="CostCompositionResult">
        <include refid="selectCostCompositionVo"/>
        <where>  
            <if test="structureName != null  and structureName != ''"> and structure_name like concat('%', #{structureName}, '%')</if>
            <if test="parentStructureId != null "> and parent_structure_id = #{parentStructureId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="generationContext != null  and generationContext != ''"> and generation_context = #{generationContext}</if>
            <if test="currencyCode != null  and currencyCode != ''"> and currency_code = #{currencyCode}</if>
            <if test="baseCurrencyCode != null  and baseCurrencyCode != ''"> and base_currency_code = #{baseCurrencyCode}</if>
            <if test="exchangeRate != null "> and exchange_rate = #{exchangeRate}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="materialCost != null "> and material_cost = #{materialCost}</if>
            <if test="laborCost != null "> and labor_cost = #{laborCost}</if>
            <if test="manufacturingCost != null "> and manufacturing_cost = #{manufacturingCost}</if>
            <if test="logisticsCost != null "> and logistics_cost = #{logisticsCost}</if>
            <if test="packagingCost != null "> and packaging_cost = #{packagingCost}</if>
            <if test="standardCost != null "> and standard_cost = #{standardCost}</if>
            <if test="standardMaterial != null "> and standard_material = #{standardMaterial}</if>
            <if test="standardLabor != null "> and standard_labor = #{standardLabor}</if>
            <if test="standardOverhead != null "> and standard_overhead = #{standardOverhead}</if>
            <if test="standardLogistics != null "> and standard_logistics = #{standardLogistics}</if>
            <if test="standardPackaging != null "> and standard_packaging = #{standardPackaging}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
        </where>
    </select>
    
    <select id="selectCostCompositionById" parameterType="Long" resultMap="CostCompositionResult">
        <include refid="selectCostCompositionVo"/>
        where id = #{id}
    </select>

    <insert id="insertCostComposition" parameterType="CostComposition" useGeneratedKeys="true" keyProperty="id">
        insert into cost_composition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="structureName != null and structureName != ''">structure_name,</if>
            <if test="parentStructureId != null">parent_structure_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="generationContext != null">generation_context,</if>
            <if test="currencyCode != null and currencyCode != ''">currency_code,</if>
            <if test="baseCurrencyCode != null and baseCurrencyCode != ''">base_currency_code,</if>
            <if test="exchangeRate != null">exchange_rate,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="materialCost != null">material_cost,</if>
            <if test="laborCost != null">labor_cost,</if>
            <if test="manufacturingCost != null">manufacturing_cost,</if>
            <if test="logisticsCost != null">logistics_cost,</if>
            <if test="packagingCost != null">packaging_cost,</if>
            <if test="standardCost != null">standard_cost,</if>
            <if test="standardMaterial != null">standard_material,</if>
            <if test="standardLabor != null">standard_labor,</if>
            <if test="standardOverhead != null">standard_overhead,</if>
            <if test="standardLogistics != null">standard_logistics,</if>
            <if test="standardPackaging != null">standard_packaging,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="version != null">version,</if>
            <if test="isDeleted != null">is_deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="structureName != null and structureName != ''">#{structureName},</if>
            <if test="parentStructureId != null">#{parentStructureId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="generationContext != null">#{generationContext},</if>
            <if test="currencyCode != null and currencyCode != ''">#{currencyCode},</if>
            <if test="baseCurrencyCode != null and baseCurrencyCode != ''">#{baseCurrencyCode},</if>
            <if test="exchangeRate != null">#{exchangeRate},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="materialCost != null">#{materialCost},</if>
            <if test="laborCost != null">#{laborCost},</if>
            <if test="manufacturingCost != null">#{manufacturingCost},</if>
            <if test="logisticsCost != null">#{logisticsCost},</if>
            <if test="packagingCost != null">#{packagingCost},</if>
            <if test="standardCost != null">#{standardCost},</if>
            <if test="standardMaterial != null">#{standardMaterial},</if>
            <if test="standardLabor != null">#{standardLabor},</if>
            <if test="standardOverhead != null">#{standardOverhead},</if>
            <if test="standardLogistics != null">#{standardLogistics},</if>
            <if test="standardPackaging != null">#{standardPackaging},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="version != null">#{version},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
         </trim>
    </insert>

    <update id="updateCostComposition" parameterType="CostComposition">
        update cost_composition
        <trim prefix="SET" suffixOverrides=",">
            <if test="structureName != null and structureName != ''">structure_name = #{structureName},</if>
            <if test="parentStructureId != null">parent_structure_id = #{parentStructureId},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="generationContext != null and generationContext != ''">generation_context = #{generationContext},</if>
            <if test="currencyCode != null and currencyCode != ''">currency_code = #{currencyCode},</if>
            <if test="baseCurrencyCode != null and baseCurrencyCode != ''">base_currency_code = #{baseCurrencyCode},</if>
            <if test="exchangeRate != null">exchange_rate = #{exchangeRate},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="materialCost != null">material_cost = #{materialCost},</if>
            <if test="laborCost != null">labor_cost = #{laborCost},</if>
            <if test="manufacturingCost != null">manufacturing_cost = #{manufacturingCost},</if>
            <if test="logisticsCost != null">logistics_cost = #{logisticsCost},</if>
            <if test="packagingCost != null">packaging_cost = #{packagingCost},</if>
            <if test="standardCost != null">standard_cost = #{standardCost},</if>
            <if test="standardMaterial != null">standard_material = #{standardMaterial},</if>
            <if test="standardLabor != null">standard_labor = #{standardLabor},</if>
            <if test="standardOverhead != null">standard_overhead = #{standardOverhead},</if>
            <if test="standardLogistics != null">standard_logistics = #{standardLogistics},</if>
            <if test="standardPackaging != null">standard_packaging = #{standardPackaging},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCostCompositionById" parameterType="Long">
        delete from cost_composition where id = #{id}
    </delete>

    <delete id="deleteCostCompositionByIds" parameterType="String">
        delete from cost_composition where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>