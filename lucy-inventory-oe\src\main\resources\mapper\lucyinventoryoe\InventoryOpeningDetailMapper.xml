<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.inventoryoe.mapper.InventoryOpeningDetailMapper">
    
    <resultMap type="InventoryOpeningDetail" id="InventoryOpeningDetailResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="financialAccount"    column="financial_account"    />
        <result property="sku"    column="sku"    />
        <result property="quantity"    column="quantity"    />
        <result property="amount"    column="amount"    />
        <result property="sourceType"    column="source_type"    />
        <result property="assetOwner"    column="asset_owner"    />
        <result property="batchNumber"    column="batch_number"    />
        <result property="encryptedBatch"    column="encrypted_batch"    />
        <result property="warehouse"    column="warehouse"    />
        <result property="storageLocation"    column="storage_location"    />
        <result property="storageZone"    column="storage_zone"    />
        <result property="valuationTime"    column="valuation_time"    />
        <result property="valuator"    column="valuator"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditor"    column="auditor"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="version"    column="version"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="structureName"    column="structure_name"    />
        <result property="parentStructureId"    column="parent_structure_id"    />
    </resultMap>

    <sql id="selectInventoryOpeningDetailVo">
        select id, accounting_period, financial_account, sku, quantity, amount, source_type, asset_owner, batch_number, encrypted_batch, warehouse, storage_location, storage_zone, valuation_time, valuator, audit_time, auditor, created_by, created_time, updated_by, updated_time, version, is_deleted, structure_name, parent_structure_id from inventory_opening_detail
    </sql>

    <select id="selectInventoryOpeningDetailList" parameterType="InventoryOpeningDetail" resultMap="InventoryOpeningDetailResult">
        <include refid="selectInventoryOpeningDetailVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period = #{accountingPeriod}</if>
            <if test="financialAccount != null  and financialAccount != ''"> and financial_account = #{financialAccount}</if>
            <if test="sku != null  and sku != ''"> and sku = #{sku}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="sourceType != null  and sourceType != ''"> and source_type = #{sourceType}</if>
            <if test="assetOwner != null  and assetOwner != ''"> and asset_owner = #{assetOwner}</if>
            <if test="batchNumber != null  and batchNumber != ''"> and batch_number = #{batchNumber}</if>
            <if test="encryptedBatch != null  and encryptedBatch != ''"> and encrypted_batch = #{encryptedBatch}</if>
            <if test="warehouse != null  and warehouse != ''"> and warehouse = #{warehouse}</if>
            <if test="storageLocation != null  and storageLocation != ''"> and storage_location = #{storageLocation}</if>
            <if test="storageZone != null  and storageZone != ''"> and storage_zone = #{storageZone}</if>
            <if test="valuationTime != null "> and valuation_time = #{valuationTime}</if>
            <if test="valuator != null  and valuator != ''"> and valuator = #{valuator}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="auditor != null  and auditor != ''"> and auditor = #{auditor}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
            <if test="structureName != null  and structureName != ''"> and structure_name like concat('%', #{structureName}, '%')</if>
            <if test="parentStructureId != null "> and parent_structure_id = #{parentStructureId}</if>
        </where>
    </select>
    
    <select id="selectInventoryOpeningDetailById" parameterType="Long" resultMap="InventoryOpeningDetailResult">
        <include refid="selectInventoryOpeningDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertInventoryOpeningDetail" parameterType="InventoryOpeningDetail" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_opening_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="financialAccount != null and financialAccount != ''">financial_account,</if>
            <if test="sku != null and sku != ''">sku,</if>
            <if test="quantity != null">quantity,</if>
            <if test="amount != null">amount,</if>
            <if test="sourceType != null and sourceType != ''">source_type,</if>
            <if test="assetOwner != null and assetOwner != ''">asset_owner,</if>
            <if test="batchNumber != null and batchNumber != ''">batch_number,</if>
            <if test="encryptedBatch != null and encryptedBatch != ''">encrypted_batch,</if>
            <if test="warehouse != null and warehouse != ''">warehouse,</if>
            <if test="storageLocation != null and storageLocation != ''">storage_location,</if>
            <if test="storageZone != null and storageZone != ''">storage_zone,</if>
            <if test="valuationTime != null">valuation_time,</if>
            <if test="valuator != null and valuator != ''">valuator,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditor != null">auditor,</if>
            <if test="createdBy != null and createdBy != ''">created_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="version != null">version,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="structureName != null and structureName != ''">structure_name,</if>
            <if test="parentStructureId != null">parent_structure_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="financialAccount != null and financialAccount != ''">#{financialAccount},</if>
            <if test="sku != null and sku != ''">#{sku},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="amount != null">#{amount},</if>
            <if test="sourceType != null and sourceType != ''">#{sourceType},</if>
            <if test="assetOwner != null and assetOwner != ''">#{assetOwner},</if>
            <if test="batchNumber != null and batchNumber != ''">#{batchNumber},</if>
            <if test="encryptedBatch != null and encryptedBatch != ''">#{encryptedBatch},</if>
            <if test="warehouse != null and warehouse != ''">#{warehouse},</if>
            <if test="storageLocation != null and storageLocation != ''">#{storageLocation},</if>
            <if test="storageZone != null and storageZone != ''">#{storageZone},</if>
            <if test="valuationTime != null">#{valuationTime},</if>
            <if test="valuator != null and valuator != ''">#{valuator},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedBy != null and updatedBy != ''">#{updatedBy},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="version != null">#{version},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="structureName != null and structureName != ''">#{structureName},</if>
            <if test="parentStructureId != null">#{parentStructureId},</if>
         </trim>
    </insert>

    <update id="updateInventoryOpeningDetail" parameterType="InventoryOpeningDetail">
        update inventory_opening_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="financialAccount != null and financialAccount != ''">financial_account = #{financialAccount},</if>
            <if test="sku != null and sku != ''">sku = #{sku},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="sourceType != null and sourceType != ''">source_type = #{sourceType},</if>
            <if test="assetOwner != null and assetOwner != ''">asset_owner = #{assetOwner},</if>
            <if test="batchNumber != null and batchNumber != ''">batch_number = #{batchNumber},</if>
            <if test="encryptedBatch != null and encryptedBatch != ''">encrypted_batch = #{encryptedBatch},</if>
            <if test="warehouse != null and warehouse != ''">warehouse = #{warehouse},</if>
            <if test="storageLocation != null and storageLocation != ''">storage_location = #{storageLocation},</if>
            <if test="storageZone != null and storageZone != ''">storage_zone = #{storageZone},</if>
            <if test="valuationTime != null">valuation_time = #{valuationTime},</if>
            <if test="valuator != null and valuator != ''">valuator = #{valuator},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="structureName != null and structureName != ''">structure_name = #{structureName},</if>
            <if test="parentStructureId != null">parent_structure_id = #{parentStructureId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryOpeningDetailById" parameterType="Long">
        delete from inventory_opening_detail where id = #{id}
    </delete>

    <delete id="deleteInventoryOpeningDetailByIds" parameterType="String">
        delete from inventory_opening_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>