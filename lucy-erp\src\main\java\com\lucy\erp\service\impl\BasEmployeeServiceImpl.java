package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasEmployeeMapper;
import com.lucy.erp.domain.BasEmployee;
import com.lucy.erp.service.IBasEmployeeService;

/**
 * 基础_员工
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasEmployeeServiceImpl implements IBasEmployeeService
{
    @Autowired
    private BasEmployeeMapper basEmployeeMapper;

    /**
     * 查询基础_员工

     * 
     * @param beNo 基础_员工
主键
     * @return 基础_员工

     */
    @Override
    public BasEmployee selectBasEmployeeByBeNo(String beNo)
    {
        return basEmployeeMapper.selectBasEmployeeByBeNo(beNo);
    }

    /**
     * 查询基础_员工
列表
     * 
     * @param basEmployee 基础_员工

     * @return 基础_员工

     */
    @Override
    public List<BasEmployee> selectBasEmployeeList(BasEmployee basEmployee)
    {
        return basEmployeeMapper.selectBasEmployeeList(basEmployee);
    }

    /**
     * 新增基础_员工

     * 
     * @param basEmployee 基础_员工

     * @return 结果
     */
    @Override
    public int insertBasEmployee(BasEmployee basEmployee)
    {
        return basEmployeeMapper.insertBasEmployee(basEmployee);
    }

    /**
     * 修改基础_员工

     * 
     * @param basEmployee 基础_员工

     * @return 结果
     */
    @Override
    public int updateBasEmployee(BasEmployee basEmployee)
    {
        return basEmployeeMapper.updateBasEmployee(basEmployee);
    }

    /**
     * 批量删除基础_员工

     * 
     * @param beNos 需要删除的基础_员工
主键
     * @return 结果
     */
    @Override
    public int deleteBasEmployeeByBeNos(String[] beNos)
    {
        return basEmployeeMapper.deleteBasEmployeeByBeNos(beNos);
    }

    /**
     * 删除基础_员工
信息
     * 
     * @param beNo 基础_员工
主键
     * @return 结果
     */
    @Override
    public int deleteBasEmployeeByBeNo(String beNo)
    {
        return basEmployeeMapper.deleteBasEmployeeByBeNo(beNo);
    }
}
