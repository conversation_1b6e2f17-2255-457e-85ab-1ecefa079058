package com.lucy.skumaster.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.skumaster.domain.SkuMaster;
import com.lucy.skumaster.service.ISkuMasterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * SKU主数据 SKU master dataController
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/skumaster/master")
public class SkuMasterController extends BaseController
{
    @Autowired
    private ISkuMasterService skuMasterService;

    /**
     * 查询SKU主数据 SKU master data列表
     */
    @PreAuthorize("@ss.hasPermi('skumaster:master:list')")
    @GetMapping("/list")
    public TableDataInfo list(SkuMaster skuMaster)
    {
        startPage();
        List<SkuMaster> list = skuMasterService.selectSkuMasterList(skuMaster);
        return getDataTable(list);
    }

    /**
     * 导出SKU主数据 SKU master data列表
     */
    @PreAuthorize("@ss.hasPermi('skumaster:master:export')")
    @Log(title = "SKU主数据 SKU master data", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SkuMaster skuMaster)
    {
        List<SkuMaster> list = skuMasterService.selectSkuMasterList(skuMaster);
        ExcelUtil<SkuMaster> util = new ExcelUtil<SkuMaster>(SkuMaster.class);
        util.exportExcel(response, list, "SKU主数据 SKU master data数据");
    }

    /**
     * 获取SKU主数据 SKU master data详细信息
     */
    @PreAuthorize("@ss.hasPermi('skumaster:master:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(skuMasterService.selectSkuMasterByCode(code));
    }

    /**
     * 新增SKU主数据 SKU master data
     */
    @PreAuthorize("@ss.hasPermi('skumaster:master:add')")
    @Log(title = "SKU主数据 SKU master data", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SkuMaster skuMaster)
    {
        return toAjax(skuMasterService.insertSkuMaster(skuMaster));
    }

    /**
     * 修改SKU主数据 SKU master data
     */
    @PreAuthorize("@ss.hasPermi('skumaster:master:edit')")
    @Log(title = "SKU主数据 SKU master data", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SkuMaster skuMaster)
    {
        return toAjax(skuMasterService.updateSkuMaster(skuMaster));
    }

    /**
     * 删除SKU主数据 SKU master data
     */
    @PreAuthorize("@ss.hasPermi('skumaster:master:remove')")
    @Log(title = "SKU主数据 SKU master data", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(skuMasterService.deleteSkuMasterByCodes(codes));
    }
}
