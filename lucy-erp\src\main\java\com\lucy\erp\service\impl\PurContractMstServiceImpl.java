package com.lucy.erp.service.impl;

import java.util.Collections;
import java.util.List;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.PurContractMstMapper;
import com.lucy.erp.domain.PurContractMst;
import com.lucy.erp.service.IPurContractMstService;

/**
 * 采购_合同_主
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@DataSource(DataSourceType.ERP)
public class PurContractMstServiceImpl implements IPurContractMstService
{
    @Autowired
    private PurContractMstMapper purContractMstMapper;

    /**
     * 查询采购_合同_主

     * 
     * @param pcmCd 采购_合同_主
主键
     * @return 采购_合同_主

     */
    @Override
    public PurContractMst selectPurContractMstByPcmCd(String pcmCd)
    {
        return purContractMstMapper.selectPurContractMstByPcmCd(pcmCd);
    }

    /**
     * 查询采购_合同_主
列表
     * 
     * @param purContractMst 采购_合同_主

     * @return 采购_合同_主

     */
    @Override
    public List<PurContractMst> selectPurContractMstList(PurContractMst purContractMst)
    {
        return purContractMstMapper.selectPurContractMstList(purContractMst);
    }

    @Override
    public List<String> selectPurContractMstListR() {
        return purContractMstMapper.selectPurContractMstListR();
    }

    /**
     * 新增采购_合同_主

     * 
     * @param purContractMst 采购_合同_主

     * @return 结果
     */
    @Override
    public int insertPurContractMst(PurContractMst purContractMst)
    {
        return purContractMstMapper.insertPurContractMst(purContractMst);
    }

    /**
     * 修改采购_合同_主

     * 
     * @param purContractMst 采购_合同_主

     * @return 结果
     */
    @Override
    public int updatePurContractMst(PurContractMst purContractMst)
    {
        return purContractMstMapper.updatePurContractMst(purContractMst);
    }

    /**
     * 批量删除采购_合同_主

     * 
     * @param pcmCds 需要删除的采购_合同_主
主键
     * @return 结果
     */
    @Override
    public int deletePurContractMstByPcmCds(String[] pcmCds)
    {
        return purContractMstMapper.deletePurContractMstByPcmCds(pcmCds);
    }

    /**
     * 删除采购_合同_主
信息
     * 
     * @param pcmCd 采购_合同_主
主键
     * @return 结果
     */
    @Override
    public int deletePurContractMstByPcmCd(String pcmCd)
    {
        return purContractMstMapper.deletePurContractMstByPcmCd(pcmCd);
    }
}
