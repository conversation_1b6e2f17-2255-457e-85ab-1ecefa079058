package com.ruoyi.inventoryentry.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.inventoryentry.domain.InventoryCurrentPeriodEntry;
import com.ruoyi.inventoryentry.service.IInventoryCurrentPeriodEntryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 本期入库明细 Current period inventory entriesController
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@RestController
@RequestMapping("/inventoryentry/entry")
public class InventoryCurrentPeriodEntryController extends BaseController
{
    @Autowired
    private IInventoryCurrentPeriodEntryService inventoryCurrentPeriodEntryService;

    /**
     * 查询本期入库明细 Current period inventory entries列表
     */
    @PreAuthorize("@ss.hasPermi('inventoryentry:entry:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry)
    {
        startPage();
        List<InventoryCurrentPeriodEntry> list = inventoryCurrentPeriodEntryService.selectInventoryCurrentPeriodEntryList(inventoryCurrentPeriodEntry);
        return getDataTable(list);
    }

    /**
     * 导出本期入库明细 Current period inventory entries列表
     */
    @PreAuthorize("@ss.hasPermi('inventoryentry:entry:export')")
    @Log(title = "本期入库明细 Current period inventory entries", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry)
    {
        List<InventoryCurrentPeriodEntry> list = inventoryCurrentPeriodEntryService.selectInventoryCurrentPeriodEntryList(inventoryCurrentPeriodEntry);
        ExcelUtil<InventoryCurrentPeriodEntry> util = new ExcelUtil<InventoryCurrentPeriodEntry>(InventoryCurrentPeriodEntry.class);
        util.exportExcel(response, list, "本期入库明细 Current period inventory entries数据");
    }

    /**
     * 获取本期入库明细 Current period inventory entries详细信息
     */
    @PreAuthorize("@ss.hasPermi('inventoryentry:entry:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryCurrentPeriodEntryService.selectInventoryCurrentPeriodEntryById(id));
    }

    /**
     * 新增本期入库明细 Current period inventory entries
     */
    @PreAuthorize("@ss.hasPermi('inventoryentry:entry:add')")
    @Log(title = "本期入库明细 Current period inventory entries", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry)
    {
        return toAjax(inventoryCurrentPeriodEntryService.insertInventoryCurrentPeriodEntry(inventoryCurrentPeriodEntry));
    }

    /**
     * 修改本期入库明细 Current period inventory entries
     */
    @PreAuthorize("@ss.hasPermi('inventoryentry:entry:edit')")
    @Log(title = "本期入库明细 Current period inventory entries", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry)
    {
        return toAjax(inventoryCurrentPeriodEntryService.updateInventoryCurrentPeriodEntry(inventoryCurrentPeriodEntry));
    }

    /**
     * 删除本期入库明细 Current period inventory entries
     */
    @PreAuthorize("@ss.hasPermi('inventoryentry:entry:remove')")
    @Log(title = "本期入库明细 Current period inventory entries", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inventoryCurrentPeriodEntryService.deleteInventoryCurrentPeriodEntryByIds(ids));
    }
}
