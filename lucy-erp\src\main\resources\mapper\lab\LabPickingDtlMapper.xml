<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LabPickingDtlMapper">
    
    <resultMap type="LabPickingDtl" id="LabPickingDtlResult">
        <result property="id"    column="id"    />
        <result property="lpdMcd"    column="lpd_mcd"    />
        <result property="lpdSlcd"    column="lpd_slcd"    />
        <result property="lpdPpcd"    column="lpd_ppcd"    />
        <result property="lpdBn"    column="lpd_bn"    />
        <result property="lpdBnEncrypt"    column="lpd_bn_encrypt"    />
        <result property="lpdQty"    column="lpd_qty"    />
        <result property="lpdUsedQty"    column="lpd_used_qty"    />
        <result property="lpdUnitp"    column="lpd_unitp"    />
        <result property="lpdTotalPrc"    column="lpd_total_prc"    />
        <result property="lpdOrgmcd"    column="lpd_orgmcd"    />
        <result property="lpdDmcd"    column="lpd_dmcd"    />
        <result property="lpdTcd"    column="lpd_tcd"    />
        <result property="lpdFinFlag"    column="lpd_fin_flag"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lpdSts"    column="lpd_sts"    />
        <result property="lpdCd"    column="lpd_cd"    />
        <result property="lpdNm"    column="lpd_nm"    />
        <result property="lpdCollectionTime"    column="lpd_collection_time"    />
        <result property="lpdPcd"    column="lpd_pcd"    />
        <result property="lpdRcd"    column="lpd_rcd"    />
        <result property="lpdRdId"    column="lpd_rd_id"    />
    </resultMap>

    <sql id="selectLabPickingDtlVo">
        select id, lpd_mcd, lpd_slcd, lpd_ppcd, lpd_bn, lpd_bn_encrypt, lpd_qty, lpd_used_qty, lpd_unitp, lpd_total_prc, lpd_orgmcd, lpd_dmcd, lpd_tcd, lpd_fin_flag, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, lpd_sts, lpd_cd, lpd_nm, lpd_collection_time, lpd_pcd, lpd_rcd, lpd_rd_id from lab_picking_dtl
    </sql>

    <select id="selectLabPickingDtlList" parameterType="LabPickingDtl" resultMap="LabPickingDtlResult">
        <include refid="selectLabPickingDtlVo"/>
        <where>  
            <if test="lpdMcd != null  and lpdMcd != ''"> and lpd_mcd = #{lpdMcd}</if>
            <if test="lpdSlcd != null  and lpdSlcd != ''"> and lpd_slcd = #{lpdSlcd}</if>
            <if test="lpdPpcd != null  and lpdPpcd != ''"> and lpd_ppcd = #{lpdPpcd}</if>
            <if test="lpdBn != null  and lpdBn != ''"> and lpd_bn = #{lpdBn}</if>
            <if test="lpdBnEncrypt != null  and lpdBnEncrypt != ''"> and lpd_bn_encrypt = #{lpdBnEncrypt}</if>
            <if test="lpdQty != null "> and lpd_qty = #{lpdQty}</if>
            <if test="lpdUsedQty != null "> and lpd_used_qty = #{lpdUsedQty}</if>
            <if test="lpdUnitp != null "> and lpd_unitp = #{lpdUnitp}</if>
            <if test="lpdTotalPrc != null "> and lpd_total_prc = #{lpdTotalPrc}</if>
            <if test="lpdOrgmcd != null  and lpdOrgmcd != ''"> and lpd_orgmcd = #{lpdOrgmcd}</if>
            <if test="lpdDmcd != null  and lpdDmcd != ''"> and lpd_dmcd = #{lpdDmcd}</if>
            <if test="lpdTcd != null  and lpdTcd != ''"> and lpd_tcd = #{lpdTcd}</if>
            <if test="lpdFinFlag != null  and lpdFinFlag != ''"> and lpd_fin_flag = #{lpdFinFlag}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="lpdSts != null  and lpdSts != ''"> and lpd_sts = #{lpdSts}</if>
            <if test="lpdNm != null  and lpdNm != ''"> and lpd_nm = #{lpdNm}</if>
            <if test="lpdCollectionTime != null "> and lpd_collection_time = #{lpdCollectionTime}</if>
            <if test="lpdPcd != null  and lpdPcd != ''"> and lpd_pcd = #{lpdPcd}</if>
            <if test="lpdRcd != null  and lpdRcd != ''"> and lpd_rcd = #{lpdRcd}</if>
            <if test="lpdRdId != null "> and lpd_rd_id = #{lpdRdId}</if>
        </where>
    </select>
    
    <select id="selectLabPickingDtlByLpdCd" parameterType="String" resultMap="LabPickingDtlResult">
        <include refid="selectLabPickingDtlVo"/>
        where lpd_cd = #{lpdCd}
    </select>

    <insert id="insertLabPickingDtl" parameterType="LabPickingDtl">
        insert into lab_picking_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lpdMcd != null">lpd_mcd,</if>
            <if test="lpdSlcd != null">lpd_slcd,</if>
            <if test="lpdPpcd != null">lpd_ppcd,</if>
            <if test="lpdBn != null">lpd_bn,</if>
            <if test="lpdBnEncrypt != null">lpd_bn_encrypt,</if>
            <if test="lpdQty != null">lpd_qty,</if>
            <if test="lpdUsedQty != null">lpd_used_qty,</if>
            <if test="lpdUnitp != null">lpd_unitp,</if>
            <if test="lpdTotalPrc != null">lpd_total_prc,</if>
            <if test="lpdOrgmcd != null">lpd_orgmcd,</if>
            <if test="lpdDmcd != null">lpd_dmcd,</if>
            <if test="lpdTcd != null">lpd_tcd,</if>
            <if test="lpdFinFlag != null">lpd_fin_flag,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="lpdSts != null">lpd_sts,</if>
            <if test="lpdCd != null">lpd_cd,</if>
            <if test="lpdNm != null">lpd_nm,</if>
            <if test="lpdCollectionTime != null">lpd_collection_time,</if>
            <if test="lpdPcd != null">lpd_pcd,</if>
            <if test="lpdRcd != null">lpd_rcd,</if>
            <if test="lpdRdId != null">lpd_rd_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lpdMcd != null">#{lpdMcd},</if>
            <if test="lpdSlcd != null">#{lpdSlcd},</if>
            <if test="lpdPpcd != null">#{lpdPpcd},</if>
            <if test="lpdBn != null">#{lpdBn},</if>
            <if test="lpdBnEncrypt != null">#{lpdBnEncrypt},</if>
            <if test="lpdQty != null">#{lpdQty},</if>
            <if test="lpdUsedQty != null">#{lpdUsedQty},</if>
            <if test="lpdUnitp != null">#{lpdUnitp},</if>
            <if test="lpdTotalPrc != null">#{lpdTotalPrc},</if>
            <if test="lpdOrgmcd != null">#{lpdOrgmcd},</if>
            <if test="lpdDmcd != null">#{lpdDmcd},</if>
            <if test="lpdTcd != null">#{lpdTcd},</if>
            <if test="lpdFinFlag != null">#{lpdFinFlag},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="lpdSts != null">#{lpdSts},</if>
            <if test="lpdCd != null">#{lpdCd},</if>
            <if test="lpdNm != null">#{lpdNm},</if>
            <if test="lpdCollectionTime != null">#{lpdCollectionTime},</if>
            <if test="lpdPcd != null">#{lpdPcd},</if>
            <if test="lpdRcd != null">#{lpdRcd},</if>
            <if test="lpdRdId != null">#{lpdRdId},</if>
         </trim>
    </insert>

    <update id="updateLabPickingDtl" parameterType="LabPickingDtl">
        update lab_picking_dtl
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lpdMcd != null">lpd_mcd = #{lpdMcd},</if>
            <if test="lpdSlcd != null">lpd_slcd = #{lpdSlcd},</if>
            <if test="lpdPpcd != null">lpd_ppcd = #{lpdPpcd},</if>
            <if test="lpdBn != null">lpd_bn = #{lpdBn},</if>
            <if test="lpdBnEncrypt != null">lpd_bn_encrypt = #{lpdBnEncrypt},</if>
            <if test="lpdQty != null">lpd_qty = #{lpdQty},</if>
            <if test="lpdUsedQty != null">lpd_used_qty = #{lpdUsedQty},</if>
            <if test="lpdUnitp != null">lpd_unitp = #{lpdUnitp},</if>
            <if test="lpdTotalPrc != null">lpd_total_prc = #{lpdTotalPrc},</if>
            <if test="lpdOrgmcd != null">lpd_orgmcd = #{lpdOrgmcd},</if>
            <if test="lpdDmcd != null">lpd_dmcd = #{lpdDmcd},</if>
            <if test="lpdTcd != null">lpd_tcd = #{lpdTcd},</if>
            <if test="lpdFinFlag != null">lpd_fin_flag = #{lpdFinFlag},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="lpdSts != null">lpd_sts = #{lpdSts},</if>
            <if test="lpdNm != null">lpd_nm = #{lpdNm},</if>
            <if test="lpdCollectionTime != null">lpd_collection_time = #{lpdCollectionTime},</if>
            <if test="lpdPcd != null">lpd_pcd = #{lpdPcd},</if>
            <if test="lpdRcd != null">lpd_rcd = #{lpdRcd},</if>
            <if test="lpdRdId != null">lpd_rd_id = #{lpdRdId},</if>
        </trim>
        where lpd_cd = #{lpdCd}
    </update>

    <delete id="deleteLabPickingDtlByLpdCd" parameterType="String">
        delete from lab_picking_dtl where lpd_cd = #{lpdCd}
    </delete>

    <delete id="deleteLabPickingDtlByLpdCds" parameterType="String">
        delete from lab_picking_dtl where lpd_cd in 
        <foreach item="lpdCd" collection="array" open="(" separator="," close=")">
            #{lpdCd}
        </foreach>
    </delete>
</mapper>