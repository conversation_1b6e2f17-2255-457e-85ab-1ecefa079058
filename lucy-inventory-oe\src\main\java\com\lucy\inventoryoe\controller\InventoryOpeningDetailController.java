package com.lucy.inventoryoe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.inventoryoe.domain.InventoryOpeningDetail;
import com.lucy.inventoryoe.service.IInventoryOpeningDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 库存期初明细 Inventory opening detail recordsController
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@RestController
@RequestMapping("/lucy-inventoryoe/detail2")
public class InventoryOpeningDetailController extends BaseController
{
    @Autowired
    private IInventoryOpeningDetailService inventoryOpeningDetailService;

    /**
     * 查询库存期初明细 Inventory opening detail records列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:detail2:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryOpeningDetail inventoryOpeningDetail)
    {
        startPage();
        List<InventoryOpeningDetail> list = inventoryOpeningDetailService.selectInventoryOpeningDetailList(inventoryOpeningDetail);
        return getDataTable(list);
    }

    /**
     * 导出库存期初明细 Inventory opening detail records列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:detail2:export')")
    @Log(title = "库存期初明细 Inventory opening detail records", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryOpeningDetail inventoryOpeningDetail)
    {
        List<InventoryOpeningDetail> list = inventoryOpeningDetailService.selectInventoryOpeningDetailList(inventoryOpeningDetail);
        ExcelUtil<InventoryOpeningDetail> util = new ExcelUtil<InventoryOpeningDetail>(InventoryOpeningDetail.class);
        util.exportExcel(response, list, "库存期初明细 Inventory opening detail records数据");
    }

    /**
     * 获取库存期初明细 Inventory opening detail records详细信息
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:detail2:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryOpeningDetailService.selectInventoryOpeningDetailById(id));
    }

    /**
     * 新增库存期初明细 Inventory opening detail records
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:detail2:add')")
    @Log(title = "库存期初明细 Inventory opening detail records", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryOpeningDetail inventoryOpeningDetail)
    {
        return toAjax(inventoryOpeningDetailService.insertInventoryOpeningDetail(inventoryOpeningDetail));
    }

    /**
     * 修改库存期初明细 Inventory opening detail records
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:detail2:edit')")
    @Log(title = "库存期初明细 Inventory opening detail records", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryOpeningDetail inventoryOpeningDetail)
    {
        return toAjax(inventoryOpeningDetailService.updateInventoryOpeningDetail(inventoryOpeningDetail));
    }

    /**
     * 删除库存期初明细 Inventory opening detail records
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:detail2:remove')")
    @Log(title = "库存期初明细 Inventory opening detail records", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inventoryOpeningDetailService.deleteInventoryOpeningDetailByIds(ids));
    }
}
