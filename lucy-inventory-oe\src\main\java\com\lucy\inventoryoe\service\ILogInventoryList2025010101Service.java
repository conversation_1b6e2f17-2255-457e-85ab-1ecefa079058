package com.lucy.inventoryoe.service;

import java.util.List;
import com.lucy.inventoryoe.domain.LogInventoryList2025010101;

/**
 * 库存清单快照Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface ILogInventoryList2025010101Service 
{
    /**
     * 查询库存清单快照
     * 
     * @param id 库存清单快照主键
     * @return 库存清单快照
     */
    public LogInventoryList2025010101 selectLogInventoryList2025010101ById(Long id);

    /**
     * 查询库存清单快照列表
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 库存清单快照集合
     */
    public List<LogInventoryList2025010101> selectLogInventoryList2025010101List(LogInventoryList2025010101 logInventoryList2025010101);

    /**
     * 新增库存清单快照
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 结果
     */
    public int insertLogInventoryList2025010101(LogInventoryList2025010101 logInventoryList2025010101);

    /**
     * 修改库存清单快照
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 结果
     */
    public int updateLogInventoryList2025010101(LogInventoryList2025010101 logInventoryList2025010101);

    /**
     * 批量删除库存清单快照
     * 
     * @param ids 需要删除的库存清单快照主键集合
     * @return 结果
     */
    public int deleteLogInventoryList2025010101ByIds(Long[] ids);

    /**
     * 删除库存清单快照信息
     * 
     * @param id 库存清单快照主键
     * @return 结果
     */
    public int deleteLogInventoryList2025010101ById(Long id);
}
