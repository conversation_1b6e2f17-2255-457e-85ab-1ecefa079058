package com.lucy.customer.service;

import java.util.List;
import com.lucy.customer.domain.CustomerCompany;

/**
 * 部门组织结构 Department groupsService接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface ICustomerCompanyService 
{
    /**
     * 查询部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 部门组织结构 Department groups
     */
    public CustomerCompany selectCustomerCompanyByCode(String code);

    /**
     * 查询部门组织结构 Department groups列表
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 部门组织结构 Department groups集合
     */
    public List<CustomerCompany> selectCustomerCompanyList(CustomerCompany customerCompany);

    /**
     * 新增部门组织结构 Department groups
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 结果
     */
    public int insertCustomerCompany(CustomerCompany customerCompany);

    /**
     * 修改部门组织结构 Department groups
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 结果
     */
    public int updateCustomerCompany(CustomerCompany customerCompany);

    /**
     * 批量删除部门组织结构 Department groups
     * 
     * @param codes 需要删除的部门组织结构 Department groups主键集合
     * @return 结果
     */
    public int deleteCustomerCompanyByCodes(String[] codes);

    /**
     * 删除部门组织结构 Department groups信息
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 结果
     */
    public int deleteCustomerCompanyByCode(String code);

    public void insertCustomerCompanyIfNotExist(String code);
}
