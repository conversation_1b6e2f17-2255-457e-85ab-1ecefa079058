package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.SalesContractInvc;

/**
 * 销售_合同_发票Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface SalesContractInvcMapper 
{
    /**
     * 查询销售_合同_发票
     * 
     * @param sciMcd 销售_合同_发票主键
     * @return 销售_合同_发票
     */
    public SalesContractInvc selectSalesContractInvcBySciMcd(String sciMcd);

    /**
     * 查询销售_合同_发票列表
     * 
     * @param salesContractInvc 销售_合同_发票
     * @return 销售_合同_发票集合
     */
    public List<SalesContractInvc> selectSalesContractInvcList(SalesContractInvc salesContractInvc);

    /**
     * 新增销售_合同_发票
     * 
     * @param salesContractInvc 销售_合同_发票
     * @return 结果
     */
    public int insertSalesContractInvc(SalesContractInvc salesContractInvc);

    /**
     * 修改销售_合同_发票
     * 
     * @param salesContractInvc 销售_合同_发票
     * @return 结果
     */
    public int updateSalesContractInvc(SalesContractInvc salesContractInvc);

    /**
     * 删除销售_合同_发票
     * 
     * @param sciMcd 销售_合同_发票主键
     * @return 结果
     */
    public int deleteSalesContractInvcBySciMcd(String sciMcd);

    /**
     * 批量删除销售_合同_发票
     * 
     * @param sciMcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSalesContractInvcBySciMcds(String[] sciMcds);
}
