<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogInventoryIoTransactionMapper">
    
    <resultMap type="LogInventoryIoTransaction" id="LogInventoryIoTransactionResult">
        <result property="id"    column="id"    />
        <result property="liotDmcd"    column="liot_dmcd"    />
        <result property="liotTcd"    column="liot_tcd"    />
        <result property="liotSlcd"    column="liot_slcd"    />
        <result property="liotPpcd"    column="liot_ppcd"    />
        <result property="liotBn"    column="liot_bn"    />
        <result property="liotBnEncrypt"    column="liot_bn_encrypt"    />
        <result property="liotPbn"    column="liot_pbn"    />
        <result property="liotSbn"    column="liot_sbn"    />
        <result property="liotQty"    column="liot_qty"    />
        <result property="liotUnitp"    column="liot_unitp"    />
        <result property="liotBit"    column="liot_bit"    />
        <result property="liotIspart"    column="liot_ispart"    />
        <result property="liotTotalValue"    column="liot_total_value"    />
        <result property="liotOrgmcd"    column="liot_orgmcd"    />
        <result property="liotTyp"    column="liot_typ"    />
        <result property="liotRmk1"    column="liot_rmk1"    />
        <result property="liotRmk2"    column="liot_rmk2"    />
        <result property="liotRmk3"    column="liot_rmk3"    />
        <result property="liotRmk4"    column="liot_rmk4"    />
        <result property="liotRmk5"    column="liot_rmk5"    />
        <result property="liotRmk6"    column="liot_rmk6"    />
        <result property="liotRmk7"    column="liot_rmk7"    />
        <result property="liotFinFlag"    column="liot_fin_flag"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectLogInventoryIoTransactionVo">
        select id, liot_dmcd, liot_tcd, liot_slcd, liot_ppcd, liot_bn, liot_bn_encrypt, liot_pbn, liot_sbn, liot_qty, liot_unitp, liot_bit, liot_ispart, liot_total_value, liot_orgmcd, liot_typ, liot_rmk1, liot_rmk2, liot_rmk3, liot_rmk4, liot_rmk5, liot_rmk6, liot_rmk7, liot_fin_flag, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from log_inventory_io_transaction
    </sql>

    <select id="selectLogInventoryIoTransactionList" parameterType="LogInventoryIoTransaction" resultMap="LogInventoryIoTransactionResult">
        <include refid="selectLogInventoryIoTransactionVo"/>
        <where>
            <if test="liotDmcd != null  and liotDmcd != ''"> and liot_dmcd = #{liotDmcd}</if>
            <if test="liotTcd != null  and liotTcd != ''"> and liot_tcd = #{liotTcd}</if>
            <if test="liotSlcd != null  and liotSlcd != ''"> and liot_slcd = #{liotSlcd}</if>
            <if test="liotPpcd != null  and liotPpcd != ''"> and liot_ppcd = #{liotPpcd}</if>
            <if test="liotBn != null  and liotBn != ''"> and liot_bn = #{liotBn}</if>
            <if test="liotBnEncrypt != null  and liotBnEncrypt != ''"> and liot_bn_encrypt = #{liotBnEncrypt}</if>
            <if test="liotPbn != null  and liotPbn != ''"> and liot_pbn = #{liotPbn}</if>
            <if test="liotSbn != null  and liotSbn != ''"> and liot_sbn = #{liotSbn}</if>
            <if test="liotQty != null "> and liot_qty = #{liotQty}</if>
            <if test="liotUnitp != null "> and liot_unitp = #{liotUnitp}</if>
            <if test="liotBit != null "> and liot_bit = #{liotBit}</if>
            <if test="liotIspart != null "> and liot_ispart = #{liotIspart}</if>
            <if test="liotTotalValue != null "> and liot_total_value = #{liotTotalValue}</if>
            <if test="liotOrgmcd != null  and liotOrgmcd != ''"> and liot_orgmcd = #{liotOrgmcd}</if>
            <if test="liotTyp != null "> and liot_typ = #{liotTyp}</if>
            <if test="liotRmk1 != null  and liotRmk1 != ''"> and liot_rmk1 = #{liotRmk1}</if>
            <if test="liotRmk2 != null  and liotRmk2 != ''"> and liot_rmk2 = #{liotRmk2}</if>
            <if test="liotRmk3 != null  and liotRmk3 != ''"> and liot_rmk3 = #{liotRmk3}</if>
            <if test="liotRmk4 != null  and liotRmk4 != ''"> and liot_rmk4 = #{liotRmk4}</if>
            <if test="liotRmk5 != null  and liotRmk5 != ''"> and liot_rmk5 = #{liotRmk5}</if>
            <if test="liotRmk6 != null  and liotRmk6 != ''"> and liot_rmk6 = #{liotRmk6}</if>
            <if test="liotRmk7 != null  and liotRmk7 != ''"> and liot_rmk7 = #{liotRmk7}</if>
            <if test="liotFinFlag != null  and liotFinFlag != ''"> and liot_fin_flag = #{liotFinFlag}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="startDate != null">
                AND r_cre_dt >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                AND r_cre_dt &lt;= DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
            </if>
            <if test="liotTyps != null and liotTyps.size() > 0">
                AND liot_typ IN
                <foreach item="item" index="index" collection="liotTyps" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    
    <select id="selectLogInventoryIoTransactionById" parameterType="Long" resultMap="LogInventoryIoTransactionResult">
        <include refid="selectLogInventoryIoTransactionVo"/>
        where id = #{id}
    </select>

    <insert id="insertLogInventoryIoTransaction" parameterType="LogInventoryIoTransaction" useGeneratedKeys="true" keyProperty="id">
        insert into log_inventory_io_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="liotDmcd != null and liotDmcd != ''">liot_dmcd,</if>
            <if test="liotTcd != null and liotTcd != ''">liot_tcd,</if>
            <if test="liotSlcd != null and liotSlcd != ''">liot_slcd,</if>
            <if test="liotPpcd != null and liotPpcd != ''">liot_ppcd,</if>
            <if test="liotBn != null and liotBn != ''">liot_bn,</if>
            <if test="liotBnEncrypt != null">liot_bn_encrypt,</if>
            <if test="liotPbn != null">liot_pbn,</if>
            <if test="liotSbn != null">liot_sbn,</if>
            <if test="liotQty != null">liot_qty,</if>
            <if test="liotUnitp != null">liot_unitp,</if>
            <if test="liotBit != null">liot_bit,</if>
            <if test="liotIspart != null">liot_ispart,</if>
            <if test="liotTotalValue != null">liot_total_value,</if>
            <if test="liotOrgmcd != null">liot_orgmcd,</if>
            <if test="liotTyp != null">liot_typ,</if>
            <if test="liotRmk1 != null">liot_rmk1,</if>
            <if test="liotRmk2 != null">liot_rmk2,</if>
            <if test="liotRmk3 != null">liot_rmk3,</if>
            <if test="liotRmk4 != null">liot_rmk4,</if>
            <if test="liotRmk5 != null">liot_rmk5,</if>
            <if test="liotRmk6 != null">liot_rmk6,</if>
            <if test="liotRmk7 != null">liot_rmk7,</if>
            <if test="liotFinFlag != null">liot_fin_flag,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="liotDmcd != null and liotDmcd != ''">#{liotDmcd},</if>
            <if test="liotTcd != null and liotTcd != ''">#{liotTcd},</if>
            <if test="liotSlcd != null and liotSlcd != ''">#{liotSlcd},</if>
            <if test="liotPpcd != null and liotPpcd != ''">#{liotPpcd},</if>
            <if test="liotBn != null and liotBn != ''">#{liotBn},</if>
            <if test="liotBnEncrypt != null">#{liotBnEncrypt},</if>
            <if test="liotPbn != null">#{liotPbn},</if>
            <if test="liotSbn != null">#{liotSbn},</if>
            <if test="liotQty != null">#{liotQty},</if>
            <if test="liotUnitp != null">#{liotUnitp},</if>
            <if test="liotBit != null">#{liotBit},</if>
            <if test="liotIspart != null">#{liotIspart},</if>
            <if test="liotTotalValue != null">#{liotTotalValue},</if>
            <if test="liotOrgmcd != null">#{liotOrgmcd},</if>
            <if test="liotTyp != null">#{liotTyp},</if>
            <if test="liotRmk1 != null">#{liotRmk1},</if>
            <if test="liotRmk2 != null">#{liotRmk2},</if>
            <if test="liotRmk3 != null">#{liotRmk3},</if>
            <if test="liotRmk4 != null">#{liotRmk4},</if>
            <if test="liotRmk5 != null">#{liotRmk5},</if>
            <if test="liotRmk6 != null">#{liotRmk6},</if>
            <if test="liotRmk7 != null">#{liotRmk7},</if>
            <if test="liotFinFlag != null">#{liotFinFlag},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateLogInventoryIoTransaction" parameterType="LogInventoryIoTransaction">
        update log_inventory_io_transaction
        <trim prefix="SET" suffixOverrides=",">
            <if test="liotDmcd != null and liotDmcd != ''">liot_dmcd = #{liotDmcd},</if>
            <if test="liotTcd != null and liotTcd != ''">liot_tcd = #{liotTcd},</if>
            <if test="liotSlcd != null and liotSlcd != ''">liot_slcd = #{liotSlcd},</if>
            <if test="liotPpcd != null and liotPpcd != ''">liot_ppcd = #{liotPpcd},</if>
            <if test="liotBn != null and liotBn != ''">liot_bn = #{liotBn},</if>
            <if test="liotBnEncrypt != null">liot_bn_encrypt = #{liotBnEncrypt},</if>
            <if test="liotPbn != null">liot_pbn = #{liotPbn},</if>
            <if test="liotSbn != null">liot_sbn = #{liotSbn},</if>
            <if test="liotQty != null">liot_qty = #{liotQty},</if>
            <if test="liotUnitp != null">liot_unitp = #{liotUnitp},</if>
            <if test="liotBit != null">liot_bit = #{liotBit},</if>
            <if test="liotIspart != null">liot_ispart = #{liotIspart},</if>
            <if test="liotTotalValue != null">liot_total_value = #{liotTotalValue},</if>
            <if test="liotOrgmcd != null">liot_orgmcd = #{liotOrgmcd},</if>
            <if test="liotTyp != null">liot_typ = #{liotTyp},</if>
            <if test="liotRmk1 != null">liot_rmk1 = #{liotRmk1},</if>
            <if test="liotRmk2 != null">liot_rmk2 = #{liotRmk2},</if>
            <if test="liotRmk3 != null">liot_rmk3 = #{liotRmk3},</if>
            <if test="liotRmk4 != null">liot_rmk4 = #{liotRmk4},</if>
            <if test="liotRmk5 != null">liot_rmk5 = #{liotRmk5},</if>
            <if test="liotRmk6 != null">liot_rmk6 = #{liotRmk6},</if>
            <if test="liotRmk7 != null">liot_rmk7 = #{liotRmk7},</if>
            <if test="liotFinFlag != null">liot_fin_flag = #{liotFinFlag},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLogInventoryIoTransactionById" parameterType="Long">
        delete from log_inventory_io_transaction where id = #{id}
    </delete>

    <delete id="deleteLogInventoryIoTransactionByIds" parameterType="String">
        delete from log_inventory_io_transaction where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>