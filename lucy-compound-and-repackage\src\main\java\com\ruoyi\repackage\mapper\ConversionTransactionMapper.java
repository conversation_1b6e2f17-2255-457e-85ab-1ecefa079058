package com.ruoyi.repackage.mapper;

import java.util.List;
import com.ruoyi.repackage.domain.ConversionTransaction;

/**
 * 分装事务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface ConversionTransactionMapper 
{
    /**
     * 查询分装事务
     * 
     * @param transId 分装事务主键
     * @return 分装事务
     */
    public ConversionTransaction selectConversionTransactionByTransId(Long transId);

    /**
     * 查询分装事务列表
     * 
     * @param conversionTransaction 分装事务
     * @return 分装事务集合
     */
    public List<ConversionTransaction> selectConversionTransactionList(ConversionTransaction conversionTransaction);

    /**
     * 新增分装事务
     * 
     * @param conversionTransaction 分装事务
     * @return 结果
     */
    public int insertConversionTransaction(ConversionTransaction conversionTransaction);

    /**
     * 修改分装事务
     * 
     * @param conversionTransaction 分装事务
     * @return 结果
     */
    public int updateConversionTransaction(ConversionTransaction conversionTransaction);

    /**
     * 删除分装事务
     * 
     * @param transId 分装事务主键
     * @return 结果
     */
    public int deleteConversionTransactionByTransId(Long transId);

    /**
     * 批量删除分装事务
     * 
     * @param transIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConversionTransactionByTransIds(Long[] transIds);
}
