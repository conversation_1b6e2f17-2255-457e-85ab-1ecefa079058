package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.LogRepackResult;

/**
 * 产成品Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface LogRepackResultMapper 
{
    /**
     * 查询产成品
     * 
     * @param lrrCd 产成品主键
     * @return 产成品
     */
    public LogRepackResult selectLogRepackResultByLrrCd(String lrrCd);

    /**
     * 查询产成品列表
     * 
     * @param logRepackResult 产成品
     * @return 产成品集合
     */
    public List<LogRepackResult> selectLogRepackResultList(LogRepackResult logRepackResult);

    /**
     * 新增产成品
     * 
     * @param logRepackResult 产成品
     * @return 结果
     */
    public int insertLogRepackResult(LogRepackResult logRepackResult);

    /**
     * 修改产成品
     * 
     * @param logRepackResult 产成品
     * @return 结果
     */
    public int updateLogRepackResult(LogRepackResult logRepackResult);

    /**
     * 删除产成品
     * 
     * @param lrrCd 产成品主键
     * @return 结果
     */
    public int deleteLogRepackResultByLrrCd(String lrrCd);

    /**
     * 批量删除产成品
     * 
     * @param lrrCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLogRepackResultByLrrCds(String[] lrrCds);
}
