package com.lucy.skumaster.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * SKU主数据 SKU master data对象 sku_master
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public class SkuMaster extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** SKU编码, SKU code */
    private String code;

    /** 商品名称, Product name */
    @Excel(name = "商品名称, Product name")
    private String name;

    /** 计量单位, Unit of measure */
    @Excel(name = "计量单位, Unit of measure")
    private String uom;

    /** 库存类型(原料/在制品/成品), Inventory type */
    @Excel(name = "库存类型(原料/在制品/成品), Inventory type")
    private String inventoryType;

    /** 成本计价方式, Costing method */
    @Excel(name = "成本计价方式, Costing method")
    private String costMethod;

    /** 产品分类编号 */
    @Excel(name = "产品分类编号")
    private String bpmCcd;

    /** 产品分类 */
    @Excel(name = "产品分类")
    private String bpmCnm;

    /** 产品种类编号 */
    @Excel(name = "产品种类编号")
    private String bpmSort;

    /** 产品种类 */
    @Excel(name = "产品种类")
    private String bpmSortnm;

    /** 品牌编号 */
    @Excel(name = "品牌编号")
    private String bpmPcd;

    /** 品牌名称 */
    @Excel(name = "品牌名称")
    private String bpmPnm;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String bpmCd;

    /** 货号 */
    @Excel(name = "货号")
    private String bpmOrgcd;

    /** 品名 */
    @Excel(name = "品名")
    private String bpmNm;

    /** 包装 */
    @Excel(name = "包装")
    private String bppPack;

    /** 标准重量kg/L */
    @Excel(name = "标准重量kg/L")
    private BigDecimal bppStdQty;

    /** CAS */
    @Excel(name = "CAS")
    private String bpacCas;

    /** 国危2022版 */
    @Excel(name = "国危2022版")
    private String bpacDgcls;

    /** 产线分类 */
    @Excel(name = "产线分类")
    private String bptNm;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String fpcCode;

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setUom(String uom) 
    {
        this.uom = uom;
    }

    public String getUom() 
    {
        return uom;
    }

    public void setInventoryType(String inventoryType) 
    {
        this.inventoryType = inventoryType;
    }

    public String getInventoryType() 
    {
        return inventoryType;
    }

    public void setCostMethod(String costMethod) 
    {
        this.costMethod = costMethod;
    }

    public String getCostMethod() 
    {
        return costMethod;
    }

    public void setBpmCcd(String bpmCcd) 
    {
        this.bpmCcd = bpmCcd;
    }

    public String getBpmCcd() 
    {
        return bpmCcd;
    }

    public void setBpmCnm(String bpmCnm) 
    {
        this.bpmCnm = bpmCnm;
    }

    public String getBpmCnm() 
    {
        return bpmCnm;
    }

    public void setBpmSort(String bpmSort) 
    {
        this.bpmSort = bpmSort;
    }

    public String getBpmSort() 
    {
        return bpmSort;
    }

    public void setBpmSortnm(String bpmSortnm) 
    {
        this.bpmSortnm = bpmSortnm;
    }

    public String getBpmSortnm() 
    {
        return bpmSortnm;
    }

    public void setBpmPcd(String bpmPcd) 
    {
        this.bpmPcd = bpmPcd;
    }

    public String getBpmPcd() 
    {
        return bpmPcd;
    }

    public void setBpmPnm(String bpmPnm) 
    {
        this.bpmPnm = bpmPnm;
    }

    public String getBpmPnm() 
    {
        return bpmPnm;
    }

    public void setBpmCd(String bpmCd) 
    {
        this.bpmCd = bpmCd;
    }

    public String getBpmCd() 
    {
        return bpmCd;
    }

    public void setBpmOrgcd(String bpmOrgcd) 
    {
        this.bpmOrgcd = bpmOrgcd;
    }

    public String getBpmOrgcd() 
    {
        return bpmOrgcd;
    }

    public void setBpmNm(String bpmNm) 
    {
        this.bpmNm = bpmNm;
    }

    public String getBpmNm() 
    {
        return bpmNm;
    }

    public void setBppPack(String bppPack) 
    {
        this.bppPack = bppPack;
    }

    public String getBppPack() 
    {
        return bppPack;
    }

    public void setBppStdQty(BigDecimal bppStdQty) 
    {
        this.bppStdQty = bppStdQty;
    }

    public BigDecimal getBppStdQty() 
    {
        return bppStdQty;
    }

    public void setBpacCas(String bpacCas) 
    {
        this.bpacCas = bpacCas;
    }

    public String getBpacCas() 
    {
        return bpacCas;
    }

    public void setBpacDgcls(String bpacDgcls) 
    {
        this.bpacDgcls = bpacDgcls;
    }

    public String getBpacDgcls() 
    {
        return bpacDgcls;
    }

    public void setBptNm(String bptNm) 
    {
        this.bptNm = bptNm;
    }

    public String getBptNm() 
    {
        return bptNm;
    }

    public void setFpcCode(String fpcCode) 
    {
        this.fpcCode = fpcCode;
    }

    public String getFpcCode() 
    {
        return fpcCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("name", getName())
            .append("uom", getUom())
            .append("inventoryType", getInventoryType())
            .append("costMethod", getCostMethod())
            .append("bpmCcd", getBpmCcd())
            .append("bpmCnm", getBpmCnm())
            .append("bpmSort", getBpmSort())
            .append("bpmSortnm", getBpmSortnm())
            .append("bpmPcd", getBpmPcd())
            .append("bpmPnm", getBpmPnm())
            .append("bpmCd", getBpmCd())
            .append("bpmOrgcd", getBpmOrgcd())
            .append("bpmNm", getBpmNm())
            .append("bppPack", getBppPack())
            .append("bppStdQty", getBppStdQty())
            .append("bpacCas", getBpacCas())
            .append("bpacDgcls", getBpacDgcls())
            .append("bptNm", getBptNm())
            .append("fpcCode", getFpcCode())
            .toString();
    }
}
