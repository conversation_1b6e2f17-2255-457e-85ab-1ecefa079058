package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.LabPickingDtl;

/**
 * 研发领用明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface LabPickingDtlMapper 
{
    /**
     * 查询研发领用明细
     * 
     * @param lpdCd 研发领用明细主键
     * @return 研发领用明细
     */
    public LabPickingDtl selectLabPickingDtlByLpdCd(String lpdCd);

    /**
     * 查询研发领用明细列表
     * 
     * @param labPickingDtl 研发领用明细
     * @return 研发领用明细集合
     */
    public List<LabPickingDtl> selectLabPickingDtlList(LabPickingDtl labPickingDtl);

    /**
     * 新增研发领用明细
     * 
     * @param labPickingDtl 研发领用明细
     * @return 结果
     */
    public int insertLabPickingDtl(LabPickingDtl labPickingDtl);

    /**
     * 修改研发领用明细
     * 
     * @param labPickingDtl 研发领用明细
     * @return 结果
     */
    public int updateLabPickingDtl(LabPickingDtl labPickingDtl);

    /**
     * 删除研发领用明细
     * 
     * @param lpdCd 研发领用明细主键
     * @return 结果
     */
    public int deleteLabPickingDtlByLpdCd(String lpdCd);

    /**
     * 批量删除研发领用明细
     * 
     * @param lpdCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLabPickingDtlByLpdCds(String[] lpdCds);
}
