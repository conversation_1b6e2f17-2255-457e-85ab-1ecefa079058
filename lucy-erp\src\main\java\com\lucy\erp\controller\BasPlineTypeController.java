package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasPlineType;
import com.lucy.erp.service.IBasPlineTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产线字典Controller
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/prod/type")
public class BasPlineTypeController extends BaseController
{
    @Autowired
    private IBasPlineTypeService basPlineTypeService;

    /**
     * 查询产线字典列表
     */
    @PreAuthorize("@ss.hasPermi('prod:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasPlineType basPlineType)
    {
        startPage();
        List<BasPlineType> list = basPlineTypeService.selectBasPlineTypeList(basPlineType);
        return getDataTable(list);
    }

    /**
     * 导出产线字典列表
     */
    @PreAuthorize("@ss.hasPermi('prod:type:export')")
    @Log(title = "产线字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasPlineType basPlineType)
    {
        List<BasPlineType> list = basPlineTypeService.selectBasPlineTypeList(basPlineType);
        ExcelUtil<BasPlineType> util = new ExcelUtil<BasPlineType>(BasPlineType.class);
        util.exportExcel(response, list, "产线字典数据");
    }

    /**
     * 获取产线字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('prod:type:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(basPlineTypeService.selectBasPlineTypeById(id));
    }

    /**
     * 新增产线字典
     */
    @PreAuthorize("@ss.hasPermi('prod:type:add')")
    @Log(title = "产线字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasPlineType basPlineType)
    {
        return toAjax(basPlineTypeService.insertBasPlineType(basPlineType));
    }

    /**
     * 修改产线字典
     */
    @PreAuthorize("@ss.hasPermi('prod:type:edit')")
    @Log(title = "产线字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasPlineType basPlineType)
    {
        return toAjax(basPlineTypeService.updateBasPlineType(basPlineType));
    }

    /**
     * 删除产线字典
     */
    @PreAuthorize("@ss.hasPermi('prod:type:remove')")
    @Log(title = "产线字典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(basPlineTypeService.deleteBasPlineTypeByIds(ids));
    }
}
