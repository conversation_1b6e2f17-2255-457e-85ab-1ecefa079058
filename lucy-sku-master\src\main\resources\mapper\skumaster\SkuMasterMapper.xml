<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.skumaster.mapper.SkuMasterMapper">
    
    <resultMap type="SkuMaster" id="SkuMasterResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="uom"    column="uom"    />
        <result property="inventoryType"    column="inventory_type"    />
        <result property="costMethod"    column="cost_method"    />
        <result property="bpmCcd"    column="bpm_ccd"    />
        <result property="bpmCnm"    column="bpm_cnm"    />
        <result property="bpmSort"    column="bpm_sort"    />
        <result property="bpmSortnm"    column="bpm_sortnm"    />
        <result property="bpmPcd"    column="bpm_pcd"    />
        <result property="bpmPnm"    column="bpm_pnm"    />
        <result property="bpmCd"    column="bpm_cd"    />
        <result property="bpmOrgcd"    column="bpm_orgcd"    />
        <result property="bpmNm"    column="bpm_nm"    />
        <result property="bppPack"    column="bpp_pack"    />
        <result property="bppStdQty"    column="bpp_std_qty"    />
        <result property="bpacCas"    column="bpac_cas"    />
        <result property="bpacDgcls"    column="bpac_dgcls"    />
        <result property="bptNm"    column="bpt_nm"    />
        <result property="fpcCode"    column="fpc_code"    />
    </resultMap>

    <sql id="selectSkuMasterVo">
        select code, name, uom, inventory_type, cost_method, bpm_ccd, bpm_cnm, bpm_sort, bpm_sortnm, bpm_pcd, bpm_pnm, bpm_cd, bpm_orgcd, bpm_nm, bpp_pack, bpp_std_qty, bpac_cas, bpac_dgcls, bpt_nm, fpc_code from sku_master
    </sql>

    <select id="selectSkuMasterList" parameterType="SkuMaster" resultMap="SkuMasterResult">
        <include refid="selectSkuMasterVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="uom != null  and uom != ''"> and uom = #{uom}</if>
            <if test="inventoryType != null  and inventoryType != ''"> and inventory_type = #{inventoryType}</if>
            <if test="costMethod != null  and costMethod != ''"> and cost_method = #{costMethod}</if>
            <if test="bpmCcd != null  and bpmCcd != ''"> and bpm_ccd = #{bpmCcd}</if>
            <if test="bpmCnm != null  and bpmCnm != ''"> and bpm_cnm = #{bpmCnm}</if>
            <if test="bpmSort != null  and bpmSort != ''"> and bpm_sort = #{bpmSort}</if>
            <if test="bpmSortnm != null  and bpmSortnm != ''"> and bpm_sortnm = #{bpmSortnm}</if>
            <if test="bpmPcd != null  and bpmPcd != ''"> and bpm_pcd = #{bpmPcd}</if>
            <if test="bpmPnm != null  and bpmPnm != ''"> and bpm_pnm = #{bpmPnm}</if>
            <if test="bpmCd != null  and bpmCd != ''"> and bpm_cd = #{bpmCd}</if>
            <if test="bpmOrgcd != null  and bpmOrgcd != ''"> and bpm_orgcd = #{bpmOrgcd}</if>
            <if test="bpmNm != null  and bpmNm != ''"> and bpm_nm = #{bpmNm}</if>
            <if test="bppPack != null  and bppPack != ''"> and bpp_pack = #{bppPack}</if>
            <if test="bppStdQty != null "> and bpp_std_qty = #{bppStdQty}</if>
            <if test="bpacCas != null  and bpacCas != ''"> and bpac_cas = #{bpacCas}</if>
            <if test="bpacDgcls != null  and bpacDgcls != ''"> and bpac_dgcls = #{bpacDgcls}</if>
            <if test="bptNm != null  and bptNm != ''"> and bpt_nm = #{bptNm}</if>
            <if test="fpcCode != null  and fpcCode != ''"> and fpc_code = #{fpcCode}</if>
        </where>
    </select>
    
    <select id="selectSkuMasterByCode" parameterType="String" resultMap="SkuMasterResult">
        <include refid="selectSkuMasterVo"/>
        where code = #{code}
    </select>

    <insert id="insertSkuMaster" parameterType="SkuMaster">
        insert into sku_master
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="uom != null and uom != ''">uom,</if>
            <if test="inventoryType != null">inventory_type,</if>
            <if test="costMethod != null">cost_method,</if>
            <if test="bpmCcd != null">bpm_ccd,</if>
            <if test="bpmCnm != null">bpm_cnm,</if>
            <if test="bpmSort != null">bpm_sort,</if>
            <if test="bpmSortnm != null">bpm_sortnm,</if>
            <if test="bpmPcd != null">bpm_pcd,</if>
            <if test="bpmPnm != null">bpm_pnm,</if>
            <if test="bpmCd != null">bpm_cd,</if>
            <if test="bpmOrgcd != null">bpm_orgcd,</if>
            <if test="bpmNm != null">bpm_nm,</if>
            <if test="bppPack != null">bpp_pack,</if>
            <if test="bppStdQty != null">bpp_std_qty,</if>
            <if test="bpacCas != null">bpac_cas,</if>
            <if test="bpacDgcls != null">bpac_dgcls,</if>
            <if test="bptNm != null">bpt_nm,</if>
            <if test="fpcCode != null">fpc_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="uom != null and uom != ''">#{uom},</if>
            <if test="inventoryType != null">#{inventoryType},</if>
            <if test="costMethod != null">#{costMethod},</if>
            <if test="bpmCcd != null">#{bpmCcd},</if>
            <if test="bpmCnm != null">#{bpmCnm},</if>
            <if test="bpmSort != null">#{bpmSort},</if>
            <if test="bpmSortnm != null">#{bpmSortnm},</if>
            <if test="bpmPcd != null">#{bpmPcd},</if>
            <if test="bpmPnm != null">#{bpmPnm},</if>
            <if test="bpmCd != null">#{bpmCd},</if>
            <if test="bpmOrgcd != null">#{bpmOrgcd},</if>
            <if test="bpmNm != null">#{bpmNm},</if>
            <if test="bppPack != null">#{bppPack},</if>
            <if test="bppStdQty != null">#{bppStdQty},</if>
            <if test="bpacCas != null">#{bpacCas},</if>
            <if test="bpacDgcls != null">#{bpacDgcls},</if>
            <if test="bptNm != null">#{bptNm},</if>
            <if test="fpcCode != null">#{fpcCode},</if>
         </trim>
    </insert>

    <update id="updateSkuMaster" parameterType="SkuMaster">
        update sku_master
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="uom != null and uom != ''">uom = #{uom},</if>
            <if test="inventoryType != null">inventory_type = #{inventoryType},</if>
            <if test="costMethod != null">cost_method = #{costMethod},</if>
            <if test="bpmCcd != null">bpm_ccd = #{bpmCcd},</if>
            <if test="bpmCnm != null">bpm_cnm = #{bpmCnm},</if>
            <if test="bpmSort != null">bpm_sort = #{bpmSort},</if>
            <if test="bpmSortnm != null">bpm_sortnm = #{bpmSortnm},</if>
            <if test="bpmPcd != null">bpm_pcd = #{bpmPcd},</if>
            <if test="bpmPnm != null">bpm_pnm = #{bpmPnm},</if>
            <if test="bpmCd != null">bpm_cd = #{bpmCd},</if>
            <if test="bpmOrgcd != null">bpm_orgcd = #{bpmOrgcd},</if>
            <if test="bpmNm != null">bpm_nm = #{bpmNm},</if>
            <if test="bppPack != null">bpp_pack = #{bppPack},</if>
            <if test="bppStdQty != null">bpp_std_qty = #{bppStdQty},</if>
            <if test="bpacCas != null">bpac_cas = #{bpacCas},</if>
            <if test="bpacDgcls != null">bpac_dgcls = #{bpacDgcls},</if>
            <if test="bptNm != null">bpt_nm = #{bptNm},</if>
            <if test="fpcCode != null">fpc_code = #{fpcCode},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteSkuMasterByCode" parameterType="String">
        delete from sku_master where code = #{code}
    </delete>

    <delete id="deleteSkuMasterByCodes" parameterType="String">
        delete from sku_master where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>