package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.FinAccAvgCostMapper;
import com.lucy.erp.domain.FinAccAvgCost;
import com.lucy.erp.service.IFinAccAvgCostService;

/**
 * 财务_平均_费Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
@DataSource(DataSourceType.ERP)
public class FinAccAvgCostServiceImpl implements IFinAccAvgCostService 
{
    @Autowired
    private FinAccAvgCostMapper finAccAvgCostMapper;

    /**
     * 查询财务_平均_费
     * 
     * @param finAccAvgCost 财务_平均_费
     * @return 财务_平均_费
     */
    @Override
    public FinAccAvgCost selectFinAccAvgCostByFaacId(FinAccAvgCost finAccAvgCost)
    {
        return finAccAvgCostMapper.selectFinAccAvgCostByFaacId(finAccAvgCost);
    }

    /**
     * 查询财务_平均_费列表
     * 
     * @param finAccAvgCost 财务_平均_费
     * @return 财务_平均_费
     */
    @Override
    public List<FinAccAvgCost> selectFinAccAvgCostList(FinAccAvgCost finAccAvgCost)
    {
        return finAccAvgCostMapper.selectFinAccAvgCostList(finAccAvgCost);
    }

    /**
     * 新增财务_平均_费
     * 
     * @param finAccAvgCost 财务_平均_费
     * @return 结果
     */
    @Override
    public int insertFinAccAvgCost(FinAccAvgCost finAccAvgCost)
    {
        return finAccAvgCostMapper.insertFinAccAvgCost(finAccAvgCost);
    }

    /**
     * 修改财务_平均_费
     * 
     * @param finAccAvgCost 财务_平均_费
     * @return 结果
     */
    @Override
    public int updateFinAccAvgCost(FinAccAvgCost finAccAvgCost)
    {
        return finAccAvgCostMapper.updateFinAccAvgCost(finAccAvgCost);
    }

    /**
     * 批量删除财务_平均_费
     * 
     * @param faacIds 需要删除的财务_平均_费主键
     * @return 结果
     */
    @Override
    public int deleteFinAccAvgCostByFaacIds(String[] faacIds)
    {
        return finAccAvgCostMapper.deleteFinAccAvgCostByFaacIds(faacIds);
    }

    /**
     * 删除财务_平均_费信息
     * 
     * @param faacId 财务_平均_费主键
     * @return 结果
     */
    @Override
    public int deleteFinAccAvgCostByFaacId(String faacId)
    {
        return finAccAvgCostMapper.deleteFinAccAvgCostByFaacId(faacId);
    }
}
