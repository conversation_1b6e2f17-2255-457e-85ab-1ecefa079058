<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.SalesContractMstMapper">
    
    <resultMap type="SalesContractMst" id="SalesContractMstResult">
        <result property="id"    column="id"    />
        <result property="scmCd"    column="scm_cd"    />
        <result property="scmCdold"    column="scm_cdold"    />
        <result property="scmTyp"    column="scm_typ"    />
        <result property="scmOrdtyp"    column="scm_ordtyp"    />
        <result property="scmPrinttyp"    column="scm_printtyp"    />
        <result property="scmScd"    column="scm_scd"    />
        <result property="scmPaytyp"    column="scm_paytyp"    />
        <result property="scmYn"    column="scm_yn"    />
        <result property="scmIsod"    column="scm_isod"    />
        <result property="scmDt"    column="scm_dt"    />
        <result property="scmInvcTyp"    column="scm_invc_typ"    />
        <result property="scmPpacd"    column="scm_ppacd"    />
        <result property="scmPocpcd"    column="scm_pocpcd"    />
        <result property="scmCocpcd"    column="scm_cocpcd"    />
        <result property="scmPrcocd"    column="scm_prcocd"    />
        <result property="scmFrcocd"    column="scm_frcocd"    />
        <result property="scmCustid"    column="scm_custid"    />
        <result property="scmCustcd"    column="scm_custcd"    />
        <result property="scmCustnm"    column="scm_custnm"    />
        <result property="scmCusttel"    column="scm_custtel"    />
        <result property="scmCustfax"    column="scm_custfax"    />
        <result property="scmEmail"    column="scm_email"    />
        <result property="scmOcpcd"    column="scm_ocpcd"    />
        <result property="scmOcd"    column="scm_ocd"    />
        <result property="scmSmid"    column="scm_smid"    />
        <result property="scmSmcd"    column="scm_smcd"    />
        <result property="scmSmnm"    column="scm_smnm"    />
        <result property="scmOuid"    column="scm_ouid"    />
        <result property="scmOucd"    column="scm_oucd"    />
        <result property="scmAmt"    column="scm_amt"    />
        <result property="scmPreamt"    column="scm_preamt"    />
        <result property="scmPnt"    column="scm_pnt"    />
        <result property="scmGold"    column="scm_gold"    />
        <result property="scmPntUsed"    column="scm_pnt_used"    />
        <result property="scmGoldUsed"    column="scm_gold_used"    />
        <result property="scmPntYn"    column="scm_pnt_yn"    />
        <result property="scmStscd"    column="scm_stscd"    />
        <result property="scmStsnm"    column="scm_stsnm"    />
        <result property="scmRmk"    column="scm_rmk"    />
        <result property="scmShdt"    column="scm_shdt"    />
        <result property="scmIcPicurl"    column="scm_ic_picurl"    />
        <result property="scmUseVerify"    column="scm_use_verify"    />
        <result property="scmLicence"    column="scm_licence"    />
        <result property="scmInvoicePdf"    column="scm_invoice_pdf"    />
        <result property="scmSalesPdf"    column="scm_sales_pdf"    />
        <result property="scmSalesCard"    column="scm_sales_card"    />
        <result property="scmAlzsVerify"    column="scm_alzs_verify"    />
        <result property="scmYzbzmPdf"    column="scm_yzbzm_pdf"    />
        <result property="scmInvcrmk"    column="scm_invcrmk"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="scmKp"    column="scm_kp"    />
        <result property="scmSptyp"    column="scm_sptyp"    />
        <result property="scmPush"    column="scm_push"    />
        <result property="scmBarcode"    column="scm_barcode"    />
        <result property="scmPaytime"    column="scm_paytime"    />
        <result property="scmCallback"    column="scm_callback"    />
        <result property="scmNationalType"    column="scm_national_type"    />
        <result property="scmCurrency"    column="scm_currency"    />
        <result property="scmVmi"    column="scm_vmi"    />
        <result property="scmJd"    column="scm_jd"    />
        <result property="scmOpcd"    column="scm_opcd"    />
        <result property="scmVmicd"    column="scm_vmicd"    />
        <result property="scmExportDmcd"    column="scm_export_dmcd"    />
        <result property="scmQumethod"    column="scm_quMethod"    />
        <result property="scmPono"    column="scm_PoNo"    />
        <result property="scmWhpType"    column="scm_whp_type"    />
        <result property="scmPocpnm"    column="scm_pocpnm"    />
        <result property="scmFrconm"    column="scm_frconm"    />
        <result property="scmOsktype"    column="scm_osktype"    />
        <result property="scmFos"    column="scm_fos"    />
    </resultMap>

    <sql id="selectSalesContractMstVo">
        select id, scm_cd, scm_cdold, scm_typ, scm_ordtyp, scm_printtyp, scm_scd, scm_paytyp, scm_yn, scm_isod, scm_dt, scm_invc_typ, scm_ppacd, scm_pocpcd, scm_cocpcd, scm_prcocd, scm_frcocd, scm_custid, scm_custcd, scm_custnm, scm_custtel, scm_custfax, scm_email, scm_ocpcd, scm_ocd, scm_smid, scm_smcd, scm_smnm, scm_ouid, scm_oucd, scm_amt, scm_preamt, scm_pnt, scm_gold, scm_pnt_used, scm_gold_used, scm_pnt_yn, scm_stscd, scm_stsnm, scm_rmk, scm_shdt, scm_ic_picurl, scm_use_verify, scm_licence, scm_invoice_pdf, scm_sales_pdf, scm_sales_card, scm_alzs_verify, scm_yzbzm_pdf, scm_invcrmk, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, scm_kp, scm_sptyp, scm_push, scm_barcode, scm_paytime, scm_callback, scm_national_type, scm_currency, scm_vmi, scm_jd, scm_opcd, scm_vmicd, scm_export_dmcd, scm_quMethod, scm_PoNo, scm_whp_type, scm_pocpnm, scm_frconm, scm_osktype, scm_fos from sales_contract_mst
    </sql>

    <select id="selectSalesContractMstList" parameterType="SalesContractMst" resultMap="SalesContractMstResult">
        <include refid="selectSalesContractMstVo"/>
        <where>  
            <if test="scmCdold != null  and scmCdold != ''"> and scm_cdold = #{scmCdold}</if>
            <if test="scmTyp != null  and scmTyp != ''"> and scm_typ = #{scmTyp}</if>
            <if test="scmOrdtyp != null  and scmOrdtyp != ''"> and scm_ordtyp = #{scmOrdtyp}</if>
            <if test="scmPrinttyp != null  and scmPrinttyp != ''"> and scm_printtyp = #{scmPrinttyp}</if>
            <if test="scmScd != null  and scmScd != ''"> and scm_scd = #{scmScd}</if>
            <if test="scmPaytyp != null  and scmPaytyp != ''"> and scm_paytyp = #{scmPaytyp}</if>
            <if test="scmYn != null  and scmYn != ''"> and scm_yn = #{scmYn}</if>
            <if test="scmIsod != null  and scmIsod != ''"> and scm_isod = #{scmIsod}</if>
            <if test="scmDt != null  and scmDt != ''"> and scm_dt = #{scmDt}</if>
            <if test="scmInvcTyp != null  and scmInvcTyp != ''"> and scm_invc_typ = #{scmInvcTyp}</if>
            <if test="scmPpacd != null  and scmPpacd != ''"> and scm_ppacd = #{scmPpacd}</if>
            <if test="scmPocpcd != null  and scmPocpcd != ''"> and scm_pocpcd = #{scmPocpcd}</if>
            <if test="scmCocpcd != null  and scmCocpcd != ''"> and scm_cocpcd = #{scmCocpcd}</if>
            <if test="scmPrcocd != null  and scmPrcocd != ''"> and scm_prcocd = #{scmPrcocd}</if>
            <if test="scmFrcocd != null  and scmFrcocd != ''"> and scm_frcocd = #{scmFrcocd}</if>
            <if test="scmCustid != null "> and scm_custid = #{scmCustid}</if>
            <if test="scmCustcd != null  and scmCustcd != ''"> and scm_custcd = #{scmCustcd}</if>
            <if test="scmCustnm != null  and scmCustnm != ''"> and scm_custnm = #{scmCustnm}</if>
            <if test="scmCusttel != null  and scmCusttel != ''"> and scm_custtel = #{scmCusttel}</if>
            <if test="scmCustfax != null  and scmCustfax != ''"> and scm_custfax = #{scmCustfax}</if>
            <if test="scmEmail != null  and scmEmail != ''"> and scm_email = #{scmEmail}</if>
            <if test="scmOcpcd != null  and scmOcpcd != ''"> and scm_ocpcd = #{scmOcpcd}</if>
            <if test="scmOcd != null  and scmOcd != ''"> and scm_ocd = #{scmOcd}</if>
            <if test="scmSmid != null "> and scm_smid = #{scmSmid}</if>
            <if test="scmSmcd != null  and scmSmcd != ''"> and scm_smcd = #{scmSmcd}</if>
            <if test="scmSmnm != null  and scmSmnm != ''"> and scm_smnm = #{scmSmnm}</if>
            <if test="scmOuid != null "> and scm_ouid = #{scmOuid}</if>
            <if test="scmOucd != null  and scmOucd != ''"> and scm_oucd = #{scmOucd}</if>
            <if test="scmAmt != null "> and scm_amt = #{scmAmt}</if>
            <if test="scmPreamt != null "> and scm_preamt = #{scmPreamt}</if>
            <if test="scmPnt != null "> and scm_pnt = #{scmPnt}</if>
            <if test="scmGold != null "> and scm_gold = #{scmGold}</if>
            <if test="scmPntUsed != null "> and scm_pnt_used = #{scmPntUsed}</if>
            <if test="scmGoldUsed != null "> and scm_gold_used = #{scmGoldUsed}</if>
            <if test="scmPntYn != null  and scmPntYn != ''"> and scm_pnt_yn = #{scmPntYn}</if>
            <if test="scmStscd != null  and scmStscd != ''"> and scm_stscd = #{scmStscd}</if>
            <if test="scmStsnm != null  and scmStsnm != ''"> and scm_stsnm = #{scmStsnm}</if>
            <if test="scmRmk != null  and scmRmk != ''"> and scm_rmk = #{scmRmk}</if>
            <if test="scmShdt != null "> and scm_shdt = #{scmShdt}</if>
            <if test="scmIcPicurl != null  and scmIcPicurl != ''"> and scm_ic_picurl = #{scmIcPicurl}</if>
            <if test="scmUseVerify != null  and scmUseVerify != ''"> and scm_use_verify = #{scmUseVerify}</if>
            <if test="scmLicence != null  and scmLicence != ''"> and scm_licence = #{scmLicence}</if>
            <if test="scmInvoicePdf != null  and scmInvoicePdf != ''"> and scm_invoice_pdf = #{scmInvoicePdf}</if>
            <if test="scmSalesPdf != null  and scmSalesPdf != ''"> and scm_sales_pdf = #{scmSalesPdf}</if>
            <if test="scmSalesCard != null  and scmSalesCard != ''"> and scm_sales_card = #{scmSalesCard}</if>
            <if test="scmAlzsVerify != null  and scmAlzsVerify != ''"> and scm_alzs_verify = #{scmAlzsVerify}</if>
            <if test="scmYzbzmPdf != null  and scmYzbzmPdf != ''"> and scm_yzbzm_pdf = #{scmYzbzmPdf}</if>
            <if test="scmInvcrmk != null  and scmInvcrmk != ''"> and scm_invcrmk = #{scmInvcrmk}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="scmKp != null  and scmKp != ''"> and scm_kp = #{scmKp}</if>
            <if test="scmSptyp != null  and scmSptyp != ''"> and scm_sptyp = #{scmSptyp}</if>
            <if test="scmPush != null  and scmPush != ''"> and scm_push = #{scmPush}</if>
            <if test="scmBarcode != null  and scmBarcode != ''"> and scm_barcode = #{scmBarcode}</if>
            <if test="scmPaytime != null "> and scm_paytime = #{scmPaytime}</if>
            <if test="scmCallback != null  and scmCallback != ''"> and scm_callback = #{scmCallback}</if>
            <if test="scmNationalType != null  and scmNationalType != ''"> and scm_national_type = #{scmNationalType}</if>
            <if test="scmCurrency != null  and scmCurrency != ''"> and scm_currency = #{scmCurrency}</if>
            <if test="scmVmi != null  and scmVmi != ''"> and scm_vmi = #{scmVmi}</if>
            <if test="scmJd != null  and scmJd != ''"> and scm_jd = #{scmJd}</if>
            <if test="scmOpcd != null  and scmOpcd != ''"> and scm_opcd = #{scmOpcd}</if>
            <if test="scmVmicd != null  and scmVmicd != ''"> and scm_vmicd = #{scmVmicd}</if>
            <if test="scmExportDmcd != null  and scmExportDmcd != ''"> and scm_export_dmcd = #{scmExportDmcd}</if>
            <if test="scmQumethod != null  and scmQumethod != ''"> and scm_quMethod = #{scmQumethod}</if>
            <if test="scmPono != null  and scmPono != ''"> and scm_PoNo = #{scmPono}</if>
            <if test="scmWhpType != null  and scmWhpType != ''"> and scm_whp_type = #{scmWhpType}</if>
            <if test="scmPocpnm != null  and scmPocpnm != ''"> and scm_pocpnm = #{scmPocpnm}</if>
            <if test="scmFrconm != null  and scmFrconm != ''"> and scm_frconm = #{scmFrconm}</if>
            <if test="scmOsktype != null  and scmOsktype != ''"> and scm_osktype = #{scmOsktype}</if>
            <if test="scmFos != null  and scmFos != ''"> and scm_fos = #{scmFos}</if>
        </where>
    </select>
    
    <select id="selectSalesContractMstByScmCd" parameterType="String" resultMap="SalesContractMstResult">
        <include refid="selectSalesContractMstVo"/>
        where scm_cd = #{scmCd}
    </select>

    <insert id="insertSalesContractMst" parameterType="SalesContractMst">
        insert into sales_contract_mst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="scmCd != null">scm_cd,</if>
            <if test="scmCdold != null">scm_cdold,</if>
            <if test="scmTyp != null">scm_typ,</if>
            <if test="scmOrdtyp != null">scm_ordtyp,</if>
            <if test="scmPrinttyp != null">scm_printtyp,</if>
            <if test="scmScd != null">scm_scd,</if>
            <if test="scmPaytyp != null">scm_paytyp,</if>
            <if test="scmYn != null">scm_yn,</if>
            <if test="scmIsod != null">scm_isod,</if>
            <if test="scmDt != null">scm_dt,</if>
            <if test="scmInvcTyp != null">scm_invc_typ,</if>
            <if test="scmPpacd != null">scm_ppacd,</if>
            <if test="scmPocpcd != null">scm_pocpcd,</if>
            <if test="scmCocpcd != null">scm_cocpcd,</if>
            <if test="scmPrcocd != null">scm_prcocd,</if>
            <if test="scmFrcocd != null">scm_frcocd,</if>
            <if test="scmCustid != null">scm_custid,</if>
            <if test="scmCustcd != null">scm_custcd,</if>
            <if test="scmCustnm != null">scm_custnm,</if>
            <if test="scmCusttel != null">scm_custtel,</if>
            <if test="scmCustfax != null">scm_custfax,</if>
            <if test="scmEmail != null">scm_email,</if>
            <if test="scmOcpcd != null">scm_ocpcd,</if>
            <if test="scmOcd != null">scm_ocd,</if>
            <if test="scmSmid != null">scm_smid,</if>
            <if test="scmSmcd != null">scm_smcd,</if>
            <if test="scmSmnm != null">scm_smnm,</if>
            <if test="scmOuid != null">scm_ouid,</if>
            <if test="scmOucd != null">scm_oucd,</if>
            <if test="scmAmt != null">scm_amt,</if>
            <if test="scmPreamt != null">scm_preamt,</if>
            <if test="scmPnt != null">scm_pnt,</if>
            <if test="scmGold != null">scm_gold,</if>
            <if test="scmPntUsed != null">scm_pnt_used,</if>
            <if test="scmGoldUsed != null">scm_gold_used,</if>
            <if test="scmPntYn != null">scm_pnt_yn,</if>
            <if test="scmStscd != null">scm_stscd,</if>
            <if test="scmStsnm != null">scm_stsnm,</if>
            <if test="scmRmk != null">scm_rmk,</if>
            <if test="scmShdt != null">scm_shdt,</if>
            <if test="scmIcPicurl != null">scm_ic_picurl,</if>
            <if test="scmUseVerify != null">scm_use_verify,</if>
            <if test="scmLicence != null">scm_licence,</if>
            <if test="scmInvoicePdf != null">scm_invoice_pdf,</if>
            <if test="scmSalesPdf != null">scm_sales_pdf,</if>
            <if test="scmSalesCard != null">scm_sales_card,</if>
            <if test="scmAlzsVerify != null">scm_alzs_verify,</if>
            <if test="scmYzbzmPdf != null">scm_yzbzm_pdf,</if>
            <if test="scmInvcrmk != null">scm_invcrmk,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="scmKp != null">scm_kp,</if>
            <if test="scmSptyp != null">scm_sptyp,</if>
            <if test="scmPush != null">scm_push,</if>
            <if test="scmBarcode != null">scm_barcode,</if>
            <if test="scmPaytime != null">scm_paytime,</if>
            <if test="scmCallback != null">scm_callback,</if>
            <if test="scmNationalType != null">scm_national_type,</if>
            <if test="scmCurrency != null">scm_currency,</if>
            <if test="scmVmi != null">scm_vmi,</if>
            <if test="scmJd != null">scm_jd,</if>
            <if test="scmOpcd != null">scm_opcd,</if>
            <if test="scmVmicd != null">scm_vmicd,</if>
            <if test="scmExportDmcd != null">scm_export_dmcd,</if>
            <if test="scmQumethod != null">scm_quMethod,</if>
            <if test="scmPono != null">scm_PoNo,</if>
            <if test="scmWhpType != null">scm_whp_type,</if>
            <if test="scmPocpnm != null">scm_pocpnm,</if>
            <if test="scmFrconm != null">scm_frconm,</if>
            <if test="scmOsktype != null">scm_osktype,</if>
            <if test="scmFos != null">scm_fos,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="scmCd != null">#{scmCd},</if>
            <if test="scmCdold != null">#{scmCdold},</if>
            <if test="scmTyp != null">#{scmTyp},</if>
            <if test="scmOrdtyp != null">#{scmOrdtyp},</if>
            <if test="scmPrinttyp != null">#{scmPrinttyp},</if>
            <if test="scmScd != null">#{scmScd},</if>
            <if test="scmPaytyp != null">#{scmPaytyp},</if>
            <if test="scmYn != null">#{scmYn},</if>
            <if test="scmIsod != null">#{scmIsod},</if>
            <if test="scmDt != null">#{scmDt},</if>
            <if test="scmInvcTyp != null">#{scmInvcTyp},</if>
            <if test="scmPpacd != null">#{scmPpacd},</if>
            <if test="scmPocpcd != null">#{scmPocpcd},</if>
            <if test="scmCocpcd != null">#{scmCocpcd},</if>
            <if test="scmPrcocd != null">#{scmPrcocd},</if>
            <if test="scmFrcocd != null">#{scmFrcocd},</if>
            <if test="scmCustid != null">#{scmCustid},</if>
            <if test="scmCustcd != null">#{scmCustcd},</if>
            <if test="scmCustnm != null">#{scmCustnm},</if>
            <if test="scmCusttel != null">#{scmCusttel},</if>
            <if test="scmCustfax != null">#{scmCustfax},</if>
            <if test="scmEmail != null">#{scmEmail},</if>
            <if test="scmOcpcd != null">#{scmOcpcd},</if>
            <if test="scmOcd != null">#{scmOcd},</if>
            <if test="scmSmid != null">#{scmSmid},</if>
            <if test="scmSmcd != null">#{scmSmcd},</if>
            <if test="scmSmnm != null">#{scmSmnm},</if>
            <if test="scmOuid != null">#{scmOuid},</if>
            <if test="scmOucd != null">#{scmOucd},</if>
            <if test="scmAmt != null">#{scmAmt},</if>
            <if test="scmPreamt != null">#{scmPreamt},</if>
            <if test="scmPnt != null">#{scmPnt},</if>
            <if test="scmGold != null">#{scmGold},</if>
            <if test="scmPntUsed != null">#{scmPntUsed},</if>
            <if test="scmGoldUsed != null">#{scmGoldUsed},</if>
            <if test="scmPntYn != null">#{scmPntYn},</if>
            <if test="scmStscd != null">#{scmStscd},</if>
            <if test="scmStsnm != null">#{scmStsnm},</if>
            <if test="scmRmk != null">#{scmRmk},</if>
            <if test="scmShdt != null">#{scmShdt},</if>
            <if test="scmIcPicurl != null">#{scmIcPicurl},</if>
            <if test="scmUseVerify != null">#{scmUseVerify},</if>
            <if test="scmLicence != null">#{scmLicence},</if>
            <if test="scmInvoicePdf != null">#{scmInvoicePdf},</if>
            <if test="scmSalesPdf != null">#{scmSalesPdf},</if>
            <if test="scmSalesCard != null">#{scmSalesCard},</if>
            <if test="scmAlzsVerify != null">#{scmAlzsVerify},</if>
            <if test="scmYzbzmPdf != null">#{scmYzbzmPdf},</if>
            <if test="scmInvcrmk != null">#{scmInvcrmk},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="scmKp != null">#{scmKp},</if>
            <if test="scmSptyp != null">#{scmSptyp},</if>
            <if test="scmPush != null">#{scmPush},</if>
            <if test="scmBarcode != null">#{scmBarcode},</if>
            <if test="scmPaytime != null">#{scmPaytime},</if>
            <if test="scmCallback != null">#{scmCallback},</if>
            <if test="scmNationalType != null">#{scmNationalType},</if>
            <if test="scmCurrency != null">#{scmCurrency},</if>
            <if test="scmVmi != null">#{scmVmi},</if>
            <if test="scmJd != null">#{scmJd},</if>
            <if test="scmOpcd != null">#{scmOpcd},</if>
            <if test="scmVmicd != null">#{scmVmicd},</if>
            <if test="scmExportDmcd != null">#{scmExportDmcd},</if>
            <if test="scmQumethod != null">#{scmQumethod},</if>
            <if test="scmPono != null">#{scmPono},</if>
            <if test="scmWhpType != null">#{scmWhpType},</if>
            <if test="scmPocpnm != null">#{scmPocpnm},</if>
            <if test="scmFrconm != null">#{scmFrconm},</if>
            <if test="scmOsktype != null">#{scmOsktype},</if>
            <if test="scmFos != null">#{scmFos},</if>
         </trim>
    </insert>

    <update id="updateSalesContractMst" parameterType="SalesContractMst">
        update sales_contract_mst
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="scmCdold != null">scm_cdold = #{scmCdold},</if>
            <if test="scmTyp != null">scm_typ = #{scmTyp},</if>
            <if test="scmOrdtyp != null">scm_ordtyp = #{scmOrdtyp},</if>
            <if test="scmPrinttyp != null">scm_printtyp = #{scmPrinttyp},</if>
            <if test="scmScd != null">scm_scd = #{scmScd},</if>
            <if test="scmPaytyp != null">scm_paytyp = #{scmPaytyp},</if>
            <if test="scmYn != null">scm_yn = #{scmYn},</if>
            <if test="scmIsod != null">scm_isod = #{scmIsod},</if>
            <if test="scmDt != null">scm_dt = #{scmDt},</if>
            <if test="scmInvcTyp != null">scm_invc_typ = #{scmInvcTyp},</if>
            <if test="scmPpacd != null">scm_ppacd = #{scmPpacd},</if>
            <if test="scmPocpcd != null">scm_pocpcd = #{scmPocpcd},</if>
            <if test="scmCocpcd != null">scm_cocpcd = #{scmCocpcd},</if>
            <if test="scmPrcocd != null">scm_prcocd = #{scmPrcocd},</if>
            <if test="scmFrcocd != null">scm_frcocd = #{scmFrcocd},</if>
            <if test="scmCustid != null">scm_custid = #{scmCustid},</if>
            <if test="scmCustcd != null">scm_custcd = #{scmCustcd},</if>
            <if test="scmCustnm != null">scm_custnm = #{scmCustnm},</if>
            <if test="scmCusttel != null">scm_custtel = #{scmCusttel},</if>
            <if test="scmCustfax != null">scm_custfax = #{scmCustfax},</if>
            <if test="scmEmail != null">scm_email = #{scmEmail},</if>
            <if test="scmOcpcd != null">scm_ocpcd = #{scmOcpcd},</if>
            <if test="scmOcd != null">scm_ocd = #{scmOcd},</if>
            <if test="scmSmid != null">scm_smid = #{scmSmid},</if>
            <if test="scmSmcd != null">scm_smcd = #{scmSmcd},</if>
            <if test="scmSmnm != null">scm_smnm = #{scmSmnm},</if>
            <if test="scmOuid != null">scm_ouid = #{scmOuid},</if>
            <if test="scmOucd != null">scm_oucd = #{scmOucd},</if>
            <if test="scmAmt != null">scm_amt = #{scmAmt},</if>
            <if test="scmPreamt != null">scm_preamt = #{scmPreamt},</if>
            <if test="scmPnt != null">scm_pnt = #{scmPnt},</if>
            <if test="scmGold != null">scm_gold = #{scmGold},</if>
            <if test="scmPntUsed != null">scm_pnt_used = #{scmPntUsed},</if>
            <if test="scmGoldUsed != null">scm_gold_used = #{scmGoldUsed},</if>
            <if test="scmPntYn != null">scm_pnt_yn = #{scmPntYn},</if>
            <if test="scmStscd != null">scm_stscd = #{scmStscd},</if>
            <if test="scmStsnm != null">scm_stsnm = #{scmStsnm},</if>
            <if test="scmRmk != null">scm_rmk = #{scmRmk},</if>
            <if test="scmShdt != null">scm_shdt = #{scmShdt},</if>
            <if test="scmIcPicurl != null">scm_ic_picurl = #{scmIcPicurl},</if>
            <if test="scmUseVerify != null">scm_use_verify = #{scmUseVerify},</if>
            <if test="scmLicence != null">scm_licence = #{scmLicence},</if>
            <if test="scmInvoicePdf != null">scm_invoice_pdf = #{scmInvoicePdf},</if>
            <if test="scmSalesPdf != null">scm_sales_pdf = #{scmSalesPdf},</if>
            <if test="scmSalesCard != null">scm_sales_card = #{scmSalesCard},</if>
            <if test="scmAlzsVerify != null">scm_alzs_verify = #{scmAlzsVerify},</if>
            <if test="scmYzbzmPdf != null">scm_yzbzm_pdf = #{scmYzbzmPdf},</if>
            <if test="scmInvcrmk != null">scm_invcrmk = #{scmInvcrmk},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="scmKp != null">scm_kp = #{scmKp},</if>
            <if test="scmSptyp != null">scm_sptyp = #{scmSptyp},</if>
            <if test="scmPush != null">scm_push = #{scmPush},</if>
            <if test="scmBarcode != null">scm_barcode = #{scmBarcode},</if>
            <if test="scmPaytime != null">scm_paytime = #{scmPaytime},</if>
            <if test="scmCallback != null">scm_callback = #{scmCallback},</if>
            <if test="scmNationalType != null">scm_national_type = #{scmNationalType},</if>
            <if test="scmCurrency != null">scm_currency = #{scmCurrency},</if>
            <if test="scmVmi != null">scm_vmi = #{scmVmi},</if>
            <if test="scmJd != null">scm_jd = #{scmJd},</if>
            <if test="scmOpcd != null">scm_opcd = #{scmOpcd},</if>
            <if test="scmVmicd != null">scm_vmicd = #{scmVmicd},</if>
            <if test="scmExportDmcd != null">scm_export_dmcd = #{scmExportDmcd},</if>
            <if test="scmQumethod != null">scm_quMethod = #{scmQumethod},</if>
            <if test="scmPono != null">scm_PoNo = #{scmPono},</if>
            <if test="scmWhpType != null">scm_whp_type = #{scmWhpType},</if>
            <if test="scmPocpnm != null">scm_pocpnm = #{scmPocpnm},</if>
            <if test="scmFrconm != null">scm_frconm = #{scmFrconm},</if>
            <if test="scmOsktype != null">scm_osktype = #{scmOsktype},</if>
            <if test="scmFos != null">scm_fos = #{scmFos},</if>
        </trim>
        where scm_cd = #{scmCd}
    </update>

    <delete id="deleteSalesContractMstByScmCd" parameterType="String">
        delete from sales_contract_mst where scm_cd = #{scmCd}
    </delete>

    <delete id="deleteSalesContractMstByScmCds" parameterType="String">
        delete from sales_contract_mst where scm_cd in 
        <foreach item="scmCd" collection="array" open="(" separator="," close=")">
            #{scmCd}
        </foreach>
    </delete>
</mapper>