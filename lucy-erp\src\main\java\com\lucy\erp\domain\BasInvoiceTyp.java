package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_发票_类型
对象 bas_invoice_typ
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class BasInvoiceTyp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 类型 */
    @Excel(name = "类型")
    private String bitTyp;

    /** 编号 */
    private String bitCd;

    /** 名称 */
    @Excel(name = "名称")
    private String bitNm;

    /** 是否有限额 */
    @Excel(name = "是否有限额")
    private String bitIsded;

    /** 是否价税分离 */
    @Excel(name = "是否价税分离")
    private String bitIsts;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal bitTr;

    /** 排序序号 */
    @Excel(name = "排序序号")
    private Long bitOs;

    /** 是否允许不隔月红冲（00：不允许，01：允许） */
    @Excel(name = "是否允许不隔月红冲", readConverterExp = "0=0：不允许，01：允许")
    private String bitRedFlush;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String rCreKid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date rCreDt;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String rUpdKid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date rUpdDt;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBitTyp(String bitTyp) 
    {
        this.bitTyp = bitTyp;
    }

    public String getBitTyp() 
    {
        return bitTyp;
    }

    public void setBitCd(String bitCd) 
    {
        this.bitCd = bitCd;
    }

    public String getBitCd() 
    {
        return bitCd;
    }

    public void setBitNm(String bitNm) 
    {
        this.bitNm = bitNm;
    }

    public String getBitNm() 
    {
        return bitNm;
    }

    public void setBitIsded(String bitIsded) 
    {
        this.bitIsded = bitIsded;
    }

    public String getBitIsded() 
    {
        return bitIsded;
    }

    public void setBitIsts(String bitIsts) 
    {
        this.bitIsts = bitIsts;
    }

    public String getBitIsts() 
    {
        return bitIsts;
    }

    public void setBitTr(BigDecimal bitTr) 
    {
        this.bitTr = bitTr;
    }

    public BigDecimal getBitTr() 
    {
        return bitTr;
    }

    public void setBitOs(Long bitOs) 
    {
        this.bitOs = bitOs;
    }

    public Long getBitOs() 
    {
        return bitOs;
    }

    public void setBitRedFlush(String bitRedFlush) 
    {
        this.bitRedFlush = bitRedFlush;
    }

    public String getBitRedFlush() 
    {
        return bitRedFlush;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bitTyp", getBitTyp())
            .append("bitCd", getBitCd())
            .append("bitNm", getBitNm())
            .append("bitIsded", getBitIsded())
            .append("bitIsts", getBitIsts())
            .append("bitTr", getBitTr())
            .append("bitOs", getBitOs())
            .append("bitRedFlush", getBitRedFlush())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
