package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.FinProdCode;
import com.lucy.erp.service.IFinProdCodeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 记账科目Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/prod/code")
public class FinProdCodeController extends BaseController
{
    @Autowired
    private IFinProdCodeService finProdCodeService;

    /**
     * 查询记账科目列表
     */
    @PreAuthorize("@ss.hasPermi('prod:code:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinProdCode finProdCode)
    {
        startPage();
        List<FinProdCode> list = finProdCodeService.selectFinProdCodeList(finProdCode);
        return getDataTable(list);
    }

    /**
     * 导出记账科目列表
     */
    @PreAuthorize("@ss.hasPermi('prod:code:export')")
    @Log(title = "记账科目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinProdCode finProdCode)
    {
        List<FinProdCode> list = finProdCodeService.selectFinProdCodeList(finProdCode);
        ExcelUtil<FinProdCode> util = new ExcelUtil<FinProdCode>(FinProdCode.class);
        util.exportExcel(response, list, "记账科目数据");
    }

    /**
     * 获取记账科目详细信息
     */
    @PreAuthorize("@ss.hasPermi('prod:code:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(finProdCodeService.selectFinProdCodeById(id));
    }

    /**
     * 新增记账科目
     */
    @PreAuthorize("@ss.hasPermi('prod:code:add')")
    @Log(title = "记账科目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinProdCode finProdCode)
    {
        return toAjax(finProdCodeService.insertFinProdCode(finProdCode));
    }

    /**
     * 修改记账科目
     */
    @PreAuthorize("@ss.hasPermi('prod:code:edit')")
    @Log(title = "记账科目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinProdCode finProdCode)
    {
        return toAjax(finProdCodeService.updateFinProdCode(finProdCode));
    }

    /**
     * 删除记账科目
     */
    @PreAuthorize("@ss.hasPermi('prod:code:remove')")
    @Log(title = "记账科目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(finProdCodeService.deleteFinProdCodeByIds(ids));
    }
}
