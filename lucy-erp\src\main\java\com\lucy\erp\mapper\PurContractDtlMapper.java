package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.PurContractDtl;

/**
 * 采购_合同_明细
Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface PurContractDtlMapper 
{
    /**
     * 查询采购_合同_明细

     * 
     * @param pcdCd 采购_合同_明细
主键
     * @return 采购_合同_明细

     */
    public PurContractDtl selectPurContractDtlByPcdCd(String pcdCd);

    /**
     * 查询采购_合同_明细
列表
     * 
     * @param purContractDtl 采购_合同_明细

     * @return 采购_合同_明细
集合
     */
    public List<PurContractDtl> selectPurContractDtlList(PurContractDtl purContractDtl);

    /**
     * 新增采购_合同_明细

     * 
     * @param purContractDtl 采购_合同_明细

     * @return 结果
     */
    public int insertPurContractDtl(PurContractDtl purContractDtl);

    /**
     * 修改采购_合同_明细

     * 
     * @param purContractDtl 采购_合同_明细

     * @return 结果
     */
    public int updatePurContractDtl(PurContractDtl purContractDtl);

    /**
     * 删除采购_合同_明细

     * 
     * @param pcdCd 采购_合同_明细
主键
     * @return 结果
     */
    public int deletePurContractDtlByPcdCd(String pcdCd);

    /**
     * 批量删除采购_合同_明细

     * 
     * @param pcdCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePurContractDtlByPcdCds(String[] pcdCds);
}
