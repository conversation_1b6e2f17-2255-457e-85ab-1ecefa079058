package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogAgListMapper;
import com.lucy.erp.domain.LogAgList;
import com.lucy.erp.service.ILogAgListService;

/**
 * 物流_到货_列
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogAgListServiceImpl implements ILogAgListService
{
    @Autowired
    private LogAgListMapper logAgListMapper;

    /**
     * 查询物流_到货_列

     * 
     * @param lalCd 物流_到货_列
主键
     * @return 物流_到货_列

     */
    @Override
    public LogAgList selectLogAgListByLalCd(String lalCd)
    {
        return logAgListMapper.selectLogAgListByLalCd(lalCd);
    }

    /**
     * 查询物流_到货_列
列表
     * 
     * @param logAgList 物流_到货_列

     * @return 物流_到货_列

     */
    @Override
    public List<LogAgList> selectLogAgListList(LogAgList logAgList)
    {
        return logAgListMapper.selectLogAgListList(logAgList);
    }

    /**
     * 新增物流_到货_列

     * 
     * @param logAgList 物流_到货_列

     * @return 结果
     */
    @Override
    public int insertLogAgList(LogAgList logAgList)
    {
        return logAgListMapper.insertLogAgList(logAgList);
    }

    /**
     * 修改物流_到货_列

     * 
     * @param logAgList 物流_到货_列

     * @return 结果
     */
    @Override
    public int updateLogAgList(LogAgList logAgList)
    {
        return logAgListMapper.updateLogAgList(logAgList);
    }

    /**
     * 批量删除物流_到货_列

     * 
     * @param lalCds 需要删除的物流_到货_列
主键
     * @return 结果
     */
    @Override
    public int deleteLogAgListByLalCds(String[] lalCds)
    {
        return logAgListMapper.deleteLogAgListByLalCds(lalCds);
    }

    /**
     * 删除物流_到货_列
信息
     * 
     * @param lalCd 物流_到货_列
主键
     * @return 结果
     */
    @Override
    public int deleteLogAgListByLalCd(String lalCd)
    {
        return logAgListMapper.deleteLogAgListByLalCd(lalCd);
    }
}
