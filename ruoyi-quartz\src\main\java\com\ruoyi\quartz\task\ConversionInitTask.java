package com.ruoyi.quartz.task;

import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.inventoryexit.service.IInventoryCurrentPeriodIssueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("conversionInitTask")
public class ConversionInitTask {

    @Autowired
    private IInventoryCurrentPeriodIssueService inventoryCurrentPeriodIssueService;

    /**
     * 初始化分装数据 分装规则和规则明细
     * @param dateStart 开始日期
     * @param dateEnd 结束日期
     */
    public void initConversionData(String dateStart, String dateEnd) {

        for (AssetOwner assetOwner : AssetOwner.values()) {
            if (!assetOwner.code().equals("BM10000050")) {
                continue;
            }
            inventoryCurrentPeriodIssueService.loadConversionData(dateStart, dateEnd, assetOwner);
        }

    }

    public void initDependencyGraph(String assetOwner) {
        inventoryCurrentPeriodIssueService.loadDependencyGraph(assetOwner);
    }
}
