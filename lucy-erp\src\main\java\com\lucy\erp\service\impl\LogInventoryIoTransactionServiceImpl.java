package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogInventoryIoTransactionMapper;
import com.lucy.erp.domain.LogInventoryIoTransaction;
import com.lucy.erp.service.ILogInventoryIoTransactionService;

/**
 * 物流_库存_清单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogInventoryIoTransactionServiceImpl implements ILogInventoryIoTransactionService 
{
    @Autowired
    private LogInventoryIoTransactionMapper logInventoryIoTransactionMapper;

    /**
     * 查询物流_库存_清单
     * 
     * @param id 物流_库存_清单主键
     * @return 物流_库存_清单
     */
    @Override
    public LogInventoryIoTransaction selectLogInventoryIoTransactionById(Long id)
    {
        return logInventoryIoTransactionMapper.selectLogInventoryIoTransactionById(id);
    }

    /**
     * 查询物流_库存_清单列表
     * 
     * @param logInventoryIoTransaction 物流_库存_清单
     * @return 物流_库存_清单
     */
    @Override
    public List<LogInventoryIoTransaction> selectLogInventoryIoTransactionList(LogInventoryIoTransaction logInventoryIoTransaction)
    {
        return logInventoryIoTransactionMapper.selectLogInventoryIoTransactionList(logInventoryIoTransaction);
    }

    /**
     * 新增物流_库存_清单
     * 
     * @param logInventoryIoTransaction 物流_库存_清单
     * @return 结果
     */
    @Override
    public int insertLogInventoryIoTransaction(LogInventoryIoTransaction logInventoryIoTransaction)
    {
        return logInventoryIoTransactionMapper.insertLogInventoryIoTransaction(logInventoryIoTransaction);
    }

    /**
     * 修改物流_库存_清单
     * 
     * @param logInventoryIoTransaction 物流_库存_清单
     * @return 结果
     */
    @Override
    public int updateLogInventoryIoTransaction(LogInventoryIoTransaction logInventoryIoTransaction)
    {
        return logInventoryIoTransactionMapper.updateLogInventoryIoTransaction(logInventoryIoTransaction);
    }

    /**
     * 批量删除物流_库存_清单
     * 
     * @param ids 需要删除的物流_库存_清单主键
     * @return 结果
     */
    @Override
    public int deleteLogInventoryIoTransactionByIds(Long[] ids)
    {
        return logInventoryIoTransactionMapper.deleteLogInventoryIoTransactionByIds(ids);
    }

    /**
     * 删除物流_库存_清单信息
     * 
     * @param id 物流_库存_清单主键
     * @return 结果
     */
    @Override
    public int deleteLogInventoryIoTransactionById(Long id)
    {
        return logInventoryIoTransactionMapper.deleteLogInventoryIoTransactionById(id);
    }
}
