package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.ExpencesAmortizationDetails;
import com.lucy.erp.service.IExpencesAmortizationDetailsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 费用分摊明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/system/details")
public class ExpencesAmortizationDetailsController extends BaseController
{
    @Autowired
    private IExpencesAmortizationDetailsService expencesAmortizationDetailsService;

    /**
     * 查询费用分摊明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:details:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExpencesAmortizationDetails expencesAmortizationDetails)
    {
        startPage();
        List<ExpencesAmortizationDetails> list = expencesAmortizationDetailsService.selectExpencesAmortizationDetailsList(expencesAmortizationDetails);
        return getDataTable(list);
    }

    /**
     * 导出费用分摊明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:details:export')")
    @Log(title = "费用分摊明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExpencesAmortizationDetails expencesAmortizationDetails)
    {
        List<ExpencesAmortizationDetails> list = expencesAmortizationDetailsService.selectExpencesAmortizationDetailsList(expencesAmortizationDetails);
        ExcelUtil<ExpencesAmortizationDetails> util = new ExcelUtil<ExpencesAmortizationDetails>(ExpencesAmortizationDetails.class);
        util.exportExcel(response, list, "费用分摊明细数据");
    }

    /**
     * 获取费用分摊明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:details:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("ead_pcd") String ead_pcd)
    {
        return success(expencesAmortizationDetailsService.selectExpencesAmortizationDetailsByEcdPcd(ead_pcd));
    }

    /**
     * 新增费用分摊明细
     */
    @PreAuthorize("@ss.hasPermi('system:details:add')")
    @Log(title = "费用分摊明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExpencesAmortizationDetails expencesAmortizationDetails)
    {
        return toAjax(expencesAmortizationDetailsService.insertExpencesAmortizationDetails(expencesAmortizationDetails));
    }

    /**
     * 修改费用分摊明细
     */
    @PreAuthorize("@ss.hasPermi('system:details:edit')")
    @Log(title = "费用分摊明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExpencesAmortizationDetails expencesAmortizationDetails)
    {
        return toAjax(expencesAmortizationDetailsService.updateExpencesAmortizationDetails(expencesAmortizationDetails));
    }

    /**
     * 删除费用分摊明细
     */
    @PreAuthorize("@ss.hasPermi('system:details:remove')")
    @Log(title = "费用分摊明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(expencesAmortizationDetailsService.deleteExpencesAmortizationDetailsByIds(ids));
    }
}
