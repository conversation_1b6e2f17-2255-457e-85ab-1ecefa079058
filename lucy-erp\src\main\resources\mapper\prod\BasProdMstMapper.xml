<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasProdMstMapper">
    
    <resultMap type="BasProdMst" id="BasProdMstResult">
        <result property="id"    column="id"    />
        <result property="bpmCcd"    column="bpm_ccd"    />
        <result property="bpmCnm"    column="bpm_cnm"    />
        <result property="bpmSccd"    column="bpm_sccd"    />
        <result property="bpmScnm"    column="bpm_scnm"    />
        <result property="bpmScd"    column="bpm_scd"    />
        <result property="bpmPcd"    column="bpm_pcd"    />
        <result property="bpmPnm"    column="bpm_pnm"    />
        <result property="bpmSort"    column="bpm_sort"    />
        <result property="bpmSortnm"    column="bpm_sortnm"    />
        <result property="bpmOrgcd"    column="bpm_orgcd"    />
        <result property="bpmSuporgcdsub"    column="bpm_suporgcdsub"    />
        <result property="bpmSuporgcd"    column="bpm_suporgcd"    />
        <result property="bpmCd"    column="bpm_cd"    />
        <result property="bpmNm"    column="bpm_nm"    />
        <result property="bpmNmInv"    column="bpm_nm_inv"    />
        <result property="bpmEnm"    column="bpm_enm"    />
        <result property="bpmQgp"    column="bpm_qgp"    />
        <result property="bpmQci"    column="bpm_qci"    />
        <result property="bpmPci"    column="bpm_pci"    />
        <result property="bpmQc"    column="bpm_qc"    />
        <result property="bpmRmk"    column="bpm_rmk"    />
        <result property="bpmDetails"    column="bpm_details"    />
        <result property="bpmDmcd"    column="bpm_dmcd"    />
        <result property="bpmSupcd"    column="bpm_supcd"    />
        <result property="bpmSupnm"    column="bpm_supnm"    />
        <result property="bpmSupcdsub"    column="bpm_supcdsub"    />
        <result property="bpmSupnmsub"    column="bpm_supnmsub"    />
        <result property="bpmPureno"    column="bpm_pureno"    />
        <result property="bpmPurenm"    column="bpm_purenm"    />
        <result property="bpmPrctyp"    column="bpm_prctyp"    />
        <result property="bpmSts"    column="bpm_sts"    />
        <result property="bpmStsrmk"    column="bpm_stsrmk"    />
        <result property="bpmNsts"    column="bpm_nsts"    />
        <result property="bpmSsts"    column="bpm_ssts"    />
        <result property="bpmSstsrmk"    column="bpm_sstsrmk"    />
        <result property="bpmOnsale"    column="bpm_onsale"    />
        <result property="bpmSyn"    column="bpm_syn"    />
        <result property="bpmTyps"    column="bpm_typs"    />
        <result property="bpmClass1"    column="bpm_class1"    />
        <result property="bpmClass2"    column="bpm_class2"    />
        <result property="bpmClass3"    column="bpm_class3"    />
        <result property="bpmClass4"    column="bpm_class4"    />
        <result property="bpmClass5"    column="bpm_class5"    />
        <result property="bpmClass6"    column="bpm_class6"    />
        <result property="bpmClass7"    column="bpm_class7"    />
        <result property="bpmCdt"    column="bpm_cdt"    />
        <result property="bpmTaxonomy"    column="bpm_taxonomy"    />
        <result property="bpmStyp"    column="bpm_styp"    />
        <result property="bpmUnit"    column="bpm_unit"    />
        <result property="bpmFinanceCode"    column="bpm_finance_code"    />
        <result property="bpmWrap"    column="bpm_wrap"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="bpmPureno3a"    column="bpm_pureno3A"    />
        <result property="bpmTips"    column="bpm_tips"    />
        <result property="bpmBizCls1"    column="bpm_biz_cls1"    />
        <result property="bpmBizCls2"    column="bpm_biz_cls2"    />
        <result property="islocked"    column="islocked"    />
        <result property="bpmGrade"    column="bpm_grade"    />
        <result property="bpmGradeDesc"    column="bpm_grade_desc"    />
        <result property="bpmStock"    column="bpm_stock"    />
        <result property="bpmPcdSon"    column="bpm_pcd_son"    />
        <result property="bpmSpecDmcd"    column="bpm_spec_dmcd"    />
        <result property="bpmMarketLabel"    column="bpm_market_label"    />
        <result property="bpmExclusivity"    column="bpm_exclusivity"    />
        <result property="goodstypeId"    column="goodstype_id"    />
        <result property="bpmBoutique"    column="bpm_boutique"    />
        <result property="bpmStdUnitp"    column="bpm_std_unitp"    />
        <result property="bpmModel"    column="bpm_model"    />
        <result property="bpmResign"    column="bpm_reSign"    />
        <result property="bpmSpecAttr"    column="bpm_spec_attr"    />
    </resultMap>

    <sql id="selectBasProdMstVo">
        select id, bpm_ccd, bpm_cnm, bpm_sccd, bpm_scnm, bpm_scd, bpm_pcd, bpm_pnm, bpm_sort, bpm_sortnm, bpm_orgcd, bpm_suporgcdsub, bpm_suporgcd, bpm_cd, bpm_nm, bpm_nm_inv, bpm_enm, bpm_qgp, bpm_qci, bpm_pci, bpm_qc, bpm_rmk, bpm_details, bpm_dmcd, bpm_supcd, bpm_supnm, bpm_supcdsub, bpm_supnmsub, bpm_pureno, bpm_purenm, bpm_prctyp, bpm_sts, bpm_stsrmk, bpm_nsts, bpm_ssts, bpm_sstsrmk, bpm_onsale, bpm_syn, bpm_typs, bpm_class1, bpm_class2, bpm_class3, bpm_class4, bpm_class5, bpm_class6, bpm_class7, bpm_cdt, bpm_taxonomy, bpm_styp, bpm_unit, bpm_finance_code, bpm_wrap, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, bpm_pureno3A, bpm_tips, bpm_biz_cls1, bpm_biz_cls2, islocked, bpm_grade, bpm_grade_desc, bpm_stock, bpm_pcd_son, bpm_spec_dmcd, bpm_market_label, bpm_exclusivity, goodstype_id, bpm_boutique, bpm_std_unitp, bpm_model, bpm_reSign, bpm_spec_attr from bas_prod_mst
    </sql>

    <select id="selectBasProdMstList" parameterType="BasProdMst" resultMap="BasProdMstResult">
        <include refid="selectBasProdMstVo"/>
        <where>  
            <if test="bpmCcd != null  and bpmCcd != ''"> and bpm_ccd = #{bpmCcd}</if>
            <if test="bpmCnm != null  and bpmCnm != ''"> and bpm_cnm = #{bpmCnm}</if>
            <if test="bpmSccd != null  and bpmSccd != ''"> and bpm_sccd = #{bpmSccd}</if>
            <if test="bpmScnm != null  and bpmScnm != ''"> and bpm_scnm = #{bpmScnm}</if>
            <if test="bpmScd != null  and bpmScd != ''"> and bpm_scd = #{bpmScd}</if>
            <if test="bpmPcd != null  and bpmPcd != ''"> and bpm_pcd = #{bpmPcd}</if>
            <if test="bpmPnm != null  and bpmPnm != ''"> and bpm_pnm = #{bpmPnm}</if>
            <if test="bpmSort != null  and bpmSort != ''"> and bpm_sort = #{bpmSort}</if>
            <if test="bpmSortnm != null  and bpmSortnm != ''"> and bpm_sortnm = #{bpmSortnm}</if>
            <if test="bpmOrgcd != null  and bpmOrgcd != ''"> and bpm_orgcd = #{bpmOrgcd}</if>
            <if test="bpmSuporgcdsub != null  and bpmSuporgcdsub != ''"> and bpm_suporgcdsub = #{bpmSuporgcdsub}</if>
            <if test="bpmSuporgcd != null  and bpmSuporgcd != ''"> and bpm_suporgcd = #{bpmSuporgcd}</if>
            <if test="bpmNm != null  and bpmNm != ''"> and bpm_nm = #{bpmNm}</if>
            <if test="bpmNmInv != null  and bpmNmInv != ''"> and bpm_nm_inv = #{bpmNmInv}</if>
            <if test="bpmEnm != null  and bpmEnm != ''"> and bpm_enm = #{bpmEnm}</if>
            <if test="bpmQgp != null  and bpmQgp != ''"> and bpm_qgp = #{bpmQgp}</if>
            <if test="bpmQci != null  and bpmQci != ''"> and bpm_qci = #{bpmQci}</if>
            <if test="bpmPci != null  and bpmPci != ''"> and bpm_pci = #{bpmPci}</if>
            <if test="bpmQc != null  and bpmQc != ''"> and bpm_qc = #{bpmQc}</if>
            <if test="bpmRmk != null  and bpmRmk != ''"> and bpm_rmk = #{bpmRmk}</if>
            <if test="bpmDetails != null  and bpmDetails != ''"> and bpm_details = #{bpmDetails}</if>
            <if test="bpmDmcd != null  and bpmDmcd != ''"> and bpm_dmcd = #{bpmDmcd}</if>
            <if test="bpmSupcd != null  and bpmSupcd != ''"> and bpm_supcd = #{bpmSupcd}</if>
            <if test="bpmSupnm != null  and bpmSupnm != ''"> and bpm_supnm = #{bpmSupnm}</if>
            <if test="bpmSupcdsub != null  and bpmSupcdsub != ''"> and bpm_supcdsub = #{bpmSupcdsub}</if>
            <if test="bpmSupnmsub != null  and bpmSupnmsub != ''"> and bpm_supnmsub = #{bpmSupnmsub}</if>
            <if test="bpmPureno != null  and bpmPureno != ''"> and bpm_pureno = #{bpmPureno}</if>
            <if test="bpmPurenm != null  and bpmPurenm != ''"> and bpm_purenm = #{bpmPurenm}</if>
            <if test="bpmPrctyp != null  and bpmPrctyp != ''"> and bpm_prctyp = #{bpmPrctyp}</if>
            <if test="bpmSts != null  and bpmSts != ''"> and bpm_sts = #{bpmSts}</if>
            <if test="bpmStsrmk != null  and bpmStsrmk != ''"> and bpm_stsrmk = #{bpmStsrmk}</if>
            <if test="bpmNsts != null  and bpmNsts != ''"> and bpm_nsts = #{bpmNsts}</if>
            <if test="bpmSsts != null  and bpmSsts != ''"> and bpm_ssts = #{bpmSsts}</if>
            <if test="bpmSstsrmk != null  and bpmSstsrmk != ''"> and bpm_sstsrmk = #{bpmSstsrmk}</if>
            <if test="bpmOnsale != null  and bpmOnsale != ''"> and bpm_onsale = #{bpmOnsale}</if>
            <if test="bpmSyn != null  and bpmSyn != ''"> and bpm_syn = #{bpmSyn}</if>
            <if test="bpmTyps != null  and bpmTyps != ''"> and bpm_typs = #{bpmTyps}</if>
            <if test="bpmClass1 != null  and bpmClass1 != ''"> and bpm_class1 = #{bpmClass1}</if>
            <if test="bpmClass2 != null  and bpmClass2 != ''"> and bpm_class2 = #{bpmClass2}</if>
            <if test="bpmClass3 != null  and bpmClass3 != ''"> and bpm_class3 = #{bpmClass3}</if>
            <if test="bpmClass4 != null  and bpmClass4 != ''"> and bpm_class4 = #{bpmClass4}</if>
            <if test="bpmClass5 != null  and bpmClass5 != ''"> and bpm_class5 = #{bpmClass5}</if>
            <if test="bpmClass6 != null  and bpmClass6 != ''"> and bpm_class6 = #{bpmClass6}</if>
            <if test="bpmClass7 != null  and bpmClass7 != ''"> and bpm_class7 = #{bpmClass7}</if>
            <if test="bpmCdt != null  and bpmCdt != ''"> and bpm_cdt = #{bpmCdt}</if>
            <if test="bpmTaxonomy != null  and bpmTaxonomy != ''"> and bpm_taxonomy = #{bpmTaxonomy}</if>
            <if test="bpmStyp != null  and bpmStyp != ''"> and bpm_styp = #{bpmStyp}</if>
            <if test="bpmUnit != null  and bpmUnit != ''"> and bpm_unit = #{bpmUnit}</if>
            <if test="bpmFinanceCode != null  and bpmFinanceCode != ''"> and bpm_finance_code = #{bpmFinanceCode}</if>
            <if test="bpmWrap != null  and bpmWrap != ''"> and bpm_wrap = #{bpmWrap}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="bpmPureno3a != null  and bpmPureno3a != ''"> and bpm_pureno3A = #{bpmPureno3a}</if>
            <if test="bpmTips != null  and bpmTips != ''"> and bpm_tips = #{bpmTips}</if>
            <if test="bpmBizCls1 != null  and bpmBizCls1 != ''"> and bpm_biz_cls1 = #{bpmBizCls1}</if>
            <if test="bpmBizCls2 != null  and bpmBizCls2 != ''"> and bpm_biz_cls2 = #{bpmBizCls2}</if>
            <if test="islocked != null "> and islocked = #{islocked}</if>
            <if test="bpmGrade != null  and bpmGrade != ''"> and bpm_grade = #{bpmGrade}</if>
            <if test="bpmGradeDesc != null  and bpmGradeDesc != ''"> and bpm_grade_desc = #{bpmGradeDesc}</if>
            <if test="bpmStock != null "> and bpm_stock = #{bpmStock}</if>
            <if test="bpmPcdSon != null  and bpmPcdSon != ''"> and bpm_pcd_son = #{bpmPcdSon}</if>
            <if test="bpmSpecDmcd != null  and bpmSpecDmcd != ''"> and bpm_spec_dmcd = #{bpmSpecDmcd}</if>
            <if test="bpmMarketLabel != null  and bpmMarketLabel != ''"> and bpm_market_label = #{bpmMarketLabel}</if>
            <if test="bpmExclusivity != null  and bpmExclusivity != ''"> and bpm_exclusivity = #{bpmExclusivity}</if>
            <if test="goodstypeId != null "> and goodstype_id = #{goodstypeId}</if>
            <if test="bpmBoutique != null "> and bpm_boutique = #{bpmBoutique}</if>
            <if test="bpmStdUnitp != null "> and bpm_std_unitp = #{bpmStdUnitp}</if>
            <if test="bpmModel != null  and bpmModel != ''"> and bpm_model = #{bpmModel}</if>
            <if test="bpmResign != null  and bpmResign != ''"> and bpm_reSign = #{bpmResign}</if>
            <if test="bpmSpecAttr != null  and bpmSpecAttr != ''"> and bpm_spec_attr = #{bpmSpecAttr}</if>
        </where>
    </select>
    
    <select id="selectBasProdMstByBpmCd" parameterType="String" resultMap="BasProdMstResult">
        <include refid="selectBasProdMstVo"/>
        where bpm_cd = #{bpmCd}
    </select>

    <insert id="insertBasProdMst" parameterType="BasProdMst">
        insert into bas_prod_mst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bpmCcd != null">bpm_ccd,</if>
            <if test="bpmCnm != null">bpm_cnm,</if>
            <if test="bpmSccd != null">bpm_sccd,</if>
            <if test="bpmScnm != null">bpm_scnm,</if>
            <if test="bpmScd != null">bpm_scd,</if>
            <if test="bpmPcd != null">bpm_pcd,</if>
            <if test="bpmPnm != null">bpm_pnm,</if>
            <if test="bpmSort != null and bpmSort != ''">bpm_sort,</if>
            <if test="bpmSortnm != null">bpm_sortnm,</if>
            <if test="bpmOrgcd != null and bpmOrgcd != ''">bpm_orgcd,</if>
            <if test="bpmSuporgcdsub != null">bpm_suporgcdsub,</if>
            <if test="bpmSuporgcd != null">bpm_suporgcd,</if>
            <if test="bpmCd != null">bpm_cd,</if>
            <if test="bpmNm != null">bpm_nm,</if>
            <if test="bpmNmInv != null">bpm_nm_inv,</if>
            <if test="bpmEnm != null">bpm_enm,</if>
            <if test="bpmQgp != null">bpm_qgp,</if>
            <if test="bpmQci != null">bpm_qci,</if>
            <if test="bpmPci != null">bpm_pci,</if>
            <if test="bpmQc != null">bpm_qc,</if>
            <if test="bpmRmk != null">bpm_rmk,</if>
            <if test="bpmDetails != null">bpm_details,</if>
            <if test="bpmDmcd != null">bpm_dmcd,</if>
            <if test="bpmSupcd != null">bpm_supcd,</if>
            <if test="bpmSupnm != null">bpm_supnm,</if>
            <if test="bpmSupcdsub != null">bpm_supcdsub,</if>
            <if test="bpmSupnmsub != null">bpm_supnmsub,</if>
            <if test="bpmPureno != null">bpm_pureno,</if>
            <if test="bpmPurenm != null">bpm_purenm,</if>
            <if test="bpmPrctyp != null and bpmPrctyp != ''">bpm_prctyp,</if>
            <if test="bpmSts != null">bpm_sts,</if>
            <if test="bpmStsrmk != null">bpm_stsrmk,</if>
            <if test="bpmNsts != null">bpm_nsts,</if>
            <if test="bpmSsts != null">bpm_ssts,</if>
            <if test="bpmSstsrmk != null">bpm_sstsrmk,</if>
            <if test="bpmOnsale != null">bpm_onsale,</if>
            <if test="bpmSyn != null">bpm_syn,</if>
            <if test="bpmTyps != null">bpm_typs,</if>
            <if test="bpmClass1 != null">bpm_class1,</if>
            <if test="bpmClass2 != null">bpm_class2,</if>
            <if test="bpmClass3 != null">bpm_class3,</if>
            <if test="bpmClass4 != null">bpm_class4,</if>
            <if test="bpmClass5 != null">bpm_class5,</if>
            <if test="bpmClass6 != null">bpm_class6,</if>
            <if test="bpmClass7 != null">bpm_class7,</if>
            <if test="bpmCdt != null">bpm_cdt,</if>
            <if test="bpmTaxonomy != null">bpm_taxonomy,</if>
            <if test="bpmStyp != null">bpm_styp,</if>
            <if test="bpmUnit != null">bpm_unit,</if>
            <if test="bpmFinanceCode != null">bpm_finance_code,</if>
            <if test="bpmWrap != null">bpm_wrap,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="bpmPureno3a != null">bpm_pureno3A,</if>
            <if test="bpmTips != null">bpm_tips,</if>
            <if test="bpmBizCls1 != null">bpm_biz_cls1,</if>
            <if test="bpmBizCls2 != null">bpm_biz_cls2,</if>
            <if test="islocked != null">islocked,</if>
            <if test="bpmGrade != null">bpm_grade,</if>
            <if test="bpmGradeDesc != null">bpm_grade_desc,</if>
            <if test="bpmStock != null">bpm_stock,</if>
            <if test="bpmPcdSon != null">bpm_pcd_son,</if>
            <if test="bpmSpecDmcd != null">bpm_spec_dmcd,</if>
            <if test="bpmMarketLabel != null">bpm_market_label,</if>
            <if test="bpmExclusivity != null">bpm_exclusivity,</if>
            <if test="goodstypeId != null">goodstype_id,</if>
            <if test="bpmBoutique != null">bpm_boutique,</if>
            <if test="bpmStdUnitp != null">bpm_std_unitp,</if>
            <if test="bpmModel != null">bpm_model,</if>
            <if test="bpmResign != null">bpm_reSign,</if>
            <if test="bpmSpecAttr != null">bpm_spec_attr,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bpmCcd != null">#{bpmCcd},</if>
            <if test="bpmCnm != null">#{bpmCnm},</if>
            <if test="bpmSccd != null">#{bpmSccd},</if>
            <if test="bpmScnm != null">#{bpmScnm},</if>
            <if test="bpmScd != null">#{bpmScd},</if>
            <if test="bpmPcd != null">#{bpmPcd},</if>
            <if test="bpmPnm != null">#{bpmPnm},</if>
            <if test="bpmSort != null and bpmSort != ''">#{bpmSort},</if>
            <if test="bpmSortnm != null">#{bpmSortnm},</if>
            <if test="bpmOrgcd != null and bpmOrgcd != ''">#{bpmOrgcd},</if>
            <if test="bpmSuporgcdsub != null">#{bpmSuporgcdsub},</if>
            <if test="bpmSuporgcd != null">#{bpmSuporgcd},</if>
            <if test="bpmCd != null">#{bpmCd},</if>
            <if test="bpmNm != null">#{bpmNm},</if>
            <if test="bpmNmInv != null">#{bpmNmInv},</if>
            <if test="bpmEnm != null">#{bpmEnm},</if>
            <if test="bpmQgp != null">#{bpmQgp},</if>
            <if test="bpmQci != null">#{bpmQci},</if>
            <if test="bpmPci != null">#{bpmPci},</if>
            <if test="bpmQc != null">#{bpmQc},</if>
            <if test="bpmRmk != null">#{bpmRmk},</if>
            <if test="bpmDetails != null">#{bpmDetails},</if>
            <if test="bpmDmcd != null">#{bpmDmcd},</if>
            <if test="bpmSupcd != null">#{bpmSupcd},</if>
            <if test="bpmSupnm != null">#{bpmSupnm},</if>
            <if test="bpmSupcdsub != null">#{bpmSupcdsub},</if>
            <if test="bpmSupnmsub != null">#{bpmSupnmsub},</if>
            <if test="bpmPureno != null">#{bpmPureno},</if>
            <if test="bpmPurenm != null">#{bpmPurenm},</if>
            <if test="bpmPrctyp != null and bpmPrctyp != ''">#{bpmPrctyp},</if>
            <if test="bpmSts != null">#{bpmSts},</if>
            <if test="bpmStsrmk != null">#{bpmStsrmk},</if>
            <if test="bpmNsts != null">#{bpmNsts},</if>
            <if test="bpmSsts != null">#{bpmSsts},</if>
            <if test="bpmSstsrmk != null">#{bpmSstsrmk},</if>
            <if test="bpmOnsale != null">#{bpmOnsale},</if>
            <if test="bpmSyn != null">#{bpmSyn},</if>
            <if test="bpmTyps != null">#{bpmTyps},</if>
            <if test="bpmClass1 != null">#{bpmClass1},</if>
            <if test="bpmClass2 != null">#{bpmClass2},</if>
            <if test="bpmClass3 != null">#{bpmClass3},</if>
            <if test="bpmClass4 != null">#{bpmClass4},</if>
            <if test="bpmClass5 != null">#{bpmClass5},</if>
            <if test="bpmClass6 != null">#{bpmClass6},</if>
            <if test="bpmClass7 != null">#{bpmClass7},</if>
            <if test="bpmCdt != null">#{bpmCdt},</if>
            <if test="bpmTaxonomy != null">#{bpmTaxonomy},</if>
            <if test="bpmStyp != null">#{bpmStyp},</if>
            <if test="bpmUnit != null">#{bpmUnit},</if>
            <if test="bpmFinanceCode != null">#{bpmFinanceCode},</if>
            <if test="bpmWrap != null">#{bpmWrap},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="bpmPureno3a != null">#{bpmPureno3a},</if>
            <if test="bpmTips != null">#{bpmTips},</if>
            <if test="bpmBizCls1 != null">#{bpmBizCls1},</if>
            <if test="bpmBizCls2 != null">#{bpmBizCls2},</if>
            <if test="islocked != null">#{islocked},</if>
            <if test="bpmGrade != null">#{bpmGrade},</if>
            <if test="bpmGradeDesc != null">#{bpmGradeDesc},</if>
            <if test="bpmStock != null">#{bpmStock},</if>
            <if test="bpmPcdSon != null">#{bpmPcdSon},</if>
            <if test="bpmSpecDmcd != null">#{bpmSpecDmcd},</if>
            <if test="bpmMarketLabel != null">#{bpmMarketLabel},</if>
            <if test="bpmExclusivity != null">#{bpmExclusivity},</if>
            <if test="goodstypeId != null">#{goodstypeId},</if>
            <if test="bpmBoutique != null">#{bpmBoutique},</if>
            <if test="bpmStdUnitp != null">#{bpmStdUnitp},</if>
            <if test="bpmModel != null">#{bpmModel},</if>
            <if test="bpmResign != null">#{bpmResign},</if>
            <if test="bpmSpecAttr != null">#{bpmSpecAttr},</if>
         </trim>
    </insert>

    <update id="updateBasProdMst" parameterType="BasProdMst">
        update bas_prod_mst
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="bpmCcd != null">bpm_ccd = #{bpmCcd},</if>
            <if test="bpmCnm != null">bpm_cnm = #{bpmCnm},</if>
            <if test="bpmSccd != null">bpm_sccd = #{bpmSccd},</if>
            <if test="bpmScnm != null">bpm_scnm = #{bpmScnm},</if>
            <if test="bpmScd != null">bpm_scd = #{bpmScd},</if>
            <if test="bpmPcd != null">bpm_pcd = #{bpmPcd},</if>
            <if test="bpmPnm != null">bpm_pnm = #{bpmPnm},</if>
            <if test="bpmSort != null and bpmSort != ''">bpm_sort = #{bpmSort},</if>
            <if test="bpmSortnm != null">bpm_sortnm = #{bpmSortnm},</if>
            <if test="bpmOrgcd != null and bpmOrgcd != ''">bpm_orgcd = #{bpmOrgcd},</if>
            <if test="bpmSuporgcdsub != null">bpm_suporgcdsub = #{bpmSuporgcdsub},</if>
            <if test="bpmSuporgcd != null">bpm_suporgcd = #{bpmSuporgcd},</if>
            <if test="bpmNm != null">bpm_nm = #{bpmNm},</if>
            <if test="bpmNmInv != null">bpm_nm_inv = #{bpmNmInv},</if>
            <if test="bpmEnm != null">bpm_enm = #{bpmEnm},</if>
            <if test="bpmQgp != null">bpm_qgp = #{bpmQgp},</if>
            <if test="bpmQci != null">bpm_qci = #{bpmQci},</if>
            <if test="bpmPci != null">bpm_pci = #{bpmPci},</if>
            <if test="bpmQc != null">bpm_qc = #{bpmQc},</if>
            <if test="bpmRmk != null">bpm_rmk = #{bpmRmk},</if>
            <if test="bpmDetails != null">bpm_details = #{bpmDetails},</if>
            <if test="bpmDmcd != null">bpm_dmcd = #{bpmDmcd},</if>
            <if test="bpmSupcd != null">bpm_supcd = #{bpmSupcd},</if>
            <if test="bpmSupnm != null">bpm_supnm = #{bpmSupnm},</if>
            <if test="bpmSupcdsub != null">bpm_supcdsub = #{bpmSupcdsub},</if>
            <if test="bpmSupnmsub != null">bpm_supnmsub = #{bpmSupnmsub},</if>
            <if test="bpmPureno != null">bpm_pureno = #{bpmPureno},</if>
            <if test="bpmPurenm != null">bpm_purenm = #{bpmPurenm},</if>
            <if test="bpmPrctyp != null and bpmPrctyp != ''">bpm_prctyp = #{bpmPrctyp},</if>
            <if test="bpmSts != null">bpm_sts = #{bpmSts},</if>
            <if test="bpmStsrmk != null">bpm_stsrmk = #{bpmStsrmk},</if>
            <if test="bpmNsts != null">bpm_nsts = #{bpmNsts},</if>
            <if test="bpmSsts != null">bpm_ssts = #{bpmSsts},</if>
            <if test="bpmSstsrmk != null">bpm_sstsrmk = #{bpmSstsrmk},</if>
            <if test="bpmOnsale != null">bpm_onsale = #{bpmOnsale},</if>
            <if test="bpmSyn != null">bpm_syn = #{bpmSyn},</if>
            <if test="bpmTyps != null">bpm_typs = #{bpmTyps},</if>
            <if test="bpmClass1 != null">bpm_class1 = #{bpmClass1},</if>
            <if test="bpmClass2 != null">bpm_class2 = #{bpmClass2},</if>
            <if test="bpmClass3 != null">bpm_class3 = #{bpmClass3},</if>
            <if test="bpmClass4 != null">bpm_class4 = #{bpmClass4},</if>
            <if test="bpmClass5 != null">bpm_class5 = #{bpmClass5},</if>
            <if test="bpmClass6 != null">bpm_class6 = #{bpmClass6},</if>
            <if test="bpmClass7 != null">bpm_class7 = #{bpmClass7},</if>
            <if test="bpmCdt != null">bpm_cdt = #{bpmCdt},</if>
            <if test="bpmTaxonomy != null">bpm_taxonomy = #{bpmTaxonomy},</if>
            <if test="bpmStyp != null">bpm_styp = #{bpmStyp},</if>
            <if test="bpmUnit != null">bpm_unit = #{bpmUnit},</if>
            <if test="bpmFinanceCode != null">bpm_finance_code = #{bpmFinanceCode},</if>
            <if test="bpmWrap != null">bpm_wrap = #{bpmWrap},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="bpmPureno3a != null">bpm_pureno3A = #{bpmPureno3a},</if>
            <if test="bpmTips != null">bpm_tips = #{bpmTips},</if>
            <if test="bpmBizCls1 != null">bpm_biz_cls1 = #{bpmBizCls1},</if>
            <if test="bpmBizCls2 != null">bpm_biz_cls2 = #{bpmBizCls2},</if>
            <if test="islocked != null">islocked = #{islocked},</if>
            <if test="bpmGrade != null">bpm_grade = #{bpmGrade},</if>
            <if test="bpmGradeDesc != null">bpm_grade_desc = #{bpmGradeDesc},</if>
            <if test="bpmStock != null">bpm_stock = #{bpmStock},</if>
            <if test="bpmPcdSon != null">bpm_pcd_son = #{bpmPcdSon},</if>
            <if test="bpmSpecDmcd != null">bpm_spec_dmcd = #{bpmSpecDmcd},</if>
            <if test="bpmMarketLabel != null">bpm_market_label = #{bpmMarketLabel},</if>
            <if test="bpmExclusivity != null">bpm_exclusivity = #{bpmExclusivity},</if>
            <if test="goodstypeId != null">goodstype_id = #{goodstypeId},</if>
            <if test="bpmBoutique != null">bpm_boutique = #{bpmBoutique},</if>
            <if test="bpmStdUnitp != null">bpm_std_unitp = #{bpmStdUnitp},</if>
            <if test="bpmModel != null">bpm_model = #{bpmModel},</if>
            <if test="bpmResign != null">bpm_reSign = #{bpmResign},</if>
            <if test="bpmSpecAttr != null">bpm_spec_attr = #{bpmSpecAttr},</if>
        </trim>
        where bpm_cd = #{bpmCd}
    </update>

    <delete id="deleteBasProdMstByBpmCd" parameterType="String">
        delete from bas_prod_mst where bpm_cd = #{bpmCd}
    </delete>

    <delete id="deleteBasProdMstByBpmCds" parameterType="String">
        delete from bas_prod_mst where bpm_cd in 
        <foreach item="bpmCd" collection="array" open="(" separator="," close=")">
            #{bpmCd}
        </foreach>
    </delete>
</mapper>