package com.lucy.assetowner.domain;

import com.lucy.assetowner.service.IAssetOwnerMasterService;

import java.util.HashMap;
import java.util.Map;

public enum AssetOwner {

    SESW("BM10000048"),
    AHZS("BM10000050"),
    ZSSH("BM10000056"),
    ZSSF("BM10000057"),
    AHST("BM10000060"),
    SENRISE_TECH("BM10000062");

    private final String code;
    private AssetOwnerMaster entity;

    AssetOwner(String code) {
        this.code = code;
    }

    public String code() { return code; }

    public AssetOwnerMaster entity() { return entity; }

    private static final Map<String, AssetOwner> CODE_MAP = new HashMap<>();

    /**
     * 为每个枚举实例填充 entity，
     */
    public static void init(IAssetOwnerMasterService assetOwnerMasterService) {
        for (AssetOwner owner : values()) {
            owner.entity = assetOwnerMasterService.get(owner.code);
            CODE_MAP.put(owner.code, owner);
        }
    }

    /**
     * 通过编码直接拿 AssetOwnerMaster
     */
    public static AssetOwnerMaster byCode(String code) {
        AssetOwner owner = CODE_MAP.get(code);
        return owner == null ? null : owner.entity;
    }
}
