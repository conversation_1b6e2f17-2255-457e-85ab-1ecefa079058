package com.lucy.supplier.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 供应商主数据 Supplier master对象 supplier_master
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public class SupplierMaster extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 供应商代码, Supplier code */
    private String code;

    /** 供应商名称, Supplier name */
    @Excel(name = "供应商名称, Supplier name")
    private String name;

    /** 供应商类型 */
    @Excel(name = "供应商类型")
    private String type;

    /** 资质证书编号, Certification number */
    @Excel(name = "资质证书编号, Certification number")
    private String certification;

    /** 税号, tax no */
    @Excel(name = "税号, tax no")
    private String taxNo;

    /** 联系人, Contact person */
    @Excel(name = "联系人, Contact person")
    private String contact;

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setCertification(String certification) 
    {
        this.certification = certification;
    }

    public String getCertification() 
    {
        return certification;
    }

    public void setTaxNo(String taxNo) 
    {
        this.taxNo = taxNo;
    }

    public String getTaxNo() 
    {
        return taxNo;
    }

    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getContact() 
    {
        return contact;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("name", getName())
            .append("type", getType())
            .append("certification", getCertification())
            .append("taxNo", getTaxNo())
            .append("contact", getContact())
            .toString();
    }
}
