package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小样工单-主料明细对象 log_rad_develo_compose
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class LogRadDeveloCompose extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 生产原料编号 */
    private String lrdcCd;

    /** 生产编号 */
    @Excel(name = "生产编号")
    private String lrdcMcd;

    /** 批号 */
    @Excel(name = "批号")
    private String lrdcBn;

    /** 加密批号 */
    @Excel(name = "加密批号")
    private String lrdcBnEncrypt;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String lrdcPcd;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String lrdcPpcd;

    /** 库位 */
    @Excel(name = "库位")
    private String lrdcSlcd;

    /** 库位类型 */
    @Excel(name = "库位类型")
    private String lrdcTcd;

    /** 仓库 */
    @Excel(name = "仓库")
    private String lrdcDmcd;

    /** 资产所属 */
    @Excel(name = "资产所属")
    private String lrdcOrgmcd;

    /** 产品批次 */
    @Excel(name = "产品批次")
    private String lrdcPbn;

    /** 供应商批次 */
    @Excel(name = "供应商批次")
    private String lrdcSbn;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal lrdcQty;

    /** 含税单价 */
    @Excel(name = "含税单价")
    private BigDecimal lrdcUnitp;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal lrdcBit;

    /** 是否价税分离 */
    @Excel(name = "是否价税分离")
    private Long lrdcIspart;

    /** 创建人 */
    @Excel(name = "创建人")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 修改人 */
    @Excel(name = "修改人")
    private String rUpdKid;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLrdcCd(String lrdcCd) 
    {
        this.lrdcCd = lrdcCd;
    }

    public String getLrdcCd() 
    {
        return lrdcCd;
    }

    public void setLrdcMcd(String lrdcMcd) 
    {
        this.lrdcMcd = lrdcMcd;
    }

    public String getLrdcMcd() 
    {
        return lrdcMcd;
    }

    public void setLrdcBn(String lrdcBn) 
    {
        this.lrdcBn = lrdcBn;
    }

    public String getLrdcBn() 
    {
        return lrdcBn;
    }

    public void setLrdcBnEncrypt(String lrdcBnEncrypt) 
    {
        this.lrdcBnEncrypt = lrdcBnEncrypt;
    }

    public String getLrdcBnEncrypt() 
    {
        return lrdcBnEncrypt;
    }

    public void setLrdcPcd(String lrdcPcd) 
    {
        this.lrdcPcd = lrdcPcd;
    }

    public String getLrdcPcd() 
    {
        return lrdcPcd;
    }

    public void setLrdcPpcd(String lrdcPpcd) 
    {
        this.lrdcPpcd = lrdcPpcd;
    }

    public String getLrdcPpcd() 
    {
        return lrdcPpcd;
    }

    public void setLrdcSlcd(String lrdcSlcd) 
    {
        this.lrdcSlcd = lrdcSlcd;
    }

    public String getLrdcSlcd() 
    {
        return lrdcSlcd;
    }

    public void setLrdcTcd(String lrdcTcd) 
    {
        this.lrdcTcd = lrdcTcd;
    }

    public String getLrdcTcd() 
    {
        return lrdcTcd;
    }

    public void setLrdcDmcd(String lrdcDmcd) 
    {
        this.lrdcDmcd = lrdcDmcd;
    }

    public String getLrdcDmcd() 
    {
        return lrdcDmcd;
    }

    public void setLrdcOrgmcd(String lrdcOrgmcd) 
    {
        this.lrdcOrgmcd = lrdcOrgmcd;
    }

    public String getLrdcOrgmcd() 
    {
        return lrdcOrgmcd;
    }

    public void setLrdcPbn(String lrdcPbn) 
    {
        this.lrdcPbn = lrdcPbn;
    }

    public String getLrdcPbn() 
    {
        return lrdcPbn;
    }

    public void setLrdcSbn(String lrdcSbn) 
    {
        this.lrdcSbn = lrdcSbn;
    }

    public String getLrdcSbn() 
    {
        return lrdcSbn;
    }

    public void setLrdcQty(BigDecimal lrdcQty) 
    {
        this.lrdcQty = lrdcQty;
    }

    public BigDecimal getLrdcQty() 
    {
        return lrdcQty;
    }

    public void setLrdcUnitp(BigDecimal lrdcUnitp) 
    {
        this.lrdcUnitp = lrdcUnitp;
    }

    public BigDecimal getLrdcUnitp() 
    {
        return lrdcUnitp;
    }

    public void setLrdcBit(BigDecimal lrdcBit) 
    {
        this.lrdcBit = lrdcBit;
    }

    public BigDecimal getLrdcBit() 
    {
        return lrdcBit;
    }

    public void setLrdcIspart(Long lrdcIspart) 
    {
        this.lrdcIspart = lrdcIspart;
    }

    public Long getLrdcIspart() 
    {
        return lrdcIspart;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lrdcCd", getLrdcCd())
            .append("lrdcMcd", getLrdcMcd())
            .append("lrdcBn", getLrdcBn())
            .append("lrdcBnEncrypt", getLrdcBnEncrypt())
            .append("lrdcPcd", getLrdcPcd())
            .append("lrdcPpcd", getLrdcPpcd())
            .append("lrdcSlcd", getLrdcSlcd())
            .append("lrdcTcd", getLrdcTcd())
            .append("lrdcDmcd", getLrdcDmcd())
            .append("lrdcOrgmcd", getLrdcOrgmcd())
            .append("lrdcPbn", getLrdcPbn())
            .append("lrdcSbn", getLrdcSbn())
            .append("lrdcQty", getLrdcQty())
            .append("lrdcUnitp", getLrdcUnitp())
            .append("lrdcBit", getLrdcBit())
            .append("lrdcIspart", getLrdcIspart())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
