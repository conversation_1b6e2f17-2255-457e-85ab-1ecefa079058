package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 财务_平均_费对象 fin_acc_avg_cost
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class FinAccAvgCost extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账套ID */
    @Excel(name = "账套ID")
    private String faacId;

    /** 发生日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发生日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date faacAccurTime;

    /** 制单人 */
    @Excel(name = "制单人")
    private String faacCreater;

    /** 审核人 */
    @Excel(name = "审核人")
    private String faacApprover;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date faacApproTime;

    /** 创建人 */
    @Excel(name = "创建人")
    private String rCreKid;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private String rCreDt;

    /** sku */
    @Excel(name = "sku")
    private String sku;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal faacQty;

    /** 平均成本 */
    @Excel(name = "平均成本")
    private BigDecimal faacCost;

    public void setFaacId(String faacId) 
    {
        this.faacId = faacId;
    }

    public String getFaacId() 
    {
        return faacId;
    }

    public void setFaacAccurTime(Date faacAccurTime) 
    {
        this.faacAccurTime = faacAccurTime;
    }

    public Date getFaacAccurTime() 
    {
        return faacAccurTime;
    }

    public void setFaacCreater(String faacCreater) 
    {
        this.faacCreater = faacCreater;
    }

    public String getFaacCreater() 
    {
        return faacCreater;
    }

    public void setFaacApprover(String faacApprover) 
    {
        this.faacApprover = faacApprover;
    }

    public String getFaacApprover() 
    {
        return faacApprover;
    }

    public void setFaacApproTime(Date faacApproTime) 
    {
        this.faacApproTime = faacApproTime;
    }

    public Date getFaacApproTime() 
    {
        return faacApproTime;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(String rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public String getrCreDt() 
    {
        return rCreDt;
    }

    public void setSku(String sku) 
    {
        this.sku = sku;
    }

    public String getSku() 
    {
        return sku;
    }

    public void setFaacQty(BigDecimal faacQty) 
    {
        this.faacQty = faacQty;
    }

    public BigDecimal getFaacQty() 
    {
        return faacQty;
    }

    public void setFaacCost(BigDecimal faacCost) 
    {
        this.faacCost = faacCost;
    }

    public BigDecimal getFaacCost() 
    {
        return faacCost;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("faacId", getFaacId())
            .append("faacAccurTime", getFaacAccurTime())
            .append("faacCreater", getFaacCreater())
            .append("faacApprover", getFaacApprover())
            .append("faacApproTime", getFaacApproTime())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("sku", getSku())
            .append("faacQty", getFaacQty())
            .append("faacCost", getFaacCost())
            .toString();
    }
}
