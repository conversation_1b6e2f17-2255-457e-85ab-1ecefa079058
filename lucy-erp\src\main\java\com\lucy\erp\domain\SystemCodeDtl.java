package com.lucy.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 系统_字典对象 system_code_dtl
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public class SystemCodeDtl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 字段类型ID */
    @Excel(name = "字段类型ID")
    private Long scdTid;

    /** 字典类型编号 */
    @Excel(name = "字典类型编号")
    private String scdTcd;

    /** 上级类型编号 */
    @Excel(name = "上级类型编号")
    private String scdUptcd;

    /** 上级ID */
    @Excel(name = "上级ID")
    private Long scdUpid;

    /** 上级编号 */
    @Excel(name = "上级编号")
    private String scdUpcd;

    /** 层数 */
    @Excel(name = "层数")
    private Long scdLvl;

    /** 字典编号 */
    @Excel(name = "字典编号")
    private String scdCd;

    /** 字典中文名 */
    @Excel(name = "字典中文名")
    private String scdNm;

    /** 排序序号 */
    @Excel(name = "排序序号")
    private Long scdSortno;

    /** 附加属性1 */
    @Excel(name = "附加属性1")
    private String scdAdc1;

    /** 附加属性2 */
    @Excel(name = "附加属性2")
    private String scdAdc2;

    /** 附加属性3 */
    @Excel(name = "附加属性3")
    private String scdAdc3;

    /** 创建用户ID */
    @Excel(name = "创建用户ID")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 修改用户ID */
    @Excel(name = "修改用户ID")
    private String rUpdKid;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本号 */
    @Excel(name = "行版本号")
    private Long rV;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String rDel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setScdTid(Long scdTid) 
    {
        this.scdTid = scdTid;
    }

    public Long getScdTid() 
    {
        return scdTid;
    }

    public void setScdTcd(String scdTcd) 
    {
        this.scdTcd = scdTcd;
    }

    public String getScdTcd() 
    {
        return scdTcd;
    }

    public void setScdUptcd(String scdUptcd) 
    {
        this.scdUptcd = scdUptcd;
    }

    public String getScdUptcd() 
    {
        return scdUptcd;
    }

    public void setScdUpid(Long scdUpid) 
    {
        this.scdUpid = scdUpid;
    }

    public Long getScdUpid() 
    {
        return scdUpid;
    }

    public void setScdUpcd(String scdUpcd) 
    {
        this.scdUpcd = scdUpcd;
    }

    public String getScdUpcd() 
    {
        return scdUpcd;
    }

    public void setScdLvl(Long scdLvl) 
    {
        this.scdLvl = scdLvl;
    }

    public Long getScdLvl() 
    {
        return scdLvl;
    }

    public void setScdCd(String scdCd) 
    {
        this.scdCd = scdCd;
    }

    public String getScdCd() 
    {
        return scdCd;
    }

    public void setScdNm(String scdNm) 
    {
        this.scdNm = scdNm;
    }

    public String getScdNm() 
    {
        return scdNm;
    }

    public void setScdSortno(Long scdSortno) 
    {
        this.scdSortno = scdSortno;
    }

    public Long getScdSortno() 
    {
        return scdSortno;
    }

    public void setScdAdc1(String scdAdc1) 
    {
        this.scdAdc1 = scdAdc1;
    }

    public String getScdAdc1() 
    {
        return scdAdc1;
    }

    public void setScdAdc2(String scdAdc2) 
    {
        this.scdAdc2 = scdAdc2;
    }

    public String getScdAdc2() 
    {
        return scdAdc2;
    }

    public void setScdAdc3(String scdAdc3) 
    {
        this.scdAdc3 = scdAdc3;
    }

    public String getScdAdc3() 
    {
        return scdAdc3;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(String rDel) 
    {
        this.rDel = rDel;
    }

    public String getrDel() 
    {
        return rDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("scdTid", getScdTid())
            .append("scdTcd", getScdTcd())
            .append("scdUptcd", getScdUptcd())
            .append("scdUpid", getScdUpid())
            .append("scdUpcd", getScdUpcd())
            .append("scdLvl", getScdLvl())
            .append("scdCd", getScdCd())
            .append("scdNm", getScdNm())
            .append("scdSortno", getScdSortno())
            .append("scdAdc1", getScdAdc1())
            .append("scdAdc2", getScdAdc2())
            .append("scdAdc3", getScdAdc3())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
