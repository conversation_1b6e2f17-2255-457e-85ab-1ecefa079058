package com.lucy.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.customer.domain.CustomerMaster;
import com.lucy.customer.service.ICustomerMasterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 客户主数据 Customer masterController
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/customer/master")
public class CustomerMasterController extends BaseController
{
    @Autowired
    private ICustomerMasterService customerMasterService;

    /**
     * 查询客户主数据 Customer master列表
     */
    @PreAuthorize("@ss.hasPermi('customer:master:list')")
    @GetMapping("/list")
    public TableDataInfo list(CustomerMaster customerMaster)
    {
        startPage();
        List<CustomerMaster> list = customerMasterService.selectCustomerMasterList(customerMaster);
        return getDataTable(list);
    }

    /**
     * 导出客户主数据 Customer master列表
     */
    @PreAuthorize("@ss.hasPermi('customer:master:export')")
    @Log(title = "客户主数据 Customer master", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CustomerMaster customerMaster)
    {
        List<CustomerMaster> list = customerMasterService.selectCustomerMasterList(customerMaster);
        ExcelUtil<CustomerMaster> util = new ExcelUtil<CustomerMaster>(CustomerMaster.class);
        util.exportExcel(response, list, "客户主数据 Customer master数据");
    }

    /**
     * 获取客户主数据 Customer master详细信息
     */
    @PreAuthorize("@ss.hasPermi('customer:master:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(customerMasterService.selectCustomerMasterByCode(code));
    }

    /**
     * 新增客户主数据 Customer master
     */
    @PreAuthorize("@ss.hasPermi('customer:master:add')")
    @Log(title = "客户主数据 Customer master", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CustomerMaster customerMaster)
    {
        return toAjax(customerMasterService.insertCustomerMaster(customerMaster));
    }

    /**
     * 修改客户主数据 Customer master
     */
    @PreAuthorize("@ss.hasPermi('customer:master:edit')")
    @Log(title = "客户主数据 Customer master", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CustomerMaster customerMaster)
    {
        return toAjax(customerMasterService.updateCustomerMaster(customerMaster));
    }

    /**
     * 删除客户主数据 Customer master
     */
    @PreAuthorize("@ss.hasPermi('customer:master:remove')")
    @Log(title = "客户主数据 Customer master", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(customerMasterService.deleteCustomerMasterByCodes(codes));
    }
}
