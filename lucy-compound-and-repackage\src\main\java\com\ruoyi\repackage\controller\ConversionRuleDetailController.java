package com.ruoyi.repackage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.repackage.domain.ConversionRuleDetail;
import com.ruoyi.repackage.service.IConversionRuleDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分装规则明细Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/repackage/detail")
public class ConversionRuleDetailController extends BaseController
{
    @Autowired
    private IConversionRuleDetailService conversionRuleDetailService;

    /**
     * 查询分装规则明细列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConversionRuleDetail conversionRuleDetail)
    {
        startPage();
        List<ConversionRuleDetail> list = conversionRuleDetailService.selectConversionRuleDetailList(conversionRuleDetail);
        return getDataTable(list);
    }

    /**
     * 导出分装规则明细列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:detail:export')")
    @Log(title = "分装规则明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConversionRuleDetail conversionRuleDetail)
    {
        List<ConversionRuleDetail> list = conversionRuleDetailService.selectConversionRuleDetailList(conversionRuleDetail);
        ExcelUtil<ConversionRuleDetail> util = new ExcelUtil<ConversionRuleDetail>(ConversionRuleDetail.class);
        util.exportExcel(response, list, "分装规则明细数据");
    }

    /**
     * 获取分装规则明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('repackage:detail:query')")
    @GetMapping(value = "/{detailId}")
    public AjaxResult getInfo(@PathVariable("detailId") Long detailId)
    {
        return success(conversionRuleDetailService.selectConversionRuleDetailByDetailId(detailId));
    }

    /**
     * 新增分装规则明细
     */
    @PreAuthorize("@ss.hasPermi('repackage:detail:add')")
    @Log(title = "分装规则明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConversionRuleDetail conversionRuleDetail)
    {
        return toAjax(conversionRuleDetailService.insertConversionRuleDetail(conversionRuleDetail));
    }

    /**
     * 修改分装规则明细
     */
    @PreAuthorize("@ss.hasPermi('repackage:detail:edit')")
    @Log(title = "分装规则明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConversionRuleDetail conversionRuleDetail)
    {
        return toAjax(conversionRuleDetailService.updateConversionRuleDetail(conversionRuleDetail));
    }

    /**
     * 删除分装规则明细
     */
    @PreAuthorize("@ss.hasPermi('repackage:detail:remove')")
    @Log(title = "分装规则明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{detailIds}")
    public AjaxResult remove(@PathVariable Long[] detailIds)
    {
        return toAjax(conversionRuleDetailService.deleteConversionRuleDetailByDetailIds(detailIds));
    }
}
