package com.ruoyi.repackage.mapper;

import java.util.List;
import com.ruoyi.repackage.domain.ConversionRuleDetail;

/**
 * 分装规则明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface ConversionRuleDetailMapper 
{
    /**
     * 查询分装规则明细
     * 
     * @param detailId 分装规则明细主键
     * @return 分装规则明细
     */
    public ConversionRuleDetail selectConversionRuleDetailByDetailId(Long detailId);

    /**
     * 查询分装规则明细列表
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 分装规则明细集合
     */
    public List<ConversionRuleDetail> selectConversionRuleDetailList(ConversionRuleDetail conversionRuleDetail);

    /**
     * 新增分装规则明细
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 结果
     */
    public int insertConversionRuleDetail(ConversionRuleDetail conversionRuleDetail);

    /**
     * 修改分装规则明细
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 结果
     */
    public int updateConversionRuleDetail(ConversionRuleDetail conversionRuleDetail);

    /**
     * 删除分装规则明细
     * 
     * @param detailId 分装规则明细主键
     * @return 结果
     */
    public int deleteConversionRuleDetailByDetailId(Long detailId);

    /**
     * 批量删除分装规则明细
     * 
     * @param detailIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConversionRuleDetailByDetailIds(Long[] detailIds);
}
