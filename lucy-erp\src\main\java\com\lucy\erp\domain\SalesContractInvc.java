package com.lucy.erp.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 销售_合同_发票对象 sales_contract_invc
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class SalesContractInvc extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 销售合同ID */
    @Excel(name = "销售合同ID")
    private Long sciMid;

    /** 收票信息id */
    @Excel(name = "收票信息id")
    private Long sciId;

    /** 销售合同CD */
    private String sciMcd;

    /** 发票类型 */
    @Excel(name = "发票类型")
    private String sciTyp;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    private String sciTitle;

    /** 收票人姓名 */
    @Excel(name = "收票人姓名")
    private String sciMan;

    /** 固定电话 */
    @Excel(name = "固定电话")
    private String sciFp;

    /** 省 */
    @Excel(name = "省")
    private String sciProvince;

    /** 市 */
    @Excel(name = "市")
    private String sciCity;

    /** 区 */
    @Excel(name = "区")
    private String sciDistrict;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String sciExaddr;

    /** 移动电话 */
    @Excel(name = "移动电话")
    private String sciMp;

    /** 收票人地址 */
    @Excel(name = "收票人地址")
    private String sciAddr;

    /** 限额 */
    @Excel(name = "限额")
    private BigDecimal sciLamt;

    /** 备注 */
    @Excel(name = "备注")
    private String sciRmk;

    /** 电子发票收票邮箱 */
    @Excel(name = "电子发票收票邮箱")
    private String sciEmail;

    /** 国家 */
    @Excel(name = "国家")
    private String sciNation;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setSciMid(Long sciMid) 
    {
        this.sciMid = sciMid;
    }

    public Long getSciMid() 
    {
        return sciMid;
    }

    public void setSciId(Long sciId) 
    {
        this.sciId = sciId;
    }

    public Long getSciId() 
    {
        return sciId;
    }

    public void setSciMcd(String sciMcd) 
    {
        this.sciMcd = sciMcd;
    }

    public String getSciMcd() 
    {
        return sciMcd;
    }

    public void setSciTyp(String sciTyp) 
    {
        this.sciTyp = sciTyp;
    }

    public String getSciTyp() 
    {
        return sciTyp;
    }

    public void setSciTitle(String sciTitle) 
    {
        this.sciTitle = sciTitle;
    }

    public String getSciTitle() 
    {
        return sciTitle;
    }

    public void setSciMan(String sciMan) 
    {
        this.sciMan = sciMan;
    }

    public String getSciMan() 
    {
        return sciMan;
    }

    public void setSciFp(String sciFp) 
    {
        this.sciFp = sciFp;
    }

    public String getSciFp() 
    {
        return sciFp;
    }

    public void setSciProvince(String sciProvince) 
    {
        this.sciProvince = sciProvince;
    }

    public String getSciProvince() 
    {
        return sciProvince;
    }

    public void setSciCity(String sciCity) 
    {
        this.sciCity = sciCity;
    }

    public String getSciCity() 
    {
        return sciCity;
    }

    public void setSciDistrict(String sciDistrict) 
    {
        this.sciDistrict = sciDistrict;
    }

    public String getSciDistrict() 
    {
        return sciDistrict;
    }

    public void setSciExaddr(String sciExaddr) 
    {
        this.sciExaddr = sciExaddr;
    }

    public String getSciExaddr() 
    {
        return sciExaddr;
    }

    public void setSciMp(String sciMp) 
    {
        this.sciMp = sciMp;
    }

    public String getSciMp() 
    {
        return sciMp;
    }

    public void setSciAddr(String sciAddr) 
    {
        this.sciAddr = sciAddr;
    }

    public String getSciAddr() 
    {
        return sciAddr;
    }

    public void setSciLamt(BigDecimal sciLamt) 
    {
        this.sciLamt = sciLamt;
    }

    public BigDecimal getSciLamt() 
    {
        return sciLamt;
    }

    public void setSciRmk(String sciRmk) 
    {
        this.sciRmk = sciRmk;
    }

    public String getSciRmk() 
    {
        return sciRmk;
    }

    public void setSciEmail(String sciEmail) 
    {
        this.sciEmail = sciEmail;
    }

    public String getSciEmail() 
    {
        return sciEmail;
    }

    public void setSciNation(String sciNation) 
    {
        this.sciNation = sciNation;
    }

    public String getSciNation() 
    {
        return sciNation;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sciMid", getSciMid())
            .append("sciId", getSciId())
            .append("sciMcd", getSciMcd())
            .append("sciTyp", getSciTyp())
            .append("sciTitle", getSciTitle())
            .append("sciMan", getSciMan())
            .append("sciFp", getSciFp())
            .append("sciProvince", getSciProvince())
            .append("sciCity", getSciCity())
            .append("sciDistrict", getSciDistrict())
            .append("sciExaddr", getSciExaddr())
            .append("sciMp", getSciMp())
            .append("sciAddr", getSciAddr())
            .append("sciLamt", getSciLamt())
            .append("sciRmk", getSciRmk())
            .append("sciEmail", getSciEmail())
            .append("sciNation", getSciNation())
            .toString();
    }
}
