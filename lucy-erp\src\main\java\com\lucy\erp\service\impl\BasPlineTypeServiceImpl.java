package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasPlineTypeMapper;
import com.lucy.erp.domain.BasPlineType;
import com.lucy.erp.service.IBasPlineTypeService;

/**
 * 产线字典Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasPlineTypeServiceImpl implements IBasPlineTypeService
{
    @Autowired
    private BasPlineTypeMapper basPlineTypeMapper;

    /**
     * 查询产线字典
     * 
     * @param id 产线字典主键
     * @return 产线字典
     */
    @Override
    public BasPlineType selectBasPlineTypeById(Long id)
    {
        return basPlineTypeMapper.selectBasPlineTypeById(id);
    }

    /**
     * 查询产线字典列表
     * 
     * @param basPlineType 产线字典
     * @return 产线字典
     */
    @Override
    public List<BasPlineType> selectBasPlineTypeList(BasPlineType basPlineType)
    {
        return basPlineTypeMapper.selectBasPlineTypeList(basPlineType);
    }

    /**
     * 新增产线字典
     * 
     * @param basPlineType 产线字典
     * @return 结果
     */
    @Override
    public int insertBasPlineType(BasPlineType basPlineType)
    {
        return basPlineTypeMapper.insertBasPlineType(basPlineType);
    }

    /**
     * 修改产线字典
     * 
     * @param basPlineType 产线字典
     * @return 结果
     */
    @Override
    public int updateBasPlineType(BasPlineType basPlineType)
    {
        return basPlineTypeMapper.updateBasPlineType(basPlineType);
    }

    /**
     * 批量删除产线字典
     * 
     * @param ids 需要删除的产线字典主键
     * @return 结果
     */
    @Override
    public int deleteBasPlineTypeByIds(Long[] ids)
    {
        return basPlineTypeMapper.deleteBasPlineTypeByIds(ids);
    }

    /**
     * 删除产线字典信息
     * 
     * @param id 产线字典主键
     * @return 结果
     */
    @Override
    public int deleteBasPlineTypeById(Long id)
    {
        return basPlineTypeMapper.deleteBasPlineTypeById(id);
    }
}
