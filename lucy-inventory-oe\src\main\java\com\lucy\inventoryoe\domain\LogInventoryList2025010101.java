package com.lucy.inventoryoe.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 库存清单快照对象 log_inventory_list_2025010101
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public class LogInventoryList2025010101 extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilDmcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilTcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilSlcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilPpcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilBn;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilQty;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilOrgmcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lilBnEncrypt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLilDmcd(String lilDmcd) 
    {
        this.lilDmcd = lilDmcd;
    }

    public String getLilDmcd() 
    {
        return lilDmcd;
    }

    public void setLilTcd(String lilTcd) 
    {
        this.lilTcd = lilTcd;
    }

    public String getLilTcd() 
    {
        return lilTcd;
    }

    public void setLilSlcd(String lilSlcd) 
    {
        this.lilSlcd = lilSlcd;
    }

    public String getLilSlcd() 
    {
        return lilSlcd;
    }

    public void setLilPpcd(String lilPpcd) 
    {
        this.lilPpcd = lilPpcd;
    }

    public String getLilPpcd() 
    {
        return lilPpcd;
    }

    public void setLilBn(String lilBn) 
    {
        this.lilBn = lilBn;
    }

    public String getLilBn() 
    {
        return lilBn;
    }

    public void setLilQty(String lilQty) 
    {
        this.lilQty = lilQty;
    }

    public String getLilQty() 
    {
        return lilQty;
    }

    public void setLilOrgmcd(String lilOrgmcd) 
    {
        this.lilOrgmcd = lilOrgmcd;
    }

    public String getLilOrgmcd() 
    {
        return lilOrgmcd;
    }

    public void setLilBnEncrypt(String lilBnEncrypt) 
    {
        this.lilBnEncrypt = lilBnEncrypt;
    }

    public String getLilBnEncrypt() 
    {
        return lilBnEncrypt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lilDmcd", getLilDmcd())
            .append("lilTcd", getLilTcd())
            .append("lilSlcd", getLilSlcd())
            .append("lilPpcd", getLilPpcd())
            .append("lilBn", getLilBn())
            .append("lilQty", getLilQty())
            .append("lilOrgmcd", getLilOrgmcd())
            .append("lilBnEncrypt", getLilBnEncrypt())
            .toString();
    }
}
