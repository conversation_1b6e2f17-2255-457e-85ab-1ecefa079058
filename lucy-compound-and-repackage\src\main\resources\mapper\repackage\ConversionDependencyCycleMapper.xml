<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repackage.mapper.ConversionDependencyCycleMapper">
    
    <resultMap type="ConversionDependencyCycle" id="ConversionDependencyCycleResult">
        <result property="cycleId"    column="cycle_id"    />
        <result property="cyclePath"    column="cycle_path"    />
        <result property="materialList"    column="material_list"    />
        <result property="status"    column="status"    />
        <result property="resolvedMethod"    column="resolved_method"    />
        <result property="detectedAt"    column="detected_at"    />
        <result property="resolvedAt"    column="resolved_at"    />
    </resultMap>

    <sql id="selectConversionDependencyCycleVo">
        select cycle_id, cycle_path, material_list, status, resolved_method, detected_at, resolved_at from conversion_dependency_cycle
    </sql>

    <select id="selectConversionDependencyCycleList" parameterType="ConversionDependencyCycle" resultMap="ConversionDependencyCycleResult">
        <include refid="selectConversionDependencyCycleVo"/>
        <where>  
            <if test="cyclePath != null  and cyclePath != ''"> and cycle_path = #{cyclePath}</if>
            <if test="materialList != null  and materialList != ''"> and material_list = #{materialList}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="resolvedMethod != null  and resolvedMethod != ''"> and resolved_method = #{resolvedMethod}</if>
            <if test="detectedAt != null "> and detected_at = #{detectedAt}</if>
            <if test="resolvedAt != null "> and resolved_at = #{resolvedAt}</if>
        </where>
    </select>
    
    <select id="selectConversionDependencyCycleByCycleId" parameterType="Long" resultMap="ConversionDependencyCycleResult">
        <include refid="selectConversionDependencyCycleVo"/>
        where cycle_id = #{cycleId}
    </select>

    <insert id="insertConversionDependencyCycle" parameterType="ConversionDependencyCycle" useGeneratedKeys="true" keyProperty="cycleId">
        insert into conversion_dependency_cycle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cyclePath != null and cyclePath != ''">cycle_path,</if>
            <if test="materialList != null and materialList != ''">material_list,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="resolvedMethod != null">resolved_method,</if>
            <if test="detectedAt != null">detected_at,</if>
            <if test="resolvedAt != null">resolved_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cyclePath != null and cyclePath != ''">#{cyclePath},</if>
            <if test="materialList != null and materialList != ''">#{materialList},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="resolvedMethod != null">#{resolvedMethod},</if>
            <if test="detectedAt != null">#{detectedAt},</if>
            <if test="resolvedAt != null">#{resolvedAt},</if>
         </trim>
    </insert>

    <update id="updateConversionDependencyCycle" parameterType="ConversionDependencyCycle">
        update conversion_dependency_cycle
        <trim prefix="SET" suffixOverrides=",">
            <if test="cyclePath != null and cyclePath != ''">cycle_path = #{cyclePath},</if>
            <if test="materialList != null and materialList != ''">material_list = #{materialList},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="resolvedMethod != null">resolved_method = #{resolvedMethod},</if>
            <if test="detectedAt != null">detected_at = #{detectedAt},</if>
            <if test="resolvedAt != null">resolved_at = #{resolvedAt},</if>
        </trim>
        where cycle_id = #{cycleId}
    </update>

    <delete id="deleteConversionDependencyCycleByCycleId" parameterType="Long">
        delete from conversion_dependency_cycle where cycle_id = #{cycleId}
    </delete>

    <delete id="deleteConversionDependencyCycleByCycleIds" parameterType="String">
        delete from conversion_dependency_cycle where cycle_id in
        <foreach item="cycleId" collection="array" open="(" separator="," close=")">
            #{cycleId}
        </foreach>
    </delete>

    <delete id="deleteAllConversionDependencyCycle">
        delete from conversion_dependency_cycle
    </delete>
</mapper>