package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产成品包材领料主对象 log_repack_package_use_plan
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class LogRepackPackageUsePlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 领料主表id */
    private Long id;

    /** 领料单编号 */
    private String lrpupCd;

    /** 分装类型 */
    @Excel(name = "分装类型")
    private String lrpupTyp;

    /** 工单号 */
    @Excel(name = "工单号")
    private String lrpupPsn;

    /** 分装明细编号 */
    @Excel(name = "分装明细编号")
    private String lrpupPsnCd;

    /** 产成品包装sku编号 */
    @Excel(name = "产成品包装sku编号")
    private String lrpupMainSkuCd;

    /** 产成品数量 */
    @Excel(name = "产成品数量")
    private BigDecimal lrpupMainSkuQty;

    /** 包装工艺编号 */
    @Excel(name = "包装工艺编号")
    private String lrpupGcd;

    /** 包装材料SKU编号 */
    @Excel(name = "包装材料SKU编号")
    private String lrpupSkuCd;

    /** 包装材料使用数量 */
    @Excel(name = "包装材料使用数量")
    private BigDecimal lrpupSkuQty;

    /** 包装材料使用总数量 */
    @Excel(name = "包装材料使用总数量")
    private BigDecimal lrpupSkuTotalQty;

    /** 包装材料总金额 */
    @Excel(name = "包装材料总金额")
    private BigDecimal lrpupSkuTotalPrc;

    /** 领料单产生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领料单产生时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lrpupMainDate;

    /** 逆向数据标记 */
    @Excel(name = "逆向数据标记")
    private Integer flag;

    /** r_cre_kid */
    @Excel(name = "r_cre_kid")
    private String rCreKid;

    /** r_cre_dt */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "r_cre_dt", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** r_upd_kid */
    @Excel(name = "r_upd_kid")
    private String rUpdKid;

    /** r_upd_dt */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "r_upd_dt", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** r_v */
    @Excel(name = "r_v")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 正向领料单编号 */
    @Excel(name = "正向领料单编号")
    private String lrpupPcd;

    private String startDate;
    private String endDate;
    private String zti;

    public String getZti() {
        return zti;
    }

    public void setZti(String zti) {
        this.zti = zti;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLrpupCd(String lrpupCd) 
    {
        this.lrpupCd = lrpupCd;
    }

    public String getLrpupCd() 
    {
        return lrpupCd;
    }

    public void setLrpupTyp(String lrpupTyp) 
    {
        this.lrpupTyp = lrpupTyp;
    }

    public String getLrpupTyp() 
    {
        return lrpupTyp;
    }

    public void setLrpupPsn(String lrpupPsn) 
    {
        this.lrpupPsn = lrpupPsn;
    }

    public String getLrpupPsn() 
    {
        return lrpupPsn;
    }

    public void setLrpupPsnCd(String lrpupPsnCd) 
    {
        this.lrpupPsnCd = lrpupPsnCd;
    }

    public String getLrpupPsnCd() 
    {
        return lrpupPsnCd;
    }

    public void setLrpupMainSkuCd(String lrpupMainSkuCd) 
    {
        this.lrpupMainSkuCd = lrpupMainSkuCd;
    }

    public String getLrpupMainSkuCd() 
    {
        return lrpupMainSkuCd;
    }

    public void setLrpupMainSkuQty(BigDecimal lrpupMainSkuQty) 
    {
        this.lrpupMainSkuQty = lrpupMainSkuQty;
    }

    public BigDecimal getLrpupMainSkuQty() 
    {
        return lrpupMainSkuQty;
    }

    public void setLrpupGcd(String lrpupGcd) 
    {
        this.lrpupGcd = lrpupGcd;
    }

    public String getLrpupGcd() 
    {
        return lrpupGcd;
    }

    public void setLrpupSkuCd(String lrpupSkuCd) 
    {
        this.lrpupSkuCd = lrpupSkuCd;
    }

    public String getLrpupSkuCd() 
    {
        return lrpupSkuCd;
    }

    public void setLrpupSkuQty(BigDecimal lrpupSkuQty) 
    {
        this.lrpupSkuQty = lrpupSkuQty;
    }

    public BigDecimal getLrpupSkuQty() 
    {
        return lrpupSkuQty;
    }

    public void setLrpupSkuTotalQty(BigDecimal lrpupSkuTotalQty) 
    {
        this.lrpupSkuTotalQty = lrpupSkuTotalQty;
    }

    public BigDecimal getLrpupSkuTotalQty() 
    {
        return lrpupSkuTotalQty;
    }

    public void setLrpupSkuTotalPrc(BigDecimal lrpupSkuTotalPrc) 
    {
        this.lrpupSkuTotalPrc = lrpupSkuTotalPrc;
    }

    public BigDecimal getLrpupSkuTotalPrc() 
    {
        return lrpupSkuTotalPrc;
    }

    public void setLrpupMainDate(Date lrpupMainDate) 
    {
        this.lrpupMainDate = lrpupMainDate;
    }

    public Date getLrpupMainDate() 
    {
        return lrpupMainDate;
    }

    public void setFlag(Integer flag) 
    {
        this.flag = flag;
    }

    public Integer getFlag() 
    {
        return flag;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setLrpupPcd(String lrpupPcd) 
    {
        this.lrpupPcd = lrpupPcd;
    }

    public String getLrpupPcd() 
    {
        return lrpupPcd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lrpupCd", getLrpupCd())
            .append("lrpupTyp", getLrpupTyp())
            .append("lrpupPsn", getLrpupPsn())
            .append("lrpupPsnCd", getLrpupPsnCd())
            .append("lrpupMainSkuCd", getLrpupMainSkuCd())
            .append("lrpupMainSkuQty", getLrpupMainSkuQty())
            .append("lrpupGcd", getLrpupGcd())
            .append("lrpupSkuCd", getLrpupSkuCd())
            .append("lrpupSkuQty", getLrpupSkuQty())
            .append("lrpupSkuTotalQty", getLrpupSkuTotalQty())
            .append("lrpupSkuTotalPrc", getLrpupSkuTotalPrc())
            .append("lrpupMainDate", getLrpupMainDate())
            .append("flag", getFlag())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("lrpupPcd", getLrpupPcd())
            .toString();
    }
}
