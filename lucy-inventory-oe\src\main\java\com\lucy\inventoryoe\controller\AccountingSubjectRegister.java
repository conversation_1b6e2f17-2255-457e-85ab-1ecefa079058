package com.lucy.inventoryoe.controller;

import com.lucy.inventoryoe.domain.AccountingSubject;
import com.lucy.inventoryoe.service.IAccountingSubjectService;
import com.lucy.inventoryoe.service.impl.AccountingSubjectServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 财会计科目 Accounting subject   registry
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Component
public class AccountingSubjectRegister implements ApplicationRunner {


    @Autowired
    private IAccountingSubjectService iAccountingSubjectService;

    // 静态快捷访问
    public static AccountingSubject C_140301;         //无机原材料(140301)
    public static AccountingSubject C_140302;         // 有机原材料(140302)
    public static AccountingSubject C_140303; // 溶剂-原材料(140303)
    public static AccountingSubject C_140304;    //  辅料(140304)
    public static AccountingSubject C_140501; //无机化学(140501)
    public static AccountingSubject C_140502; //有机化学(140502)
    public static AccountingSubject C_140503;  // 试剂(代理品牌)(140503)
    public static AccountingSubject C_140504;  // 溶剂-商品(140504)
    public static AccountingSubject C_********;  // 生化设备(********)
    public static AccountingSubject C_********;  // 生化其他(********)
    public static AccountingSubject C_140506;  // 耗材(140506)
    public static AccountingSubject C_140801;  // 委托加工费(140801)
    public static AccountingSubject C_141101;  // 低值易耗品(141101)
    public static AccountingSubject C_141102;  //物流耗材(141102)
    public static AccountingSubject C_141103;  // 生产耗材(141103)
    public static AccountingSubject C_1601;  // 固定资产(1601)
    public static AccountingSubject C_1701;  // 无形资产(1701)
    public static AccountingSubject C_押金;  // 钢瓶(押金)
    public static AccountingSubject C_;  // -(-)


    /**
     * 每天执行一次，刷新科目缓存
     */
    @Scheduled(fixedRate = ********) // 24 小时刷新一次
    @Override
    public void run(ApplicationArguments args) throws Exception {
        iAccountingSubjectService.loadData();

        C_140301 = AccountingSubjectServiceImpl.get("140301");
        C_140302 = AccountingSubjectServiceImpl.get("140302");
        C_140303 = AccountingSubjectServiceImpl.get("140303");
        C_140304 = AccountingSubjectServiceImpl.get("140304");
        C_140501 = AccountingSubjectServiceImpl.get("140501");
        C_140502 = AccountingSubjectServiceImpl.get("140502");
        C_140503 = AccountingSubjectServiceImpl.get("140503");
        C_140504 = AccountingSubjectServiceImpl.get("140504");
        C_******** = AccountingSubjectServiceImpl.get("********");
        C_******** = AccountingSubjectServiceImpl.get("********");

        C_140506 = AccountingSubjectServiceImpl.get("140506");
        C_140801 = AccountingSubjectServiceImpl.get("140801");
        C_141101 = AccountingSubjectServiceImpl.get("141101");
        C_141102 = AccountingSubjectServiceImpl.get("141102");
        C_141103 = AccountingSubjectServiceImpl.get("141103");
        C_1601 = AccountingSubjectServiceImpl.get("1601");
        C_1701 = AccountingSubjectServiceImpl.get("1701");
        C_押金 = AccountingSubjectServiceImpl.get("押金");
        C_ = AccountingSubjectServiceImpl.get("-");

    }
}
