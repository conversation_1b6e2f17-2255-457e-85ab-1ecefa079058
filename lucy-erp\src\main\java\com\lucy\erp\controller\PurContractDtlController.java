package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.PurContractDtl;
import com.lucy.erp.service.IPurContractDtlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采购_合同_明细
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/system/purdtl")
public class PurContractDtlController extends BaseController
{
    @Autowired
    private IPurContractDtlService purContractDtlService;

    /**
     * 查询采购_合同_明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:purdtl:list')")
    @GetMapping("/list")
    public TableDataInfo list(PurContractDtl purContractDtl)
    {
        startPage();
        List<PurContractDtl> list = purContractDtlService.selectPurContractDtlList(purContractDtl);
        return getDataTable(list);
    }

    /**
     * 导出采购_合同_明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:purdtl:export')")
    @Log(title = "采购_合同_明细 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PurContractDtl purContractDtl)
    {
        List<PurContractDtl> list = purContractDtlService.selectPurContractDtlList(purContractDtl);
        ExcelUtil<PurContractDtl> util = new ExcelUtil<PurContractDtl>(PurContractDtl.class);
        util.exportExcel(response, list, "采购_合同_明细数据");
    }

    /**
     * 获取采购_合同_明细
详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:purdtl:query')")
    @GetMapping(value = "/{pcdCd}")
    public AjaxResult getInfo(@PathVariable("pcdCd") String pcdCd)
    {
        return success(purContractDtlService.selectPurContractDtlByPcdCd(pcdCd));
    }

    /**
     * 新增采购_合同_明细

     */
    @PreAuthorize("@ss.hasPermi('system:purdtl:add')")
    @Log(title = "采购_合同_明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PurContractDtl purContractDtl)
    {
        return toAjax(purContractDtlService.insertPurContractDtl(purContractDtl));
    }

    /**
     * 修改采购_合同_明细

     */
    @PreAuthorize("@ss.hasPermi('system:purdtl:edit')")
    @Log(title = "采购_合同_明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PurContractDtl purContractDtl)
    {
        return toAjax(purContractDtlService.updatePurContractDtl(purContractDtl));
    }

    /**
     * 删除采购_合同_明细

     */
    @PreAuthorize("@ss.hasPermi('system:purdtl:remove')")
    @Log(title = "采购_合同_明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{pcdCds}")
    public AjaxResult remove(@PathVariable String[] pcdCds)
    {
        return toAjax(purContractDtlService.deletePurContractDtlByPcdCds(pcdCds));
    }
}
