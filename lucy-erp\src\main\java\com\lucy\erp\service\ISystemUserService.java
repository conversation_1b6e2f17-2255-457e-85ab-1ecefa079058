package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.SystemUser;

/**
 * 系统_用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface ISystemUserService 
{
    /**
     * 查询系统_用户
     * 
     * @param id 系统_用户主键
     * @return 系统_用户
     */
    public SystemUser selectSystemUserById(Long id);

    SystemUser selectSystemUserByLogno(String logno);

    SystemUser selectSystemUserByEno(String eno);

    SystemUser selectSystemUserManager(String logno);

    /**
     * 查询系统_用户列表
     * 
     * @param systemUser 系统_用户
     * @return 系统_用户集合
     */
    public List<SystemUser> selectSystemUserList(SystemUser systemUser);

    /**
     * 新增系统_用户
     * 
     * @param systemUser 系统_用户
     * @return 结果
     */
    public int insertSystemUser(SystemUser systemUser);

    /**
     * 修改系统_用户
     * 
     * @param systemUser 系统_用户
     * @return 结果
     */
    public int updateSystemUser(SystemUser systemUser);

    /**
     * 批量删除系统_用户
     * 
     * @param ids 需要删除的系统_用户主键集合
     * @return 结果
     */
    public int deleteSystemUserByIds(Long[] ids);

    /**
     * 删除系统_用户信息
     * 
     * @param id 系统_用户主键
     * @return 结果
     */
    public int deleteSystemUserById(Long id);
}
