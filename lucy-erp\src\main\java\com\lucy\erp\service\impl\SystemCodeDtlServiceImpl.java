package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.SystemCodeDtlMapper;
import com.lucy.erp.domain.SystemCodeDtl;
import com.lucy.erp.service.ISystemCodeDtlService;

/**
 * 系统_字典Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@DataSource(DataSourceType.ERP)
public class SystemCodeDtlServiceImpl implements ISystemCodeDtlService 
{
    @Autowired
    private SystemCodeDtlMapper systemCodeDtlMapper;

    /**
     * 查询系统_字典
     * 
     * @param id 系统_字典主键
     * @return 系统_字典
     */
    @Override
    public SystemCodeDtl selectSystemCodeDtlById(Long id)
    {
        return systemCodeDtlMapper.selectSystemCodeDtlById(id);
    }

    /**
     * 查询系统_字典列表
     * 
     * @param systemCodeDtl 系统_字典
     * @return 系统_字典
     */
    @Override
    public List<SystemCodeDtl> selectSystemCodeDtlList(SystemCodeDtl systemCodeDtl)
    {
        return systemCodeDtlMapper.selectSystemCodeDtlList(systemCodeDtl);
    }

    /**
     * 新增系统_字典
     * 
     * @param systemCodeDtl 系统_字典
     * @return 结果
     */
    @Override
    public int insertSystemCodeDtl(SystemCodeDtl systemCodeDtl)
    {
        return systemCodeDtlMapper.insertSystemCodeDtl(systemCodeDtl);
    }

    /**
     * 修改系统_字典
     * 
     * @param systemCodeDtl 系统_字典
     * @return 结果
     */
    @Override
    public int updateSystemCodeDtl(SystemCodeDtl systemCodeDtl)
    {
        return systemCodeDtlMapper.updateSystemCodeDtl(systemCodeDtl);
    }

    /**
     * 批量删除系统_字典
     * 
     * @param ids 需要删除的系统_字典主键
     * @return 结果
     */
    @Override
    public int deleteSystemCodeDtlByIds(Long[] ids)
    {
        return systemCodeDtlMapper.deleteSystemCodeDtlByIds(ids);
    }

    /**
     * 删除系统_字典信息
     * 
     * @param id 系统_字典主键
     * @return 结果
     */
    @Override
    public int deleteSystemCodeDtlById(Long id)
    {
        return systemCodeDtlMapper.deleteSystemCodeDtlById(id);
    }
}
