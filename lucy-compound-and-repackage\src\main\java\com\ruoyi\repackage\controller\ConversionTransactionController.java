package com.ruoyi.repackage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.repackage.domain.ConversionTransaction;
import com.ruoyi.repackage.service.IConversionTransactionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分装事务Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/repackage/transaction")
public class ConversionTransactionController extends BaseController
{
    @Autowired
    private IConversionTransactionService conversionTransactionService;

    /**
     * 查询分装事务列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:transaction:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConversionTransaction conversionTransaction)
    {
        startPage();
        List<ConversionTransaction> list = conversionTransactionService.selectConversionTransactionList(conversionTransaction);
        return getDataTable(list);
    }

    /**
     * 导出分装事务列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:transaction:export')")
    @Log(title = "分装事务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConversionTransaction conversionTransaction)
    {
        List<ConversionTransaction> list = conversionTransactionService.selectConversionTransactionList(conversionTransaction);
        ExcelUtil<ConversionTransaction> util = new ExcelUtil<ConversionTransaction>(ConversionTransaction.class);
        util.exportExcel(response, list, "分装事务数据");
    }

    /**
     * 获取分装事务详细信息
     */
    @PreAuthorize("@ss.hasPermi('repackage:transaction:query')")
    @GetMapping(value = "/{transId}")
    public AjaxResult getInfo(@PathVariable("transId") Long transId)
    {
        return success(conversionTransactionService.selectConversionTransactionByTransId(transId));
    }

    /**
     * 新增分装事务
     */
    @PreAuthorize("@ss.hasPermi('repackage:transaction:add')")
    @Log(title = "分装事务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConversionTransaction conversionTransaction)
    {
        return toAjax(conversionTransactionService.insertConversionTransaction(conversionTransaction));
    }

    /**
     * 修改分装事务
     */
    @PreAuthorize("@ss.hasPermi('repackage:transaction:edit')")
    @Log(title = "分装事务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConversionTransaction conversionTransaction)
    {
        return toAjax(conversionTransactionService.updateConversionTransaction(conversionTransaction));
    }

    /**
     * 删除分装事务
     */
    @PreAuthorize("@ss.hasPermi('repackage:transaction:remove')")
    @Log(title = "分装事务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{transIds}")
    public AjaxResult remove(@PathVariable Long[] transIds)
    {
        return toAjax(conversionTransactionService.deleteConversionTransactionByTransIds(transIds));
    }
}
