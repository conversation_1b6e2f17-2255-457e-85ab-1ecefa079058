<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.SystemCodeDtlMapper">
    
    <resultMap type="SystemCodeDtl" id="SystemCodeDtlResult">
        <result property="id"    column="id"    />
        <result property="scdTid"    column="scd_tid"    />
        <result property="scdTcd"    column="scd_tcd"    />
        <result property="scdUptcd"    column="scd_uptcd"    />
        <result property="scdUpid"    column="scd_upid"    />
        <result property="scdUpcd"    column="scd_upcd"    />
        <result property="scdLvl"    column="scd_lvl"    />
        <result property="scdCd"    column="scd_cd"    />
        <result property="scdNm"    column="scd_nm"    />
        <result property="scdSortno"    column="scd_sortno"    />
        <result property="scdAdc1"    column="scd_adc1"    />
        <result property="scdAdc2"    column="scd_adc2"    />
        <result property="scdAdc3"    column="scd_adc3"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectSystemCodeDtlVo">
        select id, scd_tid, scd_tcd, scd_uptcd, scd_upid, scd_upcd, scd_lvl, scd_cd, scd_nm, scd_sortno, scd_adc1, scd_adc2, scd_adc3, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from system_code_dtl
    </sql>

    <select id="selectSystemCodeDtlList" parameterType="SystemCodeDtl" resultMap="SystemCodeDtlResult">
        <include refid="selectSystemCodeDtlVo"/>
        <where>  
            <if test="scdTid != null "> and scd_tid = #{scdTid}</if>
            <if test="scdTcd != null  and scdTcd != ''"> and scd_tcd = #{scdTcd}</if>
            <if test="scdUptcd != null  and scdUptcd != ''"> and scd_uptcd = #{scdUptcd}</if>
            <if test="scdUpid != null "> and scd_upid = #{scdUpid}</if>
            <if test="scdUpcd != null  and scdUpcd != ''"> and scd_upcd = #{scdUpcd}</if>
            <if test="scdLvl != null "> and scd_lvl = #{scdLvl}</if>
            <if test="scdCd != null  and scdCd != ''"> and scd_cd = #{scdCd}</if>
            <if test="scdNm != null  and scdNm != ''"> and scd_nm = #{scdNm}</if>
            <if test="scdSortno != null "> and scd_sortno = #{scdSortno}</if>
            <if test="scdAdc1 != null  and scdAdc1 != ''"> and scd_adc1 = #{scdAdc1}</if>
            <if test="scdAdc2 != null  and scdAdc2 != ''"> and scd_adc2 = #{scdAdc2}</if>
            <if test="scdAdc3 != null  and scdAdc3 != ''"> and scd_adc3 = #{scdAdc3}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null  and rDel != ''"> and r_del = #{rDel}</if>
        </where>
    </select>
    
    <select id="selectSystemCodeDtlById" parameterType="Long" resultMap="SystemCodeDtlResult">
        <include refid="selectSystemCodeDtlVo"/>
        where id = #{id}
    </select>

    <insert id="insertSystemCodeDtl" parameterType="SystemCodeDtl" useGeneratedKeys="true" keyProperty="id">
        insert into system_code_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scdTid != null">scd_tid,</if>
            <if test="scdTcd != null">scd_tcd,</if>
            <if test="scdUptcd != null">scd_uptcd,</if>
            <if test="scdUpid != null">scd_upid,</if>
            <if test="scdUpcd != null">scd_upcd,</if>
            <if test="scdLvl != null">scd_lvl,</if>
            <if test="scdCd != null">scd_cd,</if>
            <if test="scdNm != null">scd_nm,</if>
            <if test="scdSortno != null">scd_sortno,</if>
            <if test="scdAdc1 != null">scd_adc1,</if>
            <if test="scdAdc2 != null">scd_adc2,</if>
            <if test="scdAdc3 != null">scd_adc3,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null and rDel != ''">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scdTid != null">#{scdTid},</if>
            <if test="scdTcd != null">#{scdTcd},</if>
            <if test="scdUptcd != null">#{scdUptcd},</if>
            <if test="scdUpid != null">#{scdUpid},</if>
            <if test="scdUpcd != null">#{scdUpcd},</if>
            <if test="scdLvl != null">#{scdLvl},</if>
            <if test="scdCd != null">#{scdCd},</if>
            <if test="scdNm != null">#{scdNm},</if>
            <if test="scdSortno != null">#{scdSortno},</if>
            <if test="scdAdc1 != null">#{scdAdc1},</if>
            <if test="scdAdc2 != null">#{scdAdc2},</if>
            <if test="scdAdc3 != null">#{scdAdc3},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null and rDel != ''">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateSystemCodeDtl" parameterType="SystemCodeDtl">
        update system_code_dtl
        <trim prefix="SET" suffixOverrides=",">
            <if test="scdTid != null">scd_tid = #{scdTid},</if>
            <if test="scdTcd != null">scd_tcd = #{scdTcd},</if>
            <if test="scdUptcd != null">scd_uptcd = #{scdUptcd},</if>
            <if test="scdUpid != null">scd_upid = #{scdUpid},</if>
            <if test="scdUpcd != null">scd_upcd = #{scdUpcd},</if>
            <if test="scdLvl != null">scd_lvl = #{scdLvl},</if>
            <if test="scdCd != null">scd_cd = #{scdCd},</if>
            <if test="scdNm != null">scd_nm = #{scdNm},</if>
            <if test="scdSortno != null">scd_sortno = #{scdSortno},</if>
            <if test="scdAdc1 != null">scd_adc1 = #{scdAdc1},</if>
            <if test="scdAdc2 != null">scd_adc2 = #{scdAdc2},</if>
            <if test="scdAdc3 != null">scd_adc3 = #{scdAdc3},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null and rDel != ''">r_del = #{rDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSystemCodeDtlById" parameterType="Long">
        delete from system_code_dtl where id = #{id}
    </delete>

    <delete id="deleteSystemCodeDtlByIds" parameterType="String">
        delete from system_code_dtl where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>