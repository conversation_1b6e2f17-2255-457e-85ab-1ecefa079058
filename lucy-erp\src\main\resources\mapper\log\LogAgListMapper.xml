<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogAgListMapper">
    
    <resultMap type="LogAgList" id="LogAgListResult">
        <result property="id"    column="id"    />
        <result property="lalCd"    column="lal_cd"    />
        <result property="lalBizcd"    column="lal_bizcd"    />
        <result property="lalPcd"    column="lal_pcd"    />
        <result property="lalPnm"    column="lal_pnm"    />
        <result property="lalCas"    column="lal_cas"    />
        <result property="lalPpcd"    column="lal_ppcd"    />
        <result property="lalAgqty"    column="lal_agqty"    />
        <result property="lalAgamt"    column="lal_agamt"    />
        <result property="lalIqty"    column="lal_iqty"    />
        <result property="lalIamt"    column="lal_iamt"    />
        <result property="lalSltyp"    column="lal_sltyp"    />
        <result property="lalDmcd"    column="lal_dmcd"    />
        <result property="lalSlcd"    column="lal_slcd"    />
        <result property="lalPlanslcd"    column="lal_planslcd"    />
        <result property="lalUnitp"    column="lal_unitp"    />
        <result property="lalBit"    column="lal_bit"    />
        <result property="lalIspart"    column="lal_ispart"    />
        <result property="lalBn"    column="lal_bn"    />
        <result property="lalPbn"    column="lal_pbn"    />
        <result property="lalSbn"    column="lal_sbn"    />
        <result property="lalPsn"    column="lal_psn"    />
        <result property="lalSuptyp"    column="lal_suptyp"    />
        <result property="lalCompanycd"    column="lal_companycd"    />
        <result property="lalUcd"    column="lal_ucd"    />
        <result property="lalArriveCd"    column="lal_arrive_cd"    />
        <result property="lalRmk"    column="lal_rmk"    />
        <result property="lalBndt"    column="lal_bndt"    />
        <result property="lalPurcycl"    column="lal_purcycl"    />
        <result property="lalTyp"    column="lal_typ"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lalOcdInvcar"    column="lal_ocd_invcar"    />
        <result property="lalOcpcdInvcar"    column="lal_ocpcd_invcar"    />
        <result property="lalInvcarAbnormal"    column="lal_invcar_abnormal"    />
        <result property="lalOrgmcd"    column="lal_orgmcd"    />
        <result property="lalBnEncrypt"    column="lal_bn_encrypt"    />
        <result property="lalCurrency"    column="lal_currency"    />
        <result property="lalExchange"    column="lal_exchange"    />
        <result property="lalTn"    column="lal_tn"    />
        <result property="paramkeysId"    column="paramKeys_id"    />
        <result property="paramKeys"    column="paramKeys"    />
        <result property="paramkeysInsideId"    column="paramKeys_inside_id"    />
        <result property="paramkeysOld"    column="paramKeys_old"    />
        <result property="lalOutRmk"    column="lal_out_rmk"    />
        <result property="lopId"    column="lop_id"    />
        <result property="lalFinFlag"    column="lal_fin_flag"    />
        <result property="lalInKid"    column="lal_in_kid"    />
        <result property="lalOpKid"    column="lal_op_kid"    />
        <result property="lalInvoiceUnitp"    column="lal_invoice_unitp"    />
        <result property="lalGroupCost"    column="lal_group_cost"    />
        <result property="lalLogisticsCost"    column="lal_logistics_cost"    />
        <result property="lalRdId"    column="lal_rd_id"    />
    </resultMap>

    <sql id="selectLogAgListVo">
        select id, lal_cd, lal_bizcd, lal_pcd, lal_pnm, lal_cas, lal_ppcd, lal_agqty, lal_agamt, lal_iqty, lal_iamt, lal_sltyp, lal_dmcd, lal_slcd, lal_planslcd, lal_unitp, lal_bit, lal_ispart, lal_bn, lal_pbn, lal_sbn, lal_psn, lal_suptyp, lal_companycd, lal_ucd, lal_arrive_cd, lal_rmk, lal_bndt, lal_purcycl, lal_typ, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, lal_ocd_invcar, lal_ocpcd_invcar, lal_invcar_abnormal, lal_orgmcd, lal_bn_encrypt, lal_currency, lal_exchange, lal_tn, paramKeys_id, paramKeys, paramKeys_inside_id, paramKeys_old, lal_out_rmk, lop_id, lal_fin_flag, lal_in_kid, lal_op_kid, lal_invoice_unitp, lal_group_cost, lal_logistics_cost, lal_rd_id from log_ag_list
    </sql>

    <select id="selectLogAgListList" parameterType="LogAgList" resultMap="LogAgListResult">
        <include refid="selectLogAgListVo"/>
        <where>  
            <if test="lalBizcd != null  and lalBizcd != ''"> and lal_bizcd = #{lalBizcd}</if>
            <if test="lalPcd != null  and lalPcd != ''"> and lal_pcd = #{lalPcd}</if>
            <if test="lalPnm != null  and lalPnm != ''"> and lal_pnm = #{lalPnm}</if>
            <if test="lalCas != null  and lalCas != ''"> and lal_cas = #{lalCas}</if>
            <if test="lalPpcd != null  and lalPpcd != ''"> and lal_ppcd = #{lalPpcd}</if>
            <if test="lalAgqty != null "> and lal_agqty = #{lalAgqty}</if>
            <if test="lalAgamt != null "> and lal_agamt = #{lalAgamt}</if>
            <if test="lalIqty != null "> and lal_iqty = #{lalIqty}</if>
            <if test="lalIamt != null "> and lal_iamt = #{lalIamt}</if>
            <if test="lalSltyp != null  and lalSltyp != ''"> and lal_sltyp = #{lalSltyp}</if>
            <if test="lalDmcd != null  and lalDmcd != ''"> and lal_dmcd = #{lalDmcd}</if>
            <if test="lalSlcd != null  and lalSlcd != ''"> and lal_slcd = #{lalSlcd}</if>
            <if test="lalPlanslcd != null  and lalPlanslcd != ''"> and lal_planslcd = #{lalPlanslcd}</if>
            <if test="lalUnitp != null "> and lal_unitp = #{lalUnitp}</if>
            <if test="lalBit != null "> and lal_bit = #{lalBit}</if>
            <if test="lalIspart != null "> and lal_ispart = #{lalIspart}</if>
            <if test="lalPbn != null  and lalPbn != ''"> and lal_pbn = #{lalPbn}</if>
            <if test="lalSbn != null  and lalSbn != ''"> and lal_sbn = #{lalSbn}</if>
            <if test="lalPsn != null  and lalPsn != ''"> and lal_psn = #{lalPsn}</if>
            <if test="lalSuptyp != null  and lalSuptyp != ''"> and lal_suptyp = #{lalSuptyp}</if>
            <if test="lalCompanycd != null  and lalCompanycd != ''"> and lal_companycd = #{lalCompanycd}</if>
            <if test="lalUcd != null  and lalUcd != ''"> and lal_ucd = #{lalUcd}</if>
            <if test="lalArriveCd != null  and lalArriveCd != ''"> and lal_arrive_cd = #{lalArriveCd}</if>
            <if test="lalRmk != null  and lalRmk != ''"> and lal_rmk = #{lalRmk}</if>
            <if test="lalBndt != null  and lalBndt != ''"> and lal_bndt = #{lalBndt}</if>
            <if test="lalPurcycl != null  and lalPurcycl != ''"> and lal_purcycl = #{lalPurcycl}</if>
            <if test="lalTyp != null  and lalTyp != ''"> and lal_typ = #{lalTyp}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="lalOcdInvcar != null  and lalOcdInvcar != ''"> and lal_ocd_invcar = #{lalOcdInvcar}</if>
            <if test="lalOcpcdInvcar != null  and lalOcpcdInvcar != ''"> and lal_ocpcd_invcar = #{lalOcpcdInvcar}</if>
            <if test="lalInvcarAbnormal != null  and lalInvcarAbnormal != ''"> and lal_invcar_abnormal = #{lalInvcarAbnormal}</if>
            <if test="lalOrgmcd != null  and lalOrgmcd != ''"> and lal_orgmcd = #{lalOrgmcd}</if>
            <if test="lalBnEncrypt != null  and lalBnEncrypt != ''"> and lal_bn_encrypt = #{lalBnEncrypt}</if>
            <if test="lalCurrency != null  and lalCurrency != ''"> and lal_currency = #{lalCurrency}</if>
            <if test="lalExchange != null "> and lal_exchange = #{lalExchange}</if>
            <if test="lalTn != null  and lalTn != ''"> and lal_tn = #{lalTn}</if>
            <if test="paramkeysId != null "> and paramKeys_id = #{paramkeysId}</if>
            <if test="paramKeys != null  and paramKeys != ''"> and paramKeys = #{paramKeys}</if>
            <if test="paramkeysInsideId != null "> and paramKeys_inside_id = #{paramkeysInsideId}</if>
            <if test="paramkeysOld != null  and paramkeysOld != ''"> and paramKeys_old = #{paramkeysOld}</if>
            <if test="lalOutRmk != null  and lalOutRmk != ''"> and lal_out_rmk = #{lalOutRmk}</if>
            <if test="lopId != null  and lopId != ''"> and lop_id = #{lopId}</if>
            <if test="lalFinFlag != null "> and lal_fin_flag = #{lalFinFlag}</if>
            <if test="lalInKid != null  and lalInKid != ''"> and lal_in_kid = #{lalInKid}</if>
            <if test="lalOpKid != null  and lalOpKid != ''"> and lal_op_kid = #{lalOpKid}</if>
            <if test="lalInvoiceUnitp != null "> and lal_invoice_unitp = #{lalInvoiceUnitp}</if>
            <if test="lalGroupCost != null "> and lal_group_cost = #{lalGroupCost}</if>
            <if test="lalLogisticsCost != null "> and lal_logistics_cost = #{lalLogisticsCost}</if>
            <if test="lalRdId != null "> and lal_rd_id = #{lalRdId}</if>
        </where>
    </select>
    
    <select id="selectLogAgListByLalCd" parameterType="String" resultMap="LogAgListResult">
        <include refid="selectLogAgListVo"/>
        where lal_cd = #{lalCd}
    </select>

    <insert id="insertLogAgList" parameterType="LogAgList">
        insert into log_ag_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lalCd != null">lal_cd,</if>
            <if test="lalBizcd != null">lal_bizcd,</if>
            <if test="lalPcd != null">lal_pcd,</if>
            <if test="lalPnm != null">lal_pnm,</if>
            <if test="lalCas != null">lal_cas,</if>
            <if test="lalPpcd != null">lal_ppcd,</if>
            <if test="lalAgqty != null">lal_agqty,</if>
            <if test="lalAgamt != null">lal_agamt,</if>
            <if test="lalIqty != null">lal_iqty,</if>
            <if test="lalIamt != null">lal_iamt,</if>
            <if test="lalSltyp != null">lal_sltyp,</if>
            <if test="lalDmcd != null">lal_dmcd,</if>
            <if test="lalSlcd != null">lal_slcd,</if>
            <if test="lalPlanslcd != null">lal_planslcd,</if>
            <if test="lalUnitp != null">lal_unitp,</if>
            <if test="lalBit != null">lal_bit,</if>
            <if test="lalIspart != null">lal_ispart,</if>
            <if test="lalBn != null">lal_bn,</if>
            <if test="lalPbn != null">lal_pbn,</if>
            <if test="lalSbn != null">lal_sbn,</if>
            <if test="lalPsn != null">lal_psn,</if>
            <if test="lalSuptyp != null">lal_suptyp,</if>
            <if test="lalCompanycd != null">lal_companycd,</if>
            <if test="lalUcd != null">lal_ucd,</if>
            <if test="lalArriveCd != null">lal_arrive_cd,</if>
            <if test="lalRmk != null">lal_rmk,</if>
            <if test="lalBndt != null">lal_bndt,</if>
            <if test="lalPurcycl != null">lal_purcycl,</if>
            <if test="lalTyp != null">lal_typ,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="lalOcdInvcar != null">lal_ocd_invcar,</if>
            <if test="lalOcpcdInvcar != null">lal_ocpcd_invcar,</if>
            <if test="lalInvcarAbnormal != null">lal_invcar_abnormal,</if>
            <if test="lalOrgmcd != null">lal_orgmcd,</if>
            <if test="lalBnEncrypt != null">lal_bn_encrypt,</if>
            <if test="lalCurrency != null">lal_currency,</if>
            <if test="lalExchange != null">lal_exchange,</if>
            <if test="lalTn != null">lal_tn,</if>
            <if test="paramkeysId != null">paramKeys_id,</if>
            <if test="paramKeys != null">paramKeys,</if>
            <if test="paramkeysInsideId != null">paramKeys_inside_id,</if>
            <if test="paramkeysOld != null">paramKeys_old,</if>
            <if test="lalOutRmk != null">lal_out_rmk,</if>
            <if test="lopId != null">lop_id,</if>
            <if test="lalFinFlag != null">lal_fin_flag,</if>
            <if test="lalInKid != null">lal_in_kid,</if>
            <if test="lalOpKid != null">lal_op_kid,</if>
            <if test="lalInvoiceUnitp != null">lal_invoice_unitp,</if>
            <if test="lalGroupCost != null">lal_group_cost,</if>
            <if test="lalLogisticsCost != null">lal_logistics_cost,</if>
            <if test="lalRdId != null">lal_rd_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lalCd != null">#{lalCd},</if>
            <if test="lalBizcd != null">#{lalBizcd},</if>
            <if test="lalPcd != null">#{lalPcd},</if>
            <if test="lalPnm != null">#{lalPnm},</if>
            <if test="lalCas != null">#{lalCas},</if>
            <if test="lalPpcd != null">#{lalPpcd},</if>
            <if test="lalAgqty != null">#{lalAgqty},</if>
            <if test="lalAgamt != null">#{lalAgamt},</if>
            <if test="lalIqty != null">#{lalIqty},</if>
            <if test="lalIamt != null">#{lalIamt},</if>
            <if test="lalSltyp != null">#{lalSltyp},</if>
            <if test="lalDmcd != null">#{lalDmcd},</if>
            <if test="lalSlcd != null">#{lalSlcd},</if>
            <if test="lalPlanslcd != null">#{lalPlanslcd},</if>
            <if test="lalUnitp != null">#{lalUnitp},</if>
            <if test="lalBit != null">#{lalBit},</if>
            <if test="lalIspart != null">#{lalIspart},</if>
            <if test="lalBn != null">#{lalBn},</if>
            <if test="lalPbn != null">#{lalPbn},</if>
            <if test="lalSbn != null">#{lalSbn},</if>
            <if test="lalPsn != null">#{lalPsn},</if>
            <if test="lalSuptyp != null">#{lalSuptyp},</if>
            <if test="lalCompanycd != null">#{lalCompanycd},</if>
            <if test="lalUcd != null">#{lalUcd},</if>
            <if test="lalArriveCd != null">#{lalArriveCd},</if>
            <if test="lalRmk != null">#{lalRmk},</if>
            <if test="lalBndt != null">#{lalBndt},</if>
            <if test="lalPurcycl != null">#{lalPurcycl},</if>
            <if test="lalTyp != null">#{lalTyp},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="lalOcdInvcar != null">#{lalOcdInvcar},</if>
            <if test="lalOcpcdInvcar != null">#{lalOcpcdInvcar},</if>
            <if test="lalInvcarAbnormal != null">#{lalInvcarAbnormal},</if>
            <if test="lalOrgmcd != null">#{lalOrgmcd},</if>
            <if test="lalBnEncrypt != null">#{lalBnEncrypt},</if>
            <if test="lalCurrency != null">#{lalCurrency},</if>
            <if test="lalExchange != null">#{lalExchange},</if>
            <if test="lalTn != null">#{lalTn},</if>
            <if test="paramkeysId != null">#{paramkeysId},</if>
            <if test="paramKeys != null">#{paramKeys},</if>
            <if test="paramkeysInsideId != null">#{paramkeysInsideId},</if>
            <if test="paramkeysOld != null">#{paramkeysOld},</if>
            <if test="lalOutRmk != null">#{lalOutRmk},</if>
            <if test="lopId != null">#{lopId},</if>
            <if test="lalFinFlag != null">#{lalFinFlag},</if>
            <if test="lalInKid != null">#{lalInKid},</if>
            <if test="lalOpKid != null">#{lalOpKid},</if>
            <if test="lalInvoiceUnitp != null">#{lalInvoiceUnitp},</if>
            <if test="lalGroupCost != null">#{lalGroupCost},</if>
            <if test="lalLogisticsCost != null">#{lalLogisticsCost},</if>
            <if test="lalRdId != null">#{lalRdId},</if>
         </trim>
    </insert>

    <update id="updateLogAgList" parameterType="LogAgList">
        update log_ag_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lalBizcd != null">lal_bizcd = #{lalBizcd},</if>
            <if test="lalPcd != null">lal_pcd = #{lalPcd},</if>
            <if test="lalPnm != null">lal_pnm = #{lalPnm},</if>
            <if test="lalCas != null">lal_cas = #{lalCas},</if>
            <if test="lalPpcd != null">lal_ppcd = #{lalPpcd},</if>
            <if test="lalAgqty != null">lal_agqty = #{lalAgqty},</if>
            <if test="lalAgamt != null">lal_agamt = #{lalAgamt},</if>
            <if test="lalIqty != null">lal_iqty = #{lalIqty},</if>
            <if test="lalIamt != null">lal_iamt = #{lalIamt},</if>
            <if test="lalSltyp != null">lal_sltyp = #{lalSltyp},</if>
            <if test="lalDmcd != null">lal_dmcd = #{lalDmcd},</if>
            <if test="lalSlcd != null">lal_slcd = #{lalSlcd},</if>
            <if test="lalPlanslcd != null">lal_planslcd = #{lalPlanslcd},</if>
            <if test="lalUnitp != null">lal_unitp = #{lalUnitp},</if>
            <if test="lalBit != null">lal_bit = #{lalBit},</if>
            <if test="lalIspart != null">lal_ispart = #{lalIspart},</if>
            <if test="lalBn != null">lal_bn = #{lalBn},</if>
            <if test="lalPbn != null">lal_pbn = #{lalPbn},</if>
            <if test="lalSbn != null">lal_sbn = #{lalSbn},</if>
            <if test="lalPsn != null">lal_psn = #{lalPsn},</if>
            <if test="lalSuptyp != null">lal_suptyp = #{lalSuptyp},</if>
            <if test="lalCompanycd != null">lal_companycd = #{lalCompanycd},</if>
            <if test="lalUcd != null">lal_ucd = #{lalUcd},</if>
            <if test="lalArriveCd != null">lal_arrive_cd = #{lalArriveCd},</if>
            <if test="lalRmk != null">lal_rmk = #{lalRmk},</if>
            <if test="lalBndt != null">lal_bndt = #{lalBndt},</if>
            <if test="lalPurcycl != null">lal_purcycl = #{lalPurcycl},</if>
            <if test="lalTyp != null">lal_typ = #{lalTyp},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="lalOcdInvcar != null">lal_ocd_invcar = #{lalOcdInvcar},</if>
            <if test="lalOcpcdInvcar != null">lal_ocpcd_invcar = #{lalOcpcdInvcar},</if>
            <if test="lalInvcarAbnormal != null">lal_invcar_abnormal = #{lalInvcarAbnormal},</if>
            <if test="lalOrgmcd != null">lal_orgmcd = #{lalOrgmcd},</if>
            <if test="lalBnEncrypt != null">lal_bn_encrypt = #{lalBnEncrypt},</if>
            <if test="lalCurrency != null">lal_currency = #{lalCurrency},</if>
            <if test="lalExchange != null">lal_exchange = #{lalExchange},</if>
            <if test="lalTn != null">lal_tn = #{lalTn},</if>
            <if test="paramkeysId != null">paramKeys_id = #{paramkeysId},</if>
            <if test="paramKeys != null">paramKeys = #{paramKeys},</if>
            <if test="paramkeysInsideId != null">paramKeys_inside_id = #{paramkeysInsideId},</if>
            <if test="paramkeysOld != null">paramKeys_old = #{paramkeysOld},</if>
            <if test="lalOutRmk != null">lal_out_rmk = #{lalOutRmk},</if>
            <if test="lopId != null">lop_id = #{lopId},</if>
            <if test="lalFinFlag != null">lal_fin_flag = #{lalFinFlag},</if>
            <if test="lalInKid != null">lal_in_kid = #{lalInKid},</if>
            <if test="lalOpKid != null">lal_op_kid = #{lalOpKid},</if>
            <if test="lalInvoiceUnitp != null">lal_invoice_unitp = #{lalInvoiceUnitp},</if>
            <if test="lalGroupCost != null">lal_group_cost = #{lalGroupCost},</if>
            <if test="lalLogisticsCost != null">lal_logistics_cost = #{lalLogisticsCost},</if>
            <if test="lalRdId != null">lal_rd_id = #{lalRdId},</if>
        </trim>
        where lal_cd = #{lalCd}
    </update>

    <delete id="deleteLogAgListByLalCd" parameterType="String">
        delete from log_ag_list where lal_cd = #{lalCd}
    </delete>

    <delete id="deleteLogAgListByLalCds" parameterType="String">
        delete from log_ag_list where lal_cd in 
        <foreach item="lalCd" collection="array" open="(" separator="," close=")">
            #{lalCd}
        </foreach>
    </delete>
</mapper>