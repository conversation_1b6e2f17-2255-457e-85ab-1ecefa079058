package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogPtMst;

/**
 * 物流_生产跟踪_主
Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ILogPtMstService 
{
    /**
     * 查询物流_生产跟踪_主

     * 
     * @param lpmCd 物流_生产跟踪_主
主键
     * @return 物流_生产跟踪_主

     */
    public LogPtMst selectLogPtMstByLpmCd(String lpmCd);

    /**
     * 查询物流_生产跟踪_主
列表
     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 物流_生产跟踪_主
集合
     */
    public List<LogPtMst> selectLogPtMstList(LogPtMst logPtMst);

    /**
     * 新增物流_生产跟踪_主

     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 结果
     */
    public int insertLogPtMst(LogPtMst logPtMst);

    /**
     * 修改物流_生产跟踪_主

     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 结果
     */
    public int updateLogPtMst(LogPtMst logPtMst);

    /**
     * 批量删除物流_生产跟踪_主

     * 
     * @param lpmCds 需要删除的物流_生产跟踪_主
主键集合
     * @return 结果
     */
    public int deleteLogPtMstByLpmCds(String[] lpmCds);

    /**
     * 删除物流_生产跟踪_主
信息
     * 
     * @param lpmCd 物流_生产跟踪_主
主键
     * @return 结果
     */
    public int deleteLogPtMstByLpmCd(String lpmCd);
}
