package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogPtMst;
import com.lucy.erp.service.ILogPtMstService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 物流_生产跟踪_主
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/log/mst")
public class LogPtMstController extends BaseController
{
    @Autowired
    private ILogPtMstService logPtMstService;

    /**
     * 查询物流_生产跟踪_主
列表
     */
    @PreAuthorize("@ss.hasPermi('log:mst:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogPtMst logPtMst)
    {
        startPage();
        List<LogPtMst> list = logPtMstService.selectLogPtMstList(logPtMst);
        return getDataTable(list);
    }

    /**
     * 导出物流_生产跟踪_主
列表
     */
    @PreAuthorize("@ss.hasPermi('log:mst:export')")
    @Log(title = "物流_生产跟踪_主 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogPtMst logPtMst)
    {
        List<LogPtMst> list = logPtMstService.selectLogPtMstList(logPtMst);
        ExcelUtil<LogPtMst> util = new ExcelUtil<LogPtMst>(LogPtMst.class);
        util.exportExcel(response, list, "物流_生产跟踪_主 数据");
    }

    /**
     * 获取物流_生产跟踪_主
详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:mst:query')")
    @GetMapping(value = "/{lpmCd}")
    public AjaxResult getInfo(@PathVariable("lpmCd") String lpmCd)
    {
        return success(logPtMstService.selectLogPtMstByLpmCd(lpmCd));
    }

    /**
     * 新增物流_生产跟踪_主

     */
    @PreAuthorize("@ss.hasPermi('log:mst:add')")
    @Log(title = "物流_生产跟踪_主 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogPtMst logPtMst)
    {
        return toAjax(logPtMstService.insertLogPtMst(logPtMst));
    }

    /**
     * 修改物流_生产跟踪_主

     */
    @PreAuthorize("@ss.hasPermi('log:mst:edit')")
    @Log(title = "物流_生产跟踪_主 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogPtMst logPtMst)
    {
        return toAjax(logPtMstService.updateLogPtMst(logPtMst));
    }

    /**
     * 删除物流_生产跟踪_主

     */
    @PreAuthorize("@ss.hasPermi('log:mst:remove')")
    @Log(title = "物流_生产跟踪_主 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lpmCds}")
    public AjaxResult remove(@PathVariable String[] lpmCds)
    {
        return toAjax(logPtMstService.deleteLogPtMstByLpmCds(lpmCds));
    }
}
