package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.FinProdCode;

/**
 * 记账科目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface FinProdCodeMapper 
{
    /**
     * 查询记账科目
     * 
     * @param id 记账科目主键
     * @return 记账科目
     */
    public FinProdCode selectFinProdCodeById(Long id);

    /**
     * 查询记账科目列表
     * 
     * @param finProdCode 记账科目
     * @return 记账科目集合
     */
    public List<FinProdCode> selectFinProdCodeList(FinProdCode finProdCode);

    /**
     * 新增记账科目
     * 
     * @param finProdCode 记账科目
     * @return 结果
     */
    public int insertFinProdCode(FinProdCode finProdCode);

    /**
     * 修改记账科目
     * 
     * @param finProdCode 记账科目
     * @return 结果
     */
    public int updateFinProdCode(FinProdCode finProdCode);

    /**
     * 删除记账科目
     * 
     * @param id 记账科目主键
     * @return 结果
     */
    public int deleteFinProdCodeById(Long id);

    /**
     * 批量删除记账科目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinProdCodeByIds(Long[] ids);
}
