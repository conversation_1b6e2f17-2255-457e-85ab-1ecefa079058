package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogRepackPackageUseResultDtl;
import com.lucy.erp.domain.LogRepackResult;

/**
 * 产成品投料明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ILogRepackPackageUseResultDtlService 
{
    /**
     * 查询产成品投料明细
     * 
     * @param lrpurdMcd 产成品投料明细主键
     * @return 产成品投料明细
     */
    public LogRepackPackageUseResultDtl selectLogRepackPackageUseResultDtlByLrpurdMcd(String lrplrpurdMcdurdCd);

    /**
     * 查询产成品投料明细列表
     * 
     * @param logRepackPackageUseResultDtl 产成品投料明细
     * @return 产成品投料明细集合
     */
    public List<LogRepackPackageUseResultDtl> selectLogRepackPackageUseResultDtlList(LogRepackPackageUseResultDtl logRepackPackageUseResultDtl);

    /**
     * 查询产成品列表
     *
     * @param logRepackResult 产成品
     * @return 产成品集合
     */
    public List<LogRepackResult> selectLogRepackPackageUseResultDtlAndLrrList(LogRepackResult logRepackResult);

    /**
     * 新增产成品投料明细
     * 
     * @param logRepackPackageUseResultDtl 产成品投料明细
     * @return 结果
     */
    public int insertLogRepackPackageUseResultDtl(LogRepackPackageUseResultDtl logRepackPackageUseResultDtl);

    /**
     * 修改产成品投料明细
     * 
     * @param logRepackPackageUseResultDtl 产成品投料明细
     * @return 结果
     */
    public int updateLogRepackPackageUseResultDtl(LogRepackPackageUseResultDtl logRepackPackageUseResultDtl);

    /**
     * 批量删除产成品投料明细
     * 
     * @param lrpurdCds 需要删除的产成品投料明细主键集合
     * @return 结果
     */
    public int deleteLogRepackPackageUseResultDtlByLrpurdCds(String[] lrpurdCds);

    /**
     * 删除产成品投料明细信息
     * 
     * @param lrpurdCd 产成品投料明细主键
     * @return 结果
     */
    public int deleteLogRepackPackageUseResultDtlByLrpurdCd(String lrpurdCd);
}
