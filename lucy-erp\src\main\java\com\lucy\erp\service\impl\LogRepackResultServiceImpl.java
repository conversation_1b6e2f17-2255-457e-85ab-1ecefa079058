package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogRepackResultMapper;
import com.lucy.erp.domain.LogRepackResult;
import com.lucy.erp.service.ILogRepackResultService;

/**
 * 产成品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogRepackResultServiceImpl implements ILogRepackResultService 
{
    @Autowired
    private LogRepackResultMapper logRepackResultMapper;

    /**
     * 查询产成品
     * 
     * @param lrrCd 产成品主键
     * @return 产成品
     */
    @Override
    public LogRepackResult selectLogRepackResultByLrrCd(String lrrCd)
    {
        return logRepackResultMapper.selectLogRepackResultByLrrCd(lrrCd);
    }

    /**
     * 查询产成品列表
     * 
     * @param logRepackResult 产成品
     * @return 产成品
     */
    @Override
    public List<LogRepackResult> selectLogRepackResultList(LogRepackResult logRepackResult)
    {
        return logRepackResultMapper.selectLogRepackResultList(logRepackResult);
    }

    /**
     * 新增产成品
     * 
     * @param logRepackResult 产成品
     * @return 结果
     */
    @Override
    public int insertLogRepackResult(LogRepackResult logRepackResult)
    {
        return logRepackResultMapper.insertLogRepackResult(logRepackResult);
    }

    /**
     * 修改产成品
     * 
     * @param logRepackResult 产成品
     * @return 结果
     */
    @Override
    public int updateLogRepackResult(LogRepackResult logRepackResult)
    {
        return logRepackResultMapper.updateLogRepackResult(logRepackResult);
    }

    /**
     * 批量删除产成品
     * 
     * @param lrrCds 需要删除的产成品主键
     * @return 结果
     */
    @Override
    public int deleteLogRepackResultByLrrCds(String[] lrrCds)
    {
        return logRepackResultMapper.deleteLogRepackResultByLrrCds(lrrCds);
    }

    /**
     * 删除产成品信息
     * 
     * @param lrrCd 产成品主键
     * @return 结果
     */
    @Override
    public int deleteLogRepackResultByLrrCd(String lrrCd)
    {
        return logRepackResultMapper.deleteLogRepackResultByLrrCd(lrrCd);
    }
}
