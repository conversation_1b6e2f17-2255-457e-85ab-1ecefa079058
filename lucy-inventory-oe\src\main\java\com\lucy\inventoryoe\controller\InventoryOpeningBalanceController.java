package com.lucy.inventoryoe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.inventoryoe.domain.InventoryOpeningBalance;
import com.lucy.inventoryoe.service.IInventoryOpeningBalanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 库存期初汇总 Inventory opening balance summaryController
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@RestController
@RequestMapping("/lucy-inventoryoe/balance2")
public class InventoryOpeningBalanceController extends BaseController
{
    @Autowired
    private IInventoryOpeningBalanceService inventoryOpeningBalanceService;

    /**
     * 查询库存期初汇总 Inventory opening balance summary列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:balance2:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryOpeningBalance inventoryOpeningBalance)
    {
        startPage();
        List<InventoryOpeningBalance> list = inventoryOpeningBalanceService.selectInventoryOpeningBalanceList(inventoryOpeningBalance);
        return getDataTable(list);
    }

    /**
     * 导出库存期初汇总 Inventory opening balance summary列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:balance2:export')")
    @Log(title = "库存期初汇总 Inventory opening balance summary", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryOpeningBalance inventoryOpeningBalance)
    {
        List<InventoryOpeningBalance> list = inventoryOpeningBalanceService.selectInventoryOpeningBalanceList(inventoryOpeningBalance);
        ExcelUtil<InventoryOpeningBalance> util = new ExcelUtil<InventoryOpeningBalance>(InventoryOpeningBalance.class);
        util.exportExcel(response, list, "库存期初汇总 Inventory opening balance summary数据");
    }

    /**
     * 获取库存期初汇总 Inventory opening balance summary详细信息
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:balance2:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryOpeningBalanceService.selectInventoryOpeningBalanceById(id));
    }

    /**
     * 新增库存期初汇总 Inventory opening balance summary
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:balance2:add')")
    @Log(title = "库存期初汇总 Inventory opening balance summary", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryOpeningBalance inventoryOpeningBalance)
    {
        return toAjax(inventoryOpeningBalanceService.insertInventoryOpeningBalance(inventoryOpeningBalance));
    }

    /**
     * 修改库存期初汇总 Inventory opening balance summary
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:balance2:edit')")
    @Log(title = "库存期初汇总 Inventory opening balance summary", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryOpeningBalance inventoryOpeningBalance)
    {
        return toAjax(inventoryOpeningBalanceService.updateInventoryOpeningBalance(inventoryOpeningBalance));
    }

    /**
     * 删除库存期初汇总 Inventory opening balance summary
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:balance2:remove')")
    @Log(title = "库存期初汇总 Inventory opening balance summary", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inventoryOpeningBalanceService.deleteInventoryOpeningBalanceByIds(ids));
    }
}
