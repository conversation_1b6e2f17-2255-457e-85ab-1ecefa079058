<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.FinProdCodeMapper">
    
    <resultMap type="FinProdCode" id="FinProdCodeResult">
        <result property="id"    column="id"    />
        <result property="bpmCnm"    column="bpm_cnm"    />
        <result property="bpmCcd"    column="bpm_ccd"    />
        <result property="bpmSortnm"    column="bpm_sortnm"    />
        <result property="bpmSort"    column="bpm_sort"    />
        <result property="code"    column="code"    />
        <result property="lgcode"    column="lgcode"    />
    </resultMap>

    <sql id="selectFinProdCodeVo">
        select id, bpm_cnm, bpm_ccd, bpm_sortnm, bpm_sort, code, lgcode from financial_account_db.fin_prod_code
    </sql>

    <select id="selectFinProdCodeList" parameterType="FinProdCode" resultMap="FinProdCodeResult">
        <include refid="selectFinProdCodeVo"/>
        <where>  
            <if test="bpmCnm != null  and bpmCnm != ''"> and bpm_cnm = #{bpmCnm}</if>
            <if test="bpmCcd != null  and bpmCcd != ''"> and bpm_ccd = #{bpmCcd}</if>
            <if test="bpmSortnm != null  and bpmSortnm != ''"> and bpm_sortnm = #{bpmSortnm}</if>
            <if test="bpmSort != null  and bpmSort != ''"> and bpm_sort = #{bpmSort}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="lgcode != null  and lgcode != ''"> and lgcode = #{lgcode}</if>
        </where>
    </select>
    
    <select id="selectFinProdCodeById" parameterType="Long" resultMap="FinProdCodeResult">
        <include refid="selectFinProdCodeVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinProdCode" parameterType="FinProdCode" useGeneratedKeys="true" keyProperty="id">
        insert into financial_account_db.fin_prod_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bpmCnm != null">bpm_cnm,</if>
            <if test="bpmCcd != null">bpm_ccd,</if>
            <if test="bpmSortnm != null">bpm_sortnm,</if>
            <if test="bpmSort != null">bpm_sort,</if>
            <if test="code != null">code,</if>
            <if test="lgcode != null">lgcode,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bpmCnm != null">#{bpmCnm},</if>
            <if test="bpmCcd != null">#{bpmCcd},</if>
            <if test="bpmSortnm != null">#{bpmSortnm},</if>
            <if test="bpmSort != null">#{bpmSort},</if>
            <if test="code != null">#{code},</if>
            <if test="lgcode != null">#{lgcode},</if>
         </trim>
    </insert>

    <update id="updateFinProdCode" parameterType="FinProdCode">
        update financial_account_db.fin_prod_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="bpmCnm != null">bpm_cnm = #{bpmCnm},</if>
            <if test="bpmCcd != null">bpm_ccd = #{bpmCcd},</if>
            <if test="bpmSortnm != null">bpm_sortnm = #{bpmSortnm},</if>
            <if test="bpmSort != null">bpm_sort = #{bpmSort},</if>
            <if test="code != null">code = #{code},</if>
            <if test="lgcode != null">lgcode = #{lgcode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinProdCodeById" parameterType="Long">
        delete from financial_account_db.fin_prod_code where id = #{id}
    </delete>

    <delete id="deleteFinProdCodeByIds" parameterType="String">
        delete from financial_account_db.fin_prod_code where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>