package com.lucy.inventoryoe.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.lucy.assetowner.domain.AssetOwnerMaster;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.lucy.inventoryoe.mapper.AccountingSubjectMapper;
import com.lucy.inventoryoe.domain.AccountingSubject;
import com.lucy.inventoryoe.service.IAccountingSubjectService;

/**
 * 会计科目 Accounting subject masterService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class AccountingSubjectServiceImpl implements IAccountingSubjectService 
{
    @Autowired
    private AccountingSubjectMapper accountingSubjectMapper;
    //缓存数据

    private static Map<String, AccountingSubject> registry = new HashMap<>();
    /**
     * 查询会计科目 Accounting subject master
     * 
     * @param code 会计科目 Accounting subject master主键
     * @return 会计科目 Accounting subject master
     */
    @Override
    public AccountingSubject selectAccountingSubjectByCode(String code)
    {
        return accountingSubjectMapper.selectAccountingSubjectByCode(code);
    }

    /**
     * 查询会计科目 Accounting subject master列表
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 会计科目 Accounting subject master
     */
    @Override
    public List<AccountingSubject> selectAccountingSubjectList(AccountingSubject accountingSubject)
    {
        return accountingSubjectMapper.selectAccountingSubjectList(accountingSubject);
    }

    /**
     * 新增会计科目 Accounting subject master
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 结果
     */
    @Override
    public int insertAccountingSubject(AccountingSubject accountingSubject)
    {
        return accountingSubjectMapper.insertAccountingSubject(accountingSubject);
    }

    /**
     * 修改会计科目 Accounting subject master
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 结果
     */
    @Override
    public int updateAccountingSubject(AccountingSubject accountingSubject)
    {
        return accountingSubjectMapper.updateAccountingSubject(accountingSubject);
    }

    /**
     * 批量删除会计科目 Accounting subject master
     * 
     * @param codes 需要删除的会计科目 Accounting subject master主键
     * @return 结果
     */
    @Override
    public int deleteAccountingSubjectByCodes(String[] codes)
    {
        return accountingSubjectMapper.deleteAccountingSubjectByCodes(codes);
    }

    /**
     * 删除会计科目 Accounting subject master信息
     * 
     * @param code 会计科目 Accounting subject master主键
     * @return 结果
     */
    @Override
    public int deleteAccountingSubjectByCode(String code)
    {
        return accountingSubjectMapper.deleteAccountingSubjectByCode(code);
    }

    @Override
    @Cacheable(value ="AccountingSubject_" ,key = "#faiSubject")
    public AccountingSubject selectAccountingSubjectByName(String faiSubject) {
        return accountingSubjectMapper.selectAccountingSubjectByName(faiSubject);
    }

    @Override
    public void loadData() {

            List<AccountingSubject>  list = accountingSubjectMapper.selectAccountingSubjectList( new AccountingSubject());
            registry = list.stream().collect(Collectors.toMap(AccountingSubject::getCode, Function.identity()));

            System.out.println("AccountingSubject 数据已加载，共 " + (list != null ? list.size() : 0) + " 条记录");



    }

    @Override
    public Map<String, AccountingSubject> fatchAll() {
        return registry;
    }

    public static AccountingSubject get(String code) {
        return registry.get(code);
    }
}
