package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasCompany;
import com.lucy.erp.service.IBasCompanyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_公司Controller
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/sales/company")
public class BasCompanyController extends BaseController
{
    @Autowired
    private IBasCompanyService basCompanyService;

    /**
     * 查询基础_公司列表
     */
    @PreAuthorize("@ss.hasPermi('sales:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCompany basCompany)
    {
        startPage();
        List<BasCompany> list = basCompanyService.selectBasCompanyList(basCompany);
        return getDataTable(list);
    }

    /**
     * 导出基础_公司列表
     */
    @PreAuthorize("@ss.hasPermi('sales:company:export')")
    @Log(title = "基础_公司", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCompany basCompany)
    {
        List<BasCompany> list = basCompanyService.selectBasCompanyList(basCompany);
        ExcelUtil<BasCompany> util = new ExcelUtil<BasCompany>(BasCompany.class);
        util.exportExcel(response, list, "基础_公司数据");
    }

    /**
     * 获取基础_公司详细信息
     */
    @PreAuthorize("@ss.hasPermi('sales:company:query')")
    @GetMapping(value = "/{bcCd}")
    public AjaxResult getInfo(@PathVariable("bcCd") String bcCd)
    {
        return success(basCompanyService.selectBasCompanyByBcCd(bcCd));
    }

    /**
     * 新增基础_公司
     */
    @PreAuthorize("@ss.hasPermi('sales:company:add')")
    @Log(title = "基础_公司", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCompany basCompany)
    {
        return toAjax(basCompanyService.insertBasCompany(basCompany));
    }

    /**
     * 修改基础_公司
     */
    @PreAuthorize("@ss.hasPermi('sales:company:edit')")
    @Log(title = "基础_公司", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCompany basCompany)
    {
        return toAjax(basCompanyService.updateBasCompany(basCompany));
    }

    /**
     * 删除基础_公司
     */
    @PreAuthorize("@ss.hasPermi('sales:company:remove')")
    @Log(title = "基础_公司", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bcCds}")
    public AjaxResult remove(@PathVariable String[] bcCds)
    {
        return toAjax(basCompanyService.deleteBasCompanyByBcCds(bcCds));
    }
}
