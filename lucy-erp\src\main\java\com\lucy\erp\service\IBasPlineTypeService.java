package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.BasPlineType;

/**
 * 产线字典Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IBasPlineTypeService 
{
    /**
     * 查询产线字典
     * 
     * @param id 产线字典主键
     * @return 产线字典
     */
    public BasPlineType selectBasPlineTypeById(Long id);

    /**
     * 查询产线字典列表
     * 
     * @param basPlineType 产线字典
     * @return 产线字典集合
     */
    public List<BasPlineType> selectBasPlineTypeList(BasPlineType basPlineType);

    /**
     * 新增产线字典
     * 
     * @param basPlineType 产线字典
     * @return 结果
     */
    public int insertBasPlineType(BasPlineType basPlineType);

    /**
     * 修改产线字典
     * 
     * @param basPlineType 产线字典
     * @return 结果
     */
    public int updateBasPlineType(BasPlineType basPlineType);

    /**
     * 批量删除产线字典
     * 
     * @param ids 需要删除的产线字典主键集合
     * @return 结果
     */
    public int deleteBasPlineTypeByIds(Long[] ids);

    /**
     * 删除产线字典信息
     * 
     * @param id 产线字典主键
     * @return 结果
     */
    public int deleteBasPlineTypeById(Long id);
}
