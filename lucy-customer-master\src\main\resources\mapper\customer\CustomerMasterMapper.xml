<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.customer.mapper.CustomerMasterMapper">
    
    <resultMap type="CustomerMaster" id="CustomerMasterResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="creditRating"    column="credit_rating"    />
        <result property="paymentTerms"    column="payment_terms"    />
        <result property="companyCode"    column="company_code"    />
        <result property="mergerSituation"    column="merger_situation"    />
        <result property="profile"    column="profile"    />
        <result property="classificationMajor"    column="classification_major"    />
        <result property="classificationSubclass"    column="classification_subclass"    />
        <result property="category"    column="category"    />
        <result property="companyName"    column="company_name"    />
        <result property="taxNo"    column="tax_no"    />
    </resultMap>

    <sql id="selectCustomerMasterVo">
        select code, name, credit_rating, payment_terms, company_code, merger_situation, profile, classification_major, classification_subclass, category, company_name, tax_no from customer_master
    </sql>

    <select id="selectCustomerMasterList" parameterType="CustomerMaster" resultMap="CustomerMasterResult">
        <include refid="selectCustomerMasterVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="creditRating != null  and creditRating != ''"> and credit_rating = #{creditRating}</if>
            <if test="paymentTerms != null  and paymentTerms != ''"> and payment_terms = #{paymentTerms}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="mergerSituation != null  and mergerSituation != ''"> and merger_situation = #{mergerSituation}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
            <if test="classificationMajor != null  and classificationMajor != ''"> and classification_major = #{classificationMajor}</if>
            <if test="classificationSubclass != null  and classificationSubclass != ''"> and classification_subclass = #{classificationSubclass}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="taxNo != null  and taxNo != ''"> and tax_no = #{taxNo}</if>
        </where>
    </select>
    
    <select id="selectCustomerMasterByCode" parameterType="String" resultMap="CustomerMasterResult">
        <include refid="selectCustomerMasterVo"/>
        where code = #{code}
    </select>

    <insert id="insertCustomerMaster" parameterType="CustomerMaster">
        insert into customer_master
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="creditRating != null">credit_rating,</if>
            <if test="paymentTerms != null">payment_terms,</if>
            <if test="companyCode != null and companyCode != ''">company_code,</if>
            <if test="mergerSituation != null">merger_situation,</if>
            <if test="profile != null">profile,</if>
            <if test="classificationMajor != null">classification_major,</if>
            <if test="classificationSubclass != null">classification_subclass,</if>
            <if test="category != null">category,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="taxNo != null">tax_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="creditRating != null">#{creditRating},</if>
            <if test="paymentTerms != null">#{paymentTerms},</if>
            <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
            <if test="mergerSituation != null">#{mergerSituation},</if>
            <if test="profile != null">#{profile},</if>
            <if test="classificationMajor != null">#{classificationMajor},</if>
            <if test="classificationSubclass != null">#{classificationSubclass},</if>
            <if test="category != null">#{category},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="taxNo != null">#{taxNo},</if>
         </trim>
    </insert>

    <update id="updateCustomerMaster" parameterType="CustomerMaster">
        update customer_master
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="creditRating != null">credit_rating = #{creditRating},</if>
            <if test="paymentTerms != null">payment_terms = #{paymentTerms},</if>
            <if test="companyCode != null and companyCode != ''">company_code = #{companyCode},</if>
            <if test="mergerSituation != null and mergerSituation != ''">merger_situation = #{mergerSituation},</if>
            <if test="profile != null and profile != ''">profile = #{profile},</if>
            <if test="classificationMajor != null and classificationMajor != ''">classification_major = #{classificationMajor},</if>
            <if test="classificationSubclass != null and classificationSubclass != ''">classification_subclass = #{classificationSubclass},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="taxNo != null and taxNo != ''">tax_no = #{taxNo},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteCustomerMasterByCode" parameterType="String">
        delete from customer_master where code = #{code}
    </delete>

    <delete id="deleteCustomerMasterByCodes" parameterType="String">
        delete from customer_master where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>