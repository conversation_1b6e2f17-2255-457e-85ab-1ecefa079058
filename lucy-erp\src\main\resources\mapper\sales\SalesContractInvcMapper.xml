<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.SalesContractInvcMapper">
    
    <resultMap type="SalesContractInvc" id="SalesContractInvcResult">
        <result property="id"    column="id"    />
        <result property="sciMid"    column="sci_mid"    />
        <result property="sciId"    column="sci_id"    />
        <result property="sciMcd"    column="sci_mcd"    />
        <result property="sciTyp"    column="sci_typ"    />
        <result property="sciTitle"    column="sci_title"    />
        <result property="sciMan"    column="sci_man"    />
        <result property="sciFp"    column="sci_fp"    />
        <result property="sciProvince"    column="sci_province"    />
        <result property="sciCity"    column="sci_city"    />
        <result property="sciDistrict"    column="sci_district"    />
        <result property="sciExaddr"    column="sci_exaddr"    />
        <result property="sciMp"    column="sci_mp"    />
        <result property="sciAddr"    column="sci_addr"    />
        <result property="sciLamt"    column="sci_lamt"    />
        <result property="sciRmk"    column="sci_rmk"    />
        <result property="sciEmail"    column="sci_email"    />
        <result property="sciNation"    column="sci_nation"    />
    </resultMap>

    <sql id="selectSalesContractInvcVo">
        select id, sci_mid, sci_id, sci_mcd, sci_typ, sci_title, sci_man, sci_fp, sci_province, sci_city, sci_district, sci_exaddr, sci_mp, sci_addr, sci_lamt, sci_rmk, sci_email, sci_nation from sales_contract_invc
    </sql>

    <select id="selectSalesContractInvcList" parameterType="SalesContractInvc" resultMap="SalesContractInvcResult">
        <include refid="selectSalesContractInvcVo"/>
        <where>  
            <if test="sciMid != null "> and sci_mid = #{sciMid}</if>
            <if test="sciId != null "> and sci_id = #{sciId}</if>
            <if test="sciTyp != null  and sciTyp != ''"> and sci_typ = #{sciTyp}</if>
            <if test="sciTitle != null  and sciTitle != ''"> and sci_title = #{sciTitle}</if>
            <if test="sciMan != null  and sciMan != ''"> and sci_man = #{sciMan}</if>
            <if test="sciFp != null  and sciFp != ''"> and sci_fp = #{sciFp}</if>
            <if test="sciProvince != null  and sciProvince != ''"> and sci_province = #{sciProvince}</if>
            <if test="sciCity != null  and sciCity != ''"> and sci_city = #{sciCity}</if>
            <if test="sciDistrict != null  and sciDistrict != ''"> and sci_district = #{sciDistrict}</if>
            <if test="sciExaddr != null  and sciExaddr != ''"> and sci_exaddr = #{sciExaddr}</if>
            <if test="sciMp != null  and sciMp != ''"> and sci_mp = #{sciMp}</if>
            <if test="sciAddr != null  and sciAddr != ''"> and sci_addr = #{sciAddr}</if>
            <if test="sciLamt != null "> and sci_lamt = #{sciLamt}</if>
            <if test="sciRmk != null  and sciRmk != ''"> and sci_rmk = #{sciRmk}</if>
            <if test="sciEmail != null  and sciEmail != ''"> and sci_email = #{sciEmail}</if>
            <if test="sciNation != null  and sciNation != ''"> and sci_nation = #{sciNation}</if>
        </where>
    </select>
    
    <select id="selectSalesContractInvcBySciMcd" parameterType="String" resultMap="SalesContractInvcResult">
        <include refid="selectSalesContractInvcVo"/>
        where sci_mcd = #{sciMcd}
    </select>

    <insert id="insertSalesContractInvc" parameterType="SalesContractInvc">
        insert into sales_contract_invc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="sciMid != null">sci_mid,</if>
            <if test="sciId != null">sci_id,</if>
            <if test="sciMcd != null">sci_mcd,</if>
            <if test="sciTyp != null">sci_typ,</if>
            <if test="sciTitle != null">sci_title,</if>
            <if test="sciMan != null">sci_man,</if>
            <if test="sciFp != null">sci_fp,</if>
            <if test="sciProvince != null">sci_province,</if>
            <if test="sciCity != null">sci_city,</if>
            <if test="sciDistrict != null">sci_district,</if>
            <if test="sciExaddr != null">sci_exaddr,</if>
            <if test="sciMp != null">sci_mp,</if>
            <if test="sciAddr != null">sci_addr,</if>
            <if test="sciLamt != null">sci_lamt,</if>
            <if test="sciRmk != null">sci_rmk,</if>
            <if test="sciEmail != null">sci_email,</if>
            <if test="sciNation != null">sci_nation,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sciMid != null">#{sciMid},</if>
            <if test="sciId != null">#{sciId},</if>
            <if test="sciMcd != null">#{sciMcd},</if>
            <if test="sciTyp != null">#{sciTyp},</if>
            <if test="sciTitle != null">#{sciTitle},</if>
            <if test="sciMan != null">#{sciMan},</if>
            <if test="sciFp != null">#{sciFp},</if>
            <if test="sciProvince != null">#{sciProvince},</if>
            <if test="sciCity != null">#{sciCity},</if>
            <if test="sciDistrict != null">#{sciDistrict},</if>
            <if test="sciExaddr != null">#{sciExaddr},</if>
            <if test="sciMp != null">#{sciMp},</if>
            <if test="sciAddr != null">#{sciAddr},</if>
            <if test="sciLamt != null">#{sciLamt},</if>
            <if test="sciRmk != null">#{sciRmk},</if>
            <if test="sciEmail != null">#{sciEmail},</if>
            <if test="sciNation != null">#{sciNation},</if>
         </trim>
    </insert>

    <update id="updateSalesContractInvc" parameterType="SalesContractInvc">
        update sales_contract_invc
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="sciMid != null">sci_mid = #{sciMid},</if>
            <if test="sciId != null">sci_id = #{sciId},</if>
            <if test="sciTyp != null">sci_typ = #{sciTyp},</if>
            <if test="sciTitle != null">sci_title = #{sciTitle},</if>
            <if test="sciMan != null">sci_man = #{sciMan},</if>
            <if test="sciFp != null">sci_fp = #{sciFp},</if>
            <if test="sciProvince != null">sci_province = #{sciProvince},</if>
            <if test="sciCity != null">sci_city = #{sciCity},</if>
            <if test="sciDistrict != null">sci_district = #{sciDistrict},</if>
            <if test="sciExaddr != null">sci_exaddr = #{sciExaddr},</if>
            <if test="sciMp != null">sci_mp = #{sciMp},</if>
            <if test="sciAddr != null">sci_addr = #{sciAddr},</if>
            <if test="sciLamt != null">sci_lamt = #{sciLamt},</if>
            <if test="sciRmk != null">sci_rmk = #{sciRmk},</if>
            <if test="sciEmail != null">sci_email = #{sciEmail},</if>
            <if test="sciNation != null">sci_nation = #{sciNation},</if>
        </trim>
        where sci_mcd = #{sciMcd}
    </update>

    <delete id="deleteSalesContractInvcBySciMcd" parameterType="String">
        delete from sales_contract_invc where sci_mcd = #{sciMcd}
    </delete>

    <delete id="deleteSalesContractInvcBySciMcds" parameterType="String">
        delete from sales_contract_invc where sci_mcd in 
        <foreach item="sciMcd" collection="array" open="(" separator="," close=")">
            #{sciMcd}
        </foreach>
    </delete>
</mapper>