package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.BasProdMst;

/**
 * 基础_产品_主
Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IBasProdMstService 
{
    /**
     * 查询基础_产品_主

     * 
     * @param bpmCd 基础_产品_主
主键
     * @return 基础_产品_主

     */
    public BasProdMst selectBasProdMstByBpmCd(String bpmCd);

    /**
     * 查询基础_产品_主
列表
     * 
     * @param basProdMst 基础_产品_主

     * @return 基础_产品_主
集合
     */
    public List<BasProdMst> selectBasProdMstList(BasProdMst basProdMst);

    /**
     * 新增基础_产品_主

     * 
     * @param basProdMst 基础_产品_主

     * @return 结果
     */
    public int insertBasProdMst(BasProdMst basProdMst);

    /**
     * 修改基础_产品_主

     * 
     * @param basProdMst 基础_产品_主

     * @return 结果
     */
    public int updateBasProdMst(BasProdMst basProdMst);

    /**
     * 批量删除基础_产品_主

     * 
     * @param bpmCds 需要删除的基础_产品_主
主键集合
     * @return 结果
     */
    public int deleteBasProdMstByBpmCds(String[] bpmCds);

    /**
     * 删除基础_产品_主
信息
     * 
     * @param bpmCd 基础_产品_主
主键
     * @return 结果
     */
    public int deleteBasProdMstByBpmCd(String bpmCd);
}
