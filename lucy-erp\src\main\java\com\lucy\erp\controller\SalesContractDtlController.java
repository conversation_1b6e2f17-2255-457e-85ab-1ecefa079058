package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.SalesContractDtl;
import com.lucy.erp.service.ISalesContractDtlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 销售_合同_明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/sales/dtl")
public class SalesContractDtlController extends BaseController
{
    @Autowired
    private ISalesContractDtlService salesContractDtlService;

    /**
     * 查询销售_合同_明细列表
     */
    @PreAuthorize("@ss.hasPermi('sales:dtl:list')")
    @GetMapping("/list")
    public TableDataInfo list(SalesContractDtl salesContractDtl)
    {
        startPage();
        List<SalesContractDtl> list = salesContractDtlService.selectSalesContractDtlList(salesContractDtl);
        return getDataTable(list);
    }

    /**
     * 导出销售_合同_明细列表
     */
    @PreAuthorize("@ss.hasPermi('sales:dtl:export')")
    @Log(title = "销售_合同_明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SalesContractDtl salesContractDtl)
    {
        List<SalesContractDtl> list = salesContractDtlService.selectSalesContractDtlList(salesContractDtl);
        ExcelUtil<SalesContractDtl> util = new ExcelUtil<SalesContractDtl>(SalesContractDtl.class);
        util.exportExcel(response, list, "销售_合同_明细数据");
    }

    /**
     * 获取销售_合同_明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('sales:dtl:query')")
    @GetMapping(value = "/{scdMcd}")
    public AjaxResult getInfo(@PathVariable("scdMcd") String scdMcd)
    {
        return success(salesContractDtlService.selectSalesContractDtlByScdcd(scdMcd));
    }

    /**
     * 新增销售_合同_明细
     */
    @PreAuthorize("@ss.hasPermi('sales:dtl:add')")
    @Log(title = "销售_合同_明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SalesContractDtl salesContractDtl)
    {
        return toAjax(salesContractDtlService.insertSalesContractDtl(salesContractDtl));
    }

    /**
     * 修改销售_合同_明细
     */
    @PreAuthorize("@ss.hasPermi('sales:dtl:edit')")
    @Log(title = "销售_合同_明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SalesContractDtl salesContractDtl)
    {
        return toAjax(salesContractDtlService.updateSalesContractDtl(salesContractDtl));
    }

    /**
     * 删除销售_合同_明细
     */
    @PreAuthorize("@ss.hasPermi('sales:dtl:remove')")
    @Log(title = "销售_合同_明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scdMcds}")
    public AjaxResult remove(@PathVariable String[] scdMcds)
    {
        return toAjax(salesContractDtlService.deleteSalesContractDtlByScdMcds(scdMcds));
    }
}
