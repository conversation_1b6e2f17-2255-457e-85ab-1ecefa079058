package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.FinAccMasterRecordsUniq;

/**
 * 账套Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface FinAccMasterRecordsUniqMapper 
{
    /**
     * 查询账套
     * 
     * @param id 账套主键
     * @return 账套
     */
    public FinAccMasterRecordsUniq selectFinAccMasterRecordsUniqById(String id);

    /**
     * 查询账套列表
     * 
     * @param finAccMasterRecordsUniq 账套
     * @return 账套集合
     */
    public List<FinAccMasterRecordsUniq> selectFinAccMasterRecordsUniqList(FinAccMasterRecordsUniq finAccMasterRecordsUniq);

    /**
     * 查询账套列表
     *
     * @param finAccMasterRecordsUniq 账套
     * @return 账套集合
     */
    public List<FinAccMasterRecordsUniq> selectFinAccMasterRecordsById(FinAccMasterRecordsUniq finAccMasterRecordsUniq);

    /**
     * 新增账套
     * 
     * @param finAccMasterRecordsUniq 账套
     * @return 结果
     */
    public int insertFinAccMasterRecordsUniq(FinAccMasterRecordsUniq finAccMasterRecordsUniq);

    /**
     * 修改账套
     * 
     * @param finAccMasterRecordsUniq 账套
     * @return 结果
     */
    public int updateFinAccMasterRecordsUniq(FinAccMasterRecordsUniq finAccMasterRecordsUniq);

    /**
     * 删除账套
     * 
     * @param id 账套主键
     * @return 结果
     */
    public int deleteFinAccMasterRecordsUniqById(String id);

    /**
     * 批量删除账套
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinAccMasterRecordsUniqByIds(String[] ids);
}
