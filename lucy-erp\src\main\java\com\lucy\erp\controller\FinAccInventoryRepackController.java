package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.FinAccInventoryRepack;
import com.lucy.erp.service.IFinAccInventoryRepackService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 原材料分装及退回原材料单据Controller
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/fin/repack")
public class FinAccInventoryRepackController extends BaseController
{
    @Autowired
    private IFinAccInventoryRepackService finAccInventoryRepackService;

    /**
     * 查询原材料分装及退回原材料单据列表
     */
    @PreAuthorize("@ss.hasPermi('fin:repack:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinAccInventoryRepack finAccInventoryRepack)
    {
        startPage();
        List<FinAccInventoryRepack> list = finAccInventoryRepackService.selectFinAccInventoryRepackList(finAccInventoryRepack);
        return getDataTable(list);
    }

    /**
     * 导出原材料分装及退回原材料单据列表
     */
    @PreAuthorize("@ss.hasPermi('fin:repack:export')")
    @Log(title = "原材料分装及退回原材料单据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinAccInventoryRepack finAccInventoryRepack)
    {
        List<FinAccInventoryRepack> list = finAccInventoryRepackService.selectFinAccInventoryRepackList(finAccInventoryRepack);
        ExcelUtil<FinAccInventoryRepack> util = new ExcelUtil<FinAccInventoryRepack>(FinAccInventoryRepack.class);
        util.exportExcel(response, list, "原材料分装及退回原材料单据数据");
    }

    /**
     * 获取原材料分装及退回原材料单据详细信息
     */
    @PreAuthorize("@ss.hasPermi('fin:repack:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@RequestBody FinAccInventoryRepack finAccInventoryRepack)
    {
        return success(finAccInventoryRepackService.selectFinAccInventoryRepackById(finAccInventoryRepack));
    }

    /**
     * 新增原材料分装及退回原材料单据
     */
    @PreAuthorize("@ss.hasPermi('fin:repack:add')")
    @Log(title = "原材料分装及退回原材料单据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinAccInventoryRepack finAccInventoryRepack)
    {
        return toAjax(finAccInventoryRepackService.insertFinAccInventoryRepack(finAccInventoryRepack));
    }

    /**
     * 修改原材料分装及退回原材料单据
     */
    @PreAuthorize("@ss.hasPermi('fin:repack:edit')")
    @Log(title = "原材料分装及退回原材料单据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinAccInventoryRepack finAccInventoryRepack)
    {
        return toAjax(finAccInventoryRepackService.updateFinAccInventoryRepack(finAccInventoryRepack));
    }

    /**
     * 删除原材料分装及退回原材料单据
     */
    @PreAuthorize("@ss.hasPermi('fin:repack:remove')")
    @Log(title = "原材料分装及退回原材料单据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(finAccInventoryRepackService.deleteFinAccInventoryRepackByIds(ids));
    }
}
