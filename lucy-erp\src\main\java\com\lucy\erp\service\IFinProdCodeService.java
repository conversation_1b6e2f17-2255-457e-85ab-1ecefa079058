package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.FinProdCode;

/**
 * 记账科目Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IFinProdCodeService 
{
    /**
     * 查询记账科目
     * 
     * @param id 记账科目主键
     * @return 记账科目
     */
    public FinProdCode selectFinProdCodeById(Long id);

    /**
     * 查询记账科目列表
     * 
     * @param finProdCode 记账科目
     * @return 记账科目集合
     */
    public List<FinProdCode> selectFinProdCodeList(FinProdCode finProdCode);

    /**
     * 新增记账科目
     * 
     * @param finProdCode 记账科目
     * @return 结果
     */
    public int insertFinProdCode(FinProdCode finProdCode);

    /**
     * 修改记账科目
     * 
     * @param finProdCode 记账科目
     * @return 结果
     */
    public int updateFinProdCode(FinProdCode finProdCode);

    /**
     * 批量删除记账科目
     * 
     * @param ids 需要删除的记账科目主键集合
     * @return 结果
     */
    public int deleteFinProdCodeByIds(Long[] ids);

    /**
     * 删除记账科目信息
     * 
     * @param id 记账科目主键
     * @return 结果
     */
    public int deleteFinProdCodeById(Long id);
}
