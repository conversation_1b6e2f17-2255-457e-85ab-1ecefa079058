package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.SalesContractInvcMapper;
import com.lucy.erp.domain.SalesContractInvc;
import com.lucy.erp.service.ISalesContractInvcService;

/**
 * 销售_合同_发票Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class SalesContractInvcServiceImpl implements ISalesContractInvcService
{
    @Autowired
    private SalesContractInvcMapper salesContractInvcMapper;

    /**
     * 查询销售_合同_发票
     * 
     * @param sciMcd 销售_合同_发票主键
     * @return 销售_合同_发票
     */
    @Override
    public SalesContractInvc selectSalesContractInvcBySciMcd(String sciMcd)
    {
        return salesContractInvcMapper.selectSalesContractInvcBySciMcd(sciMcd);
    }

    /**
     * 查询销售_合同_发票列表
     * 
     * @param salesContractInvc 销售_合同_发票
     * @return 销售_合同_发票
     */
    @Override
    public List<SalesContractInvc> selectSalesContractInvcList(SalesContractInvc salesContractInvc)
    {
        return salesContractInvcMapper.selectSalesContractInvcList(salesContractInvc);
    }

    /**
     * 新增销售_合同_发票
     * 
     * @param salesContractInvc 销售_合同_发票
     * @return 结果
     */
    @Override
    public int insertSalesContractInvc(SalesContractInvc salesContractInvc)
    {
        return salesContractInvcMapper.insertSalesContractInvc(salesContractInvc);
    }

    /**
     * 修改销售_合同_发票
     * 
     * @param salesContractInvc 销售_合同_发票
     * @return 结果
     */
    @Override
    public int updateSalesContractInvc(SalesContractInvc salesContractInvc)
    {
        return salesContractInvcMapper.updateSalesContractInvc(salesContractInvc);
    }

    /**
     * 批量删除销售_合同_发票
     * 
     * @param sciMcds 需要删除的销售_合同_发票主键
     * @return 结果
     */
    @Override
    public int deleteSalesContractInvcBySciMcds(String[] sciMcds)
    {
        return salesContractInvcMapper.deleteSalesContractInvcBySciMcds(sciMcds);
    }

    /**
     * 删除销售_合同_发票信息
     * 
     * @param sciMcd 销售_合同_发票主键
     * @return 结果
     */
    @Override
    public int deleteSalesContractInvcBySciMcd(String sciMcd)
    {
        return salesContractInvcMapper.deleteSalesContractInvcBySciMcd(sciMcd);
    }
}
