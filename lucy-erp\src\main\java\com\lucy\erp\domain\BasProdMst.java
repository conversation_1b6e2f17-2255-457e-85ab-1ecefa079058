package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_产品_主
对象 bas_prod_mst
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class BasProdMst extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 分类编号 */
    @Excel(name = "分类编号")
    private String bpmCcd;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String bpmCnm;

    /** 子分类编号 */
    @Excel(name = "子分类编号")
    private String bpmSccd;

    /** 子分类名称 */
    @Excel(name = "子分类名称")
    private String bpmScnm;

    /** 物质编号 */
    @Excel(name = "物质编号")
    private String bpmScd;

    /** 品牌编号 */
    @Excel(name = "品牌编号")
    private String bpmPcd;

    /** 品牌名称 */
    @Excel(name = "品牌名称")
    private String bpmPnm;

    /** 产品种类 */
    @Excel(name = "产品种类")
    private String bpmSort;

    /** 产品种类名称 */
    @Excel(name = "产品种类名称")
    private String bpmSortnm;

    /** 原始编号 */
    @Excel(name = "原始编号")
    private String bpmOrgcd;

    /** 供应商原始编号-辅 */
    @Excel(name = "供应商原始编号-辅")
    private String bpmSuporgcdsub;

    /** 供应商原始编号-主 */
    @Excel(name = "供应商原始编号-主")
    private String bpmSuporgcd;

    /** 产品编号 */
    private String bpmCd;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String bpmNm;

    /** 开票商品名称 */
    @Excel(name = "开票商品名称")
    private String bpmNmInv;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bpmEnm;

    /** 保质期 */
    @Excel(name = "保质期")
    private String bpmQgp;

    /** 质检周期 */
    @Excel(name = "质检周期")
    private String bpmQci;

    /** 分装周期 */
    @Excel(name = "分装周期")
    private String bpmPci;

    /** 质检项是否已添加 */
    @Excel(name = "质检项是否已添加")
    private String bpmQc;

    /** 产品描述 */
    @Excel(name = "产品描述")
    private String bpmRmk;

    /** 产品详情 */
    @Excel(name = "产品详情")
    private String bpmDetails;

    /** 默认发货仓库 */
    @Excel(name = "默认发货仓库")
    private String bpmDmcd;

    /** 默认供应商 */
    @Excel(name = "默认供应商")
    private String bpmSupcd;

    /** 默认供应商组织名称 */
    @Excel(name = "默认供应商组织名称")
    private String bpmSupnm;

    /** 默认供应商-辅助 */
    @Excel(name = "默认供应商-辅助")
    private String bpmSupcdsub;

    /** 默认供应商组织名称辅助 */
    @Excel(name = "默认供应商组织名称辅助")
    private String bpmSupnmsub;

    /** 默认采购员编号编号 */
    @Excel(name = "默认采购员编号编号")
    private String bpmPureno;

    /** 默认采购员名称 */
    @Excel(name = "默认采购员名称")
    private String bpmPurenm;

    /** 价格政策类型 */
    @Excel(name = "价格政策类型")
    private String bpmPrctyp;

    /** 产品上下架状态 */
    @Excel(name = "产品上下架状态")
    private String bpmSts;

    /** 产品上下架状态备注 */
    @Excel(name = "产品上下架状态备注")
    private String bpmStsrmk;

    /** 新产品状态 */
    @Excel(name = "新产品状态")
    private String bpmNsts;

    /** 产品售完即止 */
    @Excel(name = "产品售完即止")
    private String bpmSsts;

    /** 产品售完即止备注 */
    @Excel(name = "产品售完即止备注")
    private String bpmSstsrmk;

    /** 产品是否热卖 */
    @Excel(name = "产品是否热卖")
    private String bpmOnsale;

    /** 产品是否同步 */
    @Excel(name = "产品是否同步")
    private String bpmSyn;

    /** 产品类目 */
    @Excel(name = "产品类目")
    private String bpmTyps;

    /** 分类一 */
    @Excel(name = "分类一")
    private String bpmClass1;

    /** 分类二 */
    @Excel(name = "分类二")
    private String bpmClass2;

    /** 分类三 */
    @Excel(name = "分类三")
    private String bpmClass3;

    /** 分类四 */
    @Excel(name = "分类四")
    private String bpmClass4;

    /** 分类五 */
    @Excel(name = "分类五")
    private String bpmClass5;

    /** 分类六 */
    @Excel(name = "分类六")
    private String bpmClass6;

    /** 分类七 */
    @Excel(name = "分类七")
    private String bpmClass7;

    /** 是否可打开瓶盖 */
    @Excel(name = "是否可打开瓶盖")
    private String bpmCdt;

    /** 商品税收分类编码 */
    @Excel(name = "商品税收分类编码")
    private String bpmTaxonomy;

    /** 销售类目 */
    @Excel(name = "销售类目")
    private String bpmStyp;

    /** 单位 -开发票用 */
    @Excel(name = "单位 -开发票用")
    private String bpmUnit;

    /** 财务记账分类编号 */
    @Excel(name = "财务记账分类编号")
    private String bpmFinanceCode;

    /** 包装级别 */
    @Excel(name = "包装级别")
    private String bpmWrap;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 3A默认采购员 */
    @Excel(name = "3A默认采购员")
    private String bpmPureno3a;

    /** 制单提醒 */
    @Excel(name = "制单提醒")
    private String bpmTips;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bpmBizCls1;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bpmBizCls2;

    /** 是否锁定 1:锁定 0：未锁定 */
    @Excel(name = "是否锁定 1:锁定 0：未锁定")
    private Integer islocked;

    /** 产品等级 */
    @Excel(name = "产品等级")
    private String bpmGrade;

    /** 产品等级描述 */
    @Excel(name = "产品等级描述")
    private String bpmGradeDesc;

    /** 库存(药石平台) */
    @Excel(name = "库存(药石平台)")
    private Long bpmStock;

    /** 子品牌 */
    @Excel(name = "子品牌")
    private String bpmPcdSon;

    /** 专库专放 */
    @Excel(name = "专库专放")
    private String bpmSpecDmcd;

    /** 产品市场标签 */
    @Excel(name = "产品市场标签")
    private String bpmMarketLabel;

    /** 独家性 */
    @Excel(name = "独家性")
    private String bpmExclusivity;

    /** 模板id */
    @Excel(name = "模板id")
    private Long goodstypeId;

    /** 高价精品 1:是  0：否 */
    @Excel(name = "高价精品 1:是  0：否")
    private Integer bpmBoutique;

    /** 产品级别KG/L的标准单位成本 */
    @Excel(name = "产品级别KG/L的标准单位成本")
    private BigDecimal bpmStdUnitp;

    /** 型号 */
    @Excel(name = "型号")
    private String bpmModel;

    /** 稀土：1 非稀土：0 */
    @Excel(name = "稀土：1 非稀土：0")
    private String bpmResign;

    /** 产品规格属性 */
    @Excel(name = "产品规格属性")
    private String bpmSpecAttr;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBpmCcd(String bpmCcd) 
    {
        this.bpmCcd = bpmCcd;
    }

    public String getBpmCcd() 
    {
        return bpmCcd;
    }

    public void setBpmCnm(String bpmCnm) 
    {
        this.bpmCnm = bpmCnm;
    }

    public String getBpmCnm() 
    {
        return bpmCnm;
    }

    public void setBpmSccd(String bpmSccd) 
    {
        this.bpmSccd = bpmSccd;
    }

    public String getBpmSccd() 
    {
        return bpmSccd;
    }

    public void setBpmScnm(String bpmScnm) 
    {
        this.bpmScnm = bpmScnm;
    }

    public String getBpmScnm() 
    {
        return bpmScnm;
    }

    public void setBpmScd(String bpmScd) 
    {
        this.bpmScd = bpmScd;
    }

    public String getBpmScd() 
    {
        return bpmScd;
    }

    public void setBpmPcd(String bpmPcd) 
    {
        this.bpmPcd = bpmPcd;
    }

    public String getBpmPcd() 
    {
        return bpmPcd;
    }

    public void setBpmPnm(String bpmPnm) 
    {
        this.bpmPnm = bpmPnm;
    }

    public String getBpmPnm() 
    {
        return bpmPnm;
    }

    public void setBpmSort(String bpmSort) 
    {
        this.bpmSort = bpmSort;
    }

    public String getBpmSort() 
    {
        return bpmSort;
    }

    public void setBpmSortnm(String bpmSortnm) 
    {
        this.bpmSortnm = bpmSortnm;
    }

    public String getBpmSortnm() 
    {
        return bpmSortnm;
    }

    public void setBpmOrgcd(String bpmOrgcd) 
    {
        this.bpmOrgcd = bpmOrgcd;
    }

    public String getBpmOrgcd() 
    {
        return bpmOrgcd;
    }

    public void setBpmSuporgcdsub(String bpmSuporgcdsub) 
    {
        this.bpmSuporgcdsub = bpmSuporgcdsub;
    }

    public String getBpmSuporgcdsub() 
    {
        return bpmSuporgcdsub;
    }

    public void setBpmSuporgcd(String bpmSuporgcd) 
    {
        this.bpmSuporgcd = bpmSuporgcd;
    }

    public String getBpmSuporgcd() 
    {
        return bpmSuporgcd;
    }

    public void setBpmCd(String bpmCd) 
    {
        this.bpmCd = bpmCd;
    }

    public String getBpmCd() 
    {
        return bpmCd;
    }

    public void setBpmNm(String bpmNm) 
    {
        this.bpmNm = bpmNm;
    }

    public String getBpmNm() 
    {
        return bpmNm;
    }

    public void setBpmNmInv(String bpmNmInv) 
    {
        this.bpmNmInv = bpmNmInv;
    }

    public String getBpmNmInv() 
    {
        return bpmNmInv;
    }

    public void setBpmEnm(String bpmEnm) 
    {
        this.bpmEnm = bpmEnm;
    }

    public String getBpmEnm() 
    {
        return bpmEnm;
    }

    public void setBpmQgp(String bpmQgp) 
    {
        this.bpmQgp = bpmQgp;
    }

    public String getBpmQgp() 
    {
        return bpmQgp;
    }

    public void setBpmQci(String bpmQci) 
    {
        this.bpmQci = bpmQci;
    }

    public String getBpmQci() 
    {
        return bpmQci;
    }

    public void setBpmPci(String bpmPci) 
    {
        this.bpmPci = bpmPci;
    }

    public String getBpmPci() 
    {
        return bpmPci;
    }

    public void setBpmQc(String bpmQc) 
    {
        this.bpmQc = bpmQc;
    }

    public String getBpmQc() 
    {
        return bpmQc;
    }

    public void setBpmRmk(String bpmRmk) 
    {
        this.bpmRmk = bpmRmk;
    }

    public String getBpmRmk() 
    {
        return bpmRmk;
    }

    public void setBpmDetails(String bpmDetails) 
    {
        this.bpmDetails = bpmDetails;
    }

    public String getBpmDetails() 
    {
        return bpmDetails;
    }

    public void setBpmDmcd(String bpmDmcd) 
    {
        this.bpmDmcd = bpmDmcd;
    }

    public String getBpmDmcd() 
    {
        return bpmDmcd;
    }

    public void setBpmSupcd(String bpmSupcd) 
    {
        this.bpmSupcd = bpmSupcd;
    }

    public String getBpmSupcd() 
    {
        return bpmSupcd;
    }

    public void setBpmSupnm(String bpmSupnm) 
    {
        this.bpmSupnm = bpmSupnm;
    }

    public String getBpmSupnm() 
    {
        return bpmSupnm;
    }

    public void setBpmSupcdsub(String bpmSupcdsub) 
    {
        this.bpmSupcdsub = bpmSupcdsub;
    }

    public String getBpmSupcdsub() 
    {
        return bpmSupcdsub;
    }

    public void setBpmSupnmsub(String bpmSupnmsub) 
    {
        this.bpmSupnmsub = bpmSupnmsub;
    }

    public String getBpmSupnmsub() 
    {
        return bpmSupnmsub;
    }

    public void setBpmPureno(String bpmPureno) 
    {
        this.bpmPureno = bpmPureno;
    }

    public String getBpmPureno() 
    {
        return bpmPureno;
    }

    public void setBpmPurenm(String bpmPurenm) 
    {
        this.bpmPurenm = bpmPurenm;
    }

    public String getBpmPurenm() 
    {
        return bpmPurenm;
    }

    public void setBpmPrctyp(String bpmPrctyp) 
    {
        this.bpmPrctyp = bpmPrctyp;
    }

    public String getBpmPrctyp() 
    {
        return bpmPrctyp;
    }

    public void setBpmSts(String bpmSts) 
    {
        this.bpmSts = bpmSts;
    }

    public String getBpmSts() 
    {
        return bpmSts;
    }

    public void setBpmStsrmk(String bpmStsrmk) 
    {
        this.bpmStsrmk = bpmStsrmk;
    }

    public String getBpmStsrmk() 
    {
        return bpmStsrmk;
    }

    public void setBpmNsts(String bpmNsts) 
    {
        this.bpmNsts = bpmNsts;
    }

    public String getBpmNsts() 
    {
        return bpmNsts;
    }

    public void setBpmSsts(String bpmSsts) 
    {
        this.bpmSsts = bpmSsts;
    }

    public String getBpmSsts() 
    {
        return bpmSsts;
    }

    public void setBpmSstsrmk(String bpmSstsrmk) 
    {
        this.bpmSstsrmk = bpmSstsrmk;
    }

    public String getBpmSstsrmk() 
    {
        return bpmSstsrmk;
    }

    public void setBpmOnsale(String bpmOnsale) 
    {
        this.bpmOnsale = bpmOnsale;
    }

    public String getBpmOnsale() 
    {
        return bpmOnsale;
    }

    public void setBpmSyn(String bpmSyn) 
    {
        this.bpmSyn = bpmSyn;
    }

    public String getBpmSyn() 
    {
        return bpmSyn;
    }

    public void setBpmTyps(String bpmTyps) 
    {
        this.bpmTyps = bpmTyps;
    }

    public String getBpmTyps() 
    {
        return bpmTyps;
    }

    public void setBpmClass1(String bpmClass1) 
    {
        this.bpmClass1 = bpmClass1;
    }

    public String getBpmClass1() 
    {
        return bpmClass1;
    }

    public void setBpmClass2(String bpmClass2) 
    {
        this.bpmClass2 = bpmClass2;
    }

    public String getBpmClass2() 
    {
        return bpmClass2;
    }

    public void setBpmClass3(String bpmClass3) 
    {
        this.bpmClass3 = bpmClass3;
    }

    public String getBpmClass3() 
    {
        return bpmClass3;
    }

    public void setBpmClass4(String bpmClass4) 
    {
        this.bpmClass4 = bpmClass4;
    }

    public String getBpmClass4() 
    {
        return bpmClass4;
    }

    public void setBpmClass5(String bpmClass5) 
    {
        this.bpmClass5 = bpmClass5;
    }

    public String getBpmClass5() 
    {
        return bpmClass5;
    }

    public void setBpmClass6(String bpmClass6) 
    {
        this.bpmClass6 = bpmClass6;
    }

    public String getBpmClass6() 
    {
        return bpmClass6;
    }

    public void setBpmClass7(String bpmClass7) 
    {
        this.bpmClass7 = bpmClass7;
    }

    public String getBpmClass7() 
    {
        return bpmClass7;
    }

    public void setBpmCdt(String bpmCdt) 
    {
        this.bpmCdt = bpmCdt;
    }

    public String getBpmCdt() 
    {
        return bpmCdt;
    }

    public void setBpmTaxonomy(String bpmTaxonomy) 
    {
        this.bpmTaxonomy = bpmTaxonomy;
    }

    public String getBpmTaxonomy() 
    {
        return bpmTaxonomy;
    }

    public void setBpmStyp(String bpmStyp) 
    {
        this.bpmStyp = bpmStyp;
    }

    public String getBpmStyp() 
    {
        return bpmStyp;
    }

    public void setBpmUnit(String bpmUnit) 
    {
        this.bpmUnit = bpmUnit;
    }

    public String getBpmUnit() 
    {
        return bpmUnit;
    }

    public void setBpmFinanceCode(String bpmFinanceCode) 
    {
        this.bpmFinanceCode = bpmFinanceCode;
    }

    public String getBpmFinanceCode() 
    {
        return bpmFinanceCode;
    }

    public void setBpmWrap(String bpmWrap) 
    {
        this.bpmWrap = bpmWrap;
    }

    public String getBpmWrap() 
    {
        return bpmWrap;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setBpmPureno3a(String bpmPureno3a) 
    {
        this.bpmPureno3a = bpmPureno3a;
    }

    public String getBpmPureno3a() 
    {
        return bpmPureno3a;
    }

    public void setBpmTips(String bpmTips) 
    {
        this.bpmTips = bpmTips;
    }

    public String getBpmTips() 
    {
        return bpmTips;
    }

    public void setBpmBizCls1(String bpmBizCls1) 
    {
        this.bpmBizCls1 = bpmBizCls1;
    }

    public String getBpmBizCls1() 
    {
        return bpmBizCls1;
    }

    public void setBpmBizCls2(String bpmBizCls2) 
    {
        this.bpmBizCls2 = bpmBizCls2;
    }

    public String getBpmBizCls2() 
    {
        return bpmBizCls2;
    }

    public void setIslocked(Integer islocked) 
    {
        this.islocked = islocked;
    }

    public Integer getIslocked() 
    {
        return islocked;
    }

    public void setBpmGrade(String bpmGrade) 
    {
        this.bpmGrade = bpmGrade;
    }

    public String getBpmGrade() 
    {
        return bpmGrade;
    }

    public void setBpmGradeDesc(String bpmGradeDesc) 
    {
        this.bpmGradeDesc = bpmGradeDesc;
    }

    public String getBpmGradeDesc() 
    {
        return bpmGradeDesc;
    }

    public void setBpmStock(Long bpmStock) 
    {
        this.bpmStock = bpmStock;
    }

    public Long getBpmStock() 
    {
        return bpmStock;
    }

    public void setBpmPcdSon(String bpmPcdSon) 
    {
        this.bpmPcdSon = bpmPcdSon;
    }

    public String getBpmPcdSon() 
    {
        return bpmPcdSon;
    }

    public void setBpmSpecDmcd(String bpmSpecDmcd) 
    {
        this.bpmSpecDmcd = bpmSpecDmcd;
    }

    public String getBpmSpecDmcd() 
    {
        return bpmSpecDmcd;
    }

    public void setBpmMarketLabel(String bpmMarketLabel) 
    {
        this.bpmMarketLabel = bpmMarketLabel;
    }

    public String getBpmMarketLabel() 
    {
        return bpmMarketLabel;
    }

    public void setBpmExclusivity(String bpmExclusivity) 
    {
        this.bpmExclusivity = bpmExclusivity;
    }

    public String getBpmExclusivity() 
    {
        return bpmExclusivity;
    }

    public void setGoodstypeId(Long goodstypeId) 
    {
        this.goodstypeId = goodstypeId;
    }

    public Long getGoodstypeId() 
    {
        return goodstypeId;
    }

    public void setBpmBoutique(Integer bpmBoutique) 
    {
        this.bpmBoutique = bpmBoutique;
    }

    public Integer getBpmBoutique() 
    {
        return bpmBoutique;
    }

    public void setBpmStdUnitp(BigDecimal bpmStdUnitp) 
    {
        this.bpmStdUnitp = bpmStdUnitp;
    }

    public BigDecimal getBpmStdUnitp() 
    {
        return bpmStdUnitp;
    }

    public void setBpmModel(String bpmModel) 
    {
        this.bpmModel = bpmModel;
    }

    public String getBpmModel() 
    {
        return bpmModel;
    }

    public void setBpmResign(String bpmResign) 
    {
        this.bpmResign = bpmResign;
    }

    public String getBpmResign() 
    {
        return bpmResign;
    }

    public void setBpmSpecAttr(String bpmSpecAttr) 
    {
        this.bpmSpecAttr = bpmSpecAttr;
    }

    public String getBpmSpecAttr() 
    {
        return bpmSpecAttr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bpmCcd", getBpmCcd())
            .append("bpmCnm", getBpmCnm())
            .append("bpmSccd", getBpmSccd())
            .append("bpmScnm", getBpmScnm())
            .append("bpmScd", getBpmScd())
            .append("bpmPcd", getBpmPcd())
            .append("bpmPnm", getBpmPnm())
            .append("bpmSort", getBpmSort())
            .append("bpmSortnm", getBpmSortnm())
            .append("bpmOrgcd", getBpmOrgcd())
            .append("bpmSuporgcdsub", getBpmSuporgcdsub())
            .append("bpmSuporgcd", getBpmSuporgcd())
            .append("bpmCd", getBpmCd())
            .append("bpmNm", getBpmNm())
            .append("bpmNmInv", getBpmNmInv())
            .append("bpmEnm", getBpmEnm())
            .append("bpmQgp", getBpmQgp())
            .append("bpmQci", getBpmQci())
            .append("bpmPci", getBpmPci())
            .append("bpmQc", getBpmQc())
            .append("bpmRmk", getBpmRmk())
            .append("bpmDetails", getBpmDetails())
            .append("bpmDmcd", getBpmDmcd())
            .append("bpmSupcd", getBpmSupcd())
            .append("bpmSupnm", getBpmSupnm())
            .append("bpmSupcdsub", getBpmSupcdsub())
            .append("bpmSupnmsub", getBpmSupnmsub())
            .append("bpmPureno", getBpmPureno())
            .append("bpmPurenm", getBpmPurenm())
            .append("bpmPrctyp", getBpmPrctyp())
            .append("bpmSts", getBpmSts())
            .append("bpmStsrmk", getBpmStsrmk())
            .append("bpmNsts", getBpmNsts())
            .append("bpmSsts", getBpmSsts())
            .append("bpmSstsrmk", getBpmSstsrmk())
            .append("bpmOnsale", getBpmOnsale())
            .append("bpmSyn", getBpmSyn())
            .append("bpmTyps", getBpmTyps())
            .append("bpmClass1", getBpmClass1())
            .append("bpmClass2", getBpmClass2())
            .append("bpmClass3", getBpmClass3())
            .append("bpmClass4", getBpmClass4())
            .append("bpmClass5", getBpmClass5())
            .append("bpmClass6", getBpmClass6())
            .append("bpmClass7", getBpmClass7())
            .append("bpmCdt", getBpmCdt())
            .append("bpmTaxonomy", getBpmTaxonomy())
            .append("bpmStyp", getBpmStyp())
            .append("bpmUnit", getBpmUnit())
            .append("bpmFinanceCode", getBpmFinanceCode())
            .append("bpmWrap", getBpmWrap())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("bpmPureno3a", getBpmPureno3a())
            .append("bpmTips", getBpmTips())
            .append("bpmBizCls1", getBpmBizCls1())
            .append("bpmBizCls2", getBpmBizCls2())
            .append("islocked", getIslocked())
            .append("bpmGrade", getBpmGrade())
            .append("bpmGradeDesc", getBpmGradeDesc())
            .append("bpmStock", getBpmStock())
            .append("bpmPcdSon", getBpmPcdSon())
            .append("bpmSpecDmcd", getBpmSpecDmcd())
            .append("bpmMarketLabel", getBpmMarketLabel())
            .append("bpmExclusivity", getBpmExclusivity())
            .append("goodstypeId", getGoodstypeId())
            .append("bpmBoutique", getBpmBoutique())
            .append("bpmStdUnitp", getBpmStdUnitp())
            .append("bpmModel", getBpmModel())
            .append("bpmResign", getBpmResign())
            .append("bpmSpecAttr", getBpmSpecAttr())
            .toString();
    }
}
