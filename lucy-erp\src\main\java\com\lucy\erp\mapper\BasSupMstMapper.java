package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.BasSupMst;

/**
 * 基础_供应商Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface BasSupMstMapper 
{
    /**
     * 查询基础_供应商
     * 
     * @param bsmCcd 基础_供应商主键
     * @return 基础_供应商
     */
    public BasSupMst selectBasSupMstByBsmCcd(String bsmCcd);

    /**
     * 查询基础_供应商
     *
     * @param bsmCd 基础_供应商CD
     * @return 基础_供应商
     */
    public BasSupMst selectBasSupMstByBsmCd(String bsmCd);

    /**
     * 查询基础_供应商列表
     * 
     * @param basSupMst 基础_供应商
     * @return 基础_供应商集合
     */
    public List<BasSupMst> selectBasSupMstList(BasSupMst basSupMst);

    /**
     * 新增基础_供应商
     * 
     * @param basSupMst 基础_供应商
     * @return 结果
     */
    public int insertBasSupMst(BasSupMst basSupMst);

    /**
     * 修改基础_供应商
     * 
     * @param basSupMst 基础_供应商
     * @return 结果
     */
    public int updateBasSupMst(BasSupMst basSupMst);

    /**
     * 删除基础_供应商
     * 
     * @param bsmCcd 基础_供应商主键
     * @return 结果
     */
    public int deleteBasSupMstByBsmCcd(String bsmCcd);

    /**
     * 批量删除基础_供应商
     * 
     * @param bsmCcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasSupMstByBsmCcds(String[] bsmCcds);
}
