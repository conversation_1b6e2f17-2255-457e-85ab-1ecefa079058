package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LabProjectMapper;
import com.lucy.erp.domain.LabProject;
import com.lucy.erp.service.ILabProjectService;

/**
 * 研发项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
@DataSource(DataSourceType.ERP)
public class LabProjectServiceImpl implements ILabProjectService
{
    @Autowired
    private LabProjectMapper labProjectMapper;

    /**
     * 查询研发项目
     * 
     * @param lpCd 研发项目主键
     * @return 研发项目
     */
    @Override
    public LabProject selectLabProjectByLpCd(String lpCd)
    {
        return labProjectMapper.selectLabProjectByLpCd(lpCd);
    }

    /**
     * 查询研发项目列表
     * 
     * @param labProject 研发项目
     * @return 研发项目
     */
    @Override
    public List<LabProject> selectLabProjectList(LabProject labProject)
    {
        return labProjectMapper.selectLabProjectList(labProject);
    }

    /**
     * 新增研发项目
     * 
     * @param labProject 研发项目
     * @return 结果
     */
    @Override
    public int insertLabProject(LabProject labProject)
    {
        return labProjectMapper.insertLabProject(labProject);
    }

    /**
     * 修改研发项目
     * 
     * @param labProject 研发项目
     * @return 结果
     */
    @Override
    public int updateLabProject(LabProject labProject)
    {
        return labProjectMapper.updateLabProject(labProject);
    }

    /**
     * 批量删除研发项目
     * 
     * @param lpCds 需要删除的研发项目主键
     * @return 结果
     */
    @Override
    public int deleteLabProjectByLpCds(String[] lpCds)
    {
        return labProjectMapper.deleteLabProjectByLpCds(lpCds);
    }

    /**
     * 删除研发项目信息
     * 
     * @param lpCd 研发项目主键
     * @return 结果
     */
    @Override
    public int deleteLabProjectByLpCd(String lpCd)
    {
        return labProjectMapper.deleteLabProjectByLpCd(lpCd);
    }
}
