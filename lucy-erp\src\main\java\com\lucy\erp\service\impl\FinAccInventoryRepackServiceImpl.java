package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.FinAccInventoryRepackMapper;
import com.lucy.erp.domain.FinAccInventoryRepack;
import com.lucy.erp.service.IFinAccInventoryRepackService;

/**
 * 原材料分装及退回原材料单据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
@DataSource(DataSourceType.ERP)
public class FinAccInventoryRepackServiceImpl implements IFinAccInventoryRepackService 
{
    @Autowired
    private FinAccInventoryRepackMapper finAccInventoryRepackMapper;

    /**
     * 查询原材料分装及退回原材料单据
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 原材料分装及退回原材料单据
     */
    @Override
    public FinAccInventoryRepack selectFinAccInventoryRepackById(FinAccInventoryRepack finAccInventoryRepack)
    {
        return finAccInventoryRepackMapper.selectFinAccInventoryRepackById(finAccInventoryRepack);
    }

    /**
     * 查询原材料分装及退回原材料单据列表
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 原材料分装及退回原材料单据
     */
    @Override
    public List<FinAccInventoryRepack> selectFinAccInventoryRepackList(FinAccInventoryRepack finAccInventoryRepack)
    {
        return finAccInventoryRepackMapper.selectFinAccInventoryRepackList(finAccInventoryRepack);
    }

    /**
     * 新增原材料分装及退回原材料单据
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 结果
     */
    @Override
    public int insertFinAccInventoryRepack(FinAccInventoryRepack finAccInventoryRepack)
    {
        return finAccInventoryRepackMapper.insertFinAccInventoryRepack(finAccInventoryRepack);
    }

    /**
     * 修改原材料分装及退回原材料单据
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 结果
     */
    @Override
    public int updateFinAccInventoryRepack(FinAccInventoryRepack finAccInventoryRepack)
    {
        return finAccInventoryRepackMapper.updateFinAccInventoryRepack(finAccInventoryRepack);
    }

    /**
     * 批量删除原材料分装及退回原材料单据
     * 
     * @param ids 需要删除的原材料分装及退回原材料单据主键
     * @return 结果
     */
    @Override
    public int deleteFinAccInventoryRepackByIds(Long[] ids)
    {
        return finAccInventoryRepackMapper.deleteFinAccInventoryRepackByIds(ids);
    }

    /**
     * 删除原材料分装及退回原材料单据信息
     * 
     * @param id 原材料分装及退回原材料单据主键
     * @return 结果
     */
    @Override
    public int deleteFinAccInventoryRepackById(Long id)
    {
        return finAccInventoryRepackMapper.deleteFinAccInventoryRepackById(id);
    }
}
