<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasProdAchemMapper">
    
    <resultMap type="BasProdAchem" id="BasProdAchemResult">
        <result property="id"    column="id"    />
        <result property="bpacCd"    column="bpac_cd"    />
        <result property="bpacMcd"    column="bpac_mcd"    />
        <result property="bpacCas"    column="bpac_cas"    />
        <result property="bpacIupac"    column="bpac_iupac"    />
        <result property="bpacMdl"    column="bpac_mdl"    />
        <result property="bpacEinecs"    column="bpac_einecs"    />
        <result property="bpacSmiles"    column="bpac_smiles"    />
        <result property="bpacPurity"    column="bpac_purity"    />
        <result property="bpacFnm"    column="bpac_fnm"    />
        <result property="bpacEfnm"    column="bpac_efnm"    />
        <result property="bpacFnm1"    column="bpac_fnm1"    />
        <result property="bpacFnm2"    column="bpac_fnm2"    />
        <result property="bpacMf"    column="bpac_mf"    />
        <result property="bpacEm"    column="bpac_em"    />
        <result property="bpacMw"    column="bpac_mw"    />
        <result property="bpacColor"    column="bpac_color"    />
        <result property="bpacAppear"    column="bpac_appear"    />
        <result property="bpacLos"    column="bpac_los"    />
        <result property="bpacCharacter"    column="bpac_character"    />
        <result property="bpacUnit"    column="bpac_unit"    />
        <result property="bpacTaxonomy"    column="bpac_taxonomy"    />
        <result property="bpacSmell"    column="bpac_smell"    />
        <result property="bpacRi"    column="bpac_ri"    />
        <result property="bpacVade"    column="bpac_vade"    />
        <result property="bpacFp"    column="bpac_fp"    />
        <result property="bpacBp"    column="bpac_bp"    />
        <result property="bpacMp"    column="bpac_mp"    />
        <result property="bpacAutoign"    column="bpac_autoign"    />
        <result property="bpacSolu"    column="bpac_solu"    />
        <result property="bpacVapp"    column="bpac_vapp"    />
        <result property="bpacDens"    column="bpac_dens"    />
        <result property="bpacDgcls"    column="bpac_dgcls"    />
        <result property="bpacDgsign"    column="bpac_dgsign"    />
        <result property="bpacDgtno"    column="bpac_dgtno"    />
        <result property="bpacRiskcd"    column="bpac_riskcd"    />
        <result property="bpacSafetydesc"    column="bpac_safetydesc"    />
        <result property="bpacPackgrp"    column="bpac_packgrp"    />
        <result property="bpacWgk"    column="bpac_wgk"    />
        <result property="bpacRtecs"    column="bpac_rtecs"    />
        <result property="bpacF"    column="bpac_f"    />
        <result property="bpacScLabelPrint"    column="bpac_sc_label_print"    />
        <result property="bpacSc"    column="bpac_sc"    />
        <result property="bpacScPtst"    column="bpac_sc_ptst"    />
        <result property="bpacCp"    column="bpac_cp"    />
        <result property="bpacUsage"    column="bpac_usage"    />
        <result property="bpacIsal"    column="bpac_isal"    />
        <result property="bpacIsmp"    column="bpac_ismp"    />
        <result property="bpacPackinstr"    column="bpac_packinstr"    />
        <result property="bpacFprod"    column="bpac_fprod"    />
        <result property="bpacEp"    column="bpac_ep"    />
        <result property="bpacSnote"    column="bpac_snote"    />
        <result property="bpacSprmk"    column="bpac_sprmk"    />
        <result property="bpacSprmkPoisonod"    column="bpac_sprmk_poisonod"    />
        <result property="bpacSprmkDanger"    column="bpac_sprmk_danger"    />
        <result property="bpacImg"    column="bpac_img"    />
        <result property="bpacUnn"    column="bpac_unn"    />
        <result property="bpacGhssg"    column="bpac_ghssg"    />
        <result property="bpacGhsswg"    column="bpac_ghsswg"    />
        <result property="bpacGhsswg2"    column="bpac_ghsswg2"    />
        <result property="bpacGhshsg"    column="bpac_ghshsg"    />
        <result property="bpacGhspsg"    column="bpac_ghspsg"    />
        <result property="bpacInfo"    column="bpac_info"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="bpacRpknote"    column="bpac_rpknote"    />
        <result property="bpacUseprecautions"    column="bpac_useprecautions"    />
        <result property="bpacClassiimdlRmk"    column="bpac_classiimdl_rmk"    />
        <result property="bpacRawMaterial"    column="bpac_raw_material"    />
        <result property="bpacSprmkPoison"    column="bpac_sprmk_poison"    />
        <result property="bpacProductUse"    column="bpac_product_use"    />
        <result property="bpacLiteratureSource"    column="bpac_literature_source"    />
        <result property="bpacWarning"    column="bpac_warning"    />
        <result property="bpacPictogram"    column="bpac_pictogram"    />
        <result property="bpacPrecautionary"    column="bpac_precautionary"    />
        <result property="bpacAccident"    column="bpac_accident"    />
        <result property="bpacStorage"    column="bpac_storage"    />
        <result property="bpacDisposal"    column="bpac_disposal"    />
        <result property="bpacHscode"    column="bpac_hscode"    />
        <result property="bpacRiskcate"    column="bpac_riskCate"    />
        <result property="bpacPch"    column="bpac_pch"    />
        <result property="bpacHealth"    column="bpac_health"    />
        <result property="bpacEnvironmentalCode"    column="bpac_environmental_code"    />
        <result property="bpacExtinguish"    column="bpac_extinguish"    />
        <result property="bpacSpecialhazard"    column="bpac_specialhazard"    />
        <result property="bpacPh"    column="bpac_ph"    />
        <result property="bpacLimit"    column="bpac_limit"    />
        <result property="bpacEvaporation"    column="bpac_evaporation"    />
        <result property="bpacPartition"    column="bpac_partition"    />
        <result property="bpacDt"    column="bpac_dt"    />
        <result property="bpacFlammability"    column="bpac_flammability"    />
        <result property="bpacOral"    column="bpac_oral"    />
        <result property="bpacInhalation"    column="bpac_inhalation"    />
        <result property="bpacDermal"    column="bpac_dermal"    />
        <result property="bpacSkin"    column="bpac_skin"    />
        <result property="bpacEye"    column="bpac_eye"    />
        <result property="bpacRespiratory"    column="bpac_respiratory"    />
        <result property="bpacMutagenicity"    column="bpac_mutagenicity"    />
        <result property="bpacCarcinogenicity"    column="bpac_carcinogenicity"    />
        <result property="bpacReproductive"    column="bpac_reproductive"    />
        <result property="bpacSingle"    column="bpac_single"    />
        <result property="bpacRepeated"    column="bpac_repeated"    />
        <result property="bpacAddinfo"    column="bpac_addinfo"    />
        <result property="bpacTofish"    column="bpac_tofish"    />
        <result property="bpacTodaphnia"    column="bpac_todaphnia"    />
        <result property="bpacToalgae"    column="bpac_toalgae"    />
        <result property="bpacTobacteria"    column="bpac_tobacteria"    />
        <result property="bpacPersistence"    column="bpac_persistence"    />
        <result property="bpacBioaccumulative"    column="bpac_bioaccumulative"    />
        <result property="bpacMobility"    column="bpac_mobility"    />
        <result property="bpacEffects"    column="bpac_effects"    />
        <result property="bpacEnvironmental"    column="bpac_environmental"    />
        <result property="bpacMsdsUrl"    column="bpac_msds_url"    />
        <result property="bpacPermitThrough"    column="bpac_permit_through"    />
        <result property="bpacSnQualifications"    column="bpac_sn_qualifications"    />
        <result property="bpacIsrisk"    column="bpac_isrisk"    />
        <result property="bpacCodehs"    column="bpac_codehs"    />
        <result property="bpacDgcargo"    column="bpac_dgcargo"    />
        <result property="bpacDgsource"    column="bpac_dgsource"    />
        <result property="bpacInportlimit"    column="bpac_inportlimit"    />
        <result property="bpacExportlimit"    column="bpac_exportlimit"    />
        <result property="bpacPhscope"    column="bpac_phscope"    />
        <result property="bpacDensity"    column="bpac_density"    />
        <result property="bpacDgclsNext"    column="bpac_dgcls_next"    />
        <result property="bpacNdgclsClass"    column="bpac_ndgcls_class"    />
        <result property="bpacMaintain"    column="bpac_maintain"    />
        <result property="bpacPurityEn"    column="bpac_purity_en"    />
        <result property="bpacIsnewsubstances"    column="bpac_isNewSubstances"    />
        <result property="bpacSn"    column="bpac_SN"    />
        <result property="bpacSpecialmarking"    column="bpac_specialMarking"    />
        <result property="bpacDgclsNo"    column="bpac_dgcls_no"    />
        <result property="bpacSnReturnCode"    column="bpac_SN_return_code"    />
        <result property="bpacSnDt"    column="bpac_SN_dt"    />
        <result property="bpacForeignlock"    column="bpac_foreignLock"    />
        <result property="bpacSingaporehs"    column="bpac_singaporeHS"    />
        <result property="bpacRecordNumberSf"    column="bpac_record_number_SF"    />
        <result property="bpacCiqcode"    column="bpac_CIQcode"    />
        <result property="bpacQuality"    column="bpac_quality"    />
        <result property="bpacSupplyChain"    column="bpac_supply_chain"    />
        <result property="bpacProductInfo"    column="bpac_product_info"    />
        <result property="bpacProductMarket"    column="bpac_product_market"    />
        <result property="bpacTransportName"    column="bpac_transport_name"    />
        <result property="bpacLq"    column="bpac_lq"    />
        <result property="bpacEq"    column="bpac_eq"    />
        <result property="bpacHn"    column="bpac_hn"    />
    </resultMap>

    <sql id="selectBasProdAchemVo">
        select id, bpac_cd, bpac_mcd, bpac_cas, bpac_iupac, bpac_mdl, bpac_einecs, bpac_smiles, bpac_purity, bpac_fnm, bpac_efnm, bpac_fnm1, bpac_fnm2, bpac_mf, bpac_em, bpac_mw, bpac_color, bpac_appear, bpac_los, bpac_character, bpac_unit, bpac_taxonomy, bpac_smell, bpac_ri, bpac_vade, bpac_fp, bpac_bp, bpac_mp, bpac_autoign, bpac_solu, bpac_vapp, bpac_dens, bpac_dgcls, bpac_dgsign, bpac_dgtno, bpac_riskcd, bpac_safetydesc, bpac_packgrp, bpac_wgk, bpac_rtecs, bpac_f, bpac_sc_label_print, bpac_sc, bpac_sc_ptst, bpac_cp, bpac_usage, bpac_isal, bpac_ismp, bpac_packinstr, bpac_fprod, bpac_ep, bpac_snote, bpac_sprmk, bpac_sprmk_poisonod, bpac_sprmk_danger, bpac_img, bpac_unn, bpac_ghssg, bpac_ghsswg, bpac_ghsswg2, bpac_ghshsg, bpac_ghspsg, bpac_info, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, bpac_rpknote, bpac_useprecautions, bpac_classiimdl_rmk, bpac_raw_material, bpac_sprmk_poison, bpac_product_use, bpac_literature_source, bpac_warning, bpac_pictogram, bpac_precautionary, bpac_accident, bpac_storage, bpac_disposal, bpac_hscode, bpac_riskCate, bpac_pch, bpac_health, bpac_environmental_code, bpac_extinguish, bpac_specialhazard, bpac_ph, bpac_limit, bpac_evaporation, bpac_partition, bpac_dt, bpac_flammability, bpac_oral, bpac_inhalation, bpac_dermal, bpac_skin, bpac_eye, bpac_respiratory, bpac_mutagenicity, bpac_carcinogenicity, bpac_reproductive, bpac_single, bpac_repeated, bpac_addinfo, bpac_tofish, bpac_todaphnia, bpac_toalgae, bpac_tobacteria, bpac_persistence, bpac_bioaccumulative, bpac_mobility, bpac_effects, bpac_environmental, bpac_msds_url, bpac_permit_through, bpac_sn_qualifications, bpac_isrisk, bpac_codehs, bpac_dgcargo, bpac_dgsource, bpac_inportlimit, bpac_exportlimit, bpac_phscope, bpac_density, bpac_dgcls_next, bpac_ndgcls_class, bpac_maintain, bpac_purity_en, bpac_isNewSubstances, bpac_SN, bpac_specialMarking, bpac_dgcls_no, bpac_SN_return_code, bpac_SN_dt, bpac_foreignLock, bpac_singaporeHS, bpac_record_number_SF, bpac_CIQcode, bpac_quality, bpac_supply_chain, bpac_product_info, bpac_product_market, bpac_transport_name, bpac_lq, bpac_eq, bpac_hn from bas_prod_achem
    </sql>

    <select id="selectBasProdAchemList" parameterType="BasProdAchem" resultMap="BasProdAchemResult">
        <include refid="selectBasProdAchemVo"/>
        <where>  
            <if test="bpacCas != null  and bpacCas != ''"> and bpac_cas = #{bpacCas}</if>
            <if test="bpacIupac != null  and bpacIupac != ''"> and bpac_iupac = #{bpacIupac}</if>
            <if test="bpacMdl != null  and bpacMdl != ''"> and bpac_mdl = #{bpacMdl}</if>
            <if test="bpacEinecs != null  and bpacEinecs != ''"> and bpac_einecs = #{bpacEinecs}</if>
            <if test="bpacSmiles != null  and bpacSmiles != ''"> and bpac_smiles = #{bpacSmiles}</if>
            <if test="bpacPurity != null  and bpacPurity != ''"> and bpac_purity = #{bpacPurity}</if>
            <if test="bpacFnm != null  and bpacFnm != ''"> and bpac_fnm = #{bpacFnm}</if>
            <if test="bpacEfnm != null  and bpacEfnm != ''"> and bpac_efnm = #{bpacEfnm}</if>
            <if test="bpacFnm1 != null  and bpacFnm1 != ''"> and bpac_fnm1 = #{bpacFnm1}</if>
            <if test="bpacFnm2 != null  and bpacFnm2 != ''"> and bpac_fnm2 = #{bpacFnm2}</if>
            <if test="bpacMf != null  and bpacMf != ''"> and bpac_mf = #{bpacMf}</if>
            <if test="bpacEm != null  and bpacEm != ''"> and bpac_em = #{bpacEm}</if>
            <if test="bpacMw != null  and bpacMw != ''"> and bpac_mw = #{bpacMw}</if>
            <if test="bpacColor != null  and bpacColor != ''"> and bpac_color = #{bpacColor}</if>
            <if test="bpacAppear != null  and bpacAppear != ''"> and bpac_appear = #{bpacAppear}</if>
            <if test="bpacLos != null  and bpacLos != ''"> and bpac_los = #{bpacLos}</if>
            <if test="bpacCharacter != null  and bpacCharacter != ''"> and bpac_character = #{bpacCharacter}</if>
            <if test="bpacUnit != null  and bpacUnit != ''"> and bpac_unit = #{bpacUnit}</if>
            <if test="bpacTaxonomy != null  and bpacTaxonomy != ''"> and bpac_taxonomy = #{bpacTaxonomy}</if>
            <if test="bpacSmell != null  and bpacSmell != ''"> and bpac_smell = #{bpacSmell}</if>
            <if test="bpacRi != null  and bpacRi != ''"> and bpac_ri = #{bpacRi}</if>
            <if test="bpacVade != null  and bpacVade != ''"> and bpac_vade = #{bpacVade}</if>
            <if test="bpacFp != null  and bpacFp != ''"> and bpac_fp = #{bpacFp}</if>
            <if test="bpacBp != null  and bpacBp != ''"> and bpac_bp = #{bpacBp}</if>
            <if test="bpacMp != null  and bpacMp != ''"> and bpac_mp = #{bpacMp}</if>
            <if test="bpacAutoign != null  and bpacAutoign != ''"> and bpac_autoign = #{bpacAutoign}</if>
            <if test="bpacSolu != null  and bpacSolu != ''"> and bpac_solu = #{bpacSolu}</if>
            <if test="bpacVapp != null  and bpacVapp != ''"> and bpac_vapp = #{bpacVapp}</if>
            <if test="bpacDens != null  and bpacDens != ''"> and bpac_dens = #{bpacDens}</if>
            <if test="bpacDgcls != null  and bpacDgcls != ''"> and bpac_dgcls = #{bpacDgcls}</if>
            <if test="bpacDgsign != null  and bpacDgsign != ''"> and bpac_dgsign = #{bpacDgsign}</if>
            <if test="bpacDgtno != null  and bpacDgtno != ''"> and bpac_dgtno = #{bpacDgtno}</if>
            <if test="bpacRiskcd != null  and bpacRiskcd != ''"> and bpac_riskcd = #{bpacRiskcd}</if>
            <if test="bpacSafetydesc != null  and bpacSafetydesc != ''"> and bpac_safetydesc = #{bpacSafetydesc}</if>
            <if test="bpacPackgrp != null  and bpacPackgrp != ''"> and bpac_packgrp = #{bpacPackgrp}</if>
            <if test="bpacWgk != null  and bpacWgk != ''"> and bpac_wgk = #{bpacWgk}</if>
            <if test="bpacRtecs != null  and bpacRtecs != ''"> and bpac_rtecs = #{bpacRtecs}</if>
            <if test="bpacF != null  and bpacF != ''"> and bpac_f = #{bpacF}</if>
            <if test="bpacScLabelPrint != null  and bpacScLabelPrint != ''"> and bpac_sc_label_print = #{bpacScLabelPrint}</if>
            <if test="bpacSc != null  and bpacSc != ''"> and bpac_sc = #{bpacSc}</if>
            <if test="bpacScPtst != null  and bpacScPtst != ''"> and bpac_sc_ptst = #{bpacScPtst}</if>
            <if test="bpacCp != null  and bpacCp != ''"> and bpac_cp = #{bpacCp}</if>
            <if test="bpacUsage != null  and bpacUsage != ''"> and bpac_usage = #{bpacUsage}</if>
            <if test="bpacIsal != null  and bpacIsal != ''"> and bpac_isal = #{bpacIsal}</if>
            <if test="bpacIsmp != null  and bpacIsmp != ''"> and bpac_ismp = #{bpacIsmp}</if>
            <if test="bpacPackinstr != null  and bpacPackinstr != ''"> and bpac_packinstr = #{bpacPackinstr}</if>
            <if test="bpacFprod != null  and bpacFprod != ''"> and bpac_fprod = #{bpacFprod}</if>
            <if test="bpacEp != null  and bpacEp != ''"> and bpac_ep = #{bpacEp}</if>
            <if test="bpacSnote != null  and bpacSnote != ''"> and bpac_snote = #{bpacSnote}</if>
            <if test="bpacSprmk != null  and bpacSprmk != ''"> and bpac_sprmk = #{bpacSprmk}</if>
            <if test="bpacSprmkPoisonod != null  and bpacSprmkPoisonod != ''"> and bpac_sprmk_poisonod = #{bpacSprmkPoisonod}</if>
            <if test="bpacSprmkDanger != null  and bpacSprmkDanger != ''"> and bpac_sprmk_danger = #{bpacSprmkDanger}</if>
            <if test="bpacImg != null  and bpacImg != ''"> and bpac_img = #{bpacImg}</if>
            <if test="bpacUnn != null  and bpacUnn != ''"> and bpac_unn = #{bpacUnn}</if>
            <if test="bpacGhssg != null  and bpacGhssg != ''"> and bpac_ghssg = #{bpacGhssg}</if>
            <if test="bpacGhsswg != null  and bpacGhsswg != ''"> and bpac_ghsswg = #{bpacGhsswg}</if>
            <if test="bpacGhsswg2 != null  and bpacGhsswg2 != ''"> and bpac_ghsswg2 = #{bpacGhsswg2}</if>
            <if test="bpacGhshsg != null  and bpacGhshsg != ''"> and bpac_ghshsg = #{bpacGhshsg}</if>
            <if test="bpacGhspsg != null  and bpacGhspsg != ''"> and bpac_ghspsg = #{bpacGhspsg}</if>
            <if test="bpacInfo != null  and bpacInfo != ''"> and bpac_info = #{bpacInfo}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="bpacRpknote != null  and bpacRpknote != ''"> and bpac_rpknote = #{bpacRpknote}</if>
            <if test="bpacUseprecautions != null  and bpacUseprecautions != ''"> and bpac_useprecautions = #{bpacUseprecautions}</if>
            <if test="bpacClassiimdlRmk != null  and bpacClassiimdlRmk != ''"> and bpac_classiimdl_rmk = #{bpacClassiimdlRmk}</if>
            <if test="bpacRawMaterial != null  and bpacRawMaterial != ''"> and bpac_raw_material = #{bpacRawMaterial}</if>
            <if test="bpacSprmkPoison != null  and bpacSprmkPoison != ''"> and bpac_sprmk_poison = #{bpacSprmkPoison}</if>
            <if test="bpacProductUse != null  and bpacProductUse != ''"> and bpac_product_use = #{bpacProductUse}</if>
            <if test="bpacLiteratureSource != null  and bpacLiteratureSource != ''"> and bpac_literature_source = #{bpacLiteratureSource}</if>
            <if test="bpacWarning != null  and bpacWarning != ''"> and bpac_warning = #{bpacWarning}</if>
            <if test="bpacPictogram != null  and bpacPictogram != ''"> and bpac_pictogram = #{bpacPictogram}</if>
            <if test="bpacPrecautionary != null  and bpacPrecautionary != ''"> and bpac_precautionary = #{bpacPrecautionary}</if>
            <if test="bpacAccident != null  and bpacAccident != ''"> and bpac_accident = #{bpacAccident}</if>
            <if test="bpacStorage != null  and bpacStorage != ''"> and bpac_storage = #{bpacStorage}</if>
            <if test="bpacDisposal != null  and bpacDisposal != ''"> and bpac_disposal = #{bpacDisposal}</if>
            <if test="bpacHscode != null  and bpacHscode != ''"> and bpac_hscode = #{bpacHscode}</if>
            <if test="bpacRiskcate != null  and bpacRiskcate != ''"> and bpac_riskCate = #{bpacRiskcate}</if>
            <if test="bpacPch != null  and bpacPch != ''"> and bpac_pch = #{bpacPch}</if>
            <if test="bpacHealth != null  and bpacHealth != ''"> and bpac_health = #{bpacHealth}</if>
            <if test="bpacEnvironmentalCode != null  and bpacEnvironmentalCode != ''"> and bpac_environmental_code = #{bpacEnvironmentalCode}</if>
            <if test="bpacExtinguish != null  and bpacExtinguish != ''"> and bpac_extinguish = #{bpacExtinguish}</if>
            <if test="bpacSpecialhazard != null  and bpacSpecialhazard != ''"> and bpac_specialhazard = #{bpacSpecialhazard}</if>
            <if test="bpacPh != null  and bpacPh != ''"> and bpac_ph = #{bpacPh}</if>
            <if test="bpacLimit != null  and bpacLimit != ''"> and bpac_limit = #{bpacLimit}</if>
            <if test="bpacEvaporation != null  and bpacEvaporation != ''"> and bpac_evaporation = #{bpacEvaporation}</if>
            <if test="bpacPartition != null  and bpacPartition != ''"> and bpac_partition = #{bpacPartition}</if>
            <if test="bpacDt != null  and bpacDt != ''"> and bpac_dt = #{bpacDt}</if>
            <if test="bpacFlammability != null  and bpacFlammability != ''"> and bpac_flammability = #{bpacFlammability}</if>
            <if test="bpacOral != null  and bpacOral != ''"> and bpac_oral = #{bpacOral}</if>
            <if test="bpacInhalation != null  and bpacInhalation != ''"> and bpac_inhalation = #{bpacInhalation}</if>
            <if test="bpacDermal != null  and bpacDermal != ''"> and bpac_dermal = #{bpacDermal}</if>
            <if test="bpacSkin != null  and bpacSkin != ''"> and bpac_skin = #{bpacSkin}</if>
            <if test="bpacEye != null  and bpacEye != ''"> and bpac_eye = #{bpacEye}</if>
            <if test="bpacRespiratory != null  and bpacRespiratory != ''"> and bpac_respiratory = #{bpacRespiratory}</if>
            <if test="bpacMutagenicity != null  and bpacMutagenicity != ''"> and bpac_mutagenicity = #{bpacMutagenicity}</if>
            <if test="bpacCarcinogenicity != null  and bpacCarcinogenicity != ''"> and bpac_carcinogenicity = #{bpacCarcinogenicity}</if>
            <if test="bpacReproductive != null  and bpacReproductive != ''"> and bpac_reproductive = #{bpacReproductive}</if>
            <if test="bpacSingle != null  and bpacSingle != ''"> and bpac_single = #{bpacSingle}</if>
            <if test="bpacRepeated != null  and bpacRepeated != ''"> and bpac_repeated = #{bpacRepeated}</if>
            <if test="bpacAddinfo != null  and bpacAddinfo != ''"> and bpac_addinfo = #{bpacAddinfo}</if>
            <if test="bpacTofish != null  and bpacTofish != ''"> and bpac_tofish = #{bpacTofish}</if>
            <if test="bpacTodaphnia != null  and bpacTodaphnia != ''"> and bpac_todaphnia = #{bpacTodaphnia}</if>
            <if test="bpacToalgae != null  and bpacToalgae != ''"> and bpac_toalgae = #{bpacToalgae}</if>
            <if test="bpacTobacteria != null  and bpacTobacteria != ''"> and bpac_tobacteria = #{bpacTobacteria}</if>
            <if test="bpacPersistence != null  and bpacPersistence != ''"> and bpac_persistence = #{bpacPersistence}</if>
            <if test="bpacBioaccumulative != null  and bpacBioaccumulative != ''"> and bpac_bioaccumulative = #{bpacBioaccumulative}</if>
            <if test="bpacMobility != null  and bpacMobility != ''"> and bpac_mobility = #{bpacMobility}</if>
            <if test="bpacEffects != null  and bpacEffects != ''"> and bpac_effects = #{bpacEffects}</if>
            <if test="bpacEnvironmental != null  and bpacEnvironmental != ''"> and bpac_environmental = #{bpacEnvironmental}</if>
            <if test="bpacMsdsUrl != null  and bpacMsdsUrl != ''"> and bpac_msds_url = #{bpacMsdsUrl}</if>
            <if test="bpacPermitThrough != null  and bpacPermitThrough != ''"> and bpac_permit_through = #{bpacPermitThrough}</if>
            <if test="bpacSnQualifications != null  and bpacSnQualifications != ''"> and bpac_sn_qualifications = #{bpacSnQualifications}</if>
            <if test="bpacIsrisk != null  and bpacIsrisk != ''"> and bpac_isrisk = #{bpacIsrisk}</if>
            <if test="bpacCodehs != null  and bpacCodehs != ''"> and bpac_codehs = #{bpacCodehs}</if>
            <if test="bpacDgcargo != null  and bpacDgcargo != ''"> and bpac_dgcargo = #{bpacDgcargo}</if>
            <if test="bpacDgsource != null  and bpacDgsource != ''"> and bpac_dgsource = #{bpacDgsource}</if>
            <if test="bpacInportlimit != null  and bpacInportlimit != ''"> and bpac_inportlimit = #{bpacInportlimit}</if>
            <if test="bpacExportlimit != null  and bpacExportlimit != ''"> and bpac_exportlimit = #{bpacExportlimit}</if>
            <if test="bpacPhscope != null  and bpacPhscope != ''"> and bpac_phscope = #{bpacPhscope}</if>
            <if test="bpacDensity != null  and bpacDensity != ''"> and bpac_density = #{bpacDensity}</if>
            <if test="bpacDgclsNext != null  and bpacDgclsNext != ''"> and bpac_dgcls_next = #{bpacDgclsNext}</if>
            <if test="bpacNdgclsClass != null  and bpacNdgclsClass != ''"> and bpac_ndgcls_class = #{bpacNdgclsClass}</if>
            <if test="bpacMaintain != null  and bpacMaintain != ''"> and bpac_maintain = #{bpacMaintain}</if>
            <if test="bpacPurityEn != null  and bpacPurityEn != ''"> and bpac_purity_en = #{bpacPurityEn}</if>
            <if test="bpacIsnewsubstances != null  and bpacIsnewsubstances != ''"> and bpac_isNewSubstances = #{bpacIsnewsubstances}</if>
            <if test="bpacSn != null  and bpacSn != ''"> and bpac_SN = #{bpacSn}</if>
            <if test="bpacSpecialmarking != null  and bpacSpecialmarking != ''"> and bpac_specialMarking = #{bpacSpecialmarking}</if>
            <if test="bpacDgclsNo != null "> and bpac_dgcls_no = #{bpacDgclsNo}</if>
            <if test="bpacSnReturnCode != null  and bpacSnReturnCode != ''"> and bpac_SN_return_code = #{bpacSnReturnCode}</if>
            <if test="bpacSnDt != null  and bpacSnDt != ''"> and bpac_SN_dt = #{bpacSnDt}</if>
            <if test="bpacForeignlock != null  and bpacForeignlock != ''"> and bpac_foreignLock = #{bpacForeignlock}</if>
            <if test="bpacSingaporehs != null  and bpacSingaporehs != ''"> and bpac_singaporeHS = #{bpacSingaporehs}</if>
            <if test="bpacRecordNumberSf != null  and bpacRecordNumberSf != ''"> and bpac_record_number_SF = #{bpacRecordNumberSf}</if>
            <if test="bpacCiqcode != null  and bpacCiqcode != ''"> and bpac_CIQcode = #{bpacCiqcode}</if>
            <if test="bpacQuality != null  and bpacQuality != ''"> and bpac_quality = #{bpacQuality}</if>
            <if test="bpacSupplyChain != null  and bpacSupplyChain != ''"> and bpac_supply_chain = #{bpacSupplyChain}</if>
            <if test="bpacProductInfo != null  and bpacProductInfo != ''"> and bpac_product_info = #{bpacProductInfo}</if>
            <if test="bpacProductMarket != null  and bpacProductMarket != ''"> and bpac_product_market = #{bpacProductMarket}</if>
            <if test="bpacTransportName != null  and bpacTransportName != ''"> and bpac_transport_name like concat('%', #{bpacTransportName}, '%')</if>
            <if test="bpacLq != null  and bpacLq != ''"> and bpac_lq = #{bpacLq}</if>
            <if test="bpacEq != null  and bpacEq != ''"> and bpac_eq = #{bpacEq}</if>
            <if test="bpacHn != null  and bpacHn != ''"> and bpac_hn = #{bpacHn}</if>
        </where>
    </select>
    
    <select id="selectBasProdAchemByBpacCd" parameterType="String" resultMap="BasProdAchemResult">
        <include refid="selectBasProdAchemVo"/>
        where bpac_cd = #{bpacCd}
    </select>

    <insert id="insertBasProdAchem" parameterType="BasProdAchem">
        insert into bas_prod_achem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bpacCd != null">bpac_cd,</if>
            <if test="bpacMcd != null">bpac_mcd,</if>
            <if test="bpacCas != null">bpac_cas,</if>
            <if test="bpacIupac != null">bpac_iupac,</if>
            <if test="bpacMdl != null">bpac_mdl,</if>
            <if test="bpacEinecs != null">bpac_einecs,</if>
            <if test="bpacSmiles != null">bpac_smiles,</if>
            <if test="bpacPurity != null">bpac_purity,</if>
            <if test="bpacFnm != null">bpac_fnm,</if>
            <if test="bpacEfnm != null">bpac_efnm,</if>
            <if test="bpacFnm1 != null">bpac_fnm1,</if>
            <if test="bpacFnm2 != null">bpac_fnm2,</if>
            <if test="bpacMf != null">bpac_mf,</if>
            <if test="bpacEm != null">bpac_em,</if>
            <if test="bpacMw != null">bpac_mw,</if>
            <if test="bpacColor != null">bpac_color,</if>
            <if test="bpacAppear != null">bpac_appear,</if>
            <if test="bpacLos != null">bpac_los,</if>
            <if test="bpacCharacter != null">bpac_character,</if>
            <if test="bpacUnit != null">bpac_unit,</if>
            <if test="bpacTaxonomy != null">bpac_taxonomy,</if>
            <if test="bpacSmell != null">bpac_smell,</if>
            <if test="bpacRi != null">bpac_ri,</if>
            <if test="bpacVade != null">bpac_vade,</if>
            <if test="bpacFp != null">bpac_fp,</if>
            <if test="bpacBp != null">bpac_bp,</if>
            <if test="bpacMp != null">bpac_mp,</if>
            <if test="bpacAutoign != null">bpac_autoign,</if>
            <if test="bpacSolu != null">bpac_solu,</if>
            <if test="bpacVapp != null">bpac_vapp,</if>
            <if test="bpacDens != null">bpac_dens,</if>
            <if test="bpacDgcls != null">bpac_dgcls,</if>
            <if test="bpacDgsign != null">bpac_dgsign,</if>
            <if test="bpacDgtno != null">bpac_dgtno,</if>
            <if test="bpacRiskcd != null">bpac_riskcd,</if>
            <if test="bpacSafetydesc != null">bpac_safetydesc,</if>
            <if test="bpacPackgrp != null">bpac_packgrp,</if>
            <if test="bpacWgk != null">bpac_wgk,</if>
            <if test="bpacRtecs != null">bpac_rtecs,</if>
            <if test="bpacF != null">bpac_f,</if>
            <if test="bpacScLabelPrint != null">bpac_sc_label_print,</if>
            <if test="bpacSc != null">bpac_sc,</if>
            <if test="bpacScPtst != null">bpac_sc_ptst,</if>
            <if test="bpacCp != null">bpac_cp,</if>
            <if test="bpacUsage != null">bpac_usage,</if>
            <if test="bpacIsal != null">bpac_isal,</if>
            <if test="bpacIsmp != null">bpac_ismp,</if>
            <if test="bpacPackinstr != null">bpac_packinstr,</if>
            <if test="bpacFprod != null">bpac_fprod,</if>
            <if test="bpacEp != null">bpac_ep,</if>
            <if test="bpacSnote != null">bpac_snote,</if>
            <if test="bpacSprmk != null">bpac_sprmk,</if>
            <if test="bpacSprmkPoisonod != null">bpac_sprmk_poisonod,</if>
            <if test="bpacSprmkDanger != null">bpac_sprmk_danger,</if>
            <if test="bpacImg != null">bpac_img,</if>
            <if test="bpacUnn != null">bpac_unn,</if>
            <if test="bpacGhssg != null">bpac_ghssg,</if>
            <if test="bpacGhsswg != null">bpac_ghsswg,</if>
            <if test="bpacGhsswg2 != null">bpac_ghsswg2,</if>
            <if test="bpacGhshsg != null">bpac_ghshsg,</if>
            <if test="bpacGhspsg != null">bpac_ghspsg,</if>
            <if test="bpacInfo != null">bpac_info,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="bpacRpknote != null">bpac_rpknote,</if>
            <if test="bpacUseprecautions != null">bpac_useprecautions,</if>
            <if test="bpacClassiimdlRmk != null">bpac_classiimdl_rmk,</if>
            <if test="bpacRawMaterial != null">bpac_raw_material,</if>
            <if test="bpacSprmkPoison != null">bpac_sprmk_poison,</if>
            <if test="bpacProductUse != null">bpac_product_use,</if>
            <if test="bpacLiteratureSource != null">bpac_literature_source,</if>
            <if test="bpacWarning != null">bpac_warning,</if>
            <if test="bpacPictogram != null">bpac_pictogram,</if>
            <if test="bpacPrecautionary != null">bpac_precautionary,</if>
            <if test="bpacAccident != null">bpac_accident,</if>
            <if test="bpacStorage != null">bpac_storage,</if>
            <if test="bpacDisposal != null">bpac_disposal,</if>
            <if test="bpacHscode != null">bpac_hscode,</if>
            <if test="bpacRiskcate != null">bpac_riskCate,</if>
            <if test="bpacPch != null">bpac_pch,</if>
            <if test="bpacHealth != null">bpac_health,</if>
            <if test="bpacEnvironmentalCode != null">bpac_environmental_code,</if>
            <if test="bpacExtinguish != null">bpac_extinguish,</if>
            <if test="bpacSpecialhazard != null">bpac_specialhazard,</if>
            <if test="bpacPh != null">bpac_ph,</if>
            <if test="bpacLimit != null">bpac_limit,</if>
            <if test="bpacEvaporation != null">bpac_evaporation,</if>
            <if test="bpacPartition != null">bpac_partition,</if>
            <if test="bpacDt != null">bpac_dt,</if>
            <if test="bpacFlammability != null">bpac_flammability,</if>
            <if test="bpacOral != null">bpac_oral,</if>
            <if test="bpacInhalation != null">bpac_inhalation,</if>
            <if test="bpacDermal != null">bpac_dermal,</if>
            <if test="bpacSkin != null">bpac_skin,</if>
            <if test="bpacEye != null">bpac_eye,</if>
            <if test="bpacRespiratory != null">bpac_respiratory,</if>
            <if test="bpacMutagenicity != null">bpac_mutagenicity,</if>
            <if test="bpacCarcinogenicity != null">bpac_carcinogenicity,</if>
            <if test="bpacReproductive != null">bpac_reproductive,</if>
            <if test="bpacSingle != null">bpac_single,</if>
            <if test="bpacRepeated != null">bpac_repeated,</if>
            <if test="bpacAddinfo != null">bpac_addinfo,</if>
            <if test="bpacTofish != null">bpac_tofish,</if>
            <if test="bpacTodaphnia != null">bpac_todaphnia,</if>
            <if test="bpacToalgae != null">bpac_toalgae,</if>
            <if test="bpacTobacteria != null">bpac_tobacteria,</if>
            <if test="bpacPersistence != null">bpac_persistence,</if>
            <if test="bpacBioaccumulative != null">bpac_bioaccumulative,</if>
            <if test="bpacMobility != null">bpac_mobility,</if>
            <if test="bpacEffects != null">bpac_effects,</if>
            <if test="bpacEnvironmental != null">bpac_environmental,</if>
            <if test="bpacMsdsUrl != null">bpac_msds_url,</if>
            <if test="bpacPermitThrough != null">bpac_permit_through,</if>
            <if test="bpacSnQualifications != null">bpac_sn_qualifications,</if>
            <if test="bpacIsrisk != null">bpac_isrisk,</if>
            <if test="bpacCodehs != null">bpac_codehs,</if>
            <if test="bpacDgcargo != null">bpac_dgcargo,</if>
            <if test="bpacDgsource != null">bpac_dgsource,</if>
            <if test="bpacInportlimit != null">bpac_inportlimit,</if>
            <if test="bpacExportlimit != null">bpac_exportlimit,</if>
            <if test="bpacPhscope != null">bpac_phscope,</if>
            <if test="bpacDensity != null">bpac_density,</if>
            <if test="bpacDgclsNext != null">bpac_dgcls_next,</if>
            <if test="bpacNdgclsClass != null">bpac_ndgcls_class,</if>
            <if test="bpacMaintain != null">bpac_maintain,</if>
            <if test="bpacPurityEn != null">bpac_purity_en,</if>
            <if test="bpacIsnewsubstances != null">bpac_isNewSubstances,</if>
            <if test="bpacSn != null">bpac_SN,</if>
            <if test="bpacSpecialmarking != null">bpac_specialMarking,</if>
            <if test="bpacDgclsNo != null">bpac_dgcls_no,</if>
            <if test="bpacSnReturnCode != null">bpac_SN_return_code,</if>
            <if test="bpacSnDt != null">bpac_SN_dt,</if>
            <if test="bpacForeignlock != null">bpac_foreignLock,</if>
            <if test="bpacSingaporehs != null">bpac_singaporeHS,</if>
            <if test="bpacRecordNumberSf != null">bpac_record_number_SF,</if>
            <if test="bpacCiqcode != null">bpac_CIQcode,</if>
            <if test="bpacQuality != null">bpac_quality,</if>
            <if test="bpacSupplyChain != null">bpac_supply_chain,</if>
            <if test="bpacProductInfo != null">bpac_product_info,</if>
            <if test="bpacProductMarket != null">bpac_product_market,</if>
            <if test="bpacTransportName != null">bpac_transport_name,</if>
            <if test="bpacLq != null">bpac_lq,</if>
            <if test="bpacEq != null">bpac_eq,</if>
            <if test="bpacHn != null">bpac_hn,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bpacCd != null">#{bpacCd},</if>
            <if test="bpacMcd != null">#{bpacMcd},</if>
            <if test="bpacCas != null">#{bpacCas},</if>
            <if test="bpacIupac != null">#{bpacIupac},</if>
            <if test="bpacMdl != null">#{bpacMdl},</if>
            <if test="bpacEinecs != null">#{bpacEinecs},</if>
            <if test="bpacSmiles != null">#{bpacSmiles},</if>
            <if test="bpacPurity != null">#{bpacPurity},</if>
            <if test="bpacFnm != null">#{bpacFnm},</if>
            <if test="bpacEfnm != null">#{bpacEfnm},</if>
            <if test="bpacFnm1 != null">#{bpacFnm1},</if>
            <if test="bpacFnm2 != null">#{bpacFnm2},</if>
            <if test="bpacMf != null">#{bpacMf},</if>
            <if test="bpacEm != null">#{bpacEm},</if>
            <if test="bpacMw != null">#{bpacMw},</if>
            <if test="bpacColor != null">#{bpacColor},</if>
            <if test="bpacAppear != null">#{bpacAppear},</if>
            <if test="bpacLos != null">#{bpacLos},</if>
            <if test="bpacCharacter != null">#{bpacCharacter},</if>
            <if test="bpacUnit != null">#{bpacUnit},</if>
            <if test="bpacTaxonomy != null">#{bpacTaxonomy},</if>
            <if test="bpacSmell != null">#{bpacSmell},</if>
            <if test="bpacRi != null">#{bpacRi},</if>
            <if test="bpacVade != null">#{bpacVade},</if>
            <if test="bpacFp != null">#{bpacFp},</if>
            <if test="bpacBp != null">#{bpacBp},</if>
            <if test="bpacMp != null">#{bpacMp},</if>
            <if test="bpacAutoign != null">#{bpacAutoign},</if>
            <if test="bpacSolu != null">#{bpacSolu},</if>
            <if test="bpacVapp != null">#{bpacVapp},</if>
            <if test="bpacDens != null">#{bpacDens},</if>
            <if test="bpacDgcls != null">#{bpacDgcls},</if>
            <if test="bpacDgsign != null">#{bpacDgsign},</if>
            <if test="bpacDgtno != null">#{bpacDgtno},</if>
            <if test="bpacRiskcd != null">#{bpacRiskcd},</if>
            <if test="bpacSafetydesc != null">#{bpacSafetydesc},</if>
            <if test="bpacPackgrp != null">#{bpacPackgrp},</if>
            <if test="bpacWgk != null">#{bpacWgk},</if>
            <if test="bpacRtecs != null">#{bpacRtecs},</if>
            <if test="bpacF != null">#{bpacF},</if>
            <if test="bpacScLabelPrint != null">#{bpacScLabelPrint},</if>
            <if test="bpacSc != null">#{bpacSc},</if>
            <if test="bpacScPtst != null">#{bpacScPtst},</if>
            <if test="bpacCp != null">#{bpacCp},</if>
            <if test="bpacUsage != null">#{bpacUsage},</if>
            <if test="bpacIsal != null">#{bpacIsal},</if>
            <if test="bpacIsmp != null">#{bpacIsmp},</if>
            <if test="bpacPackinstr != null">#{bpacPackinstr},</if>
            <if test="bpacFprod != null">#{bpacFprod},</if>
            <if test="bpacEp != null">#{bpacEp},</if>
            <if test="bpacSnote != null">#{bpacSnote},</if>
            <if test="bpacSprmk != null">#{bpacSprmk},</if>
            <if test="bpacSprmkPoisonod != null">#{bpacSprmkPoisonod},</if>
            <if test="bpacSprmkDanger != null">#{bpacSprmkDanger},</if>
            <if test="bpacImg != null">#{bpacImg},</if>
            <if test="bpacUnn != null">#{bpacUnn},</if>
            <if test="bpacGhssg != null">#{bpacGhssg},</if>
            <if test="bpacGhsswg != null">#{bpacGhsswg},</if>
            <if test="bpacGhsswg2 != null">#{bpacGhsswg2},</if>
            <if test="bpacGhshsg != null">#{bpacGhshsg},</if>
            <if test="bpacGhspsg != null">#{bpacGhspsg},</if>
            <if test="bpacInfo != null">#{bpacInfo},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="bpacRpknote != null">#{bpacRpknote},</if>
            <if test="bpacUseprecautions != null">#{bpacUseprecautions},</if>
            <if test="bpacClassiimdlRmk != null">#{bpacClassiimdlRmk},</if>
            <if test="bpacRawMaterial != null">#{bpacRawMaterial},</if>
            <if test="bpacSprmkPoison != null">#{bpacSprmkPoison},</if>
            <if test="bpacProductUse != null">#{bpacProductUse},</if>
            <if test="bpacLiteratureSource != null">#{bpacLiteratureSource},</if>
            <if test="bpacWarning != null">#{bpacWarning},</if>
            <if test="bpacPictogram != null">#{bpacPictogram},</if>
            <if test="bpacPrecautionary != null">#{bpacPrecautionary},</if>
            <if test="bpacAccident != null">#{bpacAccident},</if>
            <if test="bpacStorage != null">#{bpacStorage},</if>
            <if test="bpacDisposal != null">#{bpacDisposal},</if>
            <if test="bpacHscode != null">#{bpacHscode},</if>
            <if test="bpacRiskcate != null">#{bpacRiskcate},</if>
            <if test="bpacPch != null">#{bpacPch},</if>
            <if test="bpacHealth != null">#{bpacHealth},</if>
            <if test="bpacEnvironmentalCode != null">#{bpacEnvironmentalCode},</if>
            <if test="bpacExtinguish != null">#{bpacExtinguish},</if>
            <if test="bpacSpecialhazard != null">#{bpacSpecialhazard},</if>
            <if test="bpacPh != null">#{bpacPh},</if>
            <if test="bpacLimit != null">#{bpacLimit},</if>
            <if test="bpacEvaporation != null">#{bpacEvaporation},</if>
            <if test="bpacPartition != null">#{bpacPartition},</if>
            <if test="bpacDt != null">#{bpacDt},</if>
            <if test="bpacFlammability != null">#{bpacFlammability},</if>
            <if test="bpacOral != null">#{bpacOral},</if>
            <if test="bpacInhalation != null">#{bpacInhalation},</if>
            <if test="bpacDermal != null">#{bpacDermal},</if>
            <if test="bpacSkin != null">#{bpacSkin},</if>
            <if test="bpacEye != null">#{bpacEye},</if>
            <if test="bpacRespiratory != null">#{bpacRespiratory},</if>
            <if test="bpacMutagenicity != null">#{bpacMutagenicity},</if>
            <if test="bpacCarcinogenicity != null">#{bpacCarcinogenicity},</if>
            <if test="bpacReproductive != null">#{bpacReproductive},</if>
            <if test="bpacSingle != null">#{bpacSingle},</if>
            <if test="bpacRepeated != null">#{bpacRepeated},</if>
            <if test="bpacAddinfo != null">#{bpacAddinfo},</if>
            <if test="bpacTofish != null">#{bpacTofish},</if>
            <if test="bpacTodaphnia != null">#{bpacTodaphnia},</if>
            <if test="bpacToalgae != null">#{bpacToalgae},</if>
            <if test="bpacTobacteria != null">#{bpacTobacteria},</if>
            <if test="bpacPersistence != null">#{bpacPersistence},</if>
            <if test="bpacBioaccumulative != null">#{bpacBioaccumulative},</if>
            <if test="bpacMobility != null">#{bpacMobility},</if>
            <if test="bpacEffects != null">#{bpacEffects},</if>
            <if test="bpacEnvironmental != null">#{bpacEnvironmental},</if>
            <if test="bpacMsdsUrl != null">#{bpacMsdsUrl},</if>
            <if test="bpacPermitThrough != null">#{bpacPermitThrough},</if>
            <if test="bpacSnQualifications != null">#{bpacSnQualifications},</if>
            <if test="bpacIsrisk != null">#{bpacIsrisk},</if>
            <if test="bpacCodehs != null">#{bpacCodehs},</if>
            <if test="bpacDgcargo != null">#{bpacDgcargo},</if>
            <if test="bpacDgsource != null">#{bpacDgsource},</if>
            <if test="bpacInportlimit != null">#{bpacInportlimit},</if>
            <if test="bpacExportlimit != null">#{bpacExportlimit},</if>
            <if test="bpacPhscope != null">#{bpacPhscope},</if>
            <if test="bpacDensity != null">#{bpacDensity},</if>
            <if test="bpacDgclsNext != null">#{bpacDgclsNext},</if>
            <if test="bpacNdgclsClass != null">#{bpacNdgclsClass},</if>
            <if test="bpacMaintain != null">#{bpacMaintain},</if>
            <if test="bpacPurityEn != null">#{bpacPurityEn},</if>
            <if test="bpacIsnewsubstances != null">#{bpacIsnewsubstances},</if>
            <if test="bpacSn != null">#{bpacSn},</if>
            <if test="bpacSpecialmarking != null">#{bpacSpecialmarking},</if>
            <if test="bpacDgclsNo != null">#{bpacDgclsNo},</if>
            <if test="bpacSnReturnCode != null">#{bpacSnReturnCode},</if>
            <if test="bpacSnDt != null">#{bpacSnDt},</if>
            <if test="bpacForeignlock != null">#{bpacForeignlock},</if>
            <if test="bpacSingaporehs != null">#{bpacSingaporehs},</if>
            <if test="bpacRecordNumberSf != null">#{bpacRecordNumberSf},</if>
            <if test="bpacCiqcode != null">#{bpacCiqcode},</if>
            <if test="bpacQuality != null">#{bpacQuality},</if>
            <if test="bpacSupplyChain != null">#{bpacSupplyChain},</if>
            <if test="bpacProductInfo != null">#{bpacProductInfo},</if>
            <if test="bpacProductMarket != null">#{bpacProductMarket},</if>
            <if test="bpacTransportName != null">#{bpacTransportName},</if>
            <if test="bpacLq != null">#{bpacLq},</if>
            <if test="bpacEq != null">#{bpacEq},</if>
            <if test="bpacHn != null">#{bpacHn},</if>
         </trim>
    </insert>

    <update id="updateBasProdAchem" parameterType="BasProdAchem">
        update bas_prod_achem
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="bpacMcd != null">bpac_mcd = #{bpacMcd},</if>
            <if test="bpacCas != null">bpac_cas = #{bpacCas},</if>
            <if test="bpacIupac != null">bpac_iupac = #{bpacIupac},</if>
            <if test="bpacMdl != null">bpac_mdl = #{bpacMdl},</if>
            <if test="bpacEinecs != null">bpac_einecs = #{bpacEinecs},</if>
            <if test="bpacSmiles != null">bpac_smiles = #{bpacSmiles},</if>
            <if test="bpacPurity != null">bpac_purity = #{bpacPurity},</if>
            <if test="bpacFnm != null">bpac_fnm = #{bpacFnm},</if>
            <if test="bpacEfnm != null">bpac_efnm = #{bpacEfnm},</if>
            <if test="bpacFnm1 != null">bpac_fnm1 = #{bpacFnm1},</if>
            <if test="bpacFnm2 != null">bpac_fnm2 = #{bpacFnm2},</if>
            <if test="bpacMf != null">bpac_mf = #{bpacMf},</if>
            <if test="bpacEm != null">bpac_em = #{bpacEm},</if>
            <if test="bpacMw != null">bpac_mw = #{bpacMw},</if>
            <if test="bpacColor != null">bpac_color = #{bpacColor},</if>
            <if test="bpacAppear != null">bpac_appear = #{bpacAppear},</if>
            <if test="bpacLos != null">bpac_los = #{bpacLos},</if>
            <if test="bpacCharacter != null">bpac_character = #{bpacCharacter},</if>
            <if test="bpacUnit != null">bpac_unit = #{bpacUnit},</if>
            <if test="bpacTaxonomy != null">bpac_taxonomy = #{bpacTaxonomy},</if>
            <if test="bpacSmell != null">bpac_smell = #{bpacSmell},</if>
            <if test="bpacRi != null">bpac_ri = #{bpacRi},</if>
            <if test="bpacVade != null">bpac_vade = #{bpacVade},</if>
            <if test="bpacFp != null">bpac_fp = #{bpacFp},</if>
            <if test="bpacBp != null">bpac_bp = #{bpacBp},</if>
            <if test="bpacMp != null">bpac_mp = #{bpacMp},</if>
            <if test="bpacAutoign != null">bpac_autoign = #{bpacAutoign},</if>
            <if test="bpacSolu != null">bpac_solu = #{bpacSolu},</if>
            <if test="bpacVapp != null">bpac_vapp = #{bpacVapp},</if>
            <if test="bpacDens != null">bpac_dens = #{bpacDens},</if>
            <if test="bpacDgcls != null">bpac_dgcls = #{bpacDgcls},</if>
            <if test="bpacDgsign != null">bpac_dgsign = #{bpacDgsign},</if>
            <if test="bpacDgtno != null">bpac_dgtno = #{bpacDgtno},</if>
            <if test="bpacRiskcd != null">bpac_riskcd = #{bpacRiskcd},</if>
            <if test="bpacSafetydesc != null">bpac_safetydesc = #{bpacSafetydesc},</if>
            <if test="bpacPackgrp != null">bpac_packgrp = #{bpacPackgrp},</if>
            <if test="bpacWgk != null">bpac_wgk = #{bpacWgk},</if>
            <if test="bpacRtecs != null">bpac_rtecs = #{bpacRtecs},</if>
            <if test="bpacF != null">bpac_f = #{bpacF},</if>
            <if test="bpacScLabelPrint != null">bpac_sc_label_print = #{bpacScLabelPrint},</if>
            <if test="bpacSc != null">bpac_sc = #{bpacSc},</if>
            <if test="bpacScPtst != null">bpac_sc_ptst = #{bpacScPtst},</if>
            <if test="bpacCp != null">bpac_cp = #{bpacCp},</if>
            <if test="bpacUsage != null">bpac_usage = #{bpacUsage},</if>
            <if test="bpacIsal != null">bpac_isal = #{bpacIsal},</if>
            <if test="bpacIsmp != null">bpac_ismp = #{bpacIsmp},</if>
            <if test="bpacPackinstr != null">bpac_packinstr = #{bpacPackinstr},</if>
            <if test="bpacFprod != null">bpac_fprod = #{bpacFprod},</if>
            <if test="bpacEp != null">bpac_ep = #{bpacEp},</if>
            <if test="bpacSnote != null">bpac_snote = #{bpacSnote},</if>
            <if test="bpacSprmk != null">bpac_sprmk = #{bpacSprmk},</if>
            <if test="bpacSprmkPoisonod != null">bpac_sprmk_poisonod = #{bpacSprmkPoisonod},</if>
            <if test="bpacSprmkDanger != null">bpac_sprmk_danger = #{bpacSprmkDanger},</if>
            <if test="bpacImg != null">bpac_img = #{bpacImg},</if>
            <if test="bpacUnn != null">bpac_unn = #{bpacUnn},</if>
            <if test="bpacGhssg != null">bpac_ghssg = #{bpacGhssg},</if>
            <if test="bpacGhsswg != null">bpac_ghsswg = #{bpacGhsswg},</if>
            <if test="bpacGhsswg2 != null">bpac_ghsswg2 = #{bpacGhsswg2},</if>
            <if test="bpacGhshsg != null">bpac_ghshsg = #{bpacGhshsg},</if>
            <if test="bpacGhspsg != null">bpac_ghspsg = #{bpacGhspsg},</if>
            <if test="bpacInfo != null">bpac_info = #{bpacInfo},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="bpacRpknote != null">bpac_rpknote = #{bpacRpknote},</if>
            <if test="bpacUseprecautions != null">bpac_useprecautions = #{bpacUseprecautions},</if>
            <if test="bpacClassiimdlRmk != null">bpac_classiimdl_rmk = #{bpacClassiimdlRmk},</if>
            <if test="bpacRawMaterial != null">bpac_raw_material = #{bpacRawMaterial},</if>
            <if test="bpacSprmkPoison != null">bpac_sprmk_poison = #{bpacSprmkPoison},</if>
            <if test="bpacProductUse != null">bpac_product_use = #{bpacProductUse},</if>
            <if test="bpacLiteratureSource != null">bpac_literature_source = #{bpacLiteratureSource},</if>
            <if test="bpacWarning != null">bpac_warning = #{bpacWarning},</if>
            <if test="bpacPictogram != null">bpac_pictogram = #{bpacPictogram},</if>
            <if test="bpacPrecautionary != null">bpac_precautionary = #{bpacPrecautionary},</if>
            <if test="bpacAccident != null">bpac_accident = #{bpacAccident},</if>
            <if test="bpacStorage != null">bpac_storage = #{bpacStorage},</if>
            <if test="bpacDisposal != null">bpac_disposal = #{bpacDisposal},</if>
            <if test="bpacHscode != null">bpac_hscode = #{bpacHscode},</if>
            <if test="bpacRiskcate != null">bpac_riskCate = #{bpacRiskcate},</if>
            <if test="bpacPch != null">bpac_pch = #{bpacPch},</if>
            <if test="bpacHealth != null">bpac_health = #{bpacHealth},</if>
            <if test="bpacEnvironmentalCode != null">bpac_environmental_code = #{bpacEnvironmentalCode},</if>
            <if test="bpacExtinguish != null">bpac_extinguish = #{bpacExtinguish},</if>
            <if test="bpacSpecialhazard != null">bpac_specialhazard = #{bpacSpecialhazard},</if>
            <if test="bpacPh != null">bpac_ph = #{bpacPh},</if>
            <if test="bpacLimit != null">bpac_limit = #{bpacLimit},</if>
            <if test="bpacEvaporation != null">bpac_evaporation = #{bpacEvaporation},</if>
            <if test="bpacPartition != null">bpac_partition = #{bpacPartition},</if>
            <if test="bpacDt != null">bpac_dt = #{bpacDt},</if>
            <if test="bpacFlammability != null">bpac_flammability = #{bpacFlammability},</if>
            <if test="bpacOral != null">bpac_oral = #{bpacOral},</if>
            <if test="bpacInhalation != null">bpac_inhalation = #{bpacInhalation},</if>
            <if test="bpacDermal != null">bpac_dermal = #{bpacDermal},</if>
            <if test="bpacSkin != null">bpac_skin = #{bpacSkin},</if>
            <if test="bpacEye != null">bpac_eye = #{bpacEye},</if>
            <if test="bpacRespiratory != null">bpac_respiratory = #{bpacRespiratory},</if>
            <if test="bpacMutagenicity != null">bpac_mutagenicity = #{bpacMutagenicity},</if>
            <if test="bpacCarcinogenicity != null">bpac_carcinogenicity = #{bpacCarcinogenicity},</if>
            <if test="bpacReproductive != null">bpac_reproductive = #{bpacReproductive},</if>
            <if test="bpacSingle != null">bpac_single = #{bpacSingle},</if>
            <if test="bpacRepeated != null">bpac_repeated = #{bpacRepeated},</if>
            <if test="bpacAddinfo != null">bpac_addinfo = #{bpacAddinfo},</if>
            <if test="bpacTofish != null">bpac_tofish = #{bpacTofish},</if>
            <if test="bpacTodaphnia != null">bpac_todaphnia = #{bpacTodaphnia},</if>
            <if test="bpacToalgae != null">bpac_toalgae = #{bpacToalgae},</if>
            <if test="bpacTobacteria != null">bpac_tobacteria = #{bpacTobacteria},</if>
            <if test="bpacPersistence != null">bpac_persistence = #{bpacPersistence},</if>
            <if test="bpacBioaccumulative != null">bpac_bioaccumulative = #{bpacBioaccumulative},</if>
            <if test="bpacMobility != null">bpac_mobility = #{bpacMobility},</if>
            <if test="bpacEffects != null">bpac_effects = #{bpacEffects},</if>
            <if test="bpacEnvironmental != null">bpac_environmental = #{bpacEnvironmental},</if>
            <if test="bpacMsdsUrl != null">bpac_msds_url = #{bpacMsdsUrl},</if>
            <if test="bpacPermitThrough != null">bpac_permit_through = #{bpacPermitThrough},</if>
            <if test="bpacSnQualifications != null">bpac_sn_qualifications = #{bpacSnQualifications},</if>
            <if test="bpacIsrisk != null">bpac_isrisk = #{bpacIsrisk},</if>
            <if test="bpacCodehs != null">bpac_codehs = #{bpacCodehs},</if>
            <if test="bpacDgcargo != null">bpac_dgcargo = #{bpacDgcargo},</if>
            <if test="bpacDgsource != null">bpac_dgsource = #{bpacDgsource},</if>
            <if test="bpacInportlimit != null">bpac_inportlimit = #{bpacInportlimit},</if>
            <if test="bpacExportlimit != null">bpac_exportlimit = #{bpacExportlimit},</if>
            <if test="bpacPhscope != null">bpac_phscope = #{bpacPhscope},</if>
            <if test="bpacDensity != null">bpac_density = #{bpacDensity},</if>
            <if test="bpacDgclsNext != null">bpac_dgcls_next = #{bpacDgclsNext},</if>
            <if test="bpacNdgclsClass != null">bpac_ndgcls_class = #{bpacNdgclsClass},</if>
            <if test="bpacMaintain != null">bpac_maintain = #{bpacMaintain},</if>
            <if test="bpacPurityEn != null">bpac_purity_en = #{bpacPurityEn},</if>
            <if test="bpacIsnewsubstances != null">bpac_isNewSubstances = #{bpacIsnewsubstances},</if>
            <if test="bpacSn != null">bpac_SN = #{bpacSn},</if>
            <if test="bpacSpecialmarking != null">bpac_specialMarking = #{bpacSpecialmarking},</if>
            <if test="bpacDgclsNo != null">bpac_dgcls_no = #{bpacDgclsNo},</if>
            <if test="bpacSnReturnCode != null">bpac_SN_return_code = #{bpacSnReturnCode},</if>
            <if test="bpacSnDt != null">bpac_SN_dt = #{bpacSnDt},</if>
            <if test="bpacForeignlock != null">bpac_foreignLock = #{bpacForeignlock},</if>
            <if test="bpacSingaporehs != null">bpac_singaporeHS = #{bpacSingaporehs},</if>
            <if test="bpacRecordNumberSf != null">bpac_record_number_SF = #{bpacRecordNumberSf},</if>
            <if test="bpacCiqcode != null">bpac_CIQcode = #{bpacCiqcode},</if>
            <if test="bpacQuality != null">bpac_quality = #{bpacQuality},</if>
            <if test="bpacSupplyChain != null">bpac_supply_chain = #{bpacSupplyChain},</if>
            <if test="bpacProductInfo != null">bpac_product_info = #{bpacProductInfo},</if>
            <if test="bpacProductMarket != null">bpac_product_market = #{bpacProductMarket},</if>
            <if test="bpacTransportName != null">bpac_transport_name = #{bpacTransportName},</if>
            <if test="bpacLq != null">bpac_lq = #{bpacLq},</if>
            <if test="bpacEq != null">bpac_eq = #{bpacEq},</if>
            <if test="bpacHn != null">bpac_hn = #{bpacHn},</if>
        </trim>
        where bpac_cd = #{bpacCd}
    </update>

    <delete id="deleteBasProdAchemByBpacCd" parameterType="String">
        delete from bas_prod_achem where bpac_cd = #{bpacCd}
    </delete>

    <delete id="deleteBasProdAchemByBpacCds" parameterType="String">
        delete from bas_prod_achem where bpac_cd in 
        <foreach item="bpacCd" collection="array" open="(" separator="," close=")">
            #{bpacCd}
        </foreach>
    </delete>
</mapper>