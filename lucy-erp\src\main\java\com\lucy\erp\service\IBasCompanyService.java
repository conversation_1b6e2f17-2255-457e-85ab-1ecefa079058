package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.BasCompany;

/**
 * 基础_公司Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IBasCompanyService 
{
    /**
     * 查询基础_公司
     * 
     * @param bcCd 基础_公司主键
     * @return 基础_公司
     */
    public BasCompany selectBasCompanyByBcCd(String bcCd);

    /**
     * 查询基础_公司列表
     * 
     * @param basCompany 基础_公司
     * @return 基础_公司集合
     */
    public List<BasCompany> selectBasCompanyList(BasCompany basCompany);

    /**
     * 新增基础_公司
     * 
     * @param basCompany 基础_公司
     * @return 结果
     */
    public int insertBasCompany(BasCompany basCompany);

    /**
     * 修改基础_公司
     * 
     * @param basCompany 基础_公司
     * @return 结果
     */
    public int updateBasCompany(BasCompany basCompany);

    /**
     * 批量删除基础_公司
     * 
     * @param bcCds 需要删除的基础_公司主键集合
     * @return 结果
     */
    public int deleteBasCompanyByBcCds(String[] bcCds);

    /**
     * 删除基础_公司信息
     * 
     * @param bcCd 基础_公司主键
     * @return 结果
     */
    public int deleteBasCompanyByBcCd(String bcCd);
}
