package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.SystemCodeDtl;

/**
 * 系统_字典Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface SystemCodeDtlMapper 
{
    /**
     * 查询系统_字典
     * 
     * @param id 系统_字典主键
     * @return 系统_字典
     */
    public SystemCodeDtl selectSystemCodeDtlById(Long id);

    /**
     * 查询系统_字典列表
     * 
     * @param systemCodeDtl 系统_字典
     * @return 系统_字典集合
     */
    public List<SystemCodeDtl> selectSystemCodeDtlList(SystemCodeDtl systemCodeDtl);

    /**
     * 新增系统_字典
     * 
     * @param systemCodeDtl 系统_字典
     * @return 结果
     */
    public int insertSystemCodeDtl(SystemCodeDtl systemCodeDtl);

    /**
     * 修改系统_字典
     * 
     * @param systemCodeDtl 系统_字典
     * @return 结果
     */
    public int updateSystemCodeDtl(SystemCodeDtl systemCodeDtl);

    /**
     * 删除系统_字典
     * 
     * @param id 系统_字典主键
     * @return 结果
     */
    public int deleteSystemCodeDtlById(Long id);

    /**
     * 批量删除系统_字典
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSystemCodeDtlByIds(Long[] ids);
}
