package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.BasCompany;

/**
 * 基础_公司Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface BasCompanyMapper 
{
    /**
     * 查询基础_公司
     * 
     * @param bcCd 基础_公司主键
     * @return 基础_公司
     */
    public BasCompany selectBasCompanyByBcCd(String bcCd);

    /**
     * 查询基础_公司列表
     * 
     * @param basCompany 基础_公司
     * @return 基础_公司集合
     */
    public List<BasCompany> selectBasCompanyList(BasCompany basCompany);

    /**
     * 新增基础_公司
     * 
     * @param basCompany 基础_公司
     * @return 结果
     */
    public int insertBasCompany(BasCompany basCompany);

    /**
     * 修改基础_公司
     * 
     * @param basCompany 基础_公司
     * @return 结果
     */
    public int updateBasCompany(BasCompany basCompany);

    /**
     * 删除基础_公司
     * 
     * @param bcCd 基础_公司主键
     * @return 结果
     */
    public int deleteBasCompanyByBcCd(String bcCd);

    /**
     * 批量删除基础_公司
     * 
     * @param bcCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasCompanyByBcCds(String[] bcCds);
}
