package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogRepackPackageUseResultDtl;
import com.lucy.erp.service.ILogRepackPackageUseResultDtlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产成品投料明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/log/resultdtl")
public class LogRepackPackageUseResultDtlController extends BaseController
{
    @Autowired
    private ILogRepackPackageUseResultDtlService logRepackPackageUseResultDtlService;

    /**
     * 查询产成品投料明细列表
     */
    @PreAuthorize("@ss.hasPermi('log:resultdtl:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogRepackPackageUseResultDtl logRepackPackageUseResultDtl)
    {
        startPage();
        List<LogRepackPackageUseResultDtl> list = logRepackPackageUseResultDtlService.selectLogRepackPackageUseResultDtlList(logRepackPackageUseResultDtl);
        return getDataTable(list);
    }

    /**
     * 导出产成品投料明细列表
     */
    @PreAuthorize("@ss.hasPermi('log:resultdtl:export')")
    @Log(title = "产成品投料明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogRepackPackageUseResultDtl logRepackPackageUseResultDtl)
    {
        List<LogRepackPackageUseResultDtl> list = logRepackPackageUseResultDtlService.selectLogRepackPackageUseResultDtlList(logRepackPackageUseResultDtl);
        ExcelUtil<LogRepackPackageUseResultDtl> util = new ExcelUtil<LogRepackPackageUseResultDtl>(LogRepackPackageUseResultDtl.class);
        util.exportExcel(response, list, "产成品投料明细数据");
    }

    /**
     * 获取产成品投料明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:resultdtl:query')")
    @GetMapping(value = "/{lrpurdMcd}")
    public AjaxResult getInfo(@PathVariable("lrpurdMcd") String lrpurdMcd)
    {
        return success(logRepackPackageUseResultDtlService.selectLogRepackPackageUseResultDtlByLrpurdMcd(lrpurdMcd));
    }

    /**
     * 新增产成品投料明细
     */
    @PreAuthorize("@ss.hasPermi('log:resultdtl:add')")
    @Log(title = "产成品投料明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogRepackPackageUseResultDtl logRepackPackageUseResultDtl)
    {
        return toAjax(logRepackPackageUseResultDtlService.insertLogRepackPackageUseResultDtl(logRepackPackageUseResultDtl));
    }

    /**
     * 修改产成品投料明细
     */
    @PreAuthorize("@ss.hasPermi('log:resultdtl:edit')")
    @Log(title = "产成品投料明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogRepackPackageUseResultDtl logRepackPackageUseResultDtl)
    {
        return toAjax(logRepackPackageUseResultDtlService.updateLogRepackPackageUseResultDtl(logRepackPackageUseResultDtl));
    }

    /**
     * 删除产成品投料明细
     */
    @PreAuthorize("@ss.hasPermi('log:resultdtl:remove')")
    @Log(title = "产成品投料明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lrpurdCds}")
    public AjaxResult remove(@PathVariable String[] lrpurdCds)
    {
        return toAjax(logRepackPackageUseResultDtlService.deleteLogRepackPackageUseResultDtlByLrpurdCds(lrpurdCds));
    }
}
