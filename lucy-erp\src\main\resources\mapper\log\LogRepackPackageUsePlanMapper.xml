<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogRepackPackageUsePlanMapper">
    
    <resultMap type="LogRepackPackageUsePlan" id="LogRepackPackageUsePlanResult">
        <result property="id"    column="id"    />
        <result property="lrpupCd"    column="lrpup_cd"    />
        <result property="lrpupTyp"    column="lrpup_typ"    />
        <result property="lrpupPsn"    column="lrpup_psn"    />
        <result property="lrpupPsnCd"    column="lrpup_psn_cd"    />
        <result property="lrpupMainSkuCd"    column="lrpup_main_sku_cd"    />
        <result property="lrpupMainSkuQty"    column="lrpup_main_sku_qty"    />
        <result property="lrpupGcd"    column="lrpup_gcd"    />
        <result property="lrpupSkuCd"    column="lrpup_sku_cd"    />
        <result property="lrpupSkuQty"    column="lrpup_sku_qty"    />
        <result property="lrpupSkuTotalQty"    column="lrpup_sku_total_qty"    />
        <result property="lrpupSkuTotalPrc"    column="lrpup_sku_total_prc"    />
        <result property="lrpupMainDate"    column="lrpup_main_date"    />
        <result property="flag"    column="flag"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lrpupPcd"    column="lrpup_pcd"    />
    </resultMap>

    <sql id="selectLogRepackPackageUsePlanVo">
        select lrpup.id, lrpup_cd, lrpup_typ, lrpup_psn, lrpup_psn_cd, lrpup_main_sku_cd, lrpup_main_sku_qty, lrpup_gcd, lrpup_sku_cd, lrpup_sku_qty, lrpup_sku_total_qty, lrpup_sku_total_prc, lrpup_main_date, flag, lrpup.r_cre_kid, lrpup.r_cre_dt, lrpup.r_upd_kid, lrpup.r_upd_dt, lrpup.r_v, lrpup.r_del, lrpup_pcd from log_repack_package_use_plan lrpup
    </sql>

    <select id="selectLogRepackPackageUsePlanList" parameterType="LogRepackPackageUsePlan" resultMap="LogRepackPackageUsePlanResult">
        <include refid="selectLogRepackPackageUsePlanVo"/>
        <if test="zti != null and zti != ''">
            LEFT JOIN log_pt_mst
              ON lpm_cd = lrpup_psn
        </if>
        <where>  
            <if test="lrpupTyp != null  and lrpupTyp != ''"> and lrpup_typ = #{lrpupTyp}</if>
            <if test="lrpupPsn != null  and lrpupPsn != ''"> and lrpup_psn = #{lrpupPsn}</if>
            <if test="lrpupPsnCd != null  and lrpupPsnCd != ''"> and lrpup_psn_cd = #{lrpupPsnCd}</if>
            <if test="lrpupMainSkuCd != null  and lrpupMainSkuCd != ''"> and lrpup_main_sku_cd = #{lrpupMainSkuCd}</if>
            <if test="lrpupMainSkuQty != null "> and lrpup_main_sku_qty = #{lrpupMainSkuQty}</if>
            <if test="lrpupGcd != null  and lrpupGcd != ''"> and lrpup_gcd = #{lrpupGcd}</if>
            <if test="lrpupSkuCd != null  and lrpupSkuCd != ''"> and lrpup_sku_cd = #{lrpupSkuCd}</if>
            <if test="lrpupSkuQty != null "> and lrpup_sku_qty = #{lrpupSkuQty}</if>
            <if test="lrpupSkuTotalQty != null "> and lrpup_sku_total_qty = #{lrpupSkuTotalQty}</if>
            <if test="lrpupSkuTotalPrc != null "> and lrpup_sku_total_prc = #{lrpupSkuTotalPrc}</if>
            <if test="lrpupMainDate != null "> and lrpup_main_date = #{lrpupMainDate}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and lrpup.r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and lrpup.r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and lrpup.r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and lrpup.r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and lrpup.r_v = #{rV}</if>
            <if test="rDel != null "> and lrpup.r_del = #{rDel}</if>
            <if test="lrpupPcd != null  and lrpupPcd != ''"> and lrpup_pcd = #{lrpupPcd}</if>
            <if test="startDate != null">
                AND lrpup.r_cre_dt >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                AND lrpup.r_cre_dt &lt;= DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
            </if>
            <if test="zti != null and zti != ''">
                AND lpm_loc_orgmcd = #{zti}
            </if>
            
        </where>
    </select>
    
    <select id="selectLogRepackPackageUsePlanByLrpupCd" parameterType="String" resultMap="LogRepackPackageUsePlanResult">
        <include refid="selectLogRepackPackageUsePlanVo"/>
        where lrpup_cd = #{lrpupCd}
    </select>

    <select id="selectLogRepackPackageUsePlanByLrpupPsn" parameterType="String" resultMap="LogRepackPackageUsePlanResult">
        <include refid="selectLogRepackPackageUsePlanVo"/>
        where lrpup_psn = #{lrpup_psn}
    </select>

    <insert id="insertLogRepackPackageUsePlan" parameterType="LogRepackPackageUsePlan">
        insert into log_repack_package_use_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lrpupCd != null">lrpup_cd,</if>
            <if test="lrpupTyp != null">lrpup_typ,</if>
            <if test="lrpupPsn != null and lrpupPsn != ''">lrpup_psn,</if>
            <if test="lrpupPsnCd != null">lrpup_psn_cd,</if>
            <if test="lrpupMainSkuCd != null">lrpup_main_sku_cd,</if>
            <if test="lrpupMainSkuQty != null">lrpup_main_sku_qty,</if>
            <if test="lrpupGcd != null">lrpup_gcd,</if>
            <if test="lrpupSkuCd != null and lrpupSkuCd != ''">lrpup_sku_cd,</if>
            <if test="lrpupSkuQty != null">lrpup_sku_qty,</if>
            <if test="lrpupSkuTotalQty != null">lrpup_sku_total_qty,</if>
            <if test="lrpupSkuTotalPrc != null">lrpup_sku_total_prc,</if>
            <if test="lrpupMainDate != null">lrpup_main_date,</if>
            <if test="flag != null">flag,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="lrpupPcd != null">lrpup_pcd,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lrpupCd != null">#{lrpupCd},</if>
            <if test="lrpupTyp != null">#{lrpupTyp},</if>
            <if test="lrpupPsn != null and lrpupPsn != ''">#{lrpupPsn},</if>
            <if test="lrpupPsnCd != null">#{lrpupPsnCd},</if>
            <if test="lrpupMainSkuCd != null">#{lrpupMainSkuCd},</if>
            <if test="lrpupMainSkuQty != null">#{lrpupMainSkuQty},</if>
            <if test="lrpupGcd != null">#{lrpupGcd},</if>
            <if test="lrpupSkuCd != null and lrpupSkuCd != ''">#{lrpupSkuCd},</if>
            <if test="lrpupSkuQty != null">#{lrpupSkuQty},</if>
            <if test="lrpupSkuTotalQty != null">#{lrpupSkuTotalQty},</if>
            <if test="lrpupSkuTotalPrc != null">#{lrpupSkuTotalPrc},</if>
            <if test="lrpupMainDate != null">#{lrpupMainDate},</if>
            <if test="flag != null">#{flag},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="lrpupPcd != null">#{lrpupPcd},</if>
         </trim>
    </insert>

    <update id="updateLogRepackPackageUsePlan" parameterType="LogRepackPackageUsePlan">
        update log_repack_package_use_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lrpupTyp != null">lrpup_typ = #{lrpupTyp},</if>
            <if test="lrpupPsn != null and lrpupPsn != ''">lrpup_psn = #{lrpupPsn},</if>
            <if test="lrpupPsnCd != null">lrpup_psn_cd = #{lrpupPsnCd},</if>
            <if test="lrpupMainSkuCd != null">lrpup_main_sku_cd = #{lrpupMainSkuCd},</if>
            <if test="lrpupMainSkuQty != null">lrpup_main_sku_qty = #{lrpupMainSkuQty},</if>
            <if test="lrpupGcd != null">lrpup_gcd = #{lrpupGcd},</if>
            <if test="lrpupSkuCd != null and lrpupSkuCd != ''">lrpup_sku_cd = #{lrpupSkuCd},</if>
            <if test="lrpupSkuQty != null">lrpup_sku_qty = #{lrpupSkuQty},</if>
            <if test="lrpupSkuTotalQty != null">lrpup_sku_total_qty = #{lrpupSkuTotalQty},</if>
            <if test="lrpupSkuTotalPrc != null">lrpup_sku_total_prc = #{lrpupSkuTotalPrc},</if>
            <if test="lrpupMainDate != null">lrpup_main_date = #{lrpupMainDate},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="lrpupPcd != null">lrpup_pcd = #{lrpupPcd},</if>
        </trim>
        where lrpup_cd = #{lrpupCd}
    </update>

    <delete id="deleteLogRepackPackageUsePlanByLrpupCd" parameterType="String">
        delete from log_repack_package_use_plan where lrpup_cd = #{lrpupCd}
    </delete>

    <delete id="deleteLogRepackPackageUsePlanByLrpupCds" parameterType="String">
        delete from log_repack_package_use_plan where lrpup_cd in 
        <foreach item="lrpupCd" collection="array" open="(" separator="," close=")">
            #{lrpupCd}
        </foreach>
    </delete>
</mapper>