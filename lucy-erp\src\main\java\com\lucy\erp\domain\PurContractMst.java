package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购_合同_主
对象 pur_contract_mst
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class PurContractMst extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 采购单号 */
    private String pcmCd;

    /** 采购日期 */
    @Excel(name = "采购日期")
    private String pcmDt;

    /** 发单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pcmDtfd;

    /** 组织编号 */
    @Excel(name = "组织编号")
    private String pcmOcd;

    /** 组织名称 */
    @Excel(name = "组织名称")
    private String pcmOnm;

    /** 采购员编号 */
    @Excel(name = "采购员编号")
    private String pcmUcd;

    /** 采购员姓名 */
    @Excel(name = "采购员姓名")
    private String pcmUnm;

    /** 供应商编号 */
    @Excel(name = "供应商编号")
    private String pcmScd;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String pcmSnm;

    /** 供应商联系电话 */
    @Excel(name = "供应商联系电话")
    private String pcmSfp;

    /** 传真 */
    @Excel(name = "传真")
    private String pcmSfax;

    /** 联系人编号 */
    @Excel(name = "联系人编号")
    private String pcmLmcd;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String pcmLmnm;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String pcmLmmp;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal pcmTotamt;

    /** 预付金额 */
    @Excel(name = "预付金额")
    private BigDecimal pcmPreamt;

    /** 发票类型 */
    @Excel(name = "发票类型")
    private String pcmInvtyp;

    /** 付款方式 */
    @Excel(name = "付款方式")
    private String pcmPaytyp;

    /** 付款方式明细 */
    @Excel(name = "付款方式明细")
    private String pcmPaydetailed;

    /** 采购类目 */
    @Excel(name = "采购类目")
    private String pcmPtyp;

    /** 运输条款 */
    @Excel(name = "运输条款")
    private String pcmTransport;

    /** 预计到货期 */
    @Excel(name = "预计到货期")
    private String pcmEadt;

    /** 备注 */
    @Excel(name = "备注")
    private String pcmRmk;

    /** 默认收货仓库 */
    @Excel(name = "默认收货仓库")
    private String pcmDcd;

    /** 状态编号 */
    @Excel(name = "状态编号")
    private String pcmStscd;

    /** 状态名称 */
    @Excel(name = "状态名称")
    private String pcmStsnm;

    /** 币种 */
    @Excel(name = "币种")
    private String pcmCurrency;

    /** 采购单来源，0为采购员手动制作，1为销售礼品自动生成 */
    @Excel(name = "采购单来源，0为采购员手动制作，1为销售礼品自动生成")
    private String pcmGeneration;

    /** 汇率 */
    @Excel(name = "汇率")
    private BigDecimal pcmExchange;

    /** 是否回单 */
    @Excel(name = "是否回单")
    private String pcmHuidan;

    /** 是否被导出 */
    @Excel(name = "是否被导出")
    private String pcmExce;

    /** 是否是直发用户 */
    @Excel(name = "是否是直发用户")
    private String pcmStraight;

    /** 供应商身份证 */
    @Excel(name = "供应商身份证")
    private String pcmIcPicurl;

    /** 使用证明 */
    @Excel(name = "使用证明")
    private String pcmUseVerify;

    /** 营业执照上传 */
    @Excel(name = "营业执照上传")
    private String pcmLicence;

    /** 采购员身份证复印件 */
    @Excel(name = "采购员身份证复印件")
    private String pcmPurCard;

    /** 采购合同PDF */
    @Excel(name = "采购合同PDF")
    private String pcmPurPdf;

    /** 发票PDF */
    @Excel(name = "发票PDF")
    private String pcmInvoicePdf;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Integer rDel;

    /** 需方组织 */
    @Excel(name = "需方组织")
    private String pcmDemander;

    /** 是否质检合格 */
    @Excel(name = "是否质检合格")
    private String pcmAqlyn;

    /** 是否录票 */
    @Excel(name = "是否录票")
    private String pcmInvoiceyn;

    /** 账期 */
    @Excel(name = "账期")
    private String pcmDdyn;

    /** 导出人 */
    @Excel(name = "导出人")
    private String pcmExcelNm;

    /** 是否进口业务，01 国内采购 02 进口采购 */
    @Excel(name = "是否进口业务，01 国内采购 02 进口采购")
    private String pcmIsImportPur;

    /** 出口仓（订单级别） */
    @Excel(name = "出口仓", readConverterExp = "订=单级别")
    private String pcmExportDmcd;

    /** 报价方式 CIF/FOB */
    @Excel(name = "报价方式 CIF/FOB")
    private String pcmQumethod;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPcmCd(String pcmCd) 
    {
        this.pcmCd = pcmCd;
    }

    public String getPcmCd() 
    {
        return pcmCd;
    }

    public void setPcmDt(String pcmDt) 
    {
        this.pcmDt = pcmDt;
    }

    public String getPcmDt() 
    {
        return pcmDt;
    }

    public void setPcmDtfd(Date pcmDtfd) 
    {
        this.pcmDtfd = pcmDtfd;
    }

    public Date getPcmDtfd() 
    {
        return pcmDtfd;
    }

    public void setPcmOcd(String pcmOcd) 
    {
        this.pcmOcd = pcmOcd;
    }

    public String getPcmOcd() 
    {
        return pcmOcd;
    }

    public void setPcmOnm(String pcmOnm) 
    {
        this.pcmOnm = pcmOnm;
    }

    public String getPcmOnm() 
    {
        return pcmOnm;
    }

    public void setPcmUcd(String pcmUcd) 
    {
        this.pcmUcd = pcmUcd;
    }

    public String getPcmUcd() 
    {
        return pcmUcd;
    }

    public void setPcmUnm(String pcmUnm) 
    {
        this.pcmUnm = pcmUnm;
    }

    public String getPcmUnm() 
    {
        return pcmUnm;
    }

    public void setPcmScd(String pcmScd) 
    {
        this.pcmScd = pcmScd;
    }

    public String getPcmScd() 
    {
        return pcmScd;
    }

    public void setPcmSnm(String pcmSnm) 
    {
        this.pcmSnm = pcmSnm;
    }

    public String getPcmSnm() 
    {
        return pcmSnm;
    }

    public void setPcmSfp(String pcmSfp) 
    {
        this.pcmSfp = pcmSfp;
    }

    public String getPcmSfp() 
    {
        return pcmSfp;
    }

    public void setPcmSfax(String pcmSfax) 
    {
        this.pcmSfax = pcmSfax;
    }

    public String getPcmSfax() 
    {
        return pcmSfax;
    }

    public void setPcmLmcd(String pcmLmcd) 
    {
        this.pcmLmcd = pcmLmcd;
    }

    public String getPcmLmcd() 
    {
        return pcmLmcd;
    }

    public void setPcmLmnm(String pcmLmnm) 
    {
        this.pcmLmnm = pcmLmnm;
    }

    public String getPcmLmnm() 
    {
        return pcmLmnm;
    }

    public void setPcmLmmp(String pcmLmmp) 
    {
        this.pcmLmmp = pcmLmmp;
    }

    public String getPcmLmmp() 
    {
        return pcmLmmp;
    }

    public void setPcmTotamt(BigDecimal pcmTotamt) 
    {
        this.pcmTotamt = pcmTotamt;
    }

    public BigDecimal getPcmTotamt() 
    {
        return pcmTotamt;
    }

    public void setPcmPreamt(BigDecimal pcmPreamt) 
    {
        this.pcmPreamt = pcmPreamt;
    }

    public BigDecimal getPcmPreamt() 
    {
        return pcmPreamt;
    }

    public void setPcmInvtyp(String pcmInvtyp) 
    {
        this.pcmInvtyp = pcmInvtyp;
    }

    public String getPcmInvtyp() 
    {
        return pcmInvtyp;
    }

    public void setPcmPaytyp(String pcmPaytyp) 
    {
        this.pcmPaytyp = pcmPaytyp;
    }

    public String getPcmPaytyp() 
    {
        return pcmPaytyp;
    }

    public void setPcmPaydetailed(String pcmPaydetailed) 
    {
        this.pcmPaydetailed = pcmPaydetailed;
    }

    public String getPcmPaydetailed() 
    {
        return pcmPaydetailed;
    }

    public void setPcmPtyp(String pcmPtyp) 
    {
        this.pcmPtyp = pcmPtyp;
    }

    public String getPcmPtyp() 
    {
        return pcmPtyp;
    }

    public void setPcmTransport(String pcmTransport) 
    {
        this.pcmTransport = pcmTransport;
    }

    public String getPcmTransport() 
    {
        return pcmTransport;
    }

    public void setPcmEadt(String pcmEadt) 
    {
        this.pcmEadt = pcmEadt;
    }

    public String getPcmEadt() 
    {
        return pcmEadt;
    }

    public void setPcmRmk(String pcmRmk) 
    {
        this.pcmRmk = pcmRmk;
    }

    public String getPcmRmk() 
    {
        return pcmRmk;
    }

    public void setPcmDcd(String pcmDcd) 
    {
        this.pcmDcd = pcmDcd;
    }

    public String getPcmDcd() 
    {
        return pcmDcd;
    }

    public void setPcmStscd(String pcmStscd) 
    {
        this.pcmStscd = pcmStscd;
    }

    public String getPcmStscd() 
    {
        return pcmStscd;
    }

    public void setPcmStsnm(String pcmStsnm) 
    {
        this.pcmStsnm = pcmStsnm;
    }

    public String getPcmStsnm() 
    {
        return pcmStsnm;
    }

    public void setPcmCurrency(String pcmCurrency) 
    {
        this.pcmCurrency = pcmCurrency;
    }

    public String getPcmCurrency() 
    {
        return pcmCurrency;
    }

    public void setPcmGeneration(String pcmGeneration) 
    {
        this.pcmGeneration = pcmGeneration;
    }

    public String getPcmGeneration() 
    {
        return pcmGeneration;
    }

    public void setPcmExchange(BigDecimal pcmExchange) 
    {
        this.pcmExchange = pcmExchange;
    }

    public BigDecimal getPcmExchange() 
    {
        return pcmExchange;
    }

    public void setPcmHuidan(String pcmHuidan) 
    {
        this.pcmHuidan = pcmHuidan;
    }

    public String getPcmHuidan() 
    {
        return pcmHuidan;
    }

    public void setPcmExce(String pcmExce) 
    {
        this.pcmExce = pcmExce;
    }

    public String getPcmExce() 
    {
        return pcmExce;
    }

    public void setPcmStraight(String pcmStraight) 
    {
        this.pcmStraight = pcmStraight;
    }

    public String getPcmStraight() 
    {
        return pcmStraight;
    }

    public void setPcmIcPicurl(String pcmIcPicurl) 
    {
        this.pcmIcPicurl = pcmIcPicurl;
    }

    public String getPcmIcPicurl() 
    {
        return pcmIcPicurl;
    }

    public void setPcmUseVerify(String pcmUseVerify) 
    {
        this.pcmUseVerify = pcmUseVerify;
    }

    public String getPcmUseVerify() 
    {
        return pcmUseVerify;
    }

    public void setPcmLicence(String pcmLicence) 
    {
        this.pcmLicence = pcmLicence;
    }

    public String getPcmLicence() 
    {
        return pcmLicence;
    }

    public void setPcmPurCard(String pcmPurCard) 
    {
        this.pcmPurCard = pcmPurCard;
    }

    public String getPcmPurCard() 
    {
        return pcmPurCard;
    }

    public void setPcmPurPdf(String pcmPurPdf) 
    {
        this.pcmPurPdf = pcmPurPdf;
    }

    public String getPcmPurPdf() 
    {
        return pcmPurPdf;
    }

    public void setPcmInvoicePdf(String pcmInvoicePdf) 
    {
        this.pcmInvoicePdf = pcmInvoicePdf;
    }

    public String getPcmInvoicePdf() 
    {
        return pcmInvoicePdf;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Integer rDel) 
    {
        this.rDel = rDel;
    }

    public Integer getrDel() 
    {
        return rDel;
    }

    public void setPcmDemander(String pcmDemander) 
    {
        this.pcmDemander = pcmDemander;
    }

    public String getPcmDemander() 
    {
        return pcmDemander;
    }

    public void setPcmAqlyn(String pcmAqlyn) 
    {
        this.pcmAqlyn = pcmAqlyn;
    }

    public String getPcmAqlyn() 
    {
        return pcmAqlyn;
    }

    public void setPcmInvoiceyn(String pcmInvoiceyn) 
    {
        this.pcmInvoiceyn = pcmInvoiceyn;
    }

    public String getPcmInvoiceyn() 
    {
        return pcmInvoiceyn;
    }

    public void setPcmDdyn(String pcmDdyn) 
    {
        this.pcmDdyn = pcmDdyn;
    }

    public String getPcmDdyn() 
    {
        return pcmDdyn;
    }

    public void setPcmExcelNm(String pcmExcelNm) 
    {
        this.pcmExcelNm = pcmExcelNm;
    }

    public String getPcmExcelNm() 
    {
        return pcmExcelNm;
    }

    public void setPcmIsImportPur(String pcmIsImportPur) 
    {
        this.pcmIsImportPur = pcmIsImportPur;
    }

    public String getPcmIsImportPur() 
    {
        return pcmIsImportPur;
    }

    public void setPcmExportDmcd(String pcmExportDmcd) 
    {
        this.pcmExportDmcd = pcmExportDmcd;
    }

    public String getPcmExportDmcd() 
    {
        return pcmExportDmcd;
    }

    public void setPcmQumethod(String pcmQumethod) 
    {
        this.pcmQumethod = pcmQumethod;
    }

    public String getPcmQumethod() 
    {
        return pcmQumethod;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pcmCd", getPcmCd())
            .append("pcmDt", getPcmDt())
            .append("pcmDtfd", getPcmDtfd())
            .append("pcmOcd", getPcmOcd())
            .append("pcmOnm", getPcmOnm())
            .append("pcmUcd", getPcmUcd())
            .append("pcmUnm", getPcmUnm())
            .append("pcmScd", getPcmScd())
            .append("pcmSnm", getPcmSnm())
            .append("pcmSfp", getPcmSfp())
            .append("pcmSfax", getPcmSfax())
            .append("pcmLmcd", getPcmLmcd())
            .append("pcmLmnm", getPcmLmnm())
            .append("pcmLmmp", getPcmLmmp())
            .append("pcmTotamt", getPcmTotamt())
            .append("pcmPreamt", getPcmPreamt())
            .append("pcmInvtyp", getPcmInvtyp())
            .append("pcmPaytyp", getPcmPaytyp())
            .append("pcmPaydetailed", getPcmPaydetailed())
            .append("pcmPtyp", getPcmPtyp())
            .append("pcmTransport", getPcmTransport())
            .append("pcmEadt", getPcmEadt())
            .append("pcmRmk", getPcmRmk())
            .append("pcmDcd", getPcmDcd())
            .append("pcmStscd", getPcmStscd())
            .append("pcmStsnm", getPcmStsnm())
            .append("pcmCurrency", getPcmCurrency())
            .append("pcmGeneration", getPcmGeneration())
            .append("pcmExchange", getPcmExchange())
            .append("pcmHuidan", getPcmHuidan())
            .append("pcmExce", getPcmExce())
            .append("pcmStraight", getPcmStraight())
            .append("pcmIcPicurl", getPcmIcPicurl())
            .append("pcmUseVerify", getPcmUseVerify())
            .append("pcmLicence", getPcmLicence())
            .append("pcmPurCard", getPcmPurCard())
            .append("pcmPurPdf", getPcmPurPdf())
            .append("pcmInvoicePdf", getPcmInvoicePdf())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("pcmDemander", getPcmDemander())
            .append("pcmAqlyn", getPcmAqlyn())
            .append("pcmInvoiceyn", getPcmInvoiceyn())
            .append("pcmDdyn", getPcmDdyn())
            .append("pcmExcelNm", getPcmExcelNm())
            .append("pcmIsImportPur", getPcmIsImportPur())
            .append("pcmExportDmcd", getPcmExportDmcd())
            .append("pcmQumethod", getPcmQumethod())
            .toString();
    }
}
