package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 销售_合同_明细对象 sales_contract_dtl
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class SalesContractDtl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 销售合同ID */
    @Excel(name = "销售合同ID")
    private Long scdMid;

    /** 销售合同CD */
    private String scdMcd;

    /** 销售合同明细CD */
    private String scdCd;

    /** 其它平台单号(界面填写用) */
    @Excel(name = "其它平台单号(界面填写用)")
    private String scdOpcd;

    /** 网站单号 (数据接口用) */
    @Excel(name = "网站单号 (数据接口用)")
    private String scdOscd;

    /** 类型 */
    @Excel(name = "类型")
    private String scdTyp;

    /** 打印类型 */
    @Excel(name = "打印类型")
    private String scdPrinttyp;

    /** 源编号(报价明细转合同后存入报价编号) */
    @Excel(name = "源编号(报价明细转合同后存入报价编号)")
    private String scdPrecd;

    /** 仓库 */
    @Excel(name = "仓库")
    private String scdDcd;

    /** 品牌 */
    @Excel(name = "品牌")
    private String scdBr;

    /** 产品ID */
    @Excel(name = "产品ID")
    private Long scdPmid;

    /** 产品种类 */
    @Excel(name = "产品种类")
    private String scdPmsort;

    /** 原始编号 */
    @Excel(name = "原始编号")
    private String scdPorgcd;

    /** 产品CD */
    @Excel(name = "产品CD")
    private String scdPmcd;

    /** 产品NM */
    @Excel(name = "产品NM")
    private String scdPmnm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String scdPmenm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String scdCas;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String scdPurity;

    /** 危品分类(国危2015) */
    @Excel(name = "危品分类(国危2015)")
    private String scdDcls;

    /** 特殊备注 */
    @Excel(name = "特殊备注")
    private String scdSprmk;

    /** 易涉毒备注 */
    @Excel(name = "易涉毒备注")
    private String scdSprmkPoison;

    /** 存储条件 */
    @Excel(name = "存储条件")
    private String scdStg;

    /** 包装ID */
    @Excel(name = "包装ID")
    private Long scdPpid;

    /** 包装CD */
    @Excel(name = "包装CD")
    private String scdPpcd;

    /** 包装 */
    @Excel(name = "包装")
    private String scdPack;

    /** 原价 */
    @Excel(name = "原价")
    private BigDecimal scdLstprc;

    /** 活动价 */
    @Excel(name = "活动价")
    private String scdPprc;

    /** 折扣类别 */
    @Excel(name = "折扣类别")
    private String scdDcttyp;

    /** 原折扣 */
    @Excel(name = "原折扣")
    private BigDecimal scdOrgdct;

    /** 折扣 */
    @Excel(name = "折扣")
    private BigDecimal scdDct;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal scdPrc;

    /** 单价调整 */
    @Excel(name = "单价调整")
    private BigDecimal scdPrcAdjust;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal scdQty;

    /** 预留数量 */
    @Excel(name = "预留数量")
    private BigDecimal scdQtyReserved;

    /** 计划出库数量 */
    @Excel(name = "计划出库数量")
    private BigDecimal scdPlanqty;

    /** 发运数量 */
    @Excel(name = "发运数量")
    private BigDecimal scdOutqty;

    /** 开票数量 */
    @Excel(name = "开票数量")
    private BigDecimal scdIqty;

    /** 调整总价 */
    @Excel(name = "调整总价")
    private BigDecimal scdTotprcAdjust;

    /** 活动满减价格 */
    @Excel(name = "活动满减价格")
    private BigDecimal scdFullCutPrice;

    /** 平台优惠金额 */
    @Excel(name = "平台优惠金额")
    private BigDecimal scdMallCouponAmount;

    /** 活动满折金额 */
    @Excel(name = "活动满折金额")
    private BigDecimal scdDisCountPrice;

    /** 总价 */
    @Excel(name = "总价")
    private BigDecimal scdTotprc;

    /** 金豆 */
    @Excel(name = "金豆")
    private Long scdGold;

    /** 积分 */
    @Excel(name = "积分")
    private Long scdPnt;

    /** 采购 */
    @Excel(name = "采购")
    private String scdPuryn;

    /** 货期 */
    @Excel(name = "货期")
    private String scdDd;

    /** 是否单独发货 */
    @Excel(name = "是否单独发货")
    private String scdIsss;

    /** 状态CD */
    @Excel(name = "状态CD")
    private String scdStscd;

    /** 状态NM */
    @Excel(name = "状态NM")
    private String scdStsnm;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scdSodt;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scdShdt;

    /** 最迟发货日期 */
    @Excel(name = "最迟发货日期")
    private String scdZcsd;

    /** 指定发货日期 */
    @Excel(name = "指定发货日期")
    private String scdSd;

    /** 出库日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出库日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scdOsdt;

    /** 库存备注 */
    @Excel(name = "库存备注")
    private String scdRmk;

    /** 销售备注 */
    @Excel(name = "销售备注")
    private String scdSrmk;

    /** 物流备注 */
    @Excel(name = "物流备注")
    private String scdLrmk;

    /** 出库单显示备注 */
    @Excel(name = "出库单显示备注")
    private String scdDymemo;

    /** 财务备注 */
    @Excel(name = "财务备注")
    private String scdFrmk;

    /** 使用人 */
    @Excel(name = "使用人")
    private String scdEplnm;

    /** 销售合同明细字段信息变更记录 */
    @Excel(name = "销售合同明细字段信息变更记录")
    private String scdStsrmk;

    /** 销售类目 */
    @Excel(name = "销售类目")
    private String scdStyp;

    /** 投诉事故单号 */
    @Excel(name = "投诉事故单号")
    private String scdTicketno;

    /** 使用积分 */
    @Excel(name = "使用积分")
    private Long scdPntused;

    /** 积分抵扣金额 */
    @Excel(name = "积分抵扣金额")
    private BigDecimal scdIntegralSharePrice;

    /** 使用金币 */
    @Excel(name = "使用金币")
    private Long scdGoldused;

    /** 金豆抵扣金额 */
    @Excel(name = "金豆抵扣金额")
    private BigDecimal scdGoldusedPrice;

    /** 自发（1）供应商直发（2） */
    @Excel(name = "自发", readConverterExp = "1=")
    private Integer scdTransportway;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 修改者 */
    @Excel(name = "修改者")
    private String rUpdKid;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本号 */
    @Excel(name = "行版本号")
    private Long rV;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long rDel;

    /** 指定批次发货 */
    @Excel(name = "指定批次发货")
    private String scdProdBn;

    /** 指定库位发货 */
    @Excel(name = "指定库位发货")
    private String scdSlcdBn;

    /** 是否自动开票 */
    @Excel(name = "是否自动开票")
    private String scdInvoiceMethod;

    /** 二类医疗器械备注 */
    @Excel(name = "二类医疗器械备注")
    private String scdClassiimdlRmk;

    /** 是否为虚拟礼品:1虚拟 */
    @Excel(name = "是否为虚拟礼品:1虚拟")
    private String scdVirtual;

    /** 原料药备注 */
    @Excel(name = "原料药备注")
    private String scdRawMaterial;

    /** 是否风险管控 */
    @Excel(name = "是否风险管控")
    private String scdIsrisk;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String scdMaterialcode;

    /** 询价编号 */
    @Excel(name = "询价编号")
    private String scdImcd;

    /** 客服推荐 */
    @Excel(name = "客服推荐")
    private String scdKfrmk;

    /** 打包服务(0:否，1:是) */
    @Excel(name = "打包服务(0:否，1:是)")
    private String scdPackageService;

    /** 0金额备注 */
    @Excel(name = "0金额备注")
    private String scdZamk;

    /** 仓库类型 */
    @Excel(name = "仓库类型")
    private String scdBdmType;

    /** 采购合同明细编号 */
    @Excel(name = "采购合同明细编号")
    private String scdPcdcd;

    /** 海外采购信息备注 */
    @Excel(name = "海外采购信息备注")
    private String scdRemarks;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setScdMid(Long scdMid) 
    {
        this.scdMid = scdMid;
    }

    public Long getScdMid() 
    {
        return scdMid;
    }

    public void setScdMcd(String scdMcd) 
    {
        this.scdMcd = scdMcd;
    }

    public String getScdMcd() 
    {
        return scdMcd;
    }

    public void setScdCd(String scdCd) 
    {
        this.scdCd = scdCd;
    }

    public String getScdCd() 
    {
        return scdCd;
    }

    public void setScdOpcd(String scdOpcd) 
    {
        this.scdOpcd = scdOpcd;
    }

    public String getScdOpcd() 
    {
        return scdOpcd;
    }

    public void setScdOscd(String scdOscd) 
    {
        this.scdOscd = scdOscd;
    }

    public String getScdOscd() 
    {
        return scdOscd;
    }

    public void setScdTyp(String scdTyp) 
    {
        this.scdTyp = scdTyp;
    }

    public String getScdTyp() 
    {
        return scdTyp;
    }

    public void setScdPrinttyp(String scdPrinttyp) 
    {
        this.scdPrinttyp = scdPrinttyp;
    }

    public String getScdPrinttyp() 
    {
        return scdPrinttyp;
    }

    public void setScdPrecd(String scdPrecd) 
    {
        this.scdPrecd = scdPrecd;
    }

    public String getScdPrecd() 
    {
        return scdPrecd;
    }

    public void setScdDcd(String scdDcd) 
    {
        this.scdDcd = scdDcd;
    }

    public String getScdDcd() 
    {
        return scdDcd;
    }

    public void setScdBr(String scdBr) 
    {
        this.scdBr = scdBr;
    }

    public String getScdBr() 
    {
        return scdBr;
    }

    public void setScdPmid(Long scdPmid) 
    {
        this.scdPmid = scdPmid;
    }

    public Long getScdPmid() 
    {
        return scdPmid;
    }

    public void setScdPmsort(String scdPmsort) 
    {
        this.scdPmsort = scdPmsort;
    }

    public String getScdPmsort() 
    {
        return scdPmsort;
    }

    public void setScdPorgcd(String scdPorgcd) 
    {
        this.scdPorgcd = scdPorgcd;
    }

    public String getScdPorgcd() 
    {
        return scdPorgcd;
    }

    public void setScdPmcd(String scdPmcd) 
    {
        this.scdPmcd = scdPmcd;
    }

    public String getScdPmcd() 
    {
        return scdPmcd;
    }

    public void setScdPmnm(String scdPmnm) 
    {
        this.scdPmnm = scdPmnm;
    }

    public String getScdPmnm() 
    {
        return scdPmnm;
    }

    public void setScdPmenm(String scdPmenm) 
    {
        this.scdPmenm = scdPmenm;
    }

    public String getScdPmenm() 
    {
        return scdPmenm;
    }

    public void setScdCas(String scdCas) 
    {
        this.scdCas = scdCas;
    }

    public String getScdCas() 
    {
        return scdCas;
    }

    public void setScdPurity(String scdPurity) 
    {
        this.scdPurity = scdPurity;
    }

    public String getScdPurity() 
    {
        return scdPurity;
    }

    public void setScdDcls(String scdDcls) 
    {
        this.scdDcls = scdDcls;
    }

    public String getScdDcls() 
    {
        return scdDcls;
    }

    public void setScdSprmk(String scdSprmk) 
    {
        this.scdSprmk = scdSprmk;
    }

    public String getScdSprmk() 
    {
        return scdSprmk;
    }

    public void setScdSprmkPoison(String scdSprmkPoison) 
    {
        this.scdSprmkPoison = scdSprmkPoison;
    }

    public String getScdSprmkPoison() 
    {
        return scdSprmkPoison;
    }

    public void setScdStg(String scdStg) 
    {
        this.scdStg = scdStg;
    }

    public String getScdStg() 
    {
        return scdStg;
    }

    public void setScdPpid(Long scdPpid) 
    {
        this.scdPpid = scdPpid;
    }

    public Long getScdPpid() 
    {
        return scdPpid;
    }

    public void setScdPpcd(String scdPpcd) 
    {
        this.scdPpcd = scdPpcd;
    }

    public String getScdPpcd() 
    {
        return scdPpcd;
    }

    public void setScdPack(String scdPack) 
    {
        this.scdPack = scdPack;
    }

    public String getScdPack() 
    {
        return scdPack;
    }

    public void setScdLstprc(BigDecimal scdLstprc) 
    {
        this.scdLstprc = scdLstprc;
    }

    public BigDecimal getScdLstprc() 
    {
        return scdLstprc;
    }

    public void setScdPprc(String scdPprc) 
    {
        this.scdPprc = scdPprc;
    }

    public String getScdPprc() 
    {
        return scdPprc;
    }

    public void setScdDcttyp(String scdDcttyp) 
    {
        this.scdDcttyp = scdDcttyp;
    }

    public String getScdDcttyp() 
    {
        return scdDcttyp;
    }

    public void setScdOrgdct(BigDecimal scdOrgdct) 
    {
        this.scdOrgdct = scdOrgdct;
    }

    public BigDecimal getScdOrgdct() 
    {
        return scdOrgdct;
    }

    public void setScdDct(BigDecimal scdDct) 
    {
        this.scdDct = scdDct;
    }

    public BigDecimal getScdDct() 
    {
        return scdDct;
    }

    public void setScdPrc(BigDecimal scdPrc) 
    {
        this.scdPrc = scdPrc;
    }

    public BigDecimal getScdPrc() 
    {
        return scdPrc;
    }

    public void setScdPrcAdjust(BigDecimal scdPrcAdjust) 
    {
        this.scdPrcAdjust = scdPrcAdjust;
    }

    public BigDecimal getScdPrcAdjust() 
    {
        return scdPrcAdjust;
    }

    public void setScdQty(BigDecimal scdQty) 
    {
        this.scdQty = scdQty;
    }

    public BigDecimal getScdQty() 
    {
        return scdQty;
    }

    public void setScdQtyReserved(BigDecimal scdQtyReserved) 
    {
        this.scdQtyReserved = scdQtyReserved;
    }

    public BigDecimal getScdQtyReserved() 
    {
        return scdQtyReserved;
    }

    public void setScdPlanqty(BigDecimal scdPlanqty) 
    {
        this.scdPlanqty = scdPlanqty;
    }

    public BigDecimal getScdPlanqty() 
    {
        return scdPlanqty;
    }

    public void setScdOutqty(BigDecimal scdOutqty) 
    {
        this.scdOutqty = scdOutqty;
    }

    public BigDecimal getScdOutqty() 
    {
        return scdOutqty;
    }

    public void setScdIqty(BigDecimal scdIqty) 
    {
        this.scdIqty = scdIqty;
    }

    public BigDecimal getScdIqty() 
    {
        return scdIqty;
    }

    public void setScdTotprcAdjust(BigDecimal scdTotprcAdjust) 
    {
        this.scdTotprcAdjust = scdTotprcAdjust;
    }

    public BigDecimal getScdTotprcAdjust() 
    {
        return scdTotprcAdjust;
    }

    public void setScdFullCutPrice(BigDecimal scdFullCutPrice) 
    {
        this.scdFullCutPrice = scdFullCutPrice;
    }

    public BigDecimal getScdFullCutPrice() 
    {
        return scdFullCutPrice;
    }

    public void setScdMallCouponAmount(BigDecimal scdMallCouponAmount) 
    {
        this.scdMallCouponAmount = scdMallCouponAmount;
    }

    public BigDecimal getScdMallCouponAmount() 
    {
        return scdMallCouponAmount;
    }

    public void setScdDisCountPrice(BigDecimal scdDisCountPrice) 
    {
        this.scdDisCountPrice = scdDisCountPrice;
    }

    public BigDecimal getScdDisCountPrice() 
    {
        return scdDisCountPrice;
    }

    public void setScdTotprc(BigDecimal scdTotprc) 
    {
        this.scdTotprc = scdTotprc;
    }

    public BigDecimal getScdTotprc() 
    {
        return scdTotprc;
    }

    public void setScdGold(Long scdGold) 
    {
        this.scdGold = scdGold;
    }

    public Long getScdGold() 
    {
        return scdGold;
    }

    public void setScdPnt(Long scdPnt) 
    {
        this.scdPnt = scdPnt;
    }

    public Long getScdPnt() 
    {
        return scdPnt;
    }

    public void setScdPuryn(String scdPuryn) 
    {
        this.scdPuryn = scdPuryn;
    }

    public String getScdPuryn() 
    {
        return scdPuryn;
    }

    public void setScdDd(String scdDd) 
    {
        this.scdDd = scdDd;
    }

    public String getScdDd() 
    {
        return scdDd;
    }

    public void setScdIsss(String scdIsss) 
    {
        this.scdIsss = scdIsss;
    }

    public String getScdIsss() 
    {
        return scdIsss;
    }

    public void setScdStscd(String scdStscd) 
    {
        this.scdStscd = scdStscd;
    }

    public String getScdStscd() 
    {
        return scdStscd;
    }

    public void setScdStsnm(String scdStsnm) 
    {
        this.scdStsnm = scdStsnm;
    }

    public String getScdStsnm() 
    {
        return scdStsnm;
    }

    public void setScdSodt(Date scdSodt) 
    {
        this.scdSodt = scdSodt;
    }

    public Date getScdSodt() 
    {
        return scdSodt;
    }

    public void setScdShdt(Date scdShdt) 
    {
        this.scdShdt = scdShdt;
    }

    public Date getScdShdt() 
    {
        return scdShdt;
    }

    public void setScdZcsd(String scdZcsd) 
    {
        this.scdZcsd = scdZcsd;
    }

    public String getScdZcsd() 
    {
        return scdZcsd;
    }

    public void setScdSd(String scdSd) 
    {
        this.scdSd = scdSd;
    }

    public String getScdSd() 
    {
        return scdSd;
    }

    public void setScdOsdt(Date scdOsdt) 
    {
        this.scdOsdt = scdOsdt;
    }

    public Date getScdOsdt() 
    {
        return scdOsdt;
    }

    public void setScdRmk(String scdRmk) 
    {
        this.scdRmk = scdRmk;
    }

    public String getScdRmk() 
    {
        return scdRmk;
    }

    public void setScdSrmk(String scdSrmk) 
    {
        this.scdSrmk = scdSrmk;
    }

    public String getScdSrmk() 
    {
        return scdSrmk;
    }

    public void setScdLrmk(String scdLrmk) 
    {
        this.scdLrmk = scdLrmk;
    }

    public String getScdLrmk() 
    {
        return scdLrmk;
    }

    public void setScdDymemo(String scdDymemo) 
    {
        this.scdDymemo = scdDymemo;
    }

    public String getScdDymemo() 
    {
        return scdDymemo;
    }

    public void setScdFrmk(String scdFrmk) 
    {
        this.scdFrmk = scdFrmk;
    }

    public String getScdFrmk() 
    {
        return scdFrmk;
    }

    public void setScdEplnm(String scdEplnm) 
    {
        this.scdEplnm = scdEplnm;
    }

    public String getScdEplnm() 
    {
        return scdEplnm;
    }

    public void setScdStsrmk(String scdStsrmk) 
    {
        this.scdStsrmk = scdStsrmk;
    }

    public String getScdStsrmk() 
    {
        return scdStsrmk;
    }

    public void setScdStyp(String scdStyp) 
    {
        this.scdStyp = scdStyp;
    }

    public String getScdStyp() 
    {
        return scdStyp;
    }

    public void setScdTicketno(String scdTicketno) 
    {
        this.scdTicketno = scdTicketno;
    }

    public String getScdTicketno() 
    {
        return scdTicketno;
    }

    public void setScdPntused(Long scdPntused) 
    {
        this.scdPntused = scdPntused;
    }

    public Long getScdPntused() 
    {
        return scdPntused;
    }

    public void setScdIntegralSharePrice(BigDecimal scdIntegralSharePrice) 
    {
        this.scdIntegralSharePrice = scdIntegralSharePrice;
    }

    public BigDecimal getScdIntegralSharePrice() 
    {
        return scdIntegralSharePrice;
    }

    public void setScdGoldused(Long scdGoldused) 
    {
        this.scdGoldused = scdGoldused;
    }

    public Long getScdGoldused() 
    {
        return scdGoldused;
    }

    public void setScdGoldusedPrice(BigDecimal scdGoldusedPrice) 
    {
        this.scdGoldusedPrice = scdGoldusedPrice;
    }

    public BigDecimal getScdGoldusedPrice() 
    {
        return scdGoldusedPrice;
    }

    public void setScdTransportway(Integer scdTransportway) 
    {
        this.scdTransportway = scdTransportway;
    }

    public Integer getScdTransportway() 
    {
        return scdTransportway;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setScdProdBn(String scdProdBn) 
    {
        this.scdProdBn = scdProdBn;
    }

    public String getScdProdBn() 
    {
        return scdProdBn;
    }

    public void setScdSlcdBn(String scdSlcdBn) 
    {
        this.scdSlcdBn = scdSlcdBn;
    }

    public String getScdSlcdBn() 
    {
        return scdSlcdBn;
    }

    public void setScdInvoiceMethod(String scdInvoiceMethod) 
    {
        this.scdInvoiceMethod = scdInvoiceMethod;
    }

    public String getScdInvoiceMethod() 
    {
        return scdInvoiceMethod;
    }

    public void setScdClassiimdlRmk(String scdClassiimdlRmk) 
    {
        this.scdClassiimdlRmk = scdClassiimdlRmk;
    }

    public String getScdClassiimdlRmk() 
    {
        return scdClassiimdlRmk;
    }

    public void setScdVirtual(String scdVirtual) 
    {
        this.scdVirtual = scdVirtual;
    }

    public String getScdVirtual() 
    {
        return scdVirtual;
    }

    public void setScdRawMaterial(String scdRawMaterial) 
    {
        this.scdRawMaterial = scdRawMaterial;
    }

    public String getScdRawMaterial() 
    {
        return scdRawMaterial;
    }

    public void setScdIsrisk(String scdIsrisk) 
    {
        this.scdIsrisk = scdIsrisk;
    }

    public String getScdIsrisk() 
    {
        return scdIsrisk;
    }

    public void setScdMaterialcode(String scdMaterialcode) 
    {
        this.scdMaterialcode = scdMaterialcode;
    }

    public String getScdMaterialcode() 
    {
        return scdMaterialcode;
    }

    public void setScdImcd(String scdImcd) 
    {
        this.scdImcd = scdImcd;
    }

    public String getScdImcd() 
    {
        return scdImcd;
    }

    public void setScdKfrmk(String scdKfrmk) 
    {
        this.scdKfrmk = scdKfrmk;
    }

    public String getScdKfrmk() 
    {
        return scdKfrmk;
    }

    public void setScdPackageService(String scdPackageService) 
    {
        this.scdPackageService = scdPackageService;
    }

    public String getScdPackageService() 
    {
        return scdPackageService;
    }

    public void setScdZamk(String scdZamk) 
    {
        this.scdZamk = scdZamk;
    }

    public String getScdZamk() 
    {
        return scdZamk;
    }

    public void setScdBdmType(String scdBdmType) 
    {
        this.scdBdmType = scdBdmType;
    }

    public String getScdBdmType() 
    {
        return scdBdmType;
    }

    public void setScdPcdcd(String scdPcdcd) 
    {
        this.scdPcdcd = scdPcdcd;
    }

    public String getScdPcdcd() 
    {
        return scdPcdcd;
    }

    public void setScdRemarks(String scdRemarks) 
    {
        this.scdRemarks = scdRemarks;
    }

    public String getScdRemarks() 
    {
        return scdRemarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("scdMid", getScdMid())
            .append("scdMcd", getScdMcd())
            .append("scdCd", getScdCd())
            .append("scdOpcd", getScdOpcd())
            .append("scdOscd", getScdOscd())
            .append("scdTyp", getScdTyp())
            .append("scdPrinttyp", getScdPrinttyp())
            .append("scdPrecd", getScdPrecd())
            .append("scdDcd", getScdDcd())
            .append("scdBr", getScdBr())
            .append("scdPmid", getScdPmid())
            .append("scdPmsort", getScdPmsort())
            .append("scdPorgcd", getScdPorgcd())
            .append("scdPmcd", getScdPmcd())
            .append("scdPmnm", getScdPmnm())
            .append("scdPmenm", getScdPmenm())
            .append("scdCas", getScdCas())
            .append("scdPurity", getScdPurity())
            .append("scdDcls", getScdDcls())
            .append("scdSprmk", getScdSprmk())
            .append("scdSprmkPoison", getScdSprmkPoison())
            .append("scdStg", getScdStg())
            .append("scdPpid", getScdPpid())
            .append("scdPpcd", getScdPpcd())
            .append("scdPack", getScdPack())
            .append("scdLstprc", getScdLstprc())
            .append("scdPprc", getScdPprc())
            .append("scdDcttyp", getScdDcttyp())
            .append("scdOrgdct", getScdOrgdct())
            .append("scdDct", getScdDct())
            .append("scdPrc", getScdPrc())
            .append("scdPrcAdjust", getScdPrcAdjust())
            .append("scdQty", getScdQty())
            .append("scdQtyReserved", getScdQtyReserved())
            .append("scdPlanqty", getScdPlanqty())
            .append("scdOutqty", getScdOutqty())
            .append("scdIqty", getScdIqty())
            .append("scdTotprcAdjust", getScdTotprcAdjust())
            .append("scdFullCutPrice", getScdFullCutPrice())
            .append("scdMallCouponAmount", getScdMallCouponAmount())
            .append("scdDisCountPrice", getScdDisCountPrice())
            .append("scdTotprc", getScdTotprc())
            .append("scdGold", getScdGold())
            .append("scdPnt", getScdPnt())
            .append("scdPuryn", getScdPuryn())
            .append("scdDd", getScdDd())
            .append("scdIsss", getScdIsss())
            .append("scdStscd", getScdStscd())
            .append("scdStsnm", getScdStsnm())
            .append("scdSodt", getScdSodt())
            .append("scdShdt", getScdShdt())
            .append("scdZcsd", getScdZcsd())
            .append("scdSd", getScdSd())
            .append("scdOsdt", getScdOsdt())
            .append("scdRmk", getScdRmk())
            .append("scdSrmk", getScdSrmk())
            .append("scdLrmk", getScdLrmk())
            .append("scdDymemo", getScdDymemo())
            .append("scdFrmk", getScdFrmk())
            .append("scdEplnm", getScdEplnm())
            .append("scdStsrmk", getScdStsrmk())
            .append("scdStyp", getScdStyp())
            .append("scdTicketno", getScdTicketno())
            .append("scdPntused", getScdPntused())
            .append("scdIntegralSharePrice", getScdIntegralSharePrice())
            .append("scdGoldused", getScdGoldused())
            .append("scdGoldusedPrice", getScdGoldusedPrice())
            .append("scdTransportway", getScdTransportway())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("scdProdBn", getScdProdBn())
            .append("scdSlcdBn", getScdSlcdBn())
            .append("scdInvoiceMethod", getScdInvoiceMethod())
            .append("scdClassiimdlRmk", getScdClassiimdlRmk())
            .append("scdVirtual", getScdVirtual())
            .append("scdRawMaterial", getScdRawMaterial())
            .append("scdIsrisk", getScdIsrisk())
            .append("scdMaterialcode", getScdMaterialcode())
            .append("scdImcd", getScdImcd())
            .append("scdKfrmk", getScdKfrmk())
            .append("scdPackageService", getScdPackageService())
            .append("scdZamk", getScdZamk())
            .append("scdBdmType", getScdBdmType())
            .append("scdPcdcd", getScdPcdcd())
            .append("scdRemarks", getScdRemarks())
            .toString();
    }
}
