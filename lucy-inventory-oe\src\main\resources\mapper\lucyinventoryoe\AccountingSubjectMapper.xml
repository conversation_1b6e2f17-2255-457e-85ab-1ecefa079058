<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.inventoryoe.mapper.AccountingSubjectMapper">
    
    <resultMap type="AccountingSubject" id="AccountingSubjectResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="category"    column="category"    />
        <result property="level"    column="level"    />
        <result property="parentCode"    column="parent_code"    />
    </resultMap>

    <sql id="selectAccountingSubjectVo">
        select code, name, category, level, parent_code from accounting_subject
    </sql>

    <select id="selectAccountingSubjectList" parameterType="AccountingSubject" resultMap="AccountingSubjectResult">
        <include refid="selectAccountingSubjectVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="parentCode != null  and parentCode != ''"> and parent_code = #{parentCode}</if>
        </where>
    </select>

    <select id="selectAccountingSubjectByCode" parameterType="String" resultMap="AccountingSubjectResult">
        <include refid="selectAccountingSubjectVo"/>
        where code = #{code}
    </select>

    <select id="selectAccountingSubjectByName" parameterType="String" resultMap="AccountingSubjectResult">
        <include refid="selectAccountingSubjectVo"/>
        where name = #{name}
    </select>


    <insert id="insertAccountingSubject" parameterType="AccountingSubject">
        insert into accounting_subject
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="category != null">category,</if>
            <if test="level != null">level,</if>
            <if test="parentCode != null">parent_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="category != null">#{category},</if>
            <if test="level != null">#{level},</if>
            <if test="parentCode != null">#{parentCode},</if>
         </trim>
    </insert>

    <update id="updateAccountingSubject" parameterType="AccountingSubject">
        update accounting_subject
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="category != null">category = #{category},</if>
            <if test="level != null">level = #{level},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteAccountingSubjectByCode" parameterType="String">
        delete from accounting_subject where code = #{code}
    </delete>

    <delete id="deleteAccountingSubjectByCodes" parameterType="String">
        delete from accounting_subject where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>