package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogRadDeveloCompose;

/**
 * 小样工单-主料明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ILogRadDeveloComposeService 
{
    /**
     * 查询小样工单-主料明细
     * 
     * @param lrdcCd 小样工单-主料明细主键
     * @return 小样工单-主料明细
     */
    public LogRadDeveloCompose selectLogRadDeveloComposeByLrdcCd(String lrdcCd);

    /**
     * 查询小样工单-主料明细列表
     * 
     * @param logRadDeveloCompose 小样工单-主料明细
     * @return 小样工单-主料明细集合
     */
    public List<LogRadDeveloCompose> selectLogRadDeveloComposeList(LogRadDeveloCompose logRadDeveloCompose);

    /**
     * 新增小样工单-主料明细
     * 
     * @param logRadDeveloCompose 小样工单-主料明细
     * @return 结果
     */
    public int insertLogRadDeveloCompose(LogRadDeveloCompose logRadDeveloCompose);

    /**
     * 修改小样工单-主料明细
     * 
     * @param logRadDeveloCompose 小样工单-主料明细
     * @return 结果
     */
    public int updateLogRadDeveloCompose(LogRadDeveloCompose logRadDeveloCompose);

    /**
     * 批量删除小样工单-主料明细
     * 
     * @param lrdcCds 需要删除的小样工单-主料明细主键集合
     * @return 结果
     */
    public int deleteLogRadDeveloComposeByLrdcCds(String[] lrdcCds);

    /**
     * 删除小样工单-主料明细信息
     * 
     * @param lrdcCd 小样工单-主料明细主键
     * @return 结果
     */
    public int deleteLogRadDeveloComposeByLrdcCd(String lrdcCd);
}
