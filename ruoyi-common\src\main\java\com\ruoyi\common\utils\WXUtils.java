package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.WxConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class WXUtils {


    public static final String REDIS_KEY = "wx_access_token";

    /**
     * 获取Access_Token
     * @return access_token
     */
    public static String getAccessToken() {
        RestTemplate restTemplate = new RestTemplate();

        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        // 从Redis中获取token
        String token = (String) redisCache.getCacheObject(REDIS_KEY);
        if (token == null) {
            // 如果token不存在，则重新获取
            JSONObject jsonObject = new JSONObject();

            // 构建获取 access token 的 URL
            String tokenUrl = WxConstants.WX_GET_TOKEN_URL
                .replace("ID", WxConstants.WX_APP_ID)
                .replace("SECRET", WxConstants.WX_SECRENT);
            // 发送 GET 请求获取 access token
            JSONObject result = restTemplate.getForObject(tokenUrl, JSONObject.class, jsonObject);

            if (result != null && result.containsKey("access_token")) {
                token = result.getString("access_token");
                // 存入Redis，设置过期时间为1小时
                redisCache.setCacheObject(REDIS_KEY, token, 3600, TimeUnit.SECONDS);
            }
        }

        return token;
    }

    /**
     * 调用企业微信通知
     * @param userid 通知接收者 企业微信id
     * @param content 通知内容
     */
    public static void invokeWorkNotification(String userid, String content) {
        //推送任务具体信息封装
        Map<String,Object> task = new HashMap<>();
        task.put("content", content);
        //推送任务参数
        Map<String, Object> requestParam = new  HashMap<>();
        requestParam.put("touser", userid);
        //部门分类， 暂不启用
        requestParam.put("toparty", null);
        //标签分类， 暂不启用
        requestParam.put("totag", null);
        requestParam.put("msgtype", "text");
        requestParam.put("agentid", WxConstants.WX_TASK_APP_ID);
        requestParam.put("text", task);
        requestParam.put("safe", 0);
        requestParam.put("enable_id_trans", 0);
        requestParam.put("enable_duplicate_check", 0);
        requestParam.put("duplicate_check_interval", "1800");
        String param = JSONObject.toJSONString(requestParam);

        //向企业微信推送
        String access_token = getAccessToken();
        String url = WxConstants.WX_POST_TASK_URL.replace("ACCESS_TOKEN",access_token);

        HttpUtils.sendPost(url, param, "application/json");
    }

}
