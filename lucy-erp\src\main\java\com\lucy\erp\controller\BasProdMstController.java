package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasProdMst;
import com.lucy.erp.service.IBasProdMstService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_产品_主
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/prod/mst")
public class BasProdMstController extends BaseController
{
    @Autowired
    private IBasProdMstService basProdMstService;

    /**
     * 查询基础_产品_主
列表
     */
    @PreAuthorize("@ss.hasPermi('prod:mst:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasProdMst basProdMst)
    {
        startPage();
        List<BasProdMst> list = basProdMstService.selectBasProdMstList(basProdMst);
        return getDataTable(list);
    }

    /**
     * 导出基础_产品_主
列表
     */
    @PreAuthorize("@ss.hasPermi('prod:mst:export')")
    @Log(title = "基础_产品_主 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasProdMst basProdMst)
    {
        List<BasProdMst> list = basProdMstService.selectBasProdMstList(basProdMst);
        ExcelUtil<BasProdMst> util = new ExcelUtil<BasProdMst>(BasProdMst.class);
        util.exportExcel(response, list, "基础_产品_主 数据");
    }

    /**
     * 获取基础_产品_主
详细信息
     */
    @PreAuthorize("@ss.hasPermi('prod:mst:query')")
    @GetMapping(value = "/{bpmCd}")
    public AjaxResult getInfo(@PathVariable("bpmCd") String bpmCd)
    {
        return success(basProdMstService.selectBasProdMstByBpmCd(bpmCd));
    }

    /**
     * 新增基础_产品_主

     */
    @PreAuthorize("@ss.hasPermi('prod:mst:add')")
    @Log(title = "基础_产品_主 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasProdMst basProdMst)
    {
        return toAjax(basProdMstService.insertBasProdMst(basProdMst));
    }

    /**
     * 修改基础_产品_主

     */
    @PreAuthorize("@ss.hasPermi('prod:mst:edit')")
    @Log(title = "基础_产品_主 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasProdMst basProdMst)
    {
        return toAjax(basProdMstService.updateBasProdMst(basProdMst));
    }

    /**
     * 删除基础_产品_主

     */
    @PreAuthorize("@ss.hasPermi('prod:mst:remove')")
    @Log(title = "基础_产品_主 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bpmCds}")
    public AjaxResult remove(@PathVariable String[] bpmCds)
    {
        return toAjax(basProdMstService.deleteBasProdMstByBpmCds(bpmCds));
    }
}
