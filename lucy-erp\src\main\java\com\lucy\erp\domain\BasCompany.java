package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_公司对象 bas_company
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class BasCompany extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 公司编号 */
    private String bcCd;

    /** 名称 */
    @Excel(name = "名称")
    private String bcNm;

    /** 公司类型 */
    @Excel(name = "公司类型")
    private String bcTyp;

    /** 是否有危化品证 */
    @Excel(name = "是否有危化品证")
    private String bcSpecicalVerify;

    /** 危化品证到期日期 */
    @Excel(name = "危化品证到期日期")
    private String bcVerifyDt;

    /** 危化品证上传日期 */
    @Excel(name = "危化品证上传日期")
    private String bcVerifyupDt;

    /** 危化品证上传人 */
    @Excel(name = "危化品证上传人")
    private String bcVerifyNm;

    /** 危化品证上传路径 */
    @Excel(name = "危化品证上传路径")
    private String bcVerify;

    /** 营业执照上传路径 */
    @Excel(name = "营业执照上传路径")
    private String bcLicence;

    /** 营业执照到期日期 */
    @Excel(name = "营业执照到期日期")
    private String bcLicenceDt;

    /** 开票优先级 */
    @Excel(name = "开票优先级")
    private String bcInvcPri;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    private String bcUscc;

    /** 组织机构代码 */
    @Excel(name = "组织机构代码")
    private String bcOc;

    /** 税号 */
    @Excel(name = "税号")
    private String bcTrcno;

    /** 工商注册号 */
    @Excel(name = "工商注册号")
    private String bcBrno;

    /** 开票代码 */
    @Excel(name = "开票代码")
    private String bcBcode;

    /** 法人 */
    @Excel(name = "法人")
    private String bcLp;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String bcEmail;

    /** 公司主页 */
    @Excel(name = "公司主页")
    private String bcWeb;

    /** 注册日期 */
    @Excel(name = "注册日期")
    private String bcRdt;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String bcRaddr;

    /** 注册电话 */
    @Excel(name = "注册电话")
    private String bcTel;

    /** 开户行 */
    @Excel(name = "开户行")
    private String bcBank;

    /** 账号 */
    @Excel(name = "账号")
    private String bcAno;

    /** 注册资本 */
    @Excel(name = "注册资本")
    private BigDecimal bcRc;

    /** 营业期限F */
    @Excel(name = "营业期限F")
    private String bcFbt;

    /** 营业期限T */
    @Excel(name = "营业期限T")
    private String bcTbt;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String bcBs;

    /** 状态 */
    @Excel(name = "状态")
    private String bcSts;

    /** 备注 */
    @Excel(name = "备注")
    private String bcRmk;

    /** 3a的公司编号 */
    @Excel(name = "3a的公司编号")
    private String aBcCd;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 原公司编号 */
    @Excel(name = "原公司编号")
    private String bcInitialcd;

    /** 所属集团编号 */
    @Excel(name = "所属集团编号")
    private String bcBloc;

    /** 长账期客户 */
    @Excel(name = "长账期客户")
    private String bcLongterm;

    /** 营业执照上传日期 */
    @Excel(name = "营业执照上传日期")
    private String bcLicenceupDt;

    /** 法人证书到期日期 */
    @Excel(name = "法人证书到期日期")
    private String bcLegalDt;

    /** 法人证书上传日期 */
    @Excel(name = "法人证书上传日期")
    private String bcLegalupDt;

    /** 危化品经营范围上传日期 */
    @Excel(name = "危化品经营范围上传日期")
    private String bcScopeupDt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBcCd(String bcCd) 
    {
        this.bcCd = bcCd;
    }

    public String getBcCd() 
    {
        return bcCd;
    }

    public void setBcNm(String bcNm) 
    {
        this.bcNm = bcNm;
    }

    public String getBcNm() 
    {
        return bcNm;
    }

    public void setBcTyp(String bcTyp) 
    {
        this.bcTyp = bcTyp;
    }

    public String getBcTyp() 
    {
        return bcTyp;
    }

    public void setBcSpecicalVerify(String bcSpecicalVerify) 
    {
        this.bcSpecicalVerify = bcSpecicalVerify;
    }

    public String getBcSpecicalVerify() 
    {
        return bcSpecicalVerify;
    }

    public void setBcVerifyDt(String bcVerifyDt) 
    {
        this.bcVerifyDt = bcVerifyDt;
    }

    public String getBcVerifyDt() 
    {
        return bcVerifyDt;
    }

    public void setBcVerifyupDt(String bcVerifyupDt) 
    {
        this.bcVerifyupDt = bcVerifyupDt;
    }

    public String getBcVerifyupDt() 
    {
        return bcVerifyupDt;
    }

    public void setBcVerifyNm(String bcVerifyNm) 
    {
        this.bcVerifyNm = bcVerifyNm;
    }

    public String getBcVerifyNm() 
    {
        return bcVerifyNm;
    }

    public void setBcVerify(String bcVerify) 
    {
        this.bcVerify = bcVerify;
    }

    public String getBcVerify() 
    {
        return bcVerify;
    }

    public void setBcLicence(String bcLicence) 
    {
        this.bcLicence = bcLicence;
    }

    public String getBcLicence() 
    {
        return bcLicence;
    }

    public void setBcLicenceDt(String bcLicenceDt) 
    {
        this.bcLicenceDt = bcLicenceDt;
    }

    public String getBcLicenceDt() 
    {
        return bcLicenceDt;
    }

    public void setBcInvcPri(String bcInvcPri) 
    {
        this.bcInvcPri = bcInvcPri;
    }

    public String getBcInvcPri() 
    {
        return bcInvcPri;
    }

    public void setBcUscc(String bcUscc) 
    {
        this.bcUscc = bcUscc;
    }

    public String getBcUscc() 
    {
        return bcUscc;
    }

    public void setBcOc(String bcOc) 
    {
        this.bcOc = bcOc;
    }

    public String getBcOc() 
    {
        return bcOc;
    }

    public void setBcTrcno(String bcTrcno) 
    {
        this.bcTrcno = bcTrcno;
    }

    public String getBcTrcno() 
    {
        return bcTrcno;
    }

    public void setBcBrno(String bcBrno) 
    {
        this.bcBrno = bcBrno;
    }

    public String getBcBrno() 
    {
        return bcBrno;
    }

    public void setBcBcode(String bcBcode) 
    {
        this.bcBcode = bcBcode;
    }

    public String getBcBcode() 
    {
        return bcBcode;
    }

    public void setBcLp(String bcLp) 
    {
        this.bcLp = bcLp;
    }

    public String getBcLp() 
    {
        return bcLp;
    }

    public void setBcEmail(String bcEmail) 
    {
        this.bcEmail = bcEmail;
    }

    public String getBcEmail() 
    {
        return bcEmail;
    }

    public void setBcWeb(String bcWeb) 
    {
        this.bcWeb = bcWeb;
    }

    public String getBcWeb() 
    {
        return bcWeb;
    }

    public void setBcRdt(String bcRdt) 
    {
        this.bcRdt = bcRdt;
    }

    public String getBcRdt() 
    {
        return bcRdt;
    }

    public void setBcRaddr(String bcRaddr) 
    {
        this.bcRaddr = bcRaddr;
    }

    public String getBcRaddr() 
    {
        return bcRaddr;
    }

    public void setBcTel(String bcTel) 
    {
        this.bcTel = bcTel;
    }

    public String getBcTel() 
    {
        return bcTel;
    }

    public void setBcBank(String bcBank) 
    {
        this.bcBank = bcBank;
    }

    public String getBcBank() 
    {
        return bcBank;
    }

    public void setBcAno(String bcAno) 
    {
        this.bcAno = bcAno;
    }

    public String getBcAno() 
    {
        return bcAno;
    }

    public void setBcRc(BigDecimal bcRc) 
    {
        this.bcRc = bcRc;
    }

    public BigDecimal getBcRc() 
    {
        return bcRc;
    }

    public void setBcFbt(String bcFbt) 
    {
        this.bcFbt = bcFbt;
    }

    public String getBcFbt() 
    {
        return bcFbt;
    }

    public void setBcTbt(String bcTbt) 
    {
        this.bcTbt = bcTbt;
    }

    public String getBcTbt() 
    {
        return bcTbt;
    }

    public void setBcBs(String bcBs) 
    {
        this.bcBs = bcBs;
    }

    public String getBcBs() 
    {
        return bcBs;
    }

    public void setBcSts(String bcSts) 
    {
        this.bcSts = bcSts;
    }

    public String getBcSts() 
    {
        return bcSts;
    }

    public void setBcRmk(String bcRmk) 
    {
        this.bcRmk = bcRmk;
    }

    public String getBcRmk() 
    {
        return bcRmk;
    }

    public void set3aBcCd(String aBcCd)
    {
        this.aBcCd = aBcCd;
    }

    public String get3aBcCd() 
    {
        return aBcCd;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setBcInitialcd(String bcInitialcd) 
    {
        this.bcInitialcd = bcInitialcd;
    }

    public String getBcInitialcd() 
    {
        return bcInitialcd;
    }

    public void setBcBloc(String bcBloc) 
    {
        this.bcBloc = bcBloc;
    }

    public String getBcBloc() 
    {
        return bcBloc;
    }

    public void setBcLongterm(String bcLongterm) 
    {
        this.bcLongterm = bcLongterm;
    }

    public String getBcLongterm() 
    {
        return bcLongterm;
    }

    public void setBcLicenceupDt(String bcLicenceupDt) 
    {
        this.bcLicenceupDt = bcLicenceupDt;
    }

    public String getBcLicenceupDt() 
    {
        return bcLicenceupDt;
    }

    public void setBcLegalDt(String bcLegalDt) 
    {
        this.bcLegalDt = bcLegalDt;
    }

    public String getBcLegalDt() 
    {
        return bcLegalDt;
    }

    public void setBcLegalupDt(String bcLegalupDt) 
    {
        this.bcLegalupDt = bcLegalupDt;
    }

    public String getBcLegalupDt() 
    {
        return bcLegalupDt;
    }

    public void setBcScopeupDt(String bcScopeupDt) 
    {
        this.bcScopeupDt = bcScopeupDt;
    }

    public String getBcScopeupDt() 
    {
        return bcScopeupDt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bcCd", getBcCd())
            .append("bcNm", getBcNm())
            .append("bcTyp", getBcTyp())
            .append("bcSpecicalVerify", getBcSpecicalVerify())
            .append("bcVerifyDt", getBcVerifyDt())
            .append("bcVerifyupDt", getBcVerifyupDt())
            .append("bcVerifyNm", getBcVerifyNm())
            .append("bcVerify", getBcVerify())
            .append("bcLicence", getBcLicence())
            .append("bcLicenceDt", getBcLicenceDt())
            .append("bcInvcPri", getBcInvcPri())
            .append("bcUscc", getBcUscc())
            .append("bcOc", getBcOc())
            .append("bcTrcno", getBcTrcno())
            .append("bcBrno", getBcBrno())
            .append("bcBcode", getBcBcode())
            .append("bcLp", getBcLp())
            .append("bcEmail", getBcEmail())
            .append("bcWeb", getBcWeb())
            .append("bcRdt", getBcRdt())
            .append("bcRaddr", getBcRaddr())
            .append("bcTel", getBcTel())
            .append("bcBank", getBcBank())
            .append("bcAno", getBcAno())
            .append("bcRc", getBcRc())
            .append("bcFbt", getBcFbt())
            .append("bcTbt", getBcTbt())
            .append("bcBs", getBcBs())
            .append("bcSts", getBcSts())
            .append("bcRmk", getBcRmk())
            .append("3aBcCd", get3aBcCd())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("bcInitialcd", getBcInitialcd())
            .append("bcBloc", getBcBloc())
            .append("bcLongterm", getBcLongterm())
            .append("bcLicenceupDt", getBcLicenceupDt())
            .append("bcLegalDt", getBcLegalDt())
            .append("bcLegalupDt", getBcLegalupDt())
            .append("bcScopeupDt", getBcScopeupDt())
            .toString();
    }
}
