package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LabPickingDtlMapper;
import com.lucy.erp.domain.LabPickingDtl;
import com.lucy.erp.service.ILabPickingDtlService;

/**
 * 研发领用明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
@DataSource(DataSourceType.ERP)
public class LabPickingDtlServiceImpl implements ILabPickingDtlService 
{
    @Autowired
    private LabPickingDtlMapper labPickingDtlMapper;

    /**
     * 查询研发领用明细
     * 
     * @param lpdCd 研发领用明细主键
     * @return 研发领用明细
     */
    @Override
    public LabPickingDtl selectLabPickingDtlByLpdCd(String lpdCd)
    {
        return labPickingDtlMapper.selectLabPickingDtlByLpdCd(lpdCd);
    }

    /**
     * 查询研发领用明细列表
     * 
     * @param labPickingDtl 研发领用明细
     * @return 研发领用明细
     */
    @Override
    public List<LabPickingDtl> selectLabPickingDtlList(LabPickingDtl labPickingDtl)
    {
        return labPickingDtlMapper.selectLabPickingDtlList(labPickingDtl);
    }

    /**
     * 新增研发领用明细
     * 
     * @param labPickingDtl 研发领用明细
     * @return 结果
     */
    @Override
    public int insertLabPickingDtl(LabPickingDtl labPickingDtl)
    {
        return labPickingDtlMapper.insertLabPickingDtl(labPickingDtl);
    }

    /**
     * 修改研发领用明细
     * 
     * @param labPickingDtl 研发领用明细
     * @return 结果
     */
    @Override
    public int updateLabPickingDtl(LabPickingDtl labPickingDtl)
    {
        return labPickingDtlMapper.updateLabPickingDtl(labPickingDtl);
    }

    /**
     * 批量删除研发领用明细
     * 
     * @param lpdCds 需要删除的研发领用明细主键
     * @return 结果
     */
    @Override
    public int deleteLabPickingDtlByLpdCds(String[] lpdCds)
    {
        return labPickingDtlMapper.deleteLabPickingDtlByLpdCds(lpdCds);
    }

    /**
     * 删除研发领用明细信息
     * 
     * @param lpdCd 研发领用明细主键
     * @return 结果
     */
    @Override
    public int deleteLabPickingDtlByLpdCd(String lpdCd)
    {
        return labPickingDtlMapper.deleteLabPickingDtlByLpdCd(lpdCd);
    }
}
