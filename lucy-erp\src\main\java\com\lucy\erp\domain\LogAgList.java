package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 物流_到货_列
对象 log_ag_list
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class LogAgList extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 编号 */
    private String lalCd;

    /** 业务单号 */
    @Excel(name = "业务单号")
    private String lalBizcd;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String lalPcd;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String lalPnm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lalCas;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String lalPpcd;

    /** 本次到货数量 */
    @Excel(name = "本次到货数量")
    private BigDecimal lalAgqty;

    /** 本次到货总金额 */
    @Excel(name = "本次到货总金额")
    private BigDecimal lalAgamt;

    /** 已收票数量 */
    @Excel(name = "已收票数量")
    private BigDecimal lalIqty;

    /** 已收票金额 */
    @Excel(name = "已收票金额")
    private BigDecimal lalIamt;

    /** 库位类别 */
    @Excel(name = "库位类别")
    private String lalSltyp;

    /** 仓库 */
    @Excel(name = "仓库")
    private String lalDmcd;

    /** 库位编号 */
    @Excel(name = "库位编号")
    private String lalSlcd;

    /** 计划现货库位编号 */
    @Excel(name = "计划现货库位编号")
    private String lalPlanslcd;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal lalUnitp;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal lalBit;

    /** 是否价税分离 */
    @Excel(name = "是否价税分离")
    private Long lalIspart;

    /** 批号 */
    private String lalBn;

    /** 产品批号 */
    @Excel(name = "产品批号")
    private String lalPbn;

    /** 供应商批号 */
    @Excel(name = "供应商批号")
    private String lalSbn;

    /** 生产工单号 */
    @Excel(name = "生产工单号")
    private String lalPsn;

    /** 供应商种类 */
    @Excel(name = "供应商种类")
    private String lalSuptyp;

    /** 供应商公司编号 */
    @Excel(name = "供应商公司编号")
    private String lalCompanycd;

    /** 采购员 */
    @Excel(name = "采购员")
    private String lalUcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lalArriveCd;

    /** 到货备注 */
    @Excel(name = "到货备注")
    private String lalRmk;

    /** 批号日期 */
    @Excel(name = "批号日期")
    private String lalBndt;

    /** 采购周期类型 */
    @Excel(name = "采购周期类型")
    private String lalPurcycl;

    /** 到货类别 */
    @Excel(name = "到货类别")
    private String lalTyp;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 组织编号（发票索取时确定） */
    @Excel(name = "组织编号", readConverterExp = "发=票索取时确定")
    private String lalOcdInvcar;

    /**  组织公司编号（发票索取时确定） */
    @Excel(name = " 组织公司编号", readConverterExp = "发=票索取时确定")
    private String lalOcpcdInvcar;

    /**  对账异常 （直接在字段里存入‘对账异常’ 由该字段是否为空判断） */
    @Excel(name = " 对账异常 ", readConverterExp = "直=接在字段里存入‘对账异常’,由=该字段是否为空判断")
    private String lalInvcarAbnormal;

    /** 买方组织编号 */
    @Excel(name = "买方组织编号")
    private String lalOrgmcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lalBnEncrypt;

    /** 币种 */
    @Excel(name = "币种")
    private String lalCurrency;

    /** 汇率 */
    @Excel(name = "汇率")
    private BigDecimal lalExchange;

    /** 运单号 */
    @Excel(name = "运单号")
    private String lalTn;

    /** 关联交易对应业务的到货列表id */
    @Excel(name = "关联交易对应业务的到货列表id")
    private Long paramkeysId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String paramKeys;

    /** 关联交易销售退货到货id对应的采购退货出库id */
    @Excel(name = "关联交易销售退货到货id对应的采购退货出库id")
    private Long paramkeysInsideId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String paramkeysOld;

    /** 出库单备注 */
    @Excel(name = "出库单备注")
    private String lalOutRmk;

    /** 出库计划id */
    @Excel(name = "出库计划id")
    private String lopId;

    /** 记账标记 0 是未刷,1 是标记 */
    @Excel(name = "记账标记 0 是未刷,1 是标记")
    private Integer lalFinFlag;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lalInKid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lalOpKid;

    /** 不包含其他费用单价（收发票用） */
    @Excel(name = "不包含其他费用单价", readConverterExp = "收=发票用")
    private BigDecimal lalInvoiceUnitp;

    /** 集团成本_不含税 */
    @Excel(name = "集团成本_不含税")
    private BigDecimal lalGroupCost;

    /** 物流成本_不含税 */
    @Excel(name = "物流成本_不含税")
    private BigDecimal lalLogisticsCost;

    /** rd系统库存id */
    @Excel(name = "rd系统库存id")
    private Long lalRdId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLalCd(String lalCd) 
    {
        this.lalCd = lalCd;
    }

    public String getLalCd() 
    {
        return lalCd;
    }

    public void setLalBizcd(String lalBizcd) 
    {
        this.lalBizcd = lalBizcd;
    }

    public String getLalBizcd() 
    {
        return lalBizcd;
    }

    public void setLalPcd(String lalPcd) 
    {
        this.lalPcd = lalPcd;
    }

    public String getLalPcd()
    {
        return lalPcd;
    }

    public void setLalPnm(String lalPnm) 
    {
        this.lalPnm = lalPnm;
    }

    public String getLalPnm() 
    {
        return lalPnm;
    }

    public void setLalCas(String lalCas) 
    {
        this.lalCas = lalCas;
    }

    public String getLalCas() 
    {
        return lalCas;
    }

    public void setLalPpcd(String lalPpcd) 
    {
        this.lalPpcd = lalPpcd;
    }

    public String getLalPpcd() 
    {
        return lalPpcd;
    }

    public void setLalAgqty(BigDecimal lalAgqty) 
    {
        this.lalAgqty = lalAgqty;
    }

    public BigDecimal getLalAgqty() 
    {
        return lalAgqty;
    }

    public void setLalAgamt(BigDecimal lalAgamt) 
    {
        this.lalAgamt = lalAgamt;
    }

    public BigDecimal getLalAgamt() 
    {
        return lalAgamt;
    }

    public void setLalIqty(BigDecimal lalIqty) 
    {
        this.lalIqty = lalIqty;
    }

    public BigDecimal getLalIqty() 
    {
        return lalIqty;
    }

    public void setLalIamt(BigDecimal lalIamt) 
    {
        this.lalIamt = lalIamt;
    }

    public BigDecimal getLalIamt() 
    {
        return lalIamt;
    }

    public void setLalSltyp(String lalSltyp) 
    {
        this.lalSltyp = lalSltyp;
    }

    public String getLalSltyp() 
    {
        return lalSltyp;
    }

    public void setLalDmcd(String lalDmcd) 
    {
        this.lalDmcd = lalDmcd;
    }

    public String getLalDmcd() 
    {
        return lalDmcd;
    }

    public void setLalSlcd(String lalSlcd) 
    {
        this.lalSlcd = lalSlcd;
    }

    public String getLalSlcd() 
    {
        return lalSlcd;
    }

    public void setLalPlanslcd(String lalPlanslcd) 
    {
        this.lalPlanslcd = lalPlanslcd;
    }

    public String getLalPlanslcd() 
    {
        return lalPlanslcd;
    }

    public void setLalUnitp(BigDecimal lalUnitp) 
    {
        this.lalUnitp = lalUnitp;
    }

    public BigDecimal getLalUnitp() 
    {
        return lalUnitp;
    }

    public void setLalBit(BigDecimal lalBit) 
    {
        this.lalBit = lalBit;
    }

    public BigDecimal getLalBit() 
    {
        return lalBit;
    }

    public void setLalIspart(Long lalIspart) 
    {
        this.lalIspart = lalIspart;
    }

    public Long getLalIspart() 
    {
        return lalIspart;
    }

    public void setLalBn(String lalBn) 
    {
        this.lalBn = lalBn;
    }

    public String getLalBn() 
    {
        return lalBn;
    }

    public void setLalPbn(String lalPbn) 
    {
        this.lalPbn = lalPbn;
    }

    public String getLalPbn() 
    {
        return lalPbn;
    }

    public void setLalSbn(String lalSbn) 
    {
        this.lalSbn = lalSbn;
    }

    public String getLalSbn() 
    {
        return lalSbn;
    }

    public void setLalPsn(String lalPsn) 
    {
        this.lalPsn = lalPsn;
    }

    public String getLalPsn() 
    {
        return lalPsn;
    }

    public void setLalSuptyp(String lalSuptyp) 
    {
        this.lalSuptyp = lalSuptyp;
    }

    public String getLalSuptyp() 
    {
        return lalSuptyp;
    }

    public void setLalCompanycd(String lalCompanycd) 
    {
        this.lalCompanycd = lalCompanycd;
    }

    public String getLalCompanycd() 
    {
        return lalCompanycd;
    }

    public void setLalUcd(String lalUcd) 
    {
        this.lalUcd = lalUcd;
    }

    public String getLalUcd() 
    {
        return lalUcd;
    }

    public void setLalArriveCd(String lalArriveCd) 
    {
        this.lalArriveCd = lalArriveCd;
    }

    public String getLalArriveCd() 
    {
        return lalArriveCd;
    }

    public void setLalRmk(String lalRmk) 
    {
        this.lalRmk = lalRmk;
    }

    public String getLalRmk() 
    {
        return lalRmk;
    }

    public void setLalBndt(String lalBndt) 
    {
        this.lalBndt = lalBndt;
    }

    public String getLalBndt() 
    {
        return lalBndt;
    }

    public void setLalPurcycl(String lalPurcycl) 
    {
        this.lalPurcycl = lalPurcycl;
    }

    public String getLalPurcycl() 
    {
        return lalPurcycl;
    }

    public void setLalTyp(String lalTyp) 
    {
        this.lalTyp = lalTyp;
    }

    public String getLalTyp() 
    {
        return lalTyp;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setLalOcdInvcar(String lalOcdInvcar) 
    {
        this.lalOcdInvcar = lalOcdInvcar;
    }

    public String getLalOcdInvcar() 
    {
        return lalOcdInvcar;
    }

    public void setLalOcpcdInvcar(String lalOcpcdInvcar) 
    {
        this.lalOcpcdInvcar = lalOcpcdInvcar;
    }

    public String getLalOcpcdInvcar() 
    {
        return lalOcpcdInvcar;
    }

    public void setLalInvcarAbnormal(String lalInvcarAbnormal) 
    {
        this.lalInvcarAbnormal = lalInvcarAbnormal;
    }

    public String getLalInvcarAbnormal() 
    {
        return lalInvcarAbnormal;
    }

    public void setLalOrgmcd(String lalOrgmcd) 
    {
        this.lalOrgmcd = lalOrgmcd;
    }

    public String getLalOrgmcd() 
    {
        return lalOrgmcd;
    }

    public void setLalBnEncrypt(String lalBnEncrypt) 
    {
        this.lalBnEncrypt = lalBnEncrypt;
    }

    public String getLalBnEncrypt() 
    {
        return lalBnEncrypt;
    }

    public void setLalCurrency(String lalCurrency) 
    {
        this.lalCurrency = lalCurrency;
    }

    public String getLalCurrency() 
    {
        return lalCurrency;
    }

    public void setLalExchange(BigDecimal lalExchange) 
    {
        this.lalExchange = lalExchange;
    }

    public BigDecimal getLalExchange() 
    {
        return lalExchange;
    }

    public void setLalTn(String lalTn) 
    {
        this.lalTn = lalTn;
    }

    public String getLalTn() 
    {
        return lalTn;
    }

    public void setParamkeysId(Long paramkeysId) 
    {
        this.paramkeysId = paramkeysId;
    }

    public Long getParamkeysId() 
    {
        return paramkeysId;
    }

    public void setParamKeys(String paramKeys) 
    {
        this.paramKeys = paramKeys;
    }

    public String getParamKeys() 
    {
        return paramKeys;
    }

    public void setParamkeysInsideId(Long paramkeysInsideId) 
    {
        this.paramkeysInsideId = paramkeysInsideId;
    }

    public Long getParamkeysInsideId() 
    {
        return paramkeysInsideId;
    }

    public void setParamkeysOld(String paramkeysOld) 
    {
        this.paramkeysOld = paramkeysOld;
    }

    public String getParamkeysOld() 
    {
        return paramkeysOld;
    }

    public void setLalOutRmk(String lalOutRmk) 
    {
        this.lalOutRmk = lalOutRmk;
    }

    public String getLalOutRmk() 
    {
        return lalOutRmk;
    }

    public void setLopId(String lopId) 
    {
        this.lopId = lopId;
    }

    public String getLopId() 
    {
        return lopId;
    }

    public void setLalFinFlag(Integer lalFinFlag) 
    {
        this.lalFinFlag = lalFinFlag;
    }

    public Integer getLalFinFlag() 
    {
        return lalFinFlag;
    }

    public void setLalInKid(String lalInKid) 
    {
        this.lalInKid = lalInKid;
    }

    public String getLalInKid() 
    {
        return lalInKid;
    }

    public void setLalOpKid(String lalOpKid) 
    {
        this.lalOpKid = lalOpKid;
    }

    public String getLalOpKid() 
    {
        return lalOpKid;
    }

    public void setLalInvoiceUnitp(BigDecimal lalInvoiceUnitp) 
    {
        this.lalInvoiceUnitp = lalInvoiceUnitp;
    }

    public BigDecimal getLalInvoiceUnitp() 
    {
        return lalInvoiceUnitp;
    }

    public void setLalGroupCost(BigDecimal lalGroupCost) 
    {
        this.lalGroupCost = lalGroupCost;
    }

    public BigDecimal getLalGroupCost() 
    {
        return lalGroupCost;
    }

    public void setLalLogisticsCost(BigDecimal lalLogisticsCost) 
    {
        this.lalLogisticsCost = lalLogisticsCost;
    }

    public BigDecimal getLalLogisticsCost() 
    {
        return lalLogisticsCost;
    }

    public void setLalRdId(Long lalRdId) 
    {
        this.lalRdId = lalRdId;
    }

    public Long getLalRdId() 
    {
        return lalRdId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lalCd", getLalCd())
            .append("lalBizcd", getLalBizcd())
            .append("lalPcd", getLalPcd())
            .append("lalPnm", getLalPnm())
            .append("lalCas", getLalCas())
            .append("lalPpcd", getLalPpcd())
            .append("lalAgqty", getLalAgqty())
            .append("lalAgamt", getLalAgamt())
            .append("lalIqty", getLalIqty())
            .append("lalIamt", getLalIamt())
            .append("lalSltyp", getLalSltyp())
            .append("lalDmcd", getLalDmcd())
            .append("lalSlcd", getLalSlcd())
            .append("lalPlanslcd", getLalPlanslcd())
            .append("lalUnitp", getLalUnitp())
            .append("lalBit", getLalBit())
            .append("lalIspart", getLalIspart())
            .append("lalBn", getLalBn())
            .append("lalPbn", getLalPbn())
            .append("lalSbn", getLalSbn())
            .append("lalPsn", getLalPsn())
            .append("lalSuptyp", getLalSuptyp())
            .append("lalCompanycd", getLalCompanycd())
            .append("lalUcd", getLalUcd())
            .append("lalArriveCd", getLalArriveCd())
            .append("lalRmk", getLalRmk())
            .append("lalBndt", getLalBndt())
            .append("lalPurcycl", getLalPurcycl())
            .append("lalTyp", getLalTyp())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("lalOcdInvcar", getLalOcdInvcar())
            .append("lalOcpcdInvcar", getLalOcpcdInvcar())
            .append("lalInvcarAbnormal", getLalInvcarAbnormal())
            .append("lalOrgmcd", getLalOrgmcd())
            .append("lalBnEncrypt", getLalBnEncrypt())
            .append("lalCurrency", getLalCurrency())
            .append("lalExchange", getLalExchange())
            .append("lalTn", getLalTn())
            .append("paramkeysId", getParamkeysId())
            .append("paramKeys", getParamKeys())
            .append("paramkeysInsideId", getParamkeysInsideId())
            .append("paramkeysOld", getParamkeysOld())
            .append("lalOutRmk", getLalOutRmk())
            .append("lopId", getLopId())
            .append("lalFinFlag", getLalFinFlag())
            .append("lalInKid", getLalInKid())
            .append("lalOpKid", getLalOpKid())
            .append("lalInvoiceUnitp", getLalInvoiceUnitp())
            .append("lalGroupCost", getLalGroupCost())
            .append("lalLogisticsCost", getLalLogisticsCost())
            .append("lalRdId", getLalRdId())
            .toString();
    }
}
