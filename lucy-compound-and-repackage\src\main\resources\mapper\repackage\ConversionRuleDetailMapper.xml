<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repackage.mapper.ConversionRuleDetailMapper">
    
    <resultMap type="ConversionRuleDetail" id="ConversionRuleDetailResult">
        <result property="detailId"    column="detail_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="materialSku"    column="material_sku"    />
        <result property="role"    column="role"    />
        <result property="quantity"    column="quantity"    />
        <result property="uom"    column="uom"    />
        <result property="concentration"    column="concentration"    />
        <result property="effectiveComponent"    column="effective_component"    />
        <result property="componentRatio"    column="component_ratio"    />
    </resultMap>

    <sql id="selectConversionRuleDetailVo">
        select detail_id, rule_id, material_sku, role, quantity, uom, concentration, effective_component, component_ratio from conversion_rule_detail
    </sql>

    <select id="selectConversionRuleDetailList" parameterType="ConversionRuleDetail" resultMap="ConversionRuleDetailResult">
        <include refid="selectConversionRuleDetailVo"/>
        <where>  
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="materialSku != null  and materialSku != ''"> and material_sku = #{materialSku}</if>
            <if test="role != null  and role != ''"> and role = #{role}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="uom != null  and uom != ''"> and uom = #{uom}</if>
            <if test="concentration != null "> and concentration = #{concentration}</if>
            <if test="effectiveComponent != null  and effectiveComponent != ''"> and effective_component = #{effectiveComponent}</if>
            <if test="componentRatio != null "> and component_ratio = #{componentRatio}</if>
        </where>
    </select>
    
    <select id="selectConversionRuleDetailByDetailId" parameterType="Long" resultMap="ConversionRuleDetailResult">
        <include refid="selectConversionRuleDetailVo"/>
        where detail_id = #{detailId}
    </select>

    <insert id="insertConversionRuleDetail" parameterType="ConversionRuleDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into conversion_rule_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="materialSku != null and materialSku != ''">material_sku,</if>
            <if test="role != null and role != ''">role,</if>
            <if test="quantity != null">quantity,</if>
            <if test="uom != null and uom != ''">uom,</if>
            <if test="concentration != null">concentration,</if>
            <if test="effectiveComponent != null">effective_component,</if>
            <if test="componentRatio != null">component_ratio,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="materialSku != null and materialSku != ''">#{materialSku},</if>
            <if test="role != null and role != ''">#{role},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="uom != null and uom != ''">#{uom},</if>
            <if test="concentration != null">#{concentration},</if>
            <if test="effectiveComponent != null">#{effectiveComponent},</if>
            <if test="componentRatio != null">#{componentRatio},</if>
         </trim>
    </insert>

    <update id="updateConversionRuleDetail" parameterType="ConversionRuleDetail">
        update conversion_rule_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="materialSku != null and materialSku != ''">material_sku = #{materialSku},</if>
            <if test="role != null and role != ''">role = #{role},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="uom != null and uom != ''">uom = #{uom},</if>
            <if test="concentration != null">concentration = #{concentration},</if>
            <if test="effectiveComponent != null">effective_component = #{effectiveComponent},</if>
            <if test="componentRatio != null">component_ratio = #{componentRatio},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteConversionRuleDetailByDetailId" parameterType="Long">
        delete from conversion_rule_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteConversionRuleDetailByDetailIds" parameterType="String">
        delete from conversion_rule_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
</mapper>