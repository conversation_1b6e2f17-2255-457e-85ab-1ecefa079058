package com.lucy.inventoryexit.service.impl;

import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.lucy.erp.domain.LogInventoryIoTransaction;
import com.lucy.erp.domain.SalesContractMst;
import com.lucy.erp.service.ILogInventoryIoTransactionService;
import com.lucy.erp.service.ISalesContractMstService;
import com.lucy.inventoryexit.service.InventoryExitService;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.framework.datasource.DynamicDataSourceContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 出库业务服务实现类
 */
@Service
public class InventoryExitServiceImpl implements InventoryExitService {

    @Autowired
    private ILogInventoryIoTransactionService logInventoryIoTransactionService;

    @Autowired
    private ISalesContractMstService salesContractMstService;


    @Override
    public void testDatasource() {

        String code = AssetOwner.AHZS.code();
        String name = AssetOwner.AHZS.name();
        AssetOwnerMaster entity = AssetOwner.AHZS.entity();
        AssetOwnerMaster assetOwnerMaster = AssetOwner.byCode("BM10000050");

        System.out.println("name = " + name);
        System.out.println("code = " + code);
        System.out.println("entity = " + entity);

//        DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.ERP.name());
//
//        LogInventoryIoTransaction logInventoryIoTransaction = logInventoryIoTransactionService.selectLogInventoryIoTransactionById(14038652L);
//
//        SalesContractMst salesContractMst = salesContractMstService.selectSalesContractMstByScmCd("Y052506040001");
//
//        System.out.println("logInventoryIoTransaction = " + logInventoryIoTransaction);
//
//        System.out.println("salesContractMst = " + salesContractMst);
//        DynamicDataSourceContextHolder.clearDataSourceType();
    }
}
