package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.SalesContractDtl;

/**
 * 销售_合同_明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface SalesContractDtlMapper 
{
    /**
     * 查询销售_合同_明细
     * 
     * @param scdcd 销售_合同_明细主键
     * @return 销售_合同_明细
     */
    public SalesContractDtl selectSalesContractDtlByScdcd(String scdcd);

    /**
     * 查询销售_合同_明细列表
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 销售_合同_明细集合
     */
    public List<SalesContractDtl> selectSalesContractDtlList(SalesContractDtl salesContractDtl);

    /**
     * 新增销售_合同_明细
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 结果
     */
    public int insertSalesContractDtl(SalesContractDtl salesContractDtl);

    /**
     * 修改销售_合同_明细
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 结果
     */
    public int updateSalesContractDtl(SalesContractDtl salesContractDtl);

    /**
     * 删除销售_合同_明细
     * 
     * @param scdMcd 销售_合同_明细主键
     * @return 结果
     */
    public int deleteSalesContractDtlByScdMcd(String scdMcd);

    /**
     * 批量删除销售_合同_明细
     * 
     * @param scdMcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSalesContractDtlByScdMcds(String[] scdMcds);
}
