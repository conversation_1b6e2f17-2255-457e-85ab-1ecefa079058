package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.BasProdAchem;

/**
 * 基础_化学_主
Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IBasProdAchemService 
{
    /**
     * 查询基础_化学_主

     * 
     * @param bpacCd 基础_化学_主
主键
     * @return 基础_化学_主

     */
    public BasProdAchem selectBasProdAchemByBpacCd(String bpacCd);

    /**
     * 查询基础_化学_主
列表
     * 
     * @param basProdAchem 基础_化学_主

     * @return 基础_化学_主
集合
     */
    public List<BasProdAchem> selectBasProdAchemList(BasProdAchem basProdAchem);

    /**
     * 新增基础_化学_主

     * 
     * @param basProdAchem 基础_化学_主

     * @return 结果
     */
    public int insertBasProdAchem(BasProdAchem basProdAchem);

    /**
     * 修改基础_化学_主

     * 
     * @param basProdAchem 基础_化学_主

     * @return 结果
     */
    public int updateBasProdAchem(BasProdAchem basProdAchem);

    /**
     * 批量删除基础_化学_主

     * 
     * @param bpacCds 需要删除的基础_化学_主
主键集合
     * @return 结果
     */
    public int deleteBasProdAchemByBpacCds(String[] bpacCds);

    /**
     * 删除基础_化学_主
信息
     * 
     * @param bpacCd 基础_化学_主
主键
     * @return 结果
     */
    public int deleteBasProdAchemByBpacCd(String bpacCd);
}
