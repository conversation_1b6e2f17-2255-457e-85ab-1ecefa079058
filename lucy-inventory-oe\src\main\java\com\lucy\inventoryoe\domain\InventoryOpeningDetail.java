package com.lucy.inventoryoe.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 库存期初明细 Inventory opening detail records对象 inventory_opening_detail
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public class InventoryOpeningDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID, Primary key */
    private Long id;

    /** 期初库存年月(YYYY-MM), Accounting period */
    @Excel(name = "期初库存年月(YYYY-MM), Accounting period")
    private String accountingPeriod;

    /** 财务科目代码, Financial account code */
    @Excel(name = "财务科目代码, Financial account code")
    private String financialAccount;

    /** SKU编码, Stock Keeping Unit */
    @Excel(name = "SKU编码, Stock Keeping Unit")
    private String sku;

    /** 期初数量, Opening quantity */
    @Excel(name = "期初数量, Opening quantity")
    private BigDecimal quantity;

    /** 期初金额, Opening amount */
    @Excel(name = "期初金额, Opening amount")
    private BigDecimal amount;

    /** 外采/自产, Inventory source */
    @Excel(name = "外采/自产, Inventory source")
    private String sourceType;

    /** 资产所属主体, Asset ownership entity */
    @Excel(name = "资产所属主体, Asset ownership entity")
    private String assetOwner;

    /** 库存批次, Batch/Lot number */
    @Excel(name = "库存批次, Batch/Lot number")
    private String batchNumber;

    /** 加密批次, Encrypted batch code */
    @Excel(name = "加密批次, Encrypted batch code")
    private String encryptedBatch;

    /** 仓库代码, Warehouse code */
    @Excel(name = "仓库代码, Warehouse code")
    private String warehouse;

    /** 库位代码, Storage bin location */
    @Excel(name = "库位代码, Storage bin location")
    private String storageLocation;

    /** 库区代码, Storage zone */
    @Excel(name = "库区代码, Storage zone")
    private String storageZone;

    /** 核算时间, Valuation timestamp */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "核算时间, Valuation timestamp", width = 30, dateFormat = "yyyy-MM-dd")
    private Date valuationTime;

    /** 核算人, Valuation operator */
    @Excel(name = "核算人, Valuation operator")
    private String valuator;

    /** 审核时间, Audit timestamp */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间, Audit timestamp", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核人, Audit operator */
    @Excel(name = "审核人, Audit operator")
    private String auditor;

    /** 创建人, Created by user */
    @Excel(name = "创建人, Created by user")
    private String createdBy;

    /** 创建时间, Creation time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间, Creation time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdTime;

    /** 更新人, Last updated by */
    @Excel(name = "更新人, Last updated by")
    private String updatedBy;

    /** 更新时间, Update time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间, Update time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedTime;

    /** 行版本, Row version */
    @Excel(name = "行版本, Row version")
    private Integer version;

    /** 删除标记(0:正常 1:删除), Soft delete flag */
    @Excel(name = "删除标记(0:正常 1:删除), Soft delete flag")
    private Integer isDeleted;

    /** 表结构名称 */
    @Excel(name = "表结构名称")
    private String structureName;

    /** 所在表结构ID */
    @Excel(name = "所在表结构ID")
    private Long parentStructureId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAccountingPeriod(String accountingPeriod) 
    {
        this.accountingPeriod = accountingPeriod;
    }

    public String getAccountingPeriod() 
    {
        return accountingPeriod;
    }

    public void setFinancialAccount(String financialAccount) 
    {
        this.financialAccount = financialAccount;
    }

    public String getFinancialAccount() 
    {
        return financialAccount;
    }

    public void setSku(String sku) 
    {
        this.sku = sku;
    }

    public String getSku() 
    {
        return sku;
    }

    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }

    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }

    public void setSourceType(String sourceType) 
    {
        this.sourceType = sourceType;
    }

    public String getSourceType() 
    {
        return sourceType;
    }

    public void setAssetOwner(String assetOwner) 
    {
        this.assetOwner = assetOwner;
    }

    public String getAssetOwner() 
    {
        return assetOwner;
    }

    public void setBatchNumber(String batchNumber) 
    {
        this.batchNumber = batchNumber;
    }

    public String getBatchNumber() 
    {
        return batchNumber;
    }

    public void setEncryptedBatch(String encryptedBatch) 
    {
        this.encryptedBatch = encryptedBatch;
    }

    public String getEncryptedBatch() 
    {
        return encryptedBatch;
    }

    public void setWarehouse(String warehouse) 
    {
        this.warehouse = warehouse;
    }

    public String getWarehouse() 
    {
        return warehouse;
    }

    public void setStorageLocation(String storageLocation) 
    {
        this.storageLocation = storageLocation;
    }

    public String getStorageLocation() 
    {
        return storageLocation;
    }

    public void setStorageZone(String storageZone) 
    {
        this.storageZone = storageZone;
    }

    public String getStorageZone() 
    {
        return storageZone;
    }

    public void setValuationTime(Date valuationTime) 
    {
        this.valuationTime = valuationTime;
    }

    public Date getValuationTime() 
    {
        return valuationTime;
    }

    public void setValuator(String valuator) 
    {
        this.valuator = valuator;
    }

    public String getValuator() 
    {
        return valuator;
    }

    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }

    public void setAuditor(String auditor) 
    {
        this.auditor = auditor;
    }

    public String getAuditor() 
    {
        return auditor;
    }

    public void setCreatedBy(String createdBy) 
    {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() 
    {
        return createdBy;
    }

    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }

    public void setUpdatedBy(String updatedBy) 
    {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() 
    {
        return updatedBy;
    }

    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }

    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    public void setStructureName(String structureName) 
    {
        this.structureName = structureName;
    }

    public String getStructureName() 
    {
        return structureName;
    }

    public void setParentStructureId(Long parentStructureId) 
    {
        this.parentStructureId = parentStructureId;
    }

    public Long getParentStructureId() 
    {
        return parentStructureId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("accountingPeriod", getAccountingPeriod())
            .append("financialAccount", getFinancialAccount())
            .append("sku", getSku())
            .append("quantity", getQuantity())
            .append("amount", getAmount())
            .append("sourceType", getSourceType())
            .append("assetOwner", getAssetOwner())
            .append("batchNumber", getBatchNumber())
            .append("encryptedBatch", getEncryptedBatch())
            .append("warehouse", getWarehouse())
            .append("storageLocation", getStorageLocation())
            .append("storageZone", getStorageZone())
            .append("valuationTime", getValuationTime())
            .append("valuator", getValuator())
            .append("auditTime", getAuditTime())
            .append("auditor", getAuditor())
            .append("createdBy", getCreatedBy())
            .append("createdTime", getCreatedTime())
            .append("updatedBy", getUpdatedBy())
            .append("updatedTime", getUpdatedTime())
            .append("version", getVersion())
            .append("isDeleted", getIsDeleted())
            .append("structureName", getStructureName())
            .append("parentStructureId", getParentStructureId())
            .toString();
    }
}
