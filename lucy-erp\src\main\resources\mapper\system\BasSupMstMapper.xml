<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasSupMstMapper">
    
    <resultMap type="BasSupMst" id="BasSupMstResult">
        <result property="id"    column="id"    />
        <result property="bsmCcd"    column="bsm_ccd"    />
        <result property="bsmCd"    column="bsm_cd"    />
        <result property="bsmNm"    column="bsm_nm"    />
        <result property="bsmFnm"    column="bsm_fnm"    />
        <result property="bsmNmabb"    column="bsm_nmabb"    />
        <result property="bsmXyz"    column="bsm_xyz"    />
        <result property="bsmPcd"    column="bsm_pcd"    />
        <result property="bsmTcd"    column="bsm_tcd"    />
        <result property="bsmIsos"    column="bsm_isos"    />
        <result property="bsmNationcd"    column="bsm_nationcd"    />
        <result property="bsmNation"    column="bsm_nation"    />
        <result property="bsmPaytyp"    column="bsm_paytyp"    />
        <result property="bsmFreeqc"    column="bsm_freeqc"    />
        <result property="bsmLvl"    column="bsm_lvl"    />
        <result property="bsmFp"    column="bsm_fp"    />
        <result property="bsmFax"    column="bsm_fax"    />
        <result property="bsmPrcd"    column="bsm_prcd"    />
        <result property="bsmPrnm"    column="bsm_prnm"    />
        <result property="bsmCity"    column="bsm_city"    />
        <result property="bsmAddr"    column="bsm_addr"    />
        <result property="bsmSts"    column="bsm_sts"    />
        <result property="bsmTyp"    column="bsm_typ"    />
        <result property="bsmFstOrgcd"    column="bsm_fst_orgcd"    />
        <result property="bsmInvtyp"    column="bsm_invtyp"    />
        <result property="bsmFinanceCode"    column="bsm_finance_code"    />
        <result property="bsmImport"    column="bsm_import"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="bsmInitialcd"    column="bsm_initialcd"    />
        <result property="bsmOrgcd"    column="bsm_orgcd"    />
        <result property="bsmBuyer"    column="bsm_buyer"    />
        <result property="bsmAql"    column="bsm_aql"    />
        <result property="bsmInvoice"    column="bsm_invoice"    />
        <result property="bsmDd"    column="bsm_dd"    />
        <result property="bsmYyzzDt"    column="bsm_yyzz_dt"    />
        <result property="bsmWhpDt"    column="bsm_whp_dt"    />
        <result property="bsmYyzzNm"    column="bsm_yyzz_nm"    />
        <result property="bsmWhpNm"    column="bsm_whp_nm"    />
        <result property="bsmYyzzupDt"    column="bsm_yyzzup_dt"    />
        <result property="bsmWhpupDt"    column="bsm_whpup_dt"    />
        <result property="bsmJyfwNm"    column="bsm_jyfw_nm"    />
        <result property="bsmJyfwupDt"    column="bsm_jyfwup_dt"    />
        <result property="bsmFirstScore"    column="bsm_first_score"    />
        <result property="bsmAssessYear"    column="bsm_assess_year"    />
        <result property="bsmGrade"    column="bsm_grade"    />
        <result property="bsmAssessExpiry"    column="bsm_assess_expiry"    />
        <result property="bsmConAgent"    column="bsm_con_agent"    />
        <result property="bsmSjzb"    column="bsm_sjzb"    />
        <result property="bsmHisCity"    column="bsm_his_city"    />
        <result property="bsmHisArea"    column="bsm_his_area"    />
        <result property="bsmCompType"    column="bsm_comp_type"    />
        <result property="bsmMailAddress"    column="bsm_mail_address"    />
        <result property="bsmAduitSts"    column="bsm_aduit_sts"    />
    </resultMap>

    <sql id="selectBasSupMstVo">
        select id, bsm_ccd, bsm_cd, bsm_nm, bsm_fnm, bsm_nmabb, bsm_xyz, bsm_pcd, bsm_tcd, bsm_isos, bsm_nationcd, bsm_nation, bsm_paytyp, bsm_freeqc, bsm_lvl, bsm_fp, bsm_fax, bsm_prcd, bsm_prnm, bsm_city, bsm_addr, bsm_sts, bsm_typ, bsm_fst_orgcd, bsm_invtyp, bsm_finance_code, bsm_import, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, bsm_initialcd, bsm_orgcd, bsm_buyer, bsm_aql, bsm_invoice, bsm_dd, bsm_yyzz_dt, bsm_whp_dt, bsm_yyzz_nm, bsm_whp_nm, bsm_yyzzup_dt, bsm_whpup_dt, bsm_jyfw_nm, bsm_jyfwup_dt, bsm_first_score, bsm_assess_year, bsm_grade, bsm_assess_expiry, bsm_con_agent, bsm_sjzb, bsm_his_city, bsm_his_area, bsm_comp_type, bsm_mail_address, bsm_aduit_sts from bas_sup_mst
    </sql>

    <select id="selectBasSupMstList" parameterType="BasSupMst" resultMap="BasSupMstResult">
        <include refid="selectBasSupMstVo"/>
        <where>  
            <if test="bsmNm != null  and bsmNm != ''"> and bsm_nm = #{bsmNm}</if>
            <if test="bsmFnm != null  and bsmFnm != ''"> and bsm_fnm = #{bsmFnm}</if>
            <if test="bsmNmabb != null  and bsmNmabb != ''"> and bsm_nmabb = #{bsmNmabb}</if>
            <if test="bsmXyz != null  and bsmXyz != ''"> and bsm_xyz = #{bsmXyz}</if>
            <if test="bsmPcd != null  and bsmPcd != ''"> and bsm_pcd = #{bsmPcd}</if>
            <if test="bsmTcd != null  and bsmTcd != ''"> and bsm_tcd = #{bsmTcd}</if>
            <if test="bsmIsos != null  and bsmIsos != ''"> and bsm_isos = #{bsmIsos}</if>
            <if test="bsmNationcd != null  and bsmNationcd != ''"> and bsm_nationcd = #{bsmNationcd}</if>
            <if test="bsmNation != null  and bsmNation != ''"> and bsm_nation = #{bsmNation}</if>
            <if test="bsmPaytyp != null  and bsmPaytyp != ''"> and bsm_paytyp = #{bsmPaytyp}</if>
            <if test="bsmFreeqc != null  and bsmFreeqc != ''"> and bsm_freeqc = #{bsmFreeqc}</if>
            <if test="bsmLvl != null  and bsmLvl != ''"> and bsm_lvl = #{bsmLvl}</if>
            <if test="bsmFp != null  and bsmFp != ''"> and bsm_fp = #{bsmFp}</if>
            <if test="bsmFax != null  and bsmFax != ''"> and bsm_fax = #{bsmFax}</if>
            <if test="bsmPrcd != null  and bsmPrcd != ''"> and bsm_prcd = #{bsmPrcd}</if>
            <if test="bsmPrnm != null  and bsmPrnm != ''"> and bsm_prnm = #{bsmPrnm}</if>
            <if test="bsmCity != null  and bsmCity != ''"> and bsm_city = #{bsmCity}</if>
            <if test="bsmAddr != null  and bsmAddr != ''"> and bsm_addr = #{bsmAddr}</if>
            <if test="bsmSts != null  and bsmSts != ''"> and bsm_sts = #{bsmSts}</if>
            <if test="bsmTyp != null  and bsmTyp != ''"> and bsm_typ = #{bsmTyp}</if>
            <if test="bsmFstOrgcd != null  and bsmFstOrgcd != ''"> and bsm_fst_orgcd = #{bsmFstOrgcd}</if>
            <if test="bsmInvtyp != null  and bsmInvtyp != ''"> and bsm_invtyp = #{bsmInvtyp}</if>
            <if test="bsmFinanceCode != null  and bsmFinanceCode != ''"> and bsm_finance_code = #{bsmFinanceCode}</if>
            <if test="bsmImport != null  and bsmImport != ''"> and bsm_import = #{bsmImport}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="bsmInitialcd != null  and bsmInitialcd != ''"> and bsm_initialcd = #{bsmInitialcd}</if>
            <if test="bsmOrgcd != null  and bsmOrgcd != ''"> and bsm_orgcd = #{bsmOrgcd}</if>
            <if test="bsmBuyer != null  and bsmBuyer != ''"> and bsm_buyer = #{bsmBuyer}</if>
            <if test="bsmAql != null  and bsmAql != ''"> and bsm_aql = #{bsmAql}</if>
            <if test="bsmInvoice != null  and bsmInvoice != ''"> and bsm_invoice = #{bsmInvoice}</if>
            <if test="bsmDd != null  and bsmDd != ''"> and bsm_dd = #{bsmDd}</if>
            <if test="bsmYyzzDt != null  and bsmYyzzDt != ''"> and bsm_yyzz_dt = #{bsmYyzzDt}</if>
            <if test="bsmWhpDt != null  and bsmWhpDt != ''"> and bsm_whp_dt = #{bsmWhpDt}</if>
            <if test="bsmYyzzNm != null  and bsmYyzzNm != ''"> and bsm_yyzz_nm = #{bsmYyzzNm}</if>
            <if test="bsmWhpNm != null  and bsmWhpNm != ''"> and bsm_whp_nm = #{bsmWhpNm}</if>
            <if test="bsmYyzzupDt != null  and bsmYyzzupDt != ''"> and bsm_yyzzup_dt = #{bsmYyzzupDt}</if>
            <if test="bsmWhpupDt != null  and bsmWhpupDt != ''"> and bsm_whpup_dt = #{bsmWhpupDt}</if>
            <if test="bsmJyfwNm != null  and bsmJyfwNm != ''"> and bsm_jyfw_nm = #{bsmJyfwNm}</if>
            <if test="bsmJyfwupDt != null  and bsmJyfwupDt != ''"> and bsm_jyfwup_dt = #{bsmJyfwupDt}</if>
            <if test="bsmFirstScore != null  and bsmFirstScore != ''"> and bsm_first_score = #{bsmFirstScore}</if>
            <if test="bsmAssessYear != null  and bsmAssessYear != ''"> and bsm_assess_year = #{bsmAssessYear}</if>
            <if test="bsmGrade != null  and bsmGrade != ''"> and bsm_grade = #{bsmGrade}</if>
            <if test="bsmAssessExpiry != null  and bsmAssessExpiry != ''"> and bsm_assess_expiry = #{bsmAssessExpiry}</if>
            <if test="bsmConAgent != null  and bsmConAgent != ''"> and bsm_con_agent = #{bsmConAgent}</if>
            <if test="bsmSjzb != null  and bsmSjzb != ''"> and bsm_sjzb = #{bsmSjzb}</if>
            <if test="bsmHisCity != null  and bsmHisCity != ''"> and bsm_his_city = #{bsmHisCity}</if>
            <if test="bsmHisArea != null  and bsmHisArea != ''"> and bsm_his_area = #{bsmHisArea}</if>
            <if test="bsmCompType != null  and bsmCompType != ''"> and bsm_comp_type = #{bsmCompType}</if>
            <if test="bsmMailAddress != null  and bsmMailAddress != ''"> and bsm_mail_address = #{bsmMailAddress}</if>
            <if test="bsmAduitSts != null  and bsmAduitSts != ''"> and bsm_aduit_sts = #{bsmAduitSts}</if>
        </where>
    </select>
    
    <select id="selectBasSupMstByBsmCcd" parameterType="String" resultMap="BasSupMstResult">
        <include refid="selectBasSupMstVo"/>
        where bsm_cd = #{bsmCcd}
    </select>

    <select id="selectBasSupMstByBsmCd" parameterType="String" resultMap="BasSupMstResult">
        <include refid="selectBasSupMstVo"/>
        where bsm_cd = #{bsmCd}
    </select>

    <insert id="insertBasSupMst" parameterType="BasSupMst">
        insert into bas_sup_mst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bsmCcd != null">bsm_ccd,</if>
            <if test="bsmCd != null">bsm_cd,</if>
            <if test="bsmNm != null">bsm_nm,</if>
            <if test="bsmFnm != null">bsm_fnm,</if>
            <if test="bsmNmabb != null">bsm_nmabb,</if>
            <if test="bsmXyz != null">bsm_xyz,</if>
            <if test="bsmPcd != null">bsm_pcd,</if>
            <if test="bsmTcd != null">bsm_tcd,</if>
            <if test="bsmIsos != null">bsm_isos,</if>
            <if test="bsmNationcd != null">bsm_nationcd,</if>
            <if test="bsmNation != null">bsm_nation,</if>
            <if test="bsmPaytyp != null">bsm_paytyp,</if>
            <if test="bsmFreeqc != null">bsm_freeqc,</if>
            <if test="bsmLvl != null">bsm_lvl,</if>
            <if test="bsmFp != null">bsm_fp,</if>
            <if test="bsmFax != null">bsm_fax,</if>
            <if test="bsmPrcd != null">bsm_prcd,</if>
            <if test="bsmPrnm != null">bsm_prnm,</if>
            <if test="bsmCity != null">bsm_city,</if>
            <if test="bsmAddr != null">bsm_addr,</if>
            <if test="bsmSts != null">bsm_sts,</if>
            <if test="bsmTyp != null">bsm_typ,</if>
            <if test="bsmFstOrgcd != null">bsm_fst_orgcd,</if>
            <if test="bsmInvtyp != null">bsm_invtyp,</if>
            <if test="bsmFinanceCode != null">bsm_finance_code,</if>
            <if test="bsmImport != null">bsm_import,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="bsmInitialcd != null">bsm_initialcd,</if>
            <if test="bsmOrgcd != null">bsm_orgcd,</if>
            <if test="bsmBuyer != null">bsm_buyer,</if>
            <if test="bsmAql != null">bsm_aql,</if>
            <if test="bsmInvoice != null">bsm_invoice,</if>
            <if test="bsmDd != null">bsm_dd,</if>
            <if test="bsmYyzzDt != null">bsm_yyzz_dt,</if>
            <if test="bsmWhpDt != null">bsm_whp_dt,</if>
            <if test="bsmYyzzNm != null">bsm_yyzz_nm,</if>
            <if test="bsmWhpNm != null">bsm_whp_nm,</if>
            <if test="bsmYyzzupDt != null">bsm_yyzzup_dt,</if>
            <if test="bsmWhpupDt != null">bsm_whpup_dt,</if>
            <if test="bsmJyfwNm != null">bsm_jyfw_nm,</if>
            <if test="bsmJyfwupDt != null">bsm_jyfwup_dt,</if>
            <if test="bsmFirstScore != null">bsm_first_score,</if>
            <if test="bsmAssessYear != null">bsm_assess_year,</if>
            <if test="bsmGrade != null">bsm_grade,</if>
            <if test="bsmAssessExpiry != null">bsm_assess_expiry,</if>
            <if test="bsmConAgent != null">bsm_con_agent,</if>
            <if test="bsmSjzb != null">bsm_sjzb,</if>
            <if test="bsmHisCity != null">bsm_his_city,</if>
            <if test="bsmHisArea != null">bsm_his_area,</if>
            <if test="bsmCompType != null">bsm_comp_type,</if>
            <if test="bsmMailAddress != null">bsm_mail_address,</if>
            <if test="bsmAduitSts != null">bsm_aduit_sts,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bsmCcd != null">#{bsmCcd},</if>
            <if test="bsmCd != null">#{bsmCd},</if>
            <if test="bsmNm != null">#{bsmNm},</if>
            <if test="bsmFnm != null">#{bsmFnm},</if>
            <if test="bsmNmabb != null">#{bsmNmabb},</if>
            <if test="bsmXyz != null">#{bsmXyz},</if>
            <if test="bsmPcd != null">#{bsmPcd},</if>
            <if test="bsmTcd != null">#{bsmTcd},</if>
            <if test="bsmIsos != null">#{bsmIsos},</if>
            <if test="bsmNationcd != null">#{bsmNationcd},</if>
            <if test="bsmNation != null">#{bsmNation},</if>
            <if test="bsmPaytyp != null">#{bsmPaytyp},</if>
            <if test="bsmFreeqc != null">#{bsmFreeqc},</if>
            <if test="bsmLvl != null">#{bsmLvl},</if>
            <if test="bsmFp != null">#{bsmFp},</if>
            <if test="bsmFax != null">#{bsmFax},</if>
            <if test="bsmPrcd != null">#{bsmPrcd},</if>
            <if test="bsmPrnm != null">#{bsmPrnm},</if>
            <if test="bsmCity != null">#{bsmCity},</if>
            <if test="bsmAddr != null">#{bsmAddr},</if>
            <if test="bsmSts != null">#{bsmSts},</if>
            <if test="bsmTyp != null">#{bsmTyp},</if>
            <if test="bsmFstOrgcd != null">#{bsmFstOrgcd},</if>
            <if test="bsmInvtyp != null">#{bsmInvtyp},</if>
            <if test="bsmFinanceCode != null">#{bsmFinanceCode},</if>
            <if test="bsmImport != null">#{bsmImport},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="bsmInitialcd != null">#{bsmInitialcd},</if>
            <if test="bsmOrgcd != null">#{bsmOrgcd},</if>
            <if test="bsmBuyer != null">#{bsmBuyer},</if>
            <if test="bsmAql != null">#{bsmAql},</if>
            <if test="bsmInvoice != null">#{bsmInvoice},</if>
            <if test="bsmDd != null">#{bsmDd},</if>
            <if test="bsmYyzzDt != null">#{bsmYyzzDt},</if>
            <if test="bsmWhpDt != null">#{bsmWhpDt},</if>
            <if test="bsmYyzzNm != null">#{bsmYyzzNm},</if>
            <if test="bsmWhpNm != null">#{bsmWhpNm},</if>
            <if test="bsmYyzzupDt != null">#{bsmYyzzupDt},</if>
            <if test="bsmWhpupDt != null">#{bsmWhpupDt},</if>
            <if test="bsmJyfwNm != null">#{bsmJyfwNm},</if>
            <if test="bsmJyfwupDt != null">#{bsmJyfwupDt},</if>
            <if test="bsmFirstScore != null">#{bsmFirstScore},</if>
            <if test="bsmAssessYear != null">#{bsmAssessYear},</if>
            <if test="bsmGrade != null">#{bsmGrade},</if>
            <if test="bsmAssessExpiry != null">#{bsmAssessExpiry},</if>
            <if test="bsmConAgent != null">#{bsmConAgent},</if>
            <if test="bsmSjzb != null">#{bsmSjzb},</if>
            <if test="bsmHisCity != null">#{bsmHisCity},</if>
            <if test="bsmHisArea != null">#{bsmHisArea},</if>
            <if test="bsmCompType != null">#{bsmCompType},</if>
            <if test="bsmMailAddress != null">#{bsmMailAddress},</if>
            <if test="bsmAduitSts != null">#{bsmAduitSts},</if>
         </trim>
    </insert>

    <update id="updateBasSupMst" parameterType="BasSupMst">
        update bas_sup_mst
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="bsmCd != null">bsm_cd = #{bsmCd},</if>
            <if test="bsmNm != null">bsm_nm = #{bsmNm},</if>
            <if test="bsmFnm != null">bsm_fnm = #{bsmFnm},</if>
            <if test="bsmNmabb != null">bsm_nmabb = #{bsmNmabb},</if>
            <if test="bsmXyz != null">bsm_xyz = #{bsmXyz},</if>
            <if test="bsmPcd != null">bsm_pcd = #{bsmPcd},</if>
            <if test="bsmTcd != null">bsm_tcd = #{bsmTcd},</if>
            <if test="bsmIsos != null">bsm_isos = #{bsmIsos},</if>
            <if test="bsmNationcd != null">bsm_nationcd = #{bsmNationcd},</if>
            <if test="bsmNation != null">bsm_nation = #{bsmNation},</if>
            <if test="bsmPaytyp != null">bsm_paytyp = #{bsmPaytyp},</if>
            <if test="bsmFreeqc != null">bsm_freeqc = #{bsmFreeqc},</if>
            <if test="bsmLvl != null">bsm_lvl = #{bsmLvl},</if>
            <if test="bsmFp != null">bsm_fp = #{bsmFp},</if>
            <if test="bsmFax != null">bsm_fax = #{bsmFax},</if>
            <if test="bsmPrcd != null">bsm_prcd = #{bsmPrcd},</if>
            <if test="bsmPrnm != null">bsm_prnm = #{bsmPrnm},</if>
            <if test="bsmCity != null">bsm_city = #{bsmCity},</if>
            <if test="bsmAddr != null">bsm_addr = #{bsmAddr},</if>
            <if test="bsmSts != null">bsm_sts = #{bsmSts},</if>
            <if test="bsmTyp != null">bsm_typ = #{bsmTyp},</if>
            <if test="bsmFstOrgcd != null">bsm_fst_orgcd = #{bsmFstOrgcd},</if>
            <if test="bsmInvtyp != null">bsm_invtyp = #{bsmInvtyp},</if>
            <if test="bsmFinanceCode != null">bsm_finance_code = #{bsmFinanceCode},</if>
            <if test="bsmImport != null">bsm_import = #{bsmImport},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="bsmInitialcd != null">bsm_initialcd = #{bsmInitialcd},</if>
            <if test="bsmOrgcd != null">bsm_orgcd = #{bsmOrgcd},</if>
            <if test="bsmBuyer != null">bsm_buyer = #{bsmBuyer},</if>
            <if test="bsmAql != null">bsm_aql = #{bsmAql},</if>
            <if test="bsmInvoice != null">bsm_invoice = #{bsmInvoice},</if>
            <if test="bsmDd != null">bsm_dd = #{bsmDd},</if>
            <if test="bsmYyzzDt != null">bsm_yyzz_dt = #{bsmYyzzDt},</if>
            <if test="bsmWhpDt != null">bsm_whp_dt = #{bsmWhpDt},</if>
            <if test="bsmYyzzNm != null">bsm_yyzz_nm = #{bsmYyzzNm},</if>
            <if test="bsmWhpNm != null">bsm_whp_nm = #{bsmWhpNm},</if>
            <if test="bsmYyzzupDt != null">bsm_yyzzup_dt = #{bsmYyzzupDt},</if>
            <if test="bsmWhpupDt != null">bsm_whpup_dt = #{bsmWhpupDt},</if>
            <if test="bsmJyfwNm != null">bsm_jyfw_nm = #{bsmJyfwNm},</if>
            <if test="bsmJyfwupDt != null">bsm_jyfwup_dt = #{bsmJyfwupDt},</if>
            <if test="bsmFirstScore != null">bsm_first_score = #{bsmFirstScore},</if>
            <if test="bsmAssessYear != null">bsm_assess_year = #{bsmAssessYear},</if>
            <if test="bsmGrade != null">bsm_grade = #{bsmGrade},</if>
            <if test="bsmAssessExpiry != null">bsm_assess_expiry = #{bsmAssessExpiry},</if>
            <if test="bsmConAgent != null">bsm_con_agent = #{bsmConAgent},</if>
            <if test="bsmSjzb != null">bsm_sjzb = #{bsmSjzb},</if>
            <if test="bsmHisCity != null">bsm_his_city = #{bsmHisCity},</if>
            <if test="bsmHisArea != null">bsm_his_area = #{bsmHisArea},</if>
            <if test="bsmCompType != null">bsm_comp_type = #{bsmCompType},</if>
            <if test="bsmMailAddress != null">bsm_mail_address = #{bsmMailAddress},</if>
            <if test="bsmAduitSts != null">bsm_aduit_sts = #{bsmAduitSts},</if>
        </trim>
        where bsm_ccd = #{bsmCcd}
    </update>

    <delete id="deleteBasSupMstByBsmCcd" parameterType="String">
        delete from bas_sup_mst where bsm_ccd = #{bsmCcd}
    </delete>

    <delete id="deleteBasSupMstByBsmCcds" parameterType="String">
        delete from bas_sup_mst where bsm_ccd in 
        <foreach item="bsmCcd" collection="array" open="(" separator="," close=")">
            #{bsmCcd}
        </foreach>
    </delete>
</mapper>