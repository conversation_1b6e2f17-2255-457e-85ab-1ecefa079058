package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasProdPkg;
import com.lucy.erp.service.IBasProdPkgService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_产品_包装
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/prod/pkg")
public class BasProdPkgController extends BaseController
{
    @Autowired
    private IBasProdPkgService basProdPkgService;

    /**
     * 查询基础_产品_包装
列表
     */
    @PreAuthorize("@ss.hasPermi('prod:pkg:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasProdPkg basProdPkg)
    {
        startPage();
        List<BasProdPkg> list = basProdPkgService.selectBasProdPkgList(basProdPkg);
        return getDataTable(list);
    }

    /**
     * 导出基础_产品_包装
列表
     */
    @PreAuthorize("@ss.hasPermi('prod:pkg:export')")
    @Log(title = "基础_产品_包装 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasProdPkg basProdPkg)
    {
        List<BasProdPkg> list = basProdPkgService.selectBasProdPkgList(basProdPkg);
        ExcelUtil<BasProdPkg> util = new ExcelUtil<BasProdPkg>(BasProdPkg.class);
        util.exportExcel(response, list, "基础_产品_包装 数据");
    }

    /**
     * 获取基础_产品_包装
详细信息
     */
    @PreAuthorize("@ss.hasPermi('prod:pkg:query')")
    @GetMapping(value = "/{bppCd}")
    public AjaxResult getInfo(@PathVariable("bppCd") String bppCd)
    {
        return success(basProdPkgService.selectBasProdPkgByBppCd(bppCd));
    }

    /**
     * 新增基础_产品_包装

     */
    @PreAuthorize("@ss.hasPermi('prod:pkg:add')")
    @Log(title = "基础_产品_包装 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasProdPkg basProdPkg)
    {
        return toAjax(basProdPkgService.insertBasProdPkg(basProdPkg));
    }

    /**
     * 修改基础_产品_包装

     */
    @PreAuthorize("@ss.hasPermi('prod:pkg:edit')")
    @Log(title = "基础_产品_包装 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasProdPkg basProdPkg)
    {
        return toAjax(basProdPkgService.updateBasProdPkg(basProdPkg));
    }

    /**
     * 删除基础_产品_包装

     */
    @PreAuthorize("@ss.hasPermi('prod:pkg:remove')")
    @Log(title = "基础_产品_包装 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bppCds}")
    public AjaxResult remove(@PathVariable String[] bppCds)
    {
        return toAjax(basProdPkgService.deleteBasProdPkgByBppCds(bppCds));
    }
}
