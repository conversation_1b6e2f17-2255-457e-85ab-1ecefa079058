package com.ruoyi.repackage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.repackage.domain.ConversionMaterialDependencyClosure;
import com.ruoyi.repackage.service.IConversionMaterialDependencyClosureService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 物料依赖图谱Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/repackage/closure")
public class ConversionMaterialDependencyClosureController extends BaseController
{
    @Autowired
    private IConversionMaterialDependencyClosureService conversionMaterialDependencyClosureService;

    /**
     * 查询物料依赖图谱列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:closure:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        startPage();
        List<ConversionMaterialDependencyClosure> list = conversionMaterialDependencyClosureService.selectConversionMaterialDependencyClosureList(conversionMaterialDependencyClosure);
        return getDataTable(list);
    }

    /**
     * 导出物料依赖图谱列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:closure:export')")
    @Log(title = "物料依赖图谱", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        List<ConversionMaterialDependencyClosure> list = conversionMaterialDependencyClosureService.selectConversionMaterialDependencyClosureList(conversionMaterialDependencyClosure);
        ExcelUtil<ConversionMaterialDependencyClosure> util = new ExcelUtil<ConversionMaterialDependencyClosure>(ConversionMaterialDependencyClosure.class);
        util.exportExcel(response, list, "物料依赖图谱数据");
    }

    /**
     * 获取物料依赖图谱详细信息
     */
    @PreAuthorize("@ss.hasPermi('repackage:closure:query')")
    @GetMapping(value = "/{ancestorSku}")
    public AjaxResult getInfo(@PathVariable("ancestorSku") String ancestorSku)
    {
        return success(conversionMaterialDependencyClosureService.selectConversionMaterialDependencyClosureByAncestorSku(ancestorSku));
    }

    /**
     * 新增物料依赖图谱
     */
    @PreAuthorize("@ss.hasPermi('repackage:closure:add')")
    @Log(title = "物料依赖图谱", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return toAjax(conversionMaterialDependencyClosureService.insertConversionMaterialDependencyClosure(conversionMaterialDependencyClosure));
    }

    /**
     * 修改物料依赖图谱
     */
    @PreAuthorize("@ss.hasPermi('repackage:closure:edit')")
    @Log(title = "物料依赖图谱", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return toAjax(conversionMaterialDependencyClosureService.updateConversionMaterialDependencyClosure(conversionMaterialDependencyClosure));
    }

    /**
     * 删除物料依赖图谱
     */
    @PreAuthorize("@ss.hasPermi('repackage:closure:remove')")
    @Log(title = "物料依赖图谱", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ancestorSkus}")
    public AjaxResult remove(@PathVariable String[] ancestorSkus)
    {
        return toAjax(conversionMaterialDependencyClosureService.deleteConversionMaterialDependencyClosureByAncestorSkus(ancestorSkus));
    }
}
