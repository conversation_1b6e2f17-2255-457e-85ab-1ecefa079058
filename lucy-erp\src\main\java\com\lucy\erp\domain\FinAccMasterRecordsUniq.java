package com.lucy.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 账套对象 fin_acc_master_records_uniq
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public class FinAccMasterRecordsUniq extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账套ID */
    private String id;

    /** 期间 */
    @Excel(name = "期间")
    private String famrProcess;

    /** 主体 */
    @Excel(name = "主体")
    private String famrEntity;

    /** 版本 */
    @Excel(name = "版本")
    private String famrEdition;

    /** 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date famrAddtime;

    /** 版本说明 */
    @Excel(name = "版本说明")
    private String famrEditionRmk;

    /** 创建人 */
    @Excel(name = "创建人")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setFamrProcess(String famrProcess) 
    {
        this.famrProcess = famrProcess;
    }

    public String getFamrProcess() 
    {
        return famrProcess;
    }

    public void setFamrEntity(String famrEntity) 
    {
        this.famrEntity = famrEntity;
    }

    public String getFamrEntity() 
    {
        return famrEntity;
    }

    public void setFamrEdition(String famrEdition) 
    {
        this.famrEdition = famrEdition;
    }

    public String getFamrEdition() 
    {
        return famrEdition;
    }

    public void setFamrAddtime(Date famrAddtime) 
    {
        this.famrAddtime = famrAddtime;
    }

    public Date getFamrAddtime() 
    {
        return famrAddtime;
    }

    public void setFamrEditionRmk(String famrEditionRmk) 
    {
        this.famrEditionRmk = famrEditionRmk;
    }

    public String getFamrEditionRmk() 
    {
        return famrEditionRmk;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("famrProcess", getFamrProcess())
            .append("famrEntity", getFamrEntity())
            .append("famrEdition", getFamrEdition())
            .append("famrAddtime", getFamrAddtime())
            .append("famrEditionRmk", getFamrEditionRmk())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .toString();
    }
}
