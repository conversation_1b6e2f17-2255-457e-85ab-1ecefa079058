package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.ExpencesAmortizationDetailsMapper;
import com.lucy.erp.domain.ExpencesAmortizationDetails;
import com.lucy.erp.service.IExpencesAmortizationDetailsService;

/**
 * 费用分摊明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@DataSource(DataSourceType.ERP)
public class ExpencesAmortizationDetailsServiceImpl implements IExpencesAmortizationDetailsService
{
    @Autowired
    private ExpencesAmortizationDetailsMapper expencesAmortizationDetailsMapper;

    /**
     * 查询费用分摊明细
     * 
     * @param ecd_pcd 费用分摊明细主键
     * @return 费用分摊明细
     */
    @Override
    public ExpencesAmortizationDetails selectExpencesAmortizationDetailsByEcdPcd(String ead_pcd)
    {
        return expencesAmortizationDetailsMapper.selectExpencesAmortizationDetailsByEcdPcd(ead_pcd);
    }

    /**
     * 查询费用分摊明细列表
     * 
     * @param expencesAmortizationDetails 费用分摊明细
     * @return 费用分摊明细
     */
    @Override
    public List<ExpencesAmortizationDetails> selectExpencesAmortizationDetailsList(ExpencesAmortizationDetails expencesAmortizationDetails)
    {
        return expencesAmortizationDetailsMapper.selectExpencesAmortizationDetailsList(expencesAmortizationDetails);
    }

    /**
     * 新增费用分摊明细
     * 
     * @param expencesAmortizationDetails 费用分摊明细
     * @return 结果
     */
    @Override
    public int insertExpencesAmortizationDetails(ExpencesAmortizationDetails expencesAmortizationDetails)
    {
        return expencesAmortizationDetailsMapper.insertExpencesAmortizationDetails(expencesAmortizationDetails);
    }

    /**
     * 修改费用分摊明细
     * 
     * @param expencesAmortizationDetails 费用分摊明细
     * @return 结果
     */
    @Override
    public int updateExpencesAmortizationDetails(ExpencesAmortizationDetails expencesAmortizationDetails)
    {
        return expencesAmortizationDetailsMapper.updateExpencesAmortizationDetails(expencesAmortizationDetails);
    }

    /**
     * 批量删除费用分摊明细
     * 
     * @param ids 需要删除的费用分摊明细主键
     * @return 结果
     */
    @Override
    public int deleteExpencesAmortizationDetailsByIds(Long[] ids)
    {
        return expencesAmortizationDetailsMapper.deleteExpencesAmortizationDetailsByIds(ids);
    }

    /**
     * 删除费用分摊明细信息
     * 
     * @param id 费用分摊明细主键
     * @return 结果
     */
    @Override
    public int deleteExpencesAmortizationDetailsById(Long id)
    {
        return expencesAmortizationDetailsMapper.deleteExpencesAmortizationDetailsById(id);
    }
}
