package com.ruoyi.repackage.service;

import java.util.List;
import com.ruoyi.repackage.domain.ConversionRuleDetail;

/**
 * 分装规则明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IConversionRuleDetailService 
{
    /**
     * 查询分装规则明细
     * 
     * @param detailId 分装规则明细主键
     * @return 分装规则明细
     */
    public ConversionRuleDetail selectConversionRuleDetailByDetailId(Long detailId);

    /**
     * 查询分装规则明细列表
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 分装规则明细集合
     */
    public List<ConversionRuleDetail> selectConversionRuleDetailList(ConversionRuleDetail conversionRuleDetail);

    /**
     * 新增分装规则明细
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 结果
     */
    public int insertConversionRuleDetail(ConversionRuleDetail conversionRuleDetail);

    /**
     * 修改分装规则明细
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 结果
     */
    public int updateConversionRuleDetail(ConversionRuleDetail conversionRuleDetail);

    /**
     * 批量删除分装规则明细
     * 
     * @param detailIds 需要删除的分装规则明细主键集合
     * @return 结果
     */
    public int deleteConversionRuleDetailByDetailIds(Long[] detailIds);

    /**
     * 删除分装规则明细信息
     * 
     * @param detailId 分装规则明细主键
     * @return 结果
     */
    public int deleteConversionRuleDetailByDetailId(Long detailId);

    void addConversionRuleDetail(String sku_f, String sku_t, int qty_f, int qty_t, Long ruleId);
}
