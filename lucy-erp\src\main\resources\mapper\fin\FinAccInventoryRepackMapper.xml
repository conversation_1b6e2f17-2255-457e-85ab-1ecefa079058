<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.FinAccInventoryRepackMapper">
    
    <resultMap type="FinAccInventoryRepack" id="FinAccInventoryRepackResult">
        <result property="id"    column="id"    />
        <result property="fairId"    column="fair_id"    />
        <result property="fairAccurTime"    column="fair_accur_time"    />
        <result property="fairCreater"    column="fair_creater"    />
        <result property="fairApprover"    column="fair_approver"    />
        <result property="fairApproTime"    column="fair_appro_time"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="fairType"    column="fair_type"    />
        <result property="fairSubjectF"    column="fair_subject_f"    />
        <result property="fairSubjectT"    column="fair_subject_t"    />
        <result property="fairAmt"    column="fair_amt"    />
        <result property="fairOrgAmt"    column="fair_org_amt"    />
        <result property="fairRepackPsn"    column="fair_repack_psn"    />
        <result property="fairReRepackPsn"    column="fair_re_repack_psn"    />
        <result property="fairLiotIdF"    column="fair_liot_id_f"    />
        <result property="fairLiotIdT"    column="fair_liot_id_t"    />
        <result property="fairQtyF"    column="fair_qty_f"    />
        <result property="fairQtyT"    column="fair_qty_t"    />
        <result property="skuF"    column="sku_f"    />
        <result property="skuT"    column="sku_t"    />
    </resultMap>

    <sql id="selectFinAccInventoryRepackVo">
        select id, fair_id, fair_accur_time, fair_creater, fair_approver, fair_appro_time, r_cre_kid, r_cre_dt, fair_type, fair_subject_f, fair_subject_t, fair_amt, fair_org_amt, fair_repack_psn, fair_re_repack_psn, fair_liot_id_f, fair_liot_id_t, fair_qty_f, fair_qty_t, sku_f, sku_t from financial_account_db.fin_acc_inventory_repack
    </sql>

    <select id="selectFinAccInventoryRepackList" parameterType="FinAccInventoryRepack" resultMap="FinAccInventoryRepackResult">
        <include refid="selectFinAccInventoryRepackVo"/>
        <where>  
            <if test="fairId != null  and fairId != ''"> and fair_id = #{fairId}</if>
            <if test="fairAccurTime != null "> and fair_accur_time = #{fairAccurTime}</if>
            <if test="fairCreater != null  and fairCreater != ''"> and fair_creater = #{fairCreater}</if>
            <if test="fairApprover != null  and fairApprover != ''"> and fair_approver = #{fairApprover}</if>
            <if test="fairApproTime != null "> and fair_appro_time = #{fairApproTime}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="fairType != null  and fairType != ''"> and fair_type = #{fairType}</if>
            <if test="fairSubjectF != null  and fairSubjectF != ''"> and fair_subject_f = #{fairSubjectF}</if>
            <if test="fairSubjectT != null  and fairSubjectT != ''"> and fair_subject_t = #{fairSubjectT}</if>
            <if test="fairAmt != null "> and fair_amt = #{fairAmt}</if>
            <if test="fairOrgAmt != null "> and fair_org_amt = #{fairOrgAmt}</if>
            <if test="fairRepackPsn != null  and fairRepackPsn != ''"> and fair_repack_psn = #{fairRepackPsn}</if>
            <if test="fairReRepackPsn != null  and fairReRepackPsn != ''"> and fair_re_repack_psn = #{fairReRepackPsn}</if>
            <if test="fairLiotIdF != null  and fairLiotIdF != ''"> and fair_liot_id_f = #{fairLiotIdF}</if>
            <if test="fairLiotIdT != null  and fairLiotIdT != ''"> and fair_liot_id_t = #{fairLiotIdT}</if>
            <if test="fairQtyF != null "> and fair_qty_f = #{fairQtyF}</if>
            <if test="fairQtyT != null "> and fair_qty_t = #{fairQtyT}</if>
            <if test="skuF != null  and skuF != ''"> and sku_f = #{skuF}</if>
            <if test="skuT != null  and skuT != ''"> and sku_t = #{skuT}</if>
        </where>
    </select>
    
    <select id="selectFinAccInventoryRepackById" parameterType="FinAccInventoryRepack" resultMap="FinAccInventoryRepackResult">
        <include refid="selectFinAccInventoryRepackVo"/>
        where fair_id= #{fair_id}
        AND fair_type= #{fair_type}
    </select>

    <insert id="insertFinAccInventoryRepack" parameterType="FinAccInventoryRepack" useGeneratedKeys="true" keyProperty="id">
        insert into fin_acc_inventory_repack
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fairId != null">fair_id,</if>
            <if test="fairAccurTime != null">fair_accur_time,</if>
            <if test="fairCreater != null">fair_creater,</if>
            <if test="fairApprover != null">fair_approver,</if>
            <if test="fairApproTime != null">fair_appro_time,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="fairType != null">fair_type,</if>
            <if test="fairSubjectF != null">fair_subject_f,</if>
            <if test="fairSubjectT != null">fair_subject_t,</if>
            <if test="fairAmt != null">fair_amt,</if>
            <if test="fairOrgAmt != null">fair_org_amt,</if>
            <if test="fairRepackPsn != null">fair_repack_psn,</if>
            <if test="fairReRepackPsn != null">fair_re_repack_psn,</if>
            <if test="fairLiotIdF != null">fair_liot_id_f,</if>
            <if test="fairLiotIdT != null">fair_liot_id_t,</if>
            <if test="fairQtyF != null">fair_qty_f,</if>
            <if test="fairQtyT != null">fair_qty_t,</if>
            <if test="skuF != null">sku_f,</if>
            <if test="skuT != null">sku_t,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fairId != null">#{fairId},</if>
            <if test="fairAccurTime != null">#{fairAccurTime},</if>
            <if test="fairCreater != null">#{fairCreater},</if>
            <if test="fairApprover != null">#{fairApprover},</if>
            <if test="fairApproTime != null">#{fairApproTime},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="fairType != null">#{fairType},</if>
            <if test="fairSubjectF != null">#{fairSubjectF},</if>
            <if test="fairSubjectT != null">#{fairSubjectT},</if>
            <if test="fairAmt != null">#{fairAmt},</if>
            <if test="fairOrgAmt != null">#{fairOrgAmt},</if>
            <if test="fairRepackPsn != null">#{fairRepackPsn},</if>
            <if test="fairReRepackPsn != null">#{fairReRepackPsn},</if>
            <if test="fairLiotIdF != null">#{fairLiotIdF},</if>
            <if test="fairLiotIdT != null">#{fairLiotIdT},</if>
            <if test="fairQtyF != null">#{fairQtyF},</if>
            <if test="fairQtyT != null">#{fairQtyT},</if>
            <if test="skuF != null">#{skuF},</if>
            <if test="skuT != null">#{skuT},</if>
         </trim>
    </insert>

    <update id="updateFinAccInventoryRepack" parameterType="FinAccInventoryRepack">
        update fin_acc_inventory_repack
        <trim prefix="SET" suffixOverrides=",">
            <if test="fairId != null">fair_id = #{fairId},</if>
            <if test="fairAccurTime != null">fair_accur_time = #{fairAccurTime},</if>
            <if test="fairCreater != null">fair_creater = #{fairCreater},</if>
            <if test="fairApprover != null">fair_approver = #{fairApprover},</if>
            <if test="fairApproTime != null">fair_appro_time = #{fairApproTime},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="fairType != null">fair_type = #{fairType},</if>
            <if test="fairSubjectF != null">fair_subject_f = #{fairSubjectF},</if>
            <if test="fairSubjectT != null">fair_subject_t = #{fairSubjectT},</if>
            <if test="fairAmt != null">fair_amt = #{fairAmt},</if>
            <if test="fairOrgAmt != null">fair_org_amt = #{fairOrgAmt},</if>
            <if test="fairRepackPsn != null">fair_repack_psn = #{fairRepackPsn},</if>
            <if test="fairReRepackPsn != null">fair_re_repack_psn = #{fairReRepackPsn},</if>
            <if test="fairLiotIdF != null">fair_liot_id_f = #{fairLiotIdF},</if>
            <if test="fairLiotIdT != null">fair_liot_id_t = #{fairLiotIdT},</if>
            <if test="fairQtyF != null">fair_qty_f = #{fairQtyF},</if>
            <if test="fairQtyT != null">fair_qty_t = #{fairQtyT},</if>
            <if test="skuF != null">sku_f = #{skuF},</if>
            <if test="skuT != null">sku_t = #{skuT},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinAccInventoryRepackById" parameterType="Long">
        delete from fin_acc_inventory_repack where id = #{id}
    </delete>

    <delete id="deleteFinAccInventoryRepackByIds" parameterType="String">
        delete from fin_acc_inventory_repack where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>