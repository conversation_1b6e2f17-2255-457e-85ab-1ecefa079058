<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.ExpencesAmortizationDetailsMapper">
    
    <resultMap type="ExpencesAmortizationDetails" id="ExpencesAmortizationDetailsResult">
        <result property="id"    column="id"    />
        <result property="eadEmcd"    column="ead_emcd"    />
        <result property="eadEmccd"    column="ead_emccd"    />
        <result property="eadSkuCd"    column="ead_sku_cd"    />
        <result property="eadSkuQty"    column="ead_sku_qty"    />
        <result property="eadAmt"    column="ead_amt"    />
        <result property="eadInvcTyp"    column="ead_invc_typ"    />
        <result property="eadBit"    column="ead_bit"    />
        <result property="eadIsPart"    column="ead_is_part"    />
        <result property="eadPmcd"    column="ead_pmcd"    />
        <result property="eadPcd"    column="ead_pcd"    />
        <result property="eadPskuCd"    column="ead_psku_cd"    />
        <result property="eadPskuQty"    column="ead_psku_qty"    />
        <result property="eadPtyp"    column="ead_ptyp"    />
        <result property="eadTyp"    column="ead_typ"    />
        <result property="eadAdpter"    column="ead_adpter"    />
        <result property="eadAdpterMargin"    column="ead_adpter_margin"    />
        <result property="eadIsAsset"    column="ead_is_asset"    />
        <result property="eadDt"    column="ead_dt"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectExpencesAmortizationDetailsVo">
        select id, ead_emcd, ead_emccd, ead_sku_cd, ead_sku_qty, ead_amt, ead_invc_typ, ead_bit, ead_is_part, ead_pmcd, ead_pcd, ead_psku_cd, ead_psku_qty, ead_ptyp, ead_typ, ead_adpter, ead_adpter_margin, ead_is_asset, ead_dt, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from expences_amortization_details
    </sql>

    <select id="selectExpencesAmortizationDetailsList" parameterType="ExpencesAmortizationDetails" resultMap="ExpencesAmortizationDetailsResult">
        <include refid="selectExpencesAmortizationDetailsVo"/>
        <where>  
            <if test="eadEmcd != null  and eadEmcd != ''"> and ead_emcd = #{eadEmcd}</if>
            <if test="eadEmccd != null  and eadEmccd != ''"> and ead_emccd = #{eadEmccd}</if>
            <if test="eadSkuCd != null  and eadSkuCd != ''"> and ead_sku_cd = #{eadSkuCd}</if>
            <if test="eadSkuQty != null "> and ead_sku_qty = #{eadSkuQty}</if>
            <if test="eadAmt != null "> and ead_amt = #{eadAmt}</if>
            <if test="eadInvcTyp != null  and eadInvcTyp != ''"> and ead_invc_typ = #{eadInvcTyp}</if>
            <if test="eadBit != null "> and ead_bit = #{eadBit}</if>
            <if test="eadIsPart != null "> and ead_is_part = #{eadIsPart}</if>
            <if test="eadPmcd != null  and eadPmcd != ''"> and ead_pmcd = #{eadPmcd}</if>
            <if test="eadPcd != null  and eadPcd != ''"> and ead_pcd = #{eadPcd}</if>
            <if test="eadPskuCd != null  and eadPskuCd != ''"> and ead_psku_cd = #{eadPskuCd}</if>
            <if test="eadPskuQty != null "> and ead_psku_qty = #{eadPskuQty}</if>
            <if test="eadPtyp != null  and eadPtyp != ''"> and ead_ptyp = #{eadPtyp}</if>
            <if test="eadTyp != null  and eadTyp != ''"> and ead_typ = #{eadTyp}</if>
            <if test="eadAdpter != null  and eadAdpter != ''"> and ead_adpter = #{eadAdpter}</if>
            <if test="eadAdpterMargin != null "> and ead_adpter_margin = #{eadAdpterMargin}</if>
            <if test="eadIsAsset != null "> and ead_is_asset = #{eadIsAsset}</if>
            <if test="eadDt != null "> and ead_dt = #{eadDt}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
        </where>
    </select>
    
    <select id="selectExpencesAmortizationDetailsByEcdPcd" parameterType="String" resultMap="ExpencesAmortizationDetailsResult">
        <include refid="selectExpencesAmortizationDetailsVo"/>
        where ead_pcd = #{ead_pcd}
    </select>

    <insert id="insertExpencesAmortizationDetails" parameterType="ExpencesAmortizationDetails" useGeneratedKeys="true" keyProperty="id">
        insert into expences_amortization_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eadEmcd != null">ead_emcd,</if>
            <if test="eadEmccd != null">ead_emccd,</if>
            <if test="eadSkuCd != null">ead_sku_cd,</if>
            <if test="eadSkuQty != null">ead_sku_qty,</if>
            <if test="eadAmt != null">ead_amt,</if>
            <if test="eadInvcTyp != null">ead_invc_typ,</if>
            <if test="eadBit != null">ead_bit,</if>
            <if test="eadIsPart != null">ead_is_part,</if>
            <if test="eadPmcd != null">ead_pmcd,</if>
            <if test="eadPcd != null">ead_pcd,</if>
            <if test="eadPskuCd != null">ead_psku_cd,</if>
            <if test="eadPskuQty != null">ead_psku_qty,</if>
            <if test="eadPtyp != null">ead_ptyp,</if>
            <if test="eadTyp != null">ead_typ,</if>
            <if test="eadAdpter != null">ead_adpter,</if>
            <if test="eadAdpterMargin != null">ead_adpter_margin,</if>
            <if test="eadIsAsset != null">ead_is_asset,</if>
            <if test="eadDt != null">ead_dt,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eadEmcd != null">#{eadEmcd},</if>
            <if test="eadEmccd != null">#{eadEmccd},</if>
            <if test="eadSkuCd != null">#{eadSkuCd},</if>
            <if test="eadSkuQty != null">#{eadSkuQty},</if>
            <if test="eadAmt != null">#{eadAmt},</if>
            <if test="eadInvcTyp != null">#{eadInvcTyp},</if>
            <if test="eadBit != null">#{eadBit},</if>
            <if test="eadIsPart != null">#{eadIsPart},</if>
            <if test="eadPmcd != null">#{eadPmcd},</if>
            <if test="eadPcd != null">#{eadPcd},</if>
            <if test="eadPskuCd != null">#{eadPskuCd},</if>
            <if test="eadPskuQty != null">#{eadPskuQty},</if>
            <if test="eadPtyp != null">#{eadPtyp},</if>
            <if test="eadTyp != null">#{eadTyp},</if>
            <if test="eadAdpter != null">#{eadAdpter},</if>
            <if test="eadAdpterMargin != null">#{eadAdpterMargin},</if>
            <if test="eadIsAsset != null">#{eadIsAsset},</if>
            <if test="eadDt != null">#{eadDt},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateExpencesAmortizationDetails" parameterType="ExpencesAmortizationDetails">
        update expences_amortization_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="eadEmcd != null">ead_emcd = #{eadEmcd},</if>
            <if test="eadEmccd != null">ead_emccd = #{eadEmccd},</if>
            <if test="eadSkuCd != null">ead_sku_cd = #{eadSkuCd},</if>
            <if test="eadSkuQty != null">ead_sku_qty = #{eadSkuQty},</if>
            <if test="eadAmt != null">ead_amt = #{eadAmt},</if>
            <if test="eadInvcTyp != null">ead_invc_typ = #{eadInvcTyp},</if>
            <if test="eadBit != null">ead_bit = #{eadBit},</if>
            <if test="eadIsPart != null">ead_is_part = #{eadIsPart},</if>
            <if test="eadPmcd != null">ead_pmcd = #{eadPmcd},</if>
            <if test="eadPcd != null">ead_pcd = #{eadPcd},</if>
            <if test="eadPskuCd != null">ead_psku_cd = #{eadPskuCd},</if>
            <if test="eadPskuQty != null">ead_psku_qty = #{eadPskuQty},</if>
            <if test="eadPtyp != null">ead_ptyp = #{eadPtyp},</if>
            <if test="eadTyp != null">ead_typ = #{eadTyp},</if>
            <if test="eadAdpter != null">ead_adpter = #{eadAdpter},</if>
            <if test="eadAdpterMargin != null">ead_adpter_margin = #{eadAdpterMargin},</if>
            <if test="eadIsAsset != null">ead_is_asset = #{eadIsAsset},</if>
            <if test="eadDt != null">ead_dt = #{eadDt},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExpencesAmortizationDetailsById" parameterType="Long">
        delete from expences_amortization_details where id = #{id}
    </delete>

    <delete id="deleteExpencesAmortizationDetailsByIds" parameterType="String">
        delete from expences_amortization_details where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>