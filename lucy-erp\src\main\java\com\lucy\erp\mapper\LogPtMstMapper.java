package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.LogPtMst;

/**
 * 物流_生产跟踪_主
Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface LogPtMstMapper 
{
    /**
     * 查询物流_生产跟踪_主

     * 
     * @param lpmCd 物流_生产跟踪_主
主键
     * @return 物流_生产跟踪_主

     */
    public LogPtMst selectLogPtMstByLpmCd(String lpmCd);

    /**
     * 查询物流_生产跟踪_主
列表
     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 物流_生产跟踪_主
集合
     */
    public List<LogPtMst> selectLogPtMstList(LogPtMst logPtMst);

    /**
     * 新增物流_生产跟踪_主

     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 结果
     */
    public int insertLogPtMst(LogPtMst logPtMst);

    /**
     * 修改物流_生产跟踪_主

     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 结果
     */
    public int updateLogPtMst(LogPtMst logPtMst);

    /**
     * 删除物流_生产跟踪_主

     * 
     * @param lpmCd 物流_生产跟踪_主
主键
     * @return 结果
     */
    public int deleteLogPtMstByLpmCd(String lpmCd);

    /**
     * 批量删除物流_生产跟踪_主

     * 
     * @param lpmCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLogPtMstByLpmCds(String[] lpmCds);
}
