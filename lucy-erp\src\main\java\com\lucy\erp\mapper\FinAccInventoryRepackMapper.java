package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.FinAccInventoryRepack;

/**
 * 原材料分装及退回原材料单据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface FinAccInventoryRepackMapper 
{
    /**
     * 查询原材料分装及退回原材料单据
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 原材料分装及退回原材料单据
     */
    public FinAccInventoryRepack selectFinAccInventoryRepackById(FinAccInventoryRepack finAccInventoryRepack);

    /**
     * 查询原材料分装及退回原材料单据列表
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 原材料分装及退回原材料单据集合
     */
    public List<FinAccInventoryRepack> selectFinAccInventoryRepackList(FinAccInventoryRepack finAccInventoryRepack);

    /**
     * 新增原材料分装及退回原材料单据
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 结果
     */
    public int insertFinAccInventoryRepack(FinAccInventoryRepack finAccInventoryRepack);

    /**
     * 修改原材料分装及退回原材料单据
     * 
     * @param finAccInventoryRepack 原材料分装及退回原材料单据
     * @return 结果
     */
    public int updateFinAccInventoryRepack(FinAccInventoryRepack finAccInventoryRepack);

    /**
     * 删除原材料分装及退回原材料单据
     * 
     * @param id 原材料分装及退回原材料单据主键
     * @return 结果
     */
    public int deleteFinAccInventoryRepackById(Long id);

    /**
     * 批量删除原材料分装及退回原材料单据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinAccInventoryRepackByIds(Long[] ids);
}
