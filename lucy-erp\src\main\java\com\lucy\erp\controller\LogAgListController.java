package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogAgList;
import com.lucy.erp.service.ILogAgListService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 物流_到货_列表
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/system/aglist")
public class LogAgListController extends BaseController
{
    @Autowired
    private ILogAgListService logAgListService;

    /**
     * 查询物流_到货_列表
     */
    @PreAuthorize("@ss.hasPermi('system:aglist:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogAgList logAgList)
    {
        startPage();
        List<LogAgList> list = logAgListService.selectLogAgListList(logAgList);
        return getDataTable(list);
    }

    /**
     * 导出物流_到货_列列表
     */
    @PreAuthorize("@ss.hasPermi('system:aglist:export')")
    @Log(title = "物流_到货_列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogAgList logAgList)
    {
        List<LogAgList> list = logAgListService.selectLogAgListList(logAgList);
        ExcelUtil<LogAgList> util = new ExcelUtil<LogAgList>(LogAgList.class);
        util.exportExcel(response, list, "物流_到货_列数据");
    }

    /**
     * 获取物流_到货_列详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:aglist:query')")
    @GetMapping(value = "/{lalCd}")
    public AjaxResult getInfo(@PathVariable("lalCd") String lalCd)
    {
        return success(logAgListService.selectLogAgListByLalCd(lalCd));
    }

    /**
     * 新增物流_到货_列

     */
    @PreAuthorize("@ss.hasPermi('system:aglist:add')")
    @Log(title = "物流_到货_列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogAgList logAgList)
    {
        return toAjax(logAgListService.insertLogAgList(logAgList));
    }

    /**
     * 修改物流_到货_列

     */
    @PreAuthorize("@ss.hasPermi('system:aglist:edit')")
    @Log(title = "物流_到货_列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogAgList logAgList)
    {
        return toAjax(logAgListService.updateLogAgList(logAgList));
    }

    /**
     * 删除物流_到货_列

     */
    @PreAuthorize("@ss.hasPermi('system:aglist:remove')")
    @Log(title = "物流_到货_列表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lalCds}")
    public AjaxResult remove(@PathVariable String[] lalCds)
    {
        return toAjax(logAgListService.deleteLogAgListByLalCds(lalCds));
    }
}
