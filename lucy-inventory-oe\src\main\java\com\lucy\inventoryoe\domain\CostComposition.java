package com.lucy.inventoryoe.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * Cost composition structure对象 cost_composition
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public class CostComposition extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 表结构名称 */
    @Excel(name = "表结构名称")
    private String structureName;

    /** 所在表结构ID */
    @Excel(name = "所在表结构ID")
    private Long parentStructureId;

    /** 明细状态 S：生效  X：废弃 */
    @Excel(name = "明细状态 S：生效  X：废弃")
    private String status;

    /** 明细生成背景 */
    @Excel(name = "明细生成背景")
    private String generationContext;

    /** 交易货币代码 */
    @Excel(name = "交易货币代码")
    private String currencyCode;

    /** 基准货币代码 */
    @Excel(name = "基准货币代码")
    private String baseCurrencyCode;

    /** 汇率 */
    @Excel(name = "汇率")
    private BigDecimal exchangeRate;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalAmount;

    /** 物料金额 */
    @Excel(name = "物料金额")
    private BigDecimal materialCost;

    /** 人工金额 */
    @Excel(name = "人工金额")
    private BigDecimal laborCost;

    /** 制造费用金额 */
    @Excel(name = "制造费用金额")
    private BigDecimal manufacturingCost;

    /** 物流费用金额 */
    @Excel(name = "物流费用金额")
    private BigDecimal logisticsCost;

    /** 物流包材金额 */
    @Excel(name = "物流包材金额")
    private BigDecimal packagingCost;

    /** 标准成本 */
    @Excel(name = "标准成本")
    private BigDecimal standardCost;

    /** 标准料 */
    @Excel(name = "标准料")
    private BigDecimal standardMaterial;

    /** 标准工 */
    @Excel(name = "标准工")
    private BigDecimal standardLabor;

    /** 标准费 */
    @Excel(name = "标准费")
    private BigDecimal standardOverhead;

    /** 标准物流费 */
    @Excel(name = "标准物流费")
    private BigDecimal standardLogistics;

    /** 标准物流包材费 */
    @Excel(name = "标准物流包材费")
    private BigDecimal standardPackaging;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date createdTime;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date updatedTime;

    /** 行版本 */
    @Excel(name = "行版本")
    private Integer version;

    /** 删除标记 0:正常 1:删除 */
    @Excel(name = "删除标记 0:正常 1:删除")
    private Integer isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStructureName(String structureName) 
    {
        this.structureName = structureName;
    }

    public String getStructureName() 
    {
        return structureName;
    }

    public void setParentStructureId(Long parentStructureId) 
    {
        this.parentStructureId = parentStructureId;
    }

    public Long getParentStructureId() 
    {
        return parentStructureId;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setGenerationContext(String generationContext) 
    {
        this.generationContext = generationContext;
    }

    public String getGenerationContext() 
    {
        return generationContext;
    }

    public void setCurrencyCode(String currencyCode) 
    {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyCode() 
    {
        return currencyCode;
    }

    public void setBaseCurrencyCode(String baseCurrencyCode) 
    {
        this.baseCurrencyCode = baseCurrencyCode;
    }

    public String getBaseCurrencyCode() 
    {
        return baseCurrencyCode;
    }

    public void setExchangeRate(BigDecimal exchangeRate) 
    {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getExchangeRate() 
    {
        return exchangeRate;
    }

    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }

    public void setMaterialCost(BigDecimal materialCost) 
    {
        this.materialCost = materialCost;
    }

    public BigDecimal getMaterialCost() 
    {
        return materialCost;
    }

    public void setLaborCost(BigDecimal laborCost) 
    {
        this.laborCost = laborCost;
    }

    public BigDecimal getLaborCost() 
    {
        return laborCost;
    }

    public void setManufacturingCost(BigDecimal manufacturingCost) 
    {
        this.manufacturingCost = manufacturingCost;
    }

    public BigDecimal getManufacturingCost() 
    {
        return manufacturingCost;
    }

    public void setLogisticsCost(BigDecimal logisticsCost) 
    {
        this.logisticsCost = logisticsCost;
    }

    public BigDecimal getLogisticsCost() 
    {
        return logisticsCost;
    }

    public void setPackagingCost(BigDecimal packagingCost) 
    {
        this.packagingCost = packagingCost;
    }

    public BigDecimal getPackagingCost() 
    {
        return packagingCost;
    }

    public void setStandardCost(BigDecimal standardCost) 
    {
        this.standardCost = standardCost;
    }

    public BigDecimal getStandardCost() 
    {
        return standardCost;
    }

    public void setStandardMaterial(BigDecimal standardMaterial) 
    {
        this.standardMaterial = standardMaterial;
    }

    public BigDecimal getStandardMaterial() 
    {
        return standardMaterial;
    }

    public void setStandardLabor(BigDecimal standardLabor) 
    {
        this.standardLabor = standardLabor;
    }

    public BigDecimal getStandardLabor() 
    {
        return standardLabor;
    }

    public void setStandardOverhead(BigDecimal standardOverhead) 
    {
        this.standardOverhead = standardOverhead;
    }

    public BigDecimal getStandardOverhead() 
    {
        return standardOverhead;
    }

    public void setStandardLogistics(BigDecimal standardLogistics) 
    {
        this.standardLogistics = standardLogistics;
    }

    public BigDecimal getStandardLogistics() 
    {
        return standardLogistics;
    }

    public void setStandardPackaging(BigDecimal standardPackaging) 
    {
        this.standardPackaging = standardPackaging;
    }

    public BigDecimal getStandardPackaging() 
    {
        return standardPackaging;
    }

    public void setCreatedBy(String createdBy) 
    {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() 
    {
        return createdBy;
    }

    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }

    public void setUpdatedBy(String updatedBy) 
    {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() 
    {
        return updatedBy;
    }

    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }

    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("structureName", getStructureName())
            .append("parentStructureId", getParentStructureId())
            .append("status", getStatus())
            .append("generationContext", getGenerationContext())
            .append("currencyCode", getCurrencyCode())
            .append("baseCurrencyCode", getBaseCurrencyCode())
            .append("exchangeRate", getExchangeRate())
            .append("totalAmount", getTotalAmount())
            .append("materialCost", getMaterialCost())
            .append("laborCost", getLaborCost())
            .append("manufacturingCost", getManufacturingCost())
            .append("logisticsCost", getLogisticsCost())
            .append("packagingCost", getPackagingCost())
            .append("standardCost", getStandardCost())
            .append("standardMaterial", getStandardMaterial())
            .append("standardLabor", getStandardLabor())
            .append("standardOverhead", getStandardOverhead())
            .append("standardLogistics", getStandardLogistics())
            .append("standardPackaging", getStandardPackaging())
            .append("createdBy", getCreatedBy())
            .append("createdTime", getCreatedTime())
            .append("updatedBy", getUpdatedBy())
            .append("updatedTime", getUpdatedTime())
            .append("version", getVersion())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
