package com.lucy.customer.mapper;

import java.util.List;
import com.lucy.customer.domain.DepartmentGroup;

/**
 * 部门组织结构 Department groupsMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface DepartmentGroupMapper 
{
    /**
     * 查询部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 部门组织结构 Department groups
     */
    public DepartmentGroup selectDepartmentGroupByCode(String code);

    /**
     * 查询部门组织结构 Department groups列表
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 部门组织结构 Department groups集合
     */
    public List<DepartmentGroup> selectDepartmentGroupList(DepartmentGroup departmentGroup);

    /**
     * 新增部门组织结构 Department groups
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 结果
     */
    public int insertDepartmentGroup(DepartmentGroup departmentGroup);

    /**
     * 修改部门组织结构 Department groups
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 结果
     */
    public int updateDepartmentGroup(DepartmentGroup departmentGroup);

    /**
     * 删除部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 结果
     */
    public int deleteDepartmentGroupByCode(String code);

    /**
     * 批量删除部门组织结构 Department groups
     * 
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDepartmentGroupByCodes(String[] codes);
}
