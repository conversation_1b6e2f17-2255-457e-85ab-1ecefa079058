package com.ruoyi.repackage.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分装规则明细对象 conversion_rule_detail
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class ConversionRuleDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细ID，示例值: 1001 */
    private Long detailId;

    /** 关联conversion_rule.rule_id，示例值: 101 */
    @Excel(name = "关联conversion_rule.rule_id，示例值: 101")
    private Long ruleId;

    /** 物料SKU，示例值: "RAW-ETH-95%" */
    @Excel(name = "物料SKU，示例值: RAW-ETH-95%")
    private String materialSku;

    /** 物料角色：输入/INPUT、输出/OUTPUT，示例值: "INPUT" */
    @Excel(name = "物料角色：输入/INPUT、输出/OUTPUT，示例值: INPUT")
    private String role;

    /** 数量（输入为负，输出为正），示例值: -1000.0000 (输入) */
    @Excel(name = "数量", readConverterExp = "输=入为负，输出为正")
    private BigDecimal quantity;

    /** 单位，示例值: "ml" */
    @Excel(name = "单位，示例值: ml")
    private String uom;

    /** 浓度百分比（仅纯物质），示例值: 95.00 */
    @Excel(name = "浓度百分比", readConverterExp = "仅=纯物质")
    private BigDecimal concentration;

    /** 有效成分名称（用于成本计算），示例值: "Ethanol" */
    @Excel(name = "有效成分名称", readConverterExp = "用=于成本计算")
    private String effectiveComponent;

    /** 有效成分占比（0-1），示例值: 0.95 */
    @Excel(name = "有效成分占比", readConverterExp = "0=-1")
    private BigDecimal componentRatio;

    public void setDetailId(Long detailId) 
    {
        this.detailId = detailId;
    }

    public Long getDetailId() 
    {
        return detailId;
    }

    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }

    public void setMaterialSku(String materialSku) 
    {
        this.materialSku = materialSku;
    }

    public String getMaterialSku() 
    {
        return materialSku;
    }

    public void setRole(String role) 
    {
        this.role = role;
    }

    public String getRole() 
    {
        return role;
    }

    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }

    public void setUom(String uom) 
    {
        this.uom = uom;
    }

    public String getUom() 
    {
        return uom;
    }

    public void setConcentration(BigDecimal concentration) 
    {
        this.concentration = concentration;
    }

    public BigDecimal getConcentration() 
    {
        return concentration;
    }

    public void setEffectiveComponent(String effectiveComponent) 
    {
        this.effectiveComponent = effectiveComponent;
    }

    public String getEffectiveComponent() 
    {
        return effectiveComponent;
    }

    public void setComponentRatio(BigDecimal componentRatio) 
    {
        this.componentRatio = componentRatio;
    }

    public BigDecimal getComponentRatio() 
    {
        return componentRatio;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("detailId", getDetailId())
            .append("ruleId", getRuleId())
            .append("materialSku", getMaterialSku())
            .append("role", getRole())
            .append("quantity", getQuantity())
            .append("uom", getUom())
            .append("concentration", getConcentration())
            .append("effectiveComponent", getEffectiveComponent())
            .append("componentRatio", getComponentRatio())
            .toString();
    }
}
