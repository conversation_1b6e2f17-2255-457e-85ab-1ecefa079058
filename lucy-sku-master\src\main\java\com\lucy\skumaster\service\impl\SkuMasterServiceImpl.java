package com.lucy.skumaster.service.impl;

import java.util.List;

import com.lucy.erp.domain.*;
import com.lucy.erp.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.skumaster.mapper.SkuMasterMapper;
import com.lucy.skumaster.domain.SkuMaster;
import com.lucy.skumaster.service.ISkuMasterService;

/**
 * SKU主数据 SKU master dataService业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class SkuMasterServiceImpl implements ISkuMasterService
{
    @Autowired
    private SkuMasterMapper skuMasterMapper;

    @Autowired
    private IBasProdPkgService basProdPkgService;

    @Autowired
    private IBasProdMstService basProdMstService;

    @Autowired
    private IBasProdAchemService basProdAchemService;

    @Autowired
    private IFinProdCodeService finProdCodeService;

    @Autowired
    private IBasProdLineService basProdLineService;

    @Autowired
    private IBasPlineTypeService basPlineTypeService;

    /**
     * 查询SKU主数据 SKU master data
     *
     * @param code SKU主数据 SKU master data主键
     * @return SKU主数据 SKU master data
     */
    @Override
    public SkuMaster selectSkuMasterByCode(String code)
    {
        return skuMasterMapper.selectSkuMasterByCode(code);
    }

    /**
     * 查询SKU主数据 SKU master data列表
     *
     * @param skuMaster SKU主数据 SKU master data
     * @return SKU主数据 SKU master data
     */
    @Override
    public List<SkuMaster> selectSkuMasterList(SkuMaster skuMaster)
    {
        return skuMasterMapper.selectSkuMasterList(skuMaster);
    }

    /**
     * 新增SKU主数据 SKU master data
     *
     * @param skuMaster SKU主数据 SKU master data
     * @return 结果
     */
    @Override
    public int insertSkuMaster(SkuMaster skuMaster)
    {
        return skuMasterMapper.insertSkuMaster(skuMaster);
    }

    /**
     * 修改SKU主数据 SKU master data
     *
     * @param skuMaster SKU主数据 SKU master data
     * @return 结果
     */
    @Override
    public int updateSkuMaster(SkuMaster skuMaster)
    {
        return skuMasterMapper.updateSkuMaster(skuMaster);
    }

    /**
     * 批量删除SKU主数据 SKU master data
     *
     * @param codes 需要删除的SKU主数据 SKU master data主键
     * @return 结果
     */
    @Override
    public int deleteSkuMasterByCodes(String[] codes)
    {
        return skuMasterMapper.deleteSkuMasterByCodes(codes);
    }

    /**
     * 删除SKU主数据 SKU master data信息
     *
     * @param code SKU主数据 SKU master data主键
     * @return 结果
     */
    @Override
    public int deleteSkuMasterByCode(String code)
    {
        return skuMasterMapper.deleteSkuMasterByCode(code);
    }

    @Override
    public void insertSkuMasterIfNotExist(String code) {
        SkuMaster skuMaster = skuMasterMapper.selectSkuMasterByCode(code);
        if (skuMaster == null) {
            BasProdPkg basProdPkg = basProdPkgService.selectBasProdPkgByBppCd(code);
            BasProdMst basProdMst = basProdMstService.selectBasProdMstByBpmCd(basProdPkg.getBppPcd());
            BasProdAchem basProdAchem = basProdAchemService.selectBasProdAchemByBpacCd(basProdMst.getBpmCd());
            BasProdLine basProdLine = new BasProdLine();
            basProdLine.setBplCd(basProdMst.getBpmCd());
            List<BasProdLine> basProdLines = basProdLineService.selectBasProdLineList(basProdLine);
            String bpt_nm = "";
            if (!basProdLines.isEmpty()) {
                BasPlineType basPlineType = new BasPlineType();
                basPlineType.setBptCd(basProdLines.get(0).getBplPlineType());
                List<BasPlineType> basPlineTypes = basPlineTypeService.selectBasPlineTypeList(basPlineType);
                bpt_nm = basPlineTypes.get(0).getBptNm();
            }
            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            finProdCode = finProdCodes.get(0);
            skuMaster = new SkuMaster();
            skuMaster.setCode(code);
            skuMaster.setName(basProdMst.getBpmNm());
            skuMaster.setUom(basProdPkg.getBppUnit());
            // TODO
            // 库存类型
//            skuMaster.setInventoryType("");
            // 成本计价方式
//            skuMaster.setCostMethod("");
            skuMaster.setBpmCcd(basProdMst.getBpmCcd());
            skuMaster.setBpmCnm(basProdMst.getBpmCnm());
            skuMaster.setBpmSort(basProdMst.getBpmSort());
            skuMaster.setBpmSortnm(basProdMst.getBpmSortnm());
            skuMaster.setBpmPcd(basProdMst.getBpmPcd());
            skuMaster.setBpmPnm(basProdMst.getBpmPnm());
            skuMaster.setBpmCd(basProdMst.getBpmCd());
            skuMaster.setBpmOrgcd(basProdMst.getBpmOrgcd());
            skuMaster.setBpmNm(basProdMst.getBpmNm());
            skuMaster.setBppPack(basProdPkg.getBppPack());
            skuMaster.setBppStdQty(basProdPkg.getBppStdQty());
            skuMaster.setBpacCas(basProdAchem.getBpacCas());
            skuMaster.setBpacDgcls(basProdAchem.getBpacDgcls());
            skuMaster.setBptNm(bpt_nm);
            skuMaster.setFpcCode(finProdCode.getLgcode());
            skuMasterMapper.insertSkuMaster(skuMaster);
        }
    }
}
