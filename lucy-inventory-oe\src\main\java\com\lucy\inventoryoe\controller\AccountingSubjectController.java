package com.lucy.inventoryoe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.inventoryoe.domain.AccountingSubject;
import com.lucy.inventoryoe.service.IAccountingSubjectService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 会计科目 Accounting subject masterController
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@RestController
@RequestMapping("/lucy-inventoryoe/subject2")
public class AccountingSubjectController extends BaseController
{
    @Autowired
    private IAccountingSubjectService accountingSubjectService;

    /**
     * 查询会计科目 Accounting subject master列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:subject2:list')")
    @GetMapping("/list")
    public TableDataInfo list(AccountingSubject accountingSubject)
    {
        startPage();
        List<AccountingSubject> list = accountingSubjectService.selectAccountingSubjectList(accountingSubject);
        return getDataTable(list);
    }

    /**
     * 导出会计科目 Accounting subject master列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:subject2:export')")
    @Log(title = "会计科目 Accounting subject master", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountingSubject accountingSubject)
    {
        List<AccountingSubject> list = accountingSubjectService.selectAccountingSubjectList(accountingSubject);
        ExcelUtil<AccountingSubject> util = new ExcelUtil<AccountingSubject>(AccountingSubject.class);
        util.exportExcel(response, list, "会计科目 Accounting subject master数据");
    }

    /**
     * 获取会计科目 Accounting subject master详细信息
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:subject2:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(accountingSubjectService.selectAccountingSubjectByCode(code));
    }

    /**
     * 新增会计科目 Accounting subject master
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:subject2:add')")
    @Log(title = "会计科目 Accounting subject master", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AccountingSubject accountingSubject)
    {
        return toAjax(accountingSubjectService.insertAccountingSubject(accountingSubject));
    }

    /**
     * 修改会计科目 Accounting subject master
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:subject2:edit')")
    @Log(title = "会计科目 Accounting subject master", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AccountingSubject accountingSubject)
    {
        return toAjax(accountingSubjectService.updateAccountingSubject(accountingSubject));
    }

    /**
     * 删除会计科目 Accounting subject master
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:subject2:remove')")
    @Log(title = "会计科目 Accounting subject master", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(accountingSubjectService.deleteAccountingSubjectByCodes(codes));
    }
}
