package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.LogInventoryIoTransaction;

/**
 * 物流_库存_清单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface LogInventoryIoTransactionMapper 
{
    /**
     * 查询物流_库存_清单
     * 
     * @param id 物流_库存_清单主键
     * @return 物流_库存_清单
     */
    public LogInventoryIoTransaction selectLogInventoryIoTransactionById(Long id);

    /**
     * 查询物流_库存_清单列表
     * 
     * @param logInventoryIoTransaction 物流_库存_清单
     * @return 物流_库存_清单集合
     */
    public List<LogInventoryIoTransaction> selectLogInventoryIoTransactionList(LogInventoryIoTransaction logInventoryIoTransaction);

    /**
     * 新增物流_库存_清单
     * 
     * @param logInventoryIoTransaction 物流_库存_清单
     * @return 结果
     */
    public int insertLogInventoryIoTransaction(LogInventoryIoTransaction logInventoryIoTransaction);

    /**
     * 修改物流_库存_清单
     * 
     * @param logInventoryIoTransaction 物流_库存_清单
     * @return 结果
     */
    public int updateLogInventoryIoTransaction(LogInventoryIoTransaction logInventoryIoTransaction);

    /**
     * 删除物流_库存_清单
     * 
     * @param id 物流_库存_清单主键
     * @return 结果
     */
    public int deleteLogInventoryIoTransactionById(Long id);

    /**
     * 批量删除物流_库存_清单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLogInventoryIoTransactionByIds(Long[] ids);
}
