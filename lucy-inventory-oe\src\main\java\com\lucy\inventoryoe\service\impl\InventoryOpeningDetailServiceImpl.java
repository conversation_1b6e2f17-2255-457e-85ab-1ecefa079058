package com.lucy.inventoryoe.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.inventoryoe.mapper.InventoryOpeningDetailMapper;
import com.lucy.inventoryoe.domain.InventoryOpeningDetail;
import com.lucy.inventoryoe.service.IInventoryOpeningDetailService;

/**
 * 库存期初明细 Inventory opening detail recordsService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class InventoryOpeningDetailServiceImpl implements IInventoryOpeningDetailService 
{
    @Autowired
    private InventoryOpeningDetailMapper inventoryOpeningDetailMapper;

    /**
     * 查询库存期初明细 Inventory opening detail records
     * 
     * @param id 库存期初明细 Inventory opening detail records主键
     * @return 库存期初明细 Inventory opening detail records
     */
    @Override
    public InventoryOpeningDetail selectInventoryOpeningDetailById(Long id)
    {
        return inventoryOpeningDetailMapper.selectInventoryOpeningDetailById(id);
    }

    /**
     * 查询库存期初明细 Inventory opening detail records列表
     * 
     * @param inventoryOpeningDetail 库存期初明细 Inventory opening detail records
     * @return 库存期初明细 Inventory opening detail records
     */
    @Override
    public List<InventoryOpeningDetail> selectInventoryOpeningDetailList(InventoryOpeningDetail inventoryOpeningDetail)
    {
        return inventoryOpeningDetailMapper.selectInventoryOpeningDetailList(inventoryOpeningDetail);
    }

    /**
     * 新增库存期初明细 Inventory opening detail records
     * 
     * @param inventoryOpeningDetail 库存期初明细 Inventory opening detail records
     * @return 结果
     */
    @Override
    public int insertInventoryOpeningDetail(InventoryOpeningDetail inventoryOpeningDetail)
    {
        return inventoryOpeningDetailMapper.insertInventoryOpeningDetail(inventoryOpeningDetail);
    }

    /**
     * 修改库存期初明细 Inventory opening detail records
     * 
     * @param inventoryOpeningDetail 库存期初明细 Inventory opening detail records
     * @return 结果
     */
    @Override
    public int updateInventoryOpeningDetail(InventoryOpeningDetail inventoryOpeningDetail)
    {
        return inventoryOpeningDetailMapper.updateInventoryOpeningDetail(inventoryOpeningDetail);
    }

    /**
     * 批量删除库存期初明细 Inventory opening detail records
     * 
     * @param ids 需要删除的库存期初明细 Inventory opening detail records主键
     * @return 结果
     */
    @Override
    public int deleteInventoryOpeningDetailByIds(Long[] ids)
    {
        return inventoryOpeningDetailMapper.deleteInventoryOpeningDetailByIds(ids);
    }

    /**
     * 删除库存期初明细 Inventory opening detail records信息
     * 
     * @param id 库存期初明细 Inventory opening detail records主键
     * @return 结果
     */
    @Override
    public int deleteInventoryOpeningDetailById(Long id)
    {
        return inventoryOpeningDetailMapper.deleteInventoryOpeningDetailById(id);
    }
}
