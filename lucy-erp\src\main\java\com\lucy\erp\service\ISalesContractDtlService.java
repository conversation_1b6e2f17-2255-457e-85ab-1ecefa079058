package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.SalesContractDtl;

/**
 * 销售_合同_明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ISalesContractDtlService 
{
    /**
     * 查询销售_合同_明细
     * 
     * @param scdMcd 销售_合同_明细主键
     * @return 销售_合同_明细
     */
    public SalesContractDtl selectSalesContractDtlByScdcd(String scdMcd);

    /**
     * 查询销售_合同_明细列表
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 销售_合同_明细集合
     */
    public List<SalesContractDtl> selectSalesContractDtlList(SalesContractDtl salesContractDtl);

    /**
     * 新增销售_合同_明细
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 结果
     */
    public int insertSalesContractDtl(SalesContractDtl salesContractDtl);

    /**
     * 修改销售_合同_明细
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 结果
     */
    public int updateSalesContractDtl(SalesContractDtl salesContractDtl);

    /**
     * 批量删除销售_合同_明细
     * 
     * @param scdMcds 需要删除的销售_合同_明细主键集合
     * @return 结果
     */
    public int deleteSalesContractDtlByScdMcds(String[] scdMcds);

    /**
     * 删除销售_合同_明细信息
     * 
     * @param scdMcd 销售_合同_明细主键
     * @return 结果
     */
    public int deleteSalesContractDtlByScdMcd(String scdMcd);
}
