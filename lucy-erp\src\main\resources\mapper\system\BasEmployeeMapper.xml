<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasEmployeeMapper">
    
    <resultMap type="BasEmployee" id="BasEmployeeResult">
        <result property="id"    column="id"    />
        <result property="beNo"    column="be_no"    />
        <result property="beNm"    column="be_nm"    />
        <result property="beEnm"    column="be_enm"    />
        <result property="beMcd"    column="be_mcd"    />
        <result property="beDept"    column="be_dept"    />
        <result property="beProv"    column="be_prov"    />
        <result property="beTitl"    column="be_titl"    />
        <result property="beSex"    column="be_sex"    />
        <result property="beBd"    column="be_bd"    />
        <result property="beRd"    column="be_rd"    />
        <result property="beFp"    column="be_fp"    />
        <result property="beMp"    column="be_mp"    />
        <result property="beEmail"    column="be_email"    />
        <result property="beMng"    column="be_mng"    />
        <result property="beAss"    column="be_ass"    />
        <result property="beCrd"    column="be_crd"    />
        <result property="beCrdBud"    column="be_crd_bud"    />
        <result property="beQq"    column="be_qq"    />
        <result property="beOrgcd"    column="be_orgcd"    />
        <result property="beIsquit"    column="be_isquit"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectBasEmployeeVo">
        select id, be_no, be_nm, be_enm, be_mcd, be_dept, be_prov, be_titl, be_sex, be_bd, be_rd, be_fp, be_mp, be_email, be_mng, be_ass, be_crd, be_crd_bud, be_qq, be_orgcd, be_isquit, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from bas_employee
    </sql>

    <select id="selectBasEmployeeList" parameterType="BasEmployee" resultMap="BasEmployeeResult">
        <include refid="selectBasEmployeeVo"/>
        <where>  
            <if test="beNm != null  and beNm != ''"> and be_nm = #{beNm}</if>
            <if test="beEnm != null  and beEnm != ''"> and be_enm = #{beEnm}</if>
            <if test="beMcd != null  and beMcd != ''"> and be_mcd = #{beMcd}</if>
            <if test="beDept != null  and beDept != ''"> and be_dept = #{beDept}</if>
            <if test="beProv != null  and beProv != ''"> and be_prov = #{beProv}</if>
            <if test="beTitl != null  and beTitl != ''"> and be_titl = #{beTitl}</if>
            <if test="beSex != null  and beSex != ''"> and be_sex = #{beSex}</if>
            <if test="beBd != null  and beBd != ''"> and be_bd = #{beBd}</if>
            <if test="beRd != null  and beRd != ''"> and be_rd = #{beRd}</if>
            <if test="beFp != null  and beFp != ''"> and be_fp = #{beFp}</if>
            <if test="beMp != null  and beMp != ''"> and be_mp = #{beMp}</if>
            <if test="beEmail != null  and beEmail != ''"> and be_email = #{beEmail}</if>
            <if test="beMng != null  and beMng != ''"> and be_mng = #{beMng}</if>
            <if test="beAss != null  and beAss != ''"> and be_ass = #{beAss}</if>
            <if test="beCrd != null "> and be_crd = #{beCrd}</if>
            <if test="beCrdBud != null "> and be_crd_bud = #{beCrdBud}</if>
            <if test="beQq != null  and beQq != ''"> and be_qq = #{beQq}</if>
            <if test="beOrgcd != null  and beOrgcd != ''"> and be_orgcd = #{beOrgcd}</if>
            <if test="beIsquit != null  and beIsquit != ''"> and be_isquit = #{beIsquit}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
        </where>
    </select>
    
    <select id="selectBasEmployeeByBeNo" parameterType="String" resultMap="BasEmployeeResult">
        <include refid="selectBasEmployeeVo"/>
        where be_no = #{beNo}
    </select>

    <insert id="insertBasEmployee" parameterType="BasEmployee">
        insert into bas_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="beNo != null">be_no,</if>
            <if test="beNm != null">be_nm,</if>
            <if test="beEnm != null">be_enm,</if>
            <if test="beMcd != null">be_mcd,</if>
            <if test="beDept != null">be_dept,</if>
            <if test="beProv != null">be_prov,</if>
            <if test="beTitl != null">be_titl,</if>
            <if test="beSex != null">be_sex,</if>
            <if test="beBd != null">be_bd,</if>
            <if test="beRd != null">be_rd,</if>
            <if test="beFp != null">be_fp,</if>
            <if test="beMp != null">be_mp,</if>
            <if test="beEmail != null">be_email,</if>
            <if test="beMng != null">be_mng,</if>
            <if test="beAss != null">be_ass,</if>
            <if test="beCrd != null">be_crd,</if>
            <if test="beCrdBud != null">be_crd_bud,</if>
            <if test="beQq != null">be_qq,</if>
            <if test="beOrgcd != null">be_orgcd,</if>
            <if test="beIsquit != null">be_isquit,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="beNo != null">#{beNo},</if>
            <if test="beNm != null">#{beNm},</if>
            <if test="beEnm != null">#{beEnm},</if>
            <if test="beMcd != null">#{beMcd},</if>
            <if test="beDept != null">#{beDept},</if>
            <if test="beProv != null">#{beProv},</if>
            <if test="beTitl != null">#{beTitl},</if>
            <if test="beSex != null">#{beSex},</if>
            <if test="beBd != null">#{beBd},</if>
            <if test="beRd != null">#{beRd},</if>
            <if test="beFp != null">#{beFp},</if>
            <if test="beMp != null">#{beMp},</if>
            <if test="beEmail != null">#{beEmail},</if>
            <if test="beMng != null">#{beMng},</if>
            <if test="beAss != null">#{beAss},</if>
            <if test="beCrd != null">#{beCrd},</if>
            <if test="beCrdBud != null">#{beCrdBud},</if>
            <if test="beQq != null">#{beQq},</if>
            <if test="beOrgcd != null">#{beOrgcd},</if>
            <if test="beIsquit != null">#{beIsquit},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateBasEmployee" parameterType="BasEmployee">
        update bas_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="beNm != null">be_nm = #{beNm},</if>
            <if test="beEnm != null">be_enm = #{beEnm},</if>
            <if test="beMcd != null">be_mcd = #{beMcd},</if>
            <if test="beDept != null">be_dept = #{beDept},</if>
            <if test="beProv != null">be_prov = #{beProv},</if>
            <if test="beTitl != null">be_titl = #{beTitl},</if>
            <if test="beSex != null">be_sex = #{beSex},</if>
            <if test="beBd != null">be_bd = #{beBd},</if>
            <if test="beRd != null">be_rd = #{beRd},</if>
            <if test="beFp != null">be_fp = #{beFp},</if>
            <if test="beMp != null">be_mp = #{beMp},</if>
            <if test="beEmail != null">be_email = #{beEmail},</if>
            <if test="beMng != null">be_mng = #{beMng},</if>
            <if test="beAss != null">be_ass = #{beAss},</if>
            <if test="beCrd != null">be_crd = #{beCrd},</if>
            <if test="beCrdBud != null">be_crd_bud = #{beCrdBud},</if>
            <if test="beQq != null">be_qq = #{beQq},</if>
            <if test="beOrgcd != null">be_orgcd = #{beOrgcd},</if>
            <if test="beIsquit != null">be_isquit = #{beIsquit},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where be_no = #{beNo}
    </update>

    <delete id="deleteBasEmployeeByBeNo" parameterType="String">
        delete from bas_employee where be_no = #{beNo}
    </delete>

    <delete id="deleteBasEmployeeByBeNos" parameterType="String">
        delete from bas_employee where be_no in 
        <foreach item="beNo" collection="array" open="(" separator="," close=")">
            #{beNo}
        </foreach>
    </delete>
</mapper>