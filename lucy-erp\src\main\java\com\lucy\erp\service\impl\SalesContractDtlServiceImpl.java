package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.SalesContractDtlMapper;
import com.lucy.erp.domain.SalesContractDtl;
import com.lucy.erp.service.ISalesContractDtlService;

/**
 * 销售_合同_明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class SalesContractDtlServiceImpl implements ISalesContractDtlService
{
    @Autowired
    private SalesContractDtlMapper salesContractDtlMapper;

    /**
     * 查询销售_合同_明细
     * 
     * @param scdcd 销售_合同_明细主键
     * @return 销售_合同_明细
     */
    @Override
    public SalesContractDtl selectSalesContractDtlByScdcd(String scdcd)
    {
        return salesContractDtlMapper.selectSalesContractDtlByScdcd(scdcd);
    }

    /**
     * 查询销售_合同_明细列表
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 销售_合同_明细
     */
    @Override
    public List<SalesContractDtl> selectSalesContractDtlList(SalesContractDtl salesContractDtl)
    {
        return salesContractDtlMapper.selectSalesContractDtlList(salesContractDtl);
    }

    /**
     * 新增销售_合同_明细
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 结果
     */
    @Override
    public int insertSalesContractDtl(SalesContractDtl salesContractDtl)
    {
        return salesContractDtlMapper.insertSalesContractDtl(salesContractDtl);
    }

    /**
     * 修改销售_合同_明细
     * 
     * @param salesContractDtl 销售_合同_明细
     * @return 结果
     */
    @Override
    public int updateSalesContractDtl(SalesContractDtl salesContractDtl)
    {
        return salesContractDtlMapper.updateSalesContractDtl(salesContractDtl);
    }

    /**
     * 批量删除销售_合同_明细
     * 
     * @param scdMcds 需要删除的销售_合同_明细主键
     * @return 结果
     */
    @Override
    public int deleteSalesContractDtlByScdMcds(String[] scdMcds)
    {
        return salesContractDtlMapper.deleteSalesContractDtlByScdMcds(scdMcds);
    }

    /**
     * 删除销售_合同_明细信息
     * 
     * @param scdMcd 销售_合同_明细主键
     * @return 结果
     */
    @Override
    public int deleteSalesContractDtlByScdMcd(String scdMcd)
    {
        return salesContractDtlMapper.deleteSalesContractDtlByScdMcd(scdMcd);
    }
}
