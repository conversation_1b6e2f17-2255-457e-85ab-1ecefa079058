package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogRepackPackageUsePlanMapper;
import com.lucy.erp.domain.LogRepackPackageUsePlan;
import com.lucy.erp.service.ILogRepackPackageUsePlanService;

/**
 * 产成品包材领料主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogRepackPackageUsePlanServiceImpl implements ILogRepackPackageUsePlanService
{
    @Autowired
    private LogRepackPackageUsePlanMapper logRepackPackageUsePlanMapper;

    /**
     * 查询产成品包材领料主
     * 
     * @param lrpupCd 产成品包材领料主主键
     * @return 产成品包材领料主
     */
    @Override
    public LogRepackPackageUsePlan selectLogRepackPackageUsePlanByLrpupCd(String lrpupCd)
    {
        return logRepackPackageUsePlanMapper.selectLogRepackPackageUsePlanByLrpupCd(lrpupCd);
    }

    /**
     * 查询产成品包材领料主
     *
     * @param lrpup_psn 产成品包材领料主工单号
     * @return 产成品包材领料主
     */
    @Override
    public LogRepackPackageUsePlan selectLogRepackPackageUsePlanByLrpupPsn(String lrpup_psn)
    {
        return logRepackPackageUsePlanMapper.selectLogRepackPackageUsePlanByLrpupPsn(lrpup_psn);
    }

    /**
     * 查询产成品包材领料主列表
     * 
     * @param logRepackPackageUsePlan 产成品包材领料主
     * @return 产成品包材领料主
     */
    @Override
    public List<LogRepackPackageUsePlan> selectLogRepackPackageUsePlanList(LogRepackPackageUsePlan logRepackPackageUsePlan)
    {
        return logRepackPackageUsePlanMapper.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);
    }

    /**
     * 新增产成品包材领料主
     * 
     * @param logRepackPackageUsePlan 产成品包材领料主
     * @return 结果
     */
    @Override
    public int insertLogRepackPackageUsePlan(LogRepackPackageUsePlan logRepackPackageUsePlan)
    {
        return logRepackPackageUsePlanMapper.insertLogRepackPackageUsePlan(logRepackPackageUsePlan);
    }

    /**
     * 修改产成品包材领料主
     * 
     * @param logRepackPackageUsePlan 产成品包材领料主
     * @return 结果
     */
    @Override
    public int updateLogRepackPackageUsePlan(LogRepackPackageUsePlan logRepackPackageUsePlan)
    {
        return logRepackPackageUsePlanMapper.updateLogRepackPackageUsePlan(logRepackPackageUsePlan);
    }

    /**
     * 批量删除产成品包材领料主
     * 
     * @param lrpupCds 需要删除的产成品包材领料主主键
     * @return 结果
     */
    @Override
    public int deleteLogRepackPackageUsePlanByLrpupCds(String[] lrpupCds)
    {
        return logRepackPackageUsePlanMapper.deleteLogRepackPackageUsePlanByLrpupCds(lrpupCds);
    }

    /**
     * 删除产成品包材领料主信息
     * 
     * @param lrpupCd 产成品包材领料主主键
     * @return 结果
     */
    @Override
    public int deleteLogRepackPackageUsePlanByLrpupCd(String lrpupCd)
    {
        return logRepackPackageUsePlanMapper.deleteLogRepackPackageUsePlanByLrpupCd(lrpupCd);
    }
}
