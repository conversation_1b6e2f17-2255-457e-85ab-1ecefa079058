package com.ruoyi.repackage.service.impl;

import java.math.BigDecimal;
import java.util.List;

import com.lucy.erp.domain.BasProdPkg;
import com.lucy.erp.service.IBasProdPkgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionRuleDetailMapper;
import com.ruoyi.repackage.domain.ConversionRuleDetail;
import com.ruoyi.repackage.service.IConversionRuleDetailService;

/**
 * 分装规则明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionRuleDetailServiceImpl implements IConversionRuleDetailService 
{
    @Autowired
    private ConversionRuleDetailMapper conversionRuleDetailMapper;

    @Autowired
    private IBasProdPkgService basProdPkgService;

    /**
     * 查询分装规则明细
     * 
     * @param detailId 分装规则明细主键
     * @return 分装规则明细
     */
    @Override
    public ConversionRuleDetail selectConversionRuleDetailByDetailId(Long detailId)
    {
        return conversionRuleDetailMapper.selectConversionRuleDetailByDetailId(detailId);
    }

    /**
     * 查询分装规则明细列表
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 分装规则明细
     */
    @Override
    public List<ConversionRuleDetail> selectConversionRuleDetailList(ConversionRuleDetail conversionRuleDetail)
    {
        return conversionRuleDetailMapper.selectConversionRuleDetailList(conversionRuleDetail);
    }

    /**
     * 新增分装规则明细
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 结果
     */
    @Override
    public int insertConversionRuleDetail(ConversionRuleDetail conversionRuleDetail)
    {
        return conversionRuleDetailMapper.insertConversionRuleDetail(conversionRuleDetail);
    }

    /**
     * 修改分装规则明细
     * 
     * @param conversionRuleDetail 分装规则明细
     * @return 结果
     */
    @Override
    public int updateConversionRuleDetail(ConversionRuleDetail conversionRuleDetail)
    {
        return conversionRuleDetailMapper.updateConversionRuleDetail(conversionRuleDetail);
    }

    /**
     * 批量删除分装规则明细
     * 
     * @param detailIds 需要删除的分装规则明细主键
     * @return 结果
     */
    @Override
    public int deleteConversionRuleDetailByDetailIds(Long[] detailIds)
    {
        return conversionRuleDetailMapper.deleteConversionRuleDetailByDetailIds(detailIds);
    }

    /**
     * 删除分装规则明细信息
     * 
     * @param detailId 分装规则明细主键
     * @return 结果
     */
    @Override
    public int deleteConversionRuleDetailByDetailId(Long detailId)
    {
        return conversionRuleDetailMapper.deleteConversionRuleDetailByDetailId(detailId);
    }
    
    @Override
    public void addConversionRuleDetail(String sku_f, String sku_t, int qty_f, int qty_t, Long ruleId) {
        // 如果明细已存在则不添加
        ConversionRuleDetail detailQuery = new ConversionRuleDetail();
        detailQuery.setRuleId(ruleId);
        detailQuery.setMaterialSku(sku_f);
        detailQuery.setRole("INPUT");
        List<ConversionRuleDetail> existingDetails = conversionRuleDetailMapper.selectConversionRuleDetailList(detailQuery);
        if (!existingDetails.isEmpty()) {
            return;
        }
        BasProdPkg basProdPkgF = basProdPkgService.selectBasProdPkgByBppCd(sku_f);
        BasProdPkg basProdPkgT = basProdPkgService.selectBasProdPkgByBppCd(sku_t);
        ConversionRuleDetail conversionRuleDetailInput = new ConversionRuleDetail();
        conversionRuleDetailInput.setRuleId(ruleId);
        conversionRuleDetailInput.setRole("INPUT");
        conversionRuleDetailInput.setMaterialSku(sku_f);
        conversionRuleDetailInput.setQuantity(BigDecimal.valueOf(qty_f).negate());
        conversionRuleDetailInput.setUom(basProdPkgF.getBppStdUnit());
        conversionRuleDetailMapper.insertConversionRuleDetail(conversionRuleDetailInput);

        ConversionRuleDetail conversionRuleDetailOutput = new ConversionRuleDetail();
        conversionRuleDetailOutput.setRuleId(ruleId);
        conversionRuleDetailOutput.setRole("OUTPUT");
        conversionRuleDetailOutput.setMaterialSku(sku_t);
        conversionRuleDetailOutput.setQuantity(BigDecimal.valueOf(qty_t));
        conversionRuleDetailOutput.setUom(basProdPkgT.getBppStdUnit());
        conversionRuleDetailMapper.insertConversionRuleDetail(conversionRuleDetailOutput);
    }
}
