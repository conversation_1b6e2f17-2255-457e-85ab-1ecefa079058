<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasProdPkgMapper">
    
    <resultMap type="BasProdPkg" id="BasProdPkgResult">
        <result property="id"    column="id"    />
        <result property="bppPid"    column="bpp_pid"    />
        <result property="bppPcd"    column="bpp_pcd"    />
        <result property="bppCd"    column="bpp_cd"    />
        <result property="bppSet"    column="bpp_set"    />
        <result property="bppQty"    column="bpp_qty"    />
        <result property="bppUnit"    column="bpp_unit"    />
        <result property="bppPack"    column="bpp_pack"    />
        <result property="bppUninvoice"    column="bpp_uninvoice"    />
        <result property="bppPrc"    column="bpp_prc"    />
        <result property="bppPprc"    column="bpp_pprc"    />
        <result property="bppStdQty"    column="bpp_std_qty"    />
        <result property="bppStdUnit"    column="bpp_std_unit"    />
        <result property="bppRatio"    column="bpp_ratio"    />
        <result property="bppMtw"    column="bpp_mtw"    />
        <result property="bppPpd"    column="bpp_ppd"    />
        <result property="bppSupprc"    column="bpp_supprc"    />
        <result property="bppSupreb"    column="bpp_supreb"    />
        <result property="bppSyn"    column="bpp_syn"    />
        <result property="bppSts"    column="bpp_sts"    />
        <result property="bppNsts"    column="bpp_nsts"    />
        <result property="bppStsrmk"    column="bpp_stsrmk"    />
        <result property="bppSleeve"    column="bpp_sleeve"    />
        <result property="bppHkprc"    column="bpp_hkprc"    />
        <result property="bppHkpprc"    column="bpp_hkpprc"    />
        <result property="bppUsdprc"    column="bpp_USDprc"    />
        <result property="bppDm"    column="bpp_dm"    />
        <result property="bppSsts"    column="bpp_ssts"    />
        <result property="bppSstsrmk"    column="bpp_sstsrmk"    />
        <result property="bppLs"    column="bpp_ls"    />
        <result property="bppPkgStd"    column="bpp_pkg_std"    />
        <result property="bppWrap"    column="bpp_wrap"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="bppSupSkuCd"    column="bpp_sup_sku_cd"    />
        <result property="bppSuffix"    column="bpp_suffix"    />
        <result property="bppRemark"    column="bpp_remark"    />
        <result property="bppAutoShutdownTime"    column="bpp_auto_shutdown_time"    />
        <result property="bppCdReal"    column="bpp_cd_real"    />
        <result property="vpTime"    column="vp_time"    />
        <result property="vpPriceScale"    column="vp_price_scale"    />
        <result property="isReal"    column="is_real"    />
        <result property="skuSpec"    column="sku_spec"    />
        <result property="skuAttr"    column="sku_attr"    />
        <result property="bppIsPreferential"    column="bpp_is_preferential"    />
        <result property="bppIsexport"    column="bpp_isexport"    />
        <result property="bppPackEn"    column="bpp_pack_en"    />
        <result property="bppSpecAttr"    column="bpp_spec_attr"    />
    </resultMap>

    <sql id="selectBasProdPkgVo">
        select id, bpp_pid, bpp_pcd, bpp_cd, bpp_set, bpp_qty, bpp_unit, bpp_pack, bpp_uninvoice, bpp_prc, bpp_pprc, bpp_std_qty, bpp_std_unit, bpp_ratio, bpp_mtw, bpp_ppd, bpp_supprc, bpp_supreb, bpp_syn, bpp_sts, bpp_nsts, bpp_stsrmk, bpp_sleeve, bpp_hkprc, bpp_hkpprc, bpp_USDprc, bpp_dm, bpp_ssts, bpp_sstsrmk, bpp_ls, bpp_pkg_std, bpp_wrap, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, bpp_sup_sku_cd, bpp_suffix, bpp_remark, bpp_auto_shutdown_time, bpp_cd_real, vp_time, vp_price_scale, is_real, sku_spec, sku_attr, bpp_is_preferential, bpp_isexport, bpp_pack_en, bpp_spec_attr from bas_prod_pkg
    </sql>

    <select id="selectBasProdPkgList" parameterType="BasProdPkg" resultMap="BasProdPkgResult">
        <include refid="selectBasProdPkgVo"/>
        <where>  
            <if test="bppPid != null "> and bpp_pid = #{bppPid}</if>
            <if test="bppPcd != null  and bppPcd != ''"> and bpp_pcd = #{bppPcd}</if>
            <if test="bppSet != null "> and bpp_set = #{bppSet}</if>
            <if test="bppQty != null "> and bpp_qty = #{bppQty}</if>
            <if test="bppUnit != null  and bppUnit != ''"> and bpp_unit = #{bppUnit}</if>
            <if test="bppPack != null  and bppPack != ''"> and bpp_pack = #{bppPack}</if>
            <if test="bppUninvoice != null  and bppUninvoice != ''"> and bpp_uninvoice = #{bppUninvoice}</if>
            <if test="bppPrc != null "> and bpp_prc = #{bppPrc}</if>
            <if test="bppPprc != null "> and bpp_pprc = #{bppPprc}</if>
            <if test="bppStdQty != null "> and bpp_std_qty = #{bppStdQty}</if>
            <if test="bppStdUnit != null  and bppStdUnit != ''"> and bpp_std_unit = #{bppStdUnit}</if>
            <if test="bppRatio != null "> and bpp_ratio = #{bppRatio}</if>
            <if test="bppMtw != null "> and bpp_mtw = #{bppMtw}</if>
            <if test="bppPpd != null  and bppPpd != ''"> and bpp_ppd = #{bppPpd}</if>
            <if test="bppSupprc != null "> and bpp_supprc = #{bppSupprc}</if>
            <if test="bppSupreb != null "> and bpp_supreb = #{bppSupreb}</if>
            <if test="bppSyn != null  and bppSyn != ''"> and bpp_syn = #{bppSyn}</if>
            <if test="bppSts != null  and bppSts != ''"> and bpp_sts = #{bppSts}</if>
            <if test="bppNsts != null  and bppNsts != ''"> and bpp_nsts = #{bppNsts}</if>
            <if test="bppStsrmk != null  and bppStsrmk != ''"> and bpp_stsrmk = #{bppStsrmk}</if>
            <if test="bppSleeve != null  and bppSleeve != ''"> and bpp_sleeve = #{bppSleeve}</if>
            <if test="bppHkprc != null "> and bpp_hkprc = #{bppHkprc}</if>
            <if test="bppHkpprc != null "> and bpp_hkpprc = #{bppHkpprc}</if>
            <if test="bppUsdprc != null "> and bpp_USDprc = #{bppUsdprc}</if>
            <if test="bppDm != null  and bppDm != ''"> and bpp_dm = #{bppDm}</if>
            <if test="bppSsts != null  and bppSsts != ''"> and bpp_ssts = #{bppSsts}</if>
            <if test="bppSstsrmk != null  and bppSstsrmk != ''"> and bpp_sstsrmk = #{bppSstsrmk}</if>
            <if test="bppLs != null  and bppLs != ''"> and bpp_ls = #{bppLs}</if>
            <if test="bppPkgStd != null  and bppPkgStd != ''"> and bpp_pkg_std = #{bppPkgStd}</if>
            <if test="bppWrap != null  and bppWrap != ''"> and bpp_wrap = #{bppWrap}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="bppSupSkuCd != null  and bppSupSkuCd != ''"> and bpp_sup_sku_cd = #{bppSupSkuCd}</if>
            <if test="bppSuffix != null  and bppSuffix != ''"> and bpp_suffix = #{bppSuffix}</if>
            <if test="bppRemark != null  and bppRemark != ''"> and bpp_remark = #{bppRemark}</if>
            <if test="bppAutoShutdownTime != null "> and bpp_auto_shutdown_time = #{bppAutoShutdownTime}</if>
            <if test="bppCdReal != null  and bppCdReal != ''"> and bpp_cd_real = #{bppCdReal}</if>
            <if test="vpTime != null "> and vp_time = #{vpTime}</if>
            <if test="vpPriceScale != null "> and vp_price_scale = #{vpPriceScale}</if>
            <if test="isReal != null "> and is_real = #{isReal}</if>
            <if test="skuSpec != null  and skuSpec != ''"> and sku_spec = #{skuSpec}</if>
            <if test="skuAttr != null  and skuAttr != ''"> and sku_attr = #{skuAttr}</if>
            <if test="bppIsPreferential != null "> and bpp_is_preferential = #{bppIsPreferential}</if>
            <if test="bppIsexport != null  and bppIsexport != ''"> and bpp_isexport = #{bppIsexport}</if>
            <if test="bppPackEn != null  and bppPackEn != ''"> and bpp_pack_en = #{bppPackEn}</if>
            <if test="bppSpecAttr != null  and bppSpecAttr != ''"> and bpp_spec_attr = #{bppSpecAttr}</if>
        </where>
    </select>
    
    <select id="selectBasProdPkgByBppCd" parameterType="String" resultMap="BasProdPkgResult">
        <include refid="selectBasProdPkgVo"/>
        where bpp_cd = #{bppCd}
    </select>

    <insert id="insertBasProdPkg" parameterType="BasProdPkg">
        insert into bas_prod_pkg
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bppPid != null">bpp_pid,</if>
            <if test="bppPcd != null">bpp_pcd,</if>
            <if test="bppCd != null">bpp_cd,</if>
            <if test="bppSet != null">bpp_set,</if>
            <if test="bppQty != null">bpp_qty,</if>
            <if test="bppUnit != null">bpp_unit,</if>
            <if test="bppPack != null">bpp_pack,</if>
            <if test="bppUninvoice != null">bpp_uninvoice,</if>
            <if test="bppPrc != null">bpp_prc,</if>
            <if test="bppPprc != null">bpp_pprc,</if>
            <if test="bppStdQty != null">bpp_std_qty,</if>
            <if test="bppStdUnit != null">bpp_std_unit,</if>
            <if test="bppRatio != null">bpp_ratio,</if>
            <if test="bppMtw != null">bpp_mtw,</if>
            <if test="bppPpd != null">bpp_ppd,</if>
            <if test="bppSupprc != null">bpp_supprc,</if>
            <if test="bppSupreb != null">bpp_supreb,</if>
            <if test="bppSyn != null">bpp_syn,</if>
            <if test="bppSts != null">bpp_sts,</if>
            <if test="bppNsts != null">bpp_nsts,</if>
            <if test="bppStsrmk != null">bpp_stsrmk,</if>
            <if test="bppSleeve != null">bpp_sleeve,</if>
            <if test="bppHkprc != null">bpp_hkprc,</if>
            <if test="bppHkpprc != null">bpp_hkpprc,</if>
            <if test="bppUsdprc != null">bpp_USDprc,</if>
            <if test="bppDm != null">bpp_dm,</if>
            <if test="bppSsts != null">bpp_ssts,</if>
            <if test="bppSstsrmk != null">bpp_sstsrmk,</if>
            <if test="bppLs != null">bpp_ls,</if>
            <if test="bppPkgStd != null">bpp_pkg_std,</if>
            <if test="bppWrap != null">bpp_wrap,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="bppSupSkuCd != null">bpp_sup_sku_cd,</if>
            <if test="bppSuffix != null">bpp_suffix,</if>
            <if test="bppRemark != null">bpp_remark,</if>
            <if test="bppAutoShutdownTime != null">bpp_auto_shutdown_time,</if>
            <if test="bppCdReal != null">bpp_cd_real,</if>
            <if test="vpTime != null">vp_time,</if>
            <if test="vpPriceScale != null">vp_price_scale,</if>
            <if test="isReal != null">is_real,</if>
            <if test="skuSpec != null">sku_spec,</if>
            <if test="skuAttr != null">sku_attr,</if>
            <if test="bppIsPreferential != null">bpp_is_preferential,</if>
            <if test="bppIsexport != null">bpp_isexport,</if>
            <if test="bppPackEn != null">bpp_pack_en,</if>
            <if test="bppSpecAttr != null">bpp_spec_attr,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bppPid != null">#{bppPid},</if>
            <if test="bppPcd != null">#{bppPcd},</if>
            <if test="bppCd != null">#{bppCd},</if>
            <if test="bppSet != null">#{bppSet},</if>
            <if test="bppQty != null">#{bppQty},</if>
            <if test="bppUnit != null">#{bppUnit},</if>
            <if test="bppPack != null">#{bppPack},</if>
            <if test="bppUninvoice != null">#{bppUninvoice},</if>
            <if test="bppPrc != null">#{bppPrc},</if>
            <if test="bppPprc != null">#{bppPprc},</if>
            <if test="bppStdQty != null">#{bppStdQty},</if>
            <if test="bppStdUnit != null">#{bppStdUnit},</if>
            <if test="bppRatio != null">#{bppRatio},</if>
            <if test="bppMtw != null">#{bppMtw},</if>
            <if test="bppPpd != null">#{bppPpd},</if>
            <if test="bppSupprc != null">#{bppSupprc},</if>
            <if test="bppSupreb != null">#{bppSupreb},</if>
            <if test="bppSyn != null">#{bppSyn},</if>
            <if test="bppSts != null">#{bppSts},</if>
            <if test="bppNsts != null">#{bppNsts},</if>
            <if test="bppStsrmk != null">#{bppStsrmk},</if>
            <if test="bppSleeve != null">#{bppSleeve},</if>
            <if test="bppHkprc != null">#{bppHkprc},</if>
            <if test="bppHkpprc != null">#{bppHkpprc},</if>
            <if test="bppUsdprc != null">#{bppUsdprc},</if>
            <if test="bppDm != null">#{bppDm},</if>
            <if test="bppSsts != null">#{bppSsts},</if>
            <if test="bppSstsrmk != null">#{bppSstsrmk},</if>
            <if test="bppLs != null">#{bppLs},</if>
            <if test="bppPkgStd != null">#{bppPkgStd},</if>
            <if test="bppWrap != null">#{bppWrap},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="bppSupSkuCd != null">#{bppSupSkuCd},</if>
            <if test="bppSuffix != null">#{bppSuffix},</if>
            <if test="bppRemark != null">#{bppRemark},</if>
            <if test="bppAutoShutdownTime != null">#{bppAutoShutdownTime},</if>
            <if test="bppCdReal != null">#{bppCdReal},</if>
            <if test="vpTime != null">#{vpTime},</if>
            <if test="vpPriceScale != null">#{vpPriceScale},</if>
            <if test="isReal != null">#{isReal},</if>
            <if test="skuSpec != null">#{skuSpec},</if>
            <if test="skuAttr != null">#{skuAttr},</if>
            <if test="bppIsPreferential != null">#{bppIsPreferential},</if>
            <if test="bppIsexport != null">#{bppIsexport},</if>
            <if test="bppPackEn != null">#{bppPackEn},</if>
            <if test="bppSpecAttr != null">#{bppSpecAttr},</if>
         </trim>
    </insert>

    <update id="updateBasProdPkg" parameterType="BasProdPkg">
        update bas_prod_pkg
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="bppPid != null">bpp_pid = #{bppPid},</if>
            <if test="bppPcd != null">bpp_pcd = #{bppPcd},</if>
            <if test="bppSet != null">bpp_set = #{bppSet},</if>
            <if test="bppQty != null">bpp_qty = #{bppQty},</if>
            <if test="bppUnit != null">bpp_unit = #{bppUnit},</if>
            <if test="bppPack != null">bpp_pack = #{bppPack},</if>
            <if test="bppUninvoice != null">bpp_uninvoice = #{bppUninvoice},</if>
            <if test="bppPrc != null">bpp_prc = #{bppPrc},</if>
            <if test="bppPprc != null">bpp_pprc = #{bppPprc},</if>
            <if test="bppStdQty != null">bpp_std_qty = #{bppStdQty},</if>
            <if test="bppStdUnit != null">bpp_std_unit = #{bppStdUnit},</if>
            <if test="bppRatio != null">bpp_ratio = #{bppRatio},</if>
            <if test="bppMtw != null">bpp_mtw = #{bppMtw},</if>
            <if test="bppPpd != null">bpp_ppd = #{bppPpd},</if>
            <if test="bppSupprc != null">bpp_supprc = #{bppSupprc},</if>
            <if test="bppSupreb != null">bpp_supreb = #{bppSupreb},</if>
            <if test="bppSyn != null">bpp_syn = #{bppSyn},</if>
            <if test="bppSts != null">bpp_sts = #{bppSts},</if>
            <if test="bppNsts != null">bpp_nsts = #{bppNsts},</if>
            <if test="bppStsrmk != null">bpp_stsrmk = #{bppStsrmk},</if>
            <if test="bppSleeve != null">bpp_sleeve = #{bppSleeve},</if>
            <if test="bppHkprc != null">bpp_hkprc = #{bppHkprc},</if>
            <if test="bppHkpprc != null">bpp_hkpprc = #{bppHkpprc},</if>
            <if test="bppUsdprc != null">bpp_USDprc = #{bppUsdprc},</if>
            <if test="bppDm != null">bpp_dm = #{bppDm},</if>
            <if test="bppSsts != null">bpp_ssts = #{bppSsts},</if>
            <if test="bppSstsrmk != null">bpp_sstsrmk = #{bppSstsrmk},</if>
            <if test="bppLs != null">bpp_ls = #{bppLs},</if>
            <if test="bppPkgStd != null">bpp_pkg_std = #{bppPkgStd},</if>
            <if test="bppWrap != null">bpp_wrap = #{bppWrap},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="bppSupSkuCd != null">bpp_sup_sku_cd = #{bppSupSkuCd},</if>
            <if test="bppSuffix != null">bpp_suffix = #{bppSuffix},</if>
            <if test="bppRemark != null">bpp_remark = #{bppRemark},</if>
            <if test="bppAutoShutdownTime != null">bpp_auto_shutdown_time = #{bppAutoShutdownTime},</if>
            <if test="bppCdReal != null">bpp_cd_real = #{bppCdReal},</if>
            <if test="vpTime != null">vp_time = #{vpTime},</if>
            <if test="vpPriceScale != null">vp_price_scale = #{vpPriceScale},</if>
            <if test="isReal != null">is_real = #{isReal},</if>
            <if test="skuSpec != null">sku_spec = #{skuSpec},</if>
            <if test="skuAttr != null">sku_attr = #{skuAttr},</if>
            <if test="bppIsPreferential != null">bpp_is_preferential = #{bppIsPreferential},</if>
            <if test="bppIsexport != null">bpp_isexport = #{bppIsexport},</if>
            <if test="bppPackEn != null">bpp_pack_en = #{bppPackEn},</if>
            <if test="bppSpecAttr != null">bpp_spec_attr = #{bppSpecAttr},</if>
        </trim>
        where bpp_cd = #{bppCd}
    </update>

    <delete id="deleteBasProdPkgByBppCd" parameterType="String">
        delete from bas_prod_pkg where bpp_cd = #{bppCd}
    </delete>

    <delete id="deleteBasProdPkgByBppCds" parameterType="String">
        delete from bas_prod_pkg where bpp_cd in 
        <foreach item="bppCd" collection="array" open="(" separator="," close=")">
            #{bppCd}
        </foreach>
    </delete>
</mapper>