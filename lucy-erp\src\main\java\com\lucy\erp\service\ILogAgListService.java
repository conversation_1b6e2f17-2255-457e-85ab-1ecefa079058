package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogAgList;

/**
 * 物流_到货_列
Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ILogAgListService 
{
    /**
     * 查询物流_到货_列

     * 
     * @param lalCd 物流_到货_列
主键
     * @return 物流_到货_列

     */
    public LogAgList selectLogAgListByLalCd(String lalCd);

    /**
     * 查询物流_到货_列
列表
     * 
     * @param logAgList 物流_到货_列

     * @return 物流_到货_列
集合
     */
    public List<LogAgList> selectLogAgListList(LogAgList logAgList);

    /**
     * 新增物流_到货_列

     * 
     * @param logAgList 物流_到货_列

     * @return 结果
     */
    public int insertLogAgList(LogAgList logAgList);

    /**
     * 修改物流_到货_列

     * 
     * @param logAgList 物流_到货_列

     * @return 结果
     */
    public int updateLogAgList(LogAgList logAgList);

    /**
     * 批量删除物流_到货_列

     * 
     * @param lalCds 需要删除的物流_到货_列
主键集合
     * @return 结果
     */
    public int deleteLogAgListByLalCds(String[] lalCds);

    /**
     * 删除物流_到货_列
信息
     * 
     * @param lalCd 物流_到货_列
主键
     * @return 结果
     */
    public int deleteLogAgListByLalCd(String lalCd);
}
