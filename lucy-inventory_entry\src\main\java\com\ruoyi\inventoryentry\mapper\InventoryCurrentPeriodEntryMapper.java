package com.ruoyi.inventoryentry.mapper;

import java.util.List;
import com.ruoyi.inventoryentry.domain.InventoryCurrentPeriodEntry;

/**
 * 本期入库明细 Current period inventory entriesMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface InventoryCurrentPeriodEntryMapper 
{
    /**
     * 查询本期入库明细 Current period inventory entries
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 本期入库明细 Current period inventory entries
     */
    public InventoryCurrentPeriodEntry selectInventoryCurrentPeriodEntryById(Long id);

    /**
     * 查询本期入库明细 Current period inventory entries列表
     * 
     * @param inventoryCurrentPeriodEntry 本期入库明细 Current period inventory entries
     * @return 本期入库明细 Current period inventory entries集合
     */
    public List<InventoryCurrentPeriodEntry> selectInventoryCurrentPeriodEntryList(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry);

    /**
     * 批量新增本期入库明细 Current period inventory entries
     *
     * @param inventoryCurrentPeriodIssues 本期出库明细 Current period inventory issues列表
     * @return 结果
     */
    public int batchInsertInventoryCurrentPeriodIssue(List<InventoryCurrentPeriodEntry> inventoryCurrentPeriodIssues);

    /**
     * 新增本期入库明细 Current period inventory entries
     * 
     * @param inventoryCurrentPeriodEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    public int insertInventoryCurrentPeriodEntry(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry);

    /**
     * 修改本期入库明细 Current period inventory entries
     * 
     * @param inventoryCurrentPeriodEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    public int updateInventoryCurrentPeriodEntry(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry);

    /**
     * 删除本期入库明细 Current period inventory entries
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodEntryById(Long id);

    /**
     * 批量删除本期入库明细 Current period inventory entries
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodEntryByIds(Long[] ids);
}
