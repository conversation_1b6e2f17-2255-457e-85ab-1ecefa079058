<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasProdLineMapper">
    
    <resultMap type="BasProdLine" id="BasProdLineResult">
        <result property="id"    column="id"    />
        <result property="bplCd"    column="bpl_cd"    />
        <result property="bplPlineType"    column="bpl_pline_type"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectBasProdLineVo">
        select id, bpl_cd, bpl_pline_type, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_del from bas_prod_line
    </sql>

    <select id="selectBasProdLineList" parameterType="BasProdLine" resultMap="BasProdLineResult">
        <include refid="selectBasProdLineVo"/>
        <where>  
            <if test="bplCd != null  and bplCd != ''"> and bpl_cd = #{bplCd}</if>
            <if test="bplPlineType != null  and bplPlineType != ''"> and bpl_pline_type = #{bplPlineType}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rDel != null  and rDel != ''"> and r_del = #{rDel}</if>
        </where>
    </select>
    
    <select id="selectBasProdLineById" parameterType="Long" resultMap="BasProdLineResult">
        <include refid="selectBasProdLineVo"/>
        where id = #{id}
    </select>

    <insert id="insertBasProdLine" parameterType="BasProdLine" useGeneratedKeys="true" keyProperty="id">
        insert into bas_prod_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bplCd != null">bpl_cd,</if>
            <if test="bplPlineType != null">bpl_pline_type,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bplCd != null">#{bplCd},</if>
            <if test="bplPlineType != null">#{bplPlineType},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateBasProdLine" parameterType="BasProdLine">
        update bas_prod_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="bplCd != null">bpl_cd = #{bplCd},</if>
            <if test="bplPlineType != null">bpl_pline_type = #{bplPlineType},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBasProdLineById" parameterType="Long">
        delete from bas_prod_line where id = #{id}
    </delete>

    <delete id="deleteBasProdLineByIds" parameterType="String">
        delete from bas_prod_line where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>