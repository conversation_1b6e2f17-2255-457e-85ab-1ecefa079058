package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasSupMst;
import com.lucy.erp.service.IBasSupMstService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_供应商Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/system/bassup")
public class BasSupMstController extends BaseController
{
    @Autowired
    private IBasSupMstService basSupMstService;

    /**
     * 查询基础_供应商列表
     */
    @PreAuthorize("@ss.hasPermi('system:bassup:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasSupMst basSupMst)
    {
        startPage();
        List<BasSupMst> list = basSupMstService.selectBasSupMstList(basSupMst);
        return getDataTable(list);
    }

    /**
     * 导出基础_供应商列表
     */
    @PreAuthorize("@ss.hasPermi('system:bassup:export')")
    @Log(title = "基础_供应商", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasSupMst basSupMst)
    {
        List<BasSupMst> list = basSupMstService.selectBasSupMstList(basSupMst);
        ExcelUtil<BasSupMst> util = new ExcelUtil<BasSupMst>(BasSupMst.class);
        util.exportExcel(response, list, "基础_供应商数据");
    }

    /**
     * 获取基础_供应商详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bassup:query')")
    @GetMapping(value = "/{bsmCcd}")
    public AjaxResult getInfo(@PathVariable("bsmCcd") String bsmCcd)
    {
        return success(basSupMstService.selectBasSupMstByBsmCcd(bsmCcd));
    }

    /**
     * 新增基础_供应商
     */
    @PreAuthorize("@ss.hasPermi('system:bassup:add')")
    @Log(title = "基础_供应商", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasSupMst basSupMst)
    {
        return toAjax(basSupMstService.insertBasSupMst(basSupMst));
    }

    /**
     * 修改基础_供应商
     */
    @PreAuthorize("@ss.hasPermi('system:bassup:edit')")
    @Log(title = "基础_供应商", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasSupMst basSupMst)
    {
        return toAjax(basSupMstService.updateBasSupMst(basSupMst));
    }

    /**
     * 删除基础_供应商
     */
    @PreAuthorize("@ss.hasPermi('system:bassup:remove')")
    @Log(title = "基础_供应商", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bsmCcds}")
    public AjaxResult remove(@PathVariable String[] bsmCcds)
    {
        return toAjax(basSupMstService.deleteBasSupMstByBsmCcds(bsmCcds));
    }
}
