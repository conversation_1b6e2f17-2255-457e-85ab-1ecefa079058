package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasCustorgMstMapper;
import com.lucy.erp.domain.BasCustorgMst;
import com.lucy.erp.service.IBasCustorgMstService;

/**
 * 基础_客户组织_主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasCustorgMstServiceImpl implements IBasCustorgMstService
{
    @Autowired
    private BasCustorgMstMapper basCustorgMstMapper;

    /**
     * 查询基础_客户组织_主
     * 
     * @param bcmCd 基础_客户组织_主主键
     * @return 基础_客户组织_主
     */
    @Override
    public BasCustorgMst selectBasCustorgMstByBcmCd(String bcmCd)
    {
        return basCustorgMstMapper.selectBasCustorgMstByBcmCd(bcmCd);
    }

    /**
     * 查询基础_客户组织_主列表
     * 
     * @param basCustorgMst 基础_客户组织_主
     * @return 基础_客户组织_主
     */
    @Override
    public List<BasCustorgMst> selectBasCustorgMstList(BasCustorgMst basCustorgMst)
    {
        return basCustorgMstMapper.selectBasCustorgMstList(basCustorgMst);
    }

    /**
     * 新增基础_客户组织_主
     * 
     * @param basCustorgMst 基础_客户组织_主
     * @return 结果
     */
    @Override
    public int insertBasCustorgMst(BasCustorgMst basCustorgMst)
    {
        return basCustorgMstMapper.insertBasCustorgMst(basCustorgMst);
    }

    /**
     * 修改基础_客户组织_主
     * 
     * @param basCustorgMst 基础_客户组织_主
     * @return 结果
     */
    @Override
    public int updateBasCustorgMst(BasCustorgMst basCustorgMst)
    {
        return basCustorgMstMapper.updateBasCustorgMst(basCustorgMst);
    }

    /**
     * 批量删除基础_客户组织_主
     * 
     * @param bcmCds 需要删除的基础_客户组织_主主键
     * @return 结果
     */
    @Override
    public int deleteBasCustorgMstByBcmCds(String[] bcmCds)
    {
        return basCustorgMstMapper.deleteBasCustorgMstByBcmCds(bcmCds);
    }

    /**
     * 删除基础_客户组织_主信息
     * 
     * @param bcmCd 基础_客户组织_主主键
     * @return 结果
     */
    @Override
    public int deleteBasCustorgMstByBcmCd(String bcmCd)
    {
        return basCustorgMstMapper.deleteBasCustorgMstByBcmCd(bcmCd);
    }
}
