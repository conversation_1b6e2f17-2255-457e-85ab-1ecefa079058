<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasPlineTypeMapper">
    
    <resultMap type="BasPlineType" id="BasPlineTypeResult">
        <result property="id"    column="id"    />
        <result property="bptCd"    column="bpt_cd"    />
        <result property="bptNm"    column="bpt_nm"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectBasPlineTypeVo">
        select id, bpt_cd, bpt_nm, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_del from bas_pline_type
    </sql>

    <select id="selectBasPlineTypeList" parameterType="BasPlineType" resultMap="BasPlineTypeResult">
        <include refid="selectBasPlineTypeVo"/>
        <where>  
            <if test="bptCd != null  and bptCd != ''"> and bpt_cd = #{bptCd}</if>
            <if test="bptNm != null  and bptNm != ''"> and bpt_nm = #{bptNm}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rDel != null  and rDel != ''"> and r_del = #{rDel}</if>
        </where>
    </select>
    
    <select id="selectBasPlineTypeById" parameterType="Long" resultMap="BasPlineTypeResult">
        <include refid="selectBasPlineTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertBasPlineType" parameterType="BasPlineType" useGeneratedKeys="true" keyProperty="id">
        insert into bas_pline_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bptCd != null">bpt_cd,</if>
            <if test="bptNm != null">bpt_nm,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bptCd != null">#{bptCd},</if>
            <if test="bptNm != null">#{bptNm},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateBasPlineType" parameterType="BasPlineType">
        update bas_pline_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="bptCd != null">bpt_cd = #{bptCd},</if>
            <if test="bptNm != null">bpt_nm = #{bptNm},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBasPlineTypeById" parameterType="Long">
        delete from bas_pline_type where id = #{id}
    </delete>

    <delete id="deleteBasPlineTypeByIds" parameterType="String">
        delete from bas_pline_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>