package com.lucy.assetowner.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 资产所属主体主 Asset owner master data对象 asset_owner_master
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
public class AssetOwnerMaster extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID, Primary key */
    private Long id;

    /** 主体编码, Owner code */
    @Excel(name = "主体编码, Owner code")
    private String code;

    /** 主体名称, Owner name */
    @Excel(name = "主体名称, Owner name")
    private String name;

    /** 主体简称, Short name */
    @Excel(name = "主体简称, Short name")
    private String shortName;

    /** 主体类型(公司/部门/项目/其他), Owner type */
    @Excel(name = "主体类型(公司/部门/项目/其他), Owner type")
    private String type;

    /** 上级主体编码, Parent owner code */
    @Excel(name = "上级主体编码, Parent owner code")
    private String parentCode;

    /** 组织层级, Organization level */
    @Excel(name = "组织层级, Organization level")
    private Integer level;

    /** 组织路径(存储ID路径), Organization path */
    @Excel(name = "组织路径(存储ID路径), Organization path")
    private String path;

    /** 成本中心代码, Cost center code */
    @Excel(name = "成本中心代码, Cost center code")
    private String costCenter;

    /** 利润中心代码, Profit center code */
    @Excel(name = "利润中心代码, Profit center code")
    private String profitCenter;

    /** 税号, Tax identification number */
    @Excel(name = "税号, Tax identification number")
    private String taxNumber;

    /** 状态(活跃/停用/暂停), Status */
    @Excel(name = "状态(活跃/停用/暂停), Status")
    private String status;

    /** 生效日期, Effective date */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期, Effective date", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 失效日期, Expiration date */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期, Expiration date", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 创建人, Created by */
    @Excel(name = "创建人, Created by")
    private String createdBy;

    /** 创建时间, Creation time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间, Creation time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdTime;

    /** 更新人, Updated by */
    @Excel(name = "更新人, Updated by")
    private String updatedBy;

    /** 更新时间, Update time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间, Update time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedTime;

    /** 行版本, Row version */
    @Excel(name = "行版本, Row version")
    private Integer version;

    /** 删除标记(0:正常 1:删除), Deletion flag */
    @Excel(name = "删除标记(0:正常 1:删除), Deletion flag")
    private Integer isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setShortName(String shortName) 
    {
        this.shortName = shortName;
    }

    public String getShortName() 
    {
        return shortName;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setParentCode(String parentCode) 
    {
        this.parentCode = parentCode;
    }

    public String getParentCode() 
    {
        return parentCode;
    }

    public void setLevel(Integer level) 
    {
        this.level = level;
    }

    public Integer getLevel() 
    {
        return level;
    }

    public void setPath(String path) 
    {
        this.path = path;
    }

    public String getPath() 
    {
        return path;
    }

    public void setCostCenter(String costCenter) 
    {
        this.costCenter = costCenter;
    }

    public String getCostCenter() 
    {
        return costCenter;
    }

    public void setProfitCenter(String profitCenter) 
    {
        this.profitCenter = profitCenter;
    }

    public String getProfitCenter() 
    {
        return profitCenter;
    }

    public void setTaxNumber(String taxNumber) 
    {
        this.taxNumber = taxNumber;
    }

    public String getTaxNumber() 
    {
        return taxNumber;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    public Date getStartDate() 
    {
        return startDate;
    }

    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }

    public void setCreatedBy(String createdBy) 
    {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() 
    {
        return createdBy;
    }

    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }

    public void setUpdatedBy(String updatedBy) 
    {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() 
    {
        return updatedBy;
    }

    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }

    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("name", getName())
            .append("shortName", getShortName())
            .append("type", getType())
            .append("parentCode", getParentCode())
            .append("level", getLevel())
            .append("path", getPath())
            .append("costCenter", getCostCenter())
            .append("profitCenter", getProfitCenter())
            .append("taxNumber", getTaxNumber())
            .append("status", getStatus())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("createdBy", getCreatedBy())
            .append("createdTime", getCreatedTime())
            .append("updatedBy", getUpdatedBy())
            .append("updatedTime", getUpdatedTime())
            .append("version", getVersion())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
