package com.ruoyi.repackage.domain;

import java.util.*;
import java.util.stream.Collectors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 环状依赖记录对象 conversion_dependency_cycle
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class ConversionDependencyCycle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 环ID */
    private Long cycleId;

    /** 环路径（SKU链，如"A>B>C>A"） */
    @Excel(name = "环路径", readConverterExp = "SKU链，如A>B>C>A")
    private String cyclePath;

    /** 涉及物料清单（用于锁定） */
    @Excel(name = "涉及物料清单", readConverterExp = "用于锁定")
    private String materialList;

    /** 处理状态 */
    @Excel(name = "处理状态")
    private String status;

    /** 解决方法 */
    @Excel(name = "解决方法")
    private String resolvedMethod;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date detectedAt;

    /** 解决时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "解决时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date resolvedAt;

    public void setCycleId(Long cycleId) 
    {
        this.cycleId = cycleId;
    }

    public Long getCycleId() 
    {
        return cycleId;
    }

    public void setCyclePath(String cyclePath) 
    {
        this.cyclePath = cyclePath;
    }

    public String getCyclePath() 
    {
        return cyclePath;
    }

    public void setMaterialList(String materialList) 
    {
        this.materialList = materialList;
    }

    public String getMaterialList() 
    {
        return materialList;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setResolvedMethod(String resolvedMethod) 
    {
        this.resolvedMethod = resolvedMethod;
    }

    public String getResolvedMethod() 
    {
        return resolvedMethod;
    }

    public void setDetectedAt(Date detectedAt) 
    {
        this.detectedAt = detectedAt;
    }

    public Date getDetectedAt() 
    {
        return detectedAt;
    }

    public void setResolvedAt(Date resolvedAt) 
    {
        this.resolvedAt = resolvedAt;
    }

    public Date getResolvedAt() 
    {
        return resolvedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("cycleId", getCycleId())
            .append("cyclePath", getCyclePath())
            .append("materialList", getMaterialList())
            .append("status", getStatus())
            .append("resolvedMethod", getResolvedMethod())
            .append("detectedAt", getDetectedAt())
            .append("resolvedAt", getResolvedAt())
            .toString();
    }
}
