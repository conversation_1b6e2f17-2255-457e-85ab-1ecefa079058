package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 研发领用明细对象 lab_picking_dtl
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class LabPickingDtl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 领料编号 */
    @Excel(name = "领料编号")
    private String lpdMcd;

    /** 库位编号 */
    @Excel(name = "库位编号")
    private String lpdSlcd;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String lpdPpcd;

    /** 批号 */
    @Excel(name = "批号")
    private String lpdBn;

    /** 加密批号 */
    @Excel(name = "加密批号")
    private String lpdBnEncrypt;

    /** 领料数量 */
    @Excel(name = "领料数量")
    private BigDecimal lpdQty;

    /** 用料数量 */
    @Excel(name = "用料数量")
    private BigDecimal lpdUsedQty;

    /** 领料单价 */
    @Excel(name = "领料单价")
    private BigDecimal lpdUnitp;

    /** 领料总金额 */
    @Excel(name = "领料总金额")
    private BigDecimal lpdTotalPrc;

    /** 资产所属组织编号 */
    @Excel(name = "资产所属组织编号")
    private String lpdOrgmcd;

    /** 仓库编号 */
    @Excel(name = "仓库编号")
    private String lpdDmcd;

    /** 库位类别 */
    @Excel(name = "库位类别")
    private String lpdTcd;

    /** 记账标记 */
    @Excel(name = "记账标记")
    private String lpdFinFlag;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long rV;

    /** 删除标志 */
    @Excel(name = "删除标志")
    private Long rDel;

    /** 状态（00 草稿，01以确认） */
    @Excel(name = "状态", readConverterExp = "0=0,草=稿，01以确认")
    private String lpdSts;

    /** 领料明细编号 */
    private String lpdCd;

    /** 实际领料人 */
    @Excel(name = "实际领料人")
    private String lpdNm;

    /** 领用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领用时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lpdCollectionTime;

    /** 项目编号 */
    @Excel(name = "项目编号")
    private String lpdPcd;

    /** 领料退库编号 */
    @Excel(name = "领料退库编号")
    private String lpdRcd;

    /** 岩峰RD系统库存事务ID */
    @Excel(name = "岩峰RD系统库存事务ID")
    private Long lpdRdId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLpdMcd(String lpdMcd) 
    {
        this.lpdMcd = lpdMcd;
    }

    public String getLpdMcd() 
    {
        return lpdMcd;
    }

    public void setLpdSlcd(String lpdSlcd) 
    {
        this.lpdSlcd = lpdSlcd;
    }

    public String getLpdSlcd() 
    {
        return lpdSlcd;
    }

    public void setLpdPpcd(String lpdPpcd) 
    {
        this.lpdPpcd = lpdPpcd;
    }

    public String getLpdPpcd() 
    {
        return lpdPpcd;
    }

    public void setLpdBn(String lpdBn) 
    {
        this.lpdBn = lpdBn;
    }

    public String getLpdBn() 
    {
        return lpdBn;
    }

    public void setLpdBnEncrypt(String lpdBnEncrypt) 
    {
        this.lpdBnEncrypt = lpdBnEncrypt;
    }

    public String getLpdBnEncrypt() 
    {
        return lpdBnEncrypt;
    }

    public void setLpdQty(BigDecimal lpdQty) 
    {
        this.lpdQty = lpdQty;
    }

    public BigDecimal getLpdQty() 
    {
        return lpdQty;
    }

    public void setLpdUsedQty(BigDecimal lpdUsedQty) 
    {
        this.lpdUsedQty = lpdUsedQty;
    }

    public BigDecimal getLpdUsedQty() 
    {
        return lpdUsedQty;
    }

    public void setLpdUnitp(BigDecimal lpdUnitp) 
    {
        this.lpdUnitp = lpdUnitp;
    }

    public BigDecimal getLpdUnitp() 
    {
        return lpdUnitp;
    }

    public void setLpdTotalPrc(BigDecimal lpdTotalPrc) 
    {
        this.lpdTotalPrc = lpdTotalPrc;
    }

    public BigDecimal getLpdTotalPrc() 
    {
        return lpdTotalPrc;
    }

    public void setLpdOrgmcd(String lpdOrgmcd) 
    {
        this.lpdOrgmcd = lpdOrgmcd;
    }

    public String getLpdOrgmcd() 
    {
        return lpdOrgmcd;
    }

    public void setLpdDmcd(String lpdDmcd) 
    {
        this.lpdDmcd = lpdDmcd;
    }

    public String getLpdDmcd() 
    {
        return lpdDmcd;
    }

    public void setLpdTcd(String lpdTcd) 
    {
        this.lpdTcd = lpdTcd;
    }

    public String getLpdTcd() 
    {
        return lpdTcd;
    }

    public void setLpdFinFlag(String lpdFinFlag) 
    {
        this.lpdFinFlag = lpdFinFlag;
    }

    public String getLpdFinFlag() 
    {
        return lpdFinFlag;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setLpdSts(String lpdSts) 
    {
        this.lpdSts = lpdSts;
    }

    public String getLpdSts() 
    {
        return lpdSts;
    }

    public void setLpdCd(String lpdCd) 
    {
        this.lpdCd = lpdCd;
    }

    public String getLpdCd() 
    {
        return lpdCd;
    }

    public void setLpdNm(String lpdNm) 
    {
        this.lpdNm = lpdNm;
    }

    public String getLpdNm() 
    {
        return lpdNm;
    }

    public void setLpdCollectionTime(Date lpdCollectionTime) 
    {
        this.lpdCollectionTime = lpdCollectionTime;
    }

    public Date getLpdCollectionTime() 
    {
        return lpdCollectionTime;
    }

    public void setLpdPcd(String lpdPcd) 
    {
        this.lpdPcd = lpdPcd;
    }

    public String getLpdPcd() 
    {
        return lpdPcd;
    }

    public void setLpdRcd(String lpdRcd) 
    {
        this.lpdRcd = lpdRcd;
    }

    public String getLpdRcd() 
    {
        return lpdRcd;
    }

    public void setLpdRdId(Long lpdRdId) 
    {
        this.lpdRdId = lpdRdId;
    }

    public Long getLpdRdId() 
    {
        return lpdRdId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lpdMcd", getLpdMcd())
            .append("lpdSlcd", getLpdSlcd())
            .append("lpdPpcd", getLpdPpcd())
            .append("lpdBn", getLpdBn())
            .append("lpdBnEncrypt", getLpdBnEncrypt())
            .append("lpdQty", getLpdQty())
            .append("lpdUsedQty", getLpdUsedQty())
            .append("lpdUnitp", getLpdUnitp())
            .append("lpdTotalPrc", getLpdTotalPrc())
            .append("lpdOrgmcd", getLpdOrgmcd())
            .append("lpdDmcd", getLpdDmcd())
            .append("lpdTcd", getLpdTcd())
            .append("lpdFinFlag", getLpdFinFlag())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("lpdSts", getLpdSts())
            .append("lpdCd", getLpdCd())
            .append("lpdNm", getLpdNm())
            .append("lpdCollectionTime", getLpdCollectionTime())
            .append("lpdPcd", getLpdPcd())
            .append("lpdRcd", getLpdRcd())
            .append("lpdRdId", getLpdRdId())
            .toString();
    }
}
