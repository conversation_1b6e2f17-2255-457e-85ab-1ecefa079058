package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 原材料分装及退回原材料单据对象 fin_acc_inventory_repack
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public class FinAccInventoryRepack extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 账套ID */
    @Excel(name = "账套ID")
    private String fairId;

    /** 发生日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发生日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fairAccurTime;

    /** 制单人 */
    @Excel(name = "制单人")
    private String fairCreater;

    /** 审核人 */
    @Excel(name = "审核人")
    private String fairApprover;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fairApproTime;

    /** 创建人 */
    @Excel(name = "创建人")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 单据类型 */
    @Excel(name = "单据类型")
    private String fairType;

    /** 物料名称f */
    @Excel(name = "物料名称f")
    private String fairSubjectF;

    /** 物料名称t */
    @Excel(name = "物料名称t")
    private String fairSubjectT;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal fairAmt;

    /** 原采购金额 */
    @Excel(name = "原采购金额")
    private BigDecimal fairOrgAmt;

    /** 分装工单单号 */
    @Excel(name = "分装工单单号")
    private String fairRepackPsn;

    /** 原材料还原工单号 */
    @Excel(name = "原材料还原工单号")
    private String fairReRepackPsn;

    /** 库存事务id_f */
    @Excel(name = "库存事务id_f")
    private String fairLiotIdF;

    /** 库存事务id_t */
    @Excel(name = "库存事务id_t")
    private String fairLiotIdT;

    /** 原材料数量 */
    @Excel(name = "原材料数量")
    private BigDecimal fairQtyF;

    /** 产出数量 */
    @Excel(name = "产出数量")
    private BigDecimal fairQtyT;

    /** sku_f */
    @Excel(name = "sku_f")
    private String skuF;

    /** sku_t */
    @Excel(name = "sku_t")
    private String skuT;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setFairId(String fairId) 
    {
        this.fairId = fairId;
    }

    public String getFairId() 
    {
        return fairId;
    }

    public void setFairAccurTime(Date fairAccurTime) 
    {
        this.fairAccurTime = fairAccurTime;
    }

    public Date getFairAccurTime() 
    {
        return fairAccurTime;
    }

    public void setFairCreater(String fairCreater) 
    {
        this.fairCreater = fairCreater;
    }

    public String getFairCreater() 
    {
        return fairCreater;
    }

    public void setFairApprover(String fairApprover) 
    {
        this.fairApprover = fairApprover;
    }

    public String getFairApprover() 
    {
        return fairApprover;
    }

    public void setFairApproTime(Date fairApproTime) 
    {
        this.fairApproTime = fairApproTime;
    }

    public Date getFairApproTime() 
    {
        return fairApproTime;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setFairType(String fairType) 
    {
        this.fairType = fairType;
    }

    public String getFairType() 
    {
        return fairType;
    }

    public void setFairSubjectF(String fairSubjectF) 
    {
        this.fairSubjectF = fairSubjectF;
    }

    public String getFairSubjectF() 
    {
        return fairSubjectF;
    }

    public void setFairSubjectT(String fairSubjectT) 
    {
        this.fairSubjectT = fairSubjectT;
    }

    public String getFairSubjectT() 
    {
        return fairSubjectT;
    }

    public void setFairAmt(BigDecimal fairAmt) 
    {
        this.fairAmt = fairAmt;
    }

    public BigDecimal getFairAmt() 
    {
        return fairAmt;
    }

    public void setFairOrgAmt(BigDecimal fairOrgAmt) 
    {
        this.fairOrgAmt = fairOrgAmt;
    }

    public BigDecimal getFairOrgAmt() 
    {
        return fairOrgAmt;
    }

    public void setFairRepackPsn(String fairRepackPsn) 
    {
        this.fairRepackPsn = fairRepackPsn;
    }

    public String getFairRepackPsn() 
    {
        return fairRepackPsn;
    }

    public void setFairReRepackPsn(String fairReRepackPsn) 
    {
        this.fairReRepackPsn = fairReRepackPsn;
    }

    public String getFairReRepackPsn() 
    {
        return fairReRepackPsn;
    }

    public void setFairLiotIdF(String fairLiotIdF) 
    {
        this.fairLiotIdF = fairLiotIdF;
    }

    public String getFairLiotIdF() 
    {
        return fairLiotIdF;
    }

    public void setFairLiotIdT(String fairLiotIdT) 
    {
        this.fairLiotIdT = fairLiotIdT;
    }

    public String getFairLiotIdT() 
    {
        return fairLiotIdT;
    }

    public void setFairQtyF(BigDecimal fairQtyF) 
    {
        this.fairQtyF = fairQtyF;
    }

    public BigDecimal getFairQtyF() 
    {
        return fairQtyF;
    }

    public void setFairQtyT(BigDecimal fairQtyT) 
    {
        this.fairQtyT = fairQtyT;
    }

    public BigDecimal getFairQtyT() 
    {
        return fairQtyT;
    }

    public void setSkuF(String skuF) 
    {
        this.skuF = skuF;
    }

    public String getSkuF() 
    {
        return skuF;
    }

    public void setSkuT(String skuT) 
    {
        this.skuT = skuT;
    }

    public String getSkuT() 
    {
        return skuT;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("fairId", getFairId())
            .append("fairAccurTime", getFairAccurTime())
            .append("fairCreater", getFairCreater())
            .append("fairApprover", getFairApprover())
            .append("fairApproTime", getFairApproTime())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("fairType", getFairType())
            .append("fairSubjectF", getFairSubjectF())
            .append("fairSubjectT", getFairSubjectT())
            .append("fairAmt", getFairAmt())
            .append("fairOrgAmt", getFairOrgAmt())
            .append("fairRepackPsn", getFairRepackPsn())
            .append("fairReRepackPsn", getFairReRepackPsn())
            .append("fairLiotIdF", getFairLiotIdF())
            .append("fairLiotIdT", getFairLiotIdT())
            .append("fairQtyF", getFairQtyF())
            .append("fairQtyT", getFairQtyT())
            .append("skuF", getSkuF())
            .append("skuT", getSkuT())
            .toString();
    }
}
