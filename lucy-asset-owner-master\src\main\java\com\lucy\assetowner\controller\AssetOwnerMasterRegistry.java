package com.lucy.assetowner.controller;

 import com.lucy.assetowner.domain.AssetOwner;
 import com.lucy.assetowner.service.IAssetOwnerMasterService;
 import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
 import org.springframework.scheduling.annotation.Scheduled;
 import org.springframework.stereotype.Component;


/**
 * 资产所属主体主 Asset owner master registry
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Component
public class AssetOwnerMasterRegistry implements ApplicationRunner {

    private final IAssetOwnerMasterService assetOwnerMasterService;

    public AssetOwnerMasterRegistry(IAssetOwnerMasterService assetOwnerMasterService) {
        this.assetOwnerMasterService = assetOwnerMasterService;
    }

    @Scheduled(fixedRate = 86400000) // 每天刷新一次
    @Override
    public void run(ApplicationArguments args) throws Exception {
        assetOwnerMasterService.loadData();
        AssetOwner.init(assetOwnerMasterService);
    }
}
