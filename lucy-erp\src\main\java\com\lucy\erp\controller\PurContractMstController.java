package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.PurContractMst;
import com.lucy.erp.service.IPurContractMstService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采购_合同_主
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/system/purmst")
public class PurContractMstController extends BaseController
{
    @Autowired
    private IPurContractMstService purContractMstService;

    /**
     * 查询采购_合同_主
列表
     */
    @PreAuthorize("@ss.hasPermi('system:purmst:list')")
    @GetMapping("/list")
    public TableDataInfo list(PurContractMst purContractMst)
    {
        startPage();
        List<PurContractMst> list = purContractMstService.selectPurContractMstList(purContractMst);
        return getDataTable(list);
    }

    /**
     * 导出采购_合同_主
列表
     */
    @PreAuthorize("@ss.hasPermi('system:purmst:export')")
    @Log(title = "采购_合同_主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PurContractMst purContractMst)
    {
        List<PurContractMst> list = purContractMstService.selectPurContractMstList(purContractMst);
        ExcelUtil<PurContractMst> util = new ExcelUtil<PurContractMst>(PurContractMst.class);
        util.exportExcel(response, list, "采购_合同_主数据");
    }

    /**
     * 获取采购_合同_主
详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:purmst:query')")
    @GetMapping(value = "/{pcmCd}")
    public AjaxResult getInfo(@PathVariable("pcmCd") String pcmCd)
    {
        return success(purContractMstService.selectPurContractMstByPcmCd(pcmCd));
    }

    /**
     * 新增采购_合同_主

     */
    @PreAuthorize("@ss.hasPermi('system:purmst:add')")
    @Log(title = "采购_合同_主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PurContractMst purContractMst)
    {
        return toAjax(purContractMstService.insertPurContractMst(purContractMst));
    }

    /**
     * 修改采购_合同_主

     */
    @PreAuthorize("@ss.hasPermi('system:purmst:edit')")
    @Log(title = "采购_合同_主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PurContractMst purContractMst)
    {
        return toAjax(purContractMstService.updatePurContractMst(purContractMst));
    }

    /**
     * 删除采购_合同_主

     */
    @PreAuthorize("@ss.hasPermi('system:purmst:remove')")
    @Log(title = "采购_合同_主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{pcmCds}")
    public AjaxResult remove(@PathVariable String[] pcmCds)
    {
        return toAjax(purContractMstService.deletePurContractMstByPcmCds(pcmCds));
    }
}
