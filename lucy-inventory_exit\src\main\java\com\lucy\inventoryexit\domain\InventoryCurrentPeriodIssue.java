package com.lucy.inventoryexit.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 本期出库明细 Current period inventory issues对象 inventory_current_period_issue
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class InventoryCurrentPeriodIssue extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID, Primary key */
    private Long id;

    /** 本期出库年月(YYYY-MM), Accounting period */
    @Excel(name = "本期出库年月(YYYY-MM), Accounting period")
    private String accountingPeriod;

    /** 财务科目代码, Financial account code */
    @Excel(name = "财务科目代码, Financial account code")
    private String financialAccount;

    /** SKU编码, Stock Keeping Unit */
    @Excel(name = "SKU编码, Stock Keeping Unit")
    private String sku;

    /** 出库数量, Issue quantity */
    @Excel(name = "出库数量, Issue quantity")
    private BigDecimal quantity;

    /** 出库金额, Issue amount */
    @Excel(name = "出库金额, Issue amount")
    private BigDecimal amount;

    /** 外采/自产, Source type */
    @Excel(name = "外采/自产, Source type")
    private String sourceType;

    /** 国内/国外采购, Purchase scope */
    @Excel(name = "国内/国外采购, Purchase scope")
    private String purchaseScope;

    /** 客户编号, Customer code */
    @Excel(name = "客户编号, Customer code")
    private String customerCode;

    /** 客户名称, Customer name */
    @Excel(name = "客户名称, Customer name")
    private String customerName;

    /** 部门组编号, Department group code */
    @Excel(name = "部门组编号, Department group code")
    private String departmentGroupCode;

    /** 部门组名称, Department group name */
    @Excel(name = "部门组名称, Department group name")
    private String departmentGroupName;

    /** 公司编号, company group code */
    @Excel(name = "公司编号, company group code")
    private String companyGroupCode;

    /** 公司名称, company group name */
    @Excel(name = "公司名称, company group name")
    private String companyGroupName;

    /** 批次号, Batch number */
    @Excel(name = "批次号, Batch number")
    private String batchNumber;

    /** 仓库代码, Warehouse code */
    @Excel(name = "仓库代码, Warehouse code")
    private String warehouse;

    /** 库位代码, Storage location */
    @Excel(name = "库位代码, Storage location")
    private String storageLocation;

    /** 库区代码, Storage zone */
    @Excel(name = "库区代码, Storage zone")
    private String storageZone;

    /** 来源数据表名, Source table name */
    @Excel(name = "来源数据表名, Source table name")
    private String sourceTable;

    /** 来源记录ID, Source record ID */
    @Excel(name = "来源记录ID, Source record ID")
    private Long sourceRecordId;

    /** 出库时间, Issue timestamp */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出库时间, Issue timestamp", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issueTime;

    /** 出库人, Operator */
    @Excel(name = "出库人, Operator")
    private String operator;

    /** 审核人, Auditor */
    @Excel(name = "审核人, Auditor")
    private String auditor;

    /** 审核时间, Audit timestamp */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间, Audit timestamp", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 加密批次, Encrypted batch */
    @Excel(name = "加密批次, Encrypted batch")
    private String encryptedBatch;

    /** 单据类型, Document type */
    @Excel(name = "单据类型, Document type")
    private String documentType;

    /** 资产所属主体, Asset owner */
    @Excel(name = "资产所属主体, Asset owner")
    private String assetOwner;

    /** 经办人, Processor */
    @Excel(name = "经办人, Processor")
    private String processor;

    /** 经办时间, Process time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "经办时间, Process time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date processTime;

    /** 经办部门, Department */
    @Excel(name = "经办部门, Department")
    private String department;

    /** 部门审核人, Department auditor */
    @Excel(name = "部门审核人, Department auditor")
    private String departmentAuditor;

    /** 部门审核时间, Department audit time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "部门审核时间, Department audit time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date departmentAuditTime;

    /** 结转成当月费用标志, Expense transfer flag */
    @Excel(name = "结转成当月费用标志, Expense transfer flag")
    private String expenseTransferFlag;

    /** Site点代码, Site code */
    @Excel(name = "Site点代码, Site code")
    private String siteCode;

    /** 项目编号, Project code */
    @Excel(name = "项目编号, Project code")
    private String projectCode;

    /** 项目名称, Project name */
    @Excel(name = "项目名称, Project name")
    private String projectName;

    /** RD系统ID, RD system ID */
    @Excel(name = "RD系统ID, RD system ID")
    private String rdSystemId;

    /** 创建人, Created by */
    @Excel(name = "创建人, Created by")
    private String createdBy;

    /** 创建时间, Creation time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间, Creation time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdTime;

    /** 更新人, Updated by */
    @Excel(name = "更新人, Updated by")
    private String updatedBy;

    /** 更新时间, Update time */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间, Update time", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedTime;

    /** 行版本, Row version */
    @Excel(name = "行版本, Row version")
    private Integer version;

    /** 删除标记(0:正常 1:删除), Deletion flag */
    @Excel(name = "删除标记(0:正常 1:删除), Deletion flag")
    private Integer isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAccountingPeriod(String accountingPeriod) 
    {
        this.accountingPeriod = accountingPeriod;
    }

    public String getAccountingPeriod() 
    {
        return accountingPeriod;
    }

    public void setFinancialAccount(String financialAccount) 
    {
        this.financialAccount = financialAccount;
    }

    public String getFinancialAccount() 
    {
        return financialAccount;
    }

    public void setSku(String sku) 
    {
        this.sku = sku;
    }

    public String getSku() 
    {
        return sku;
    }

    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }

    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }

    public void setSourceType(String sourceType) 
    {
        this.sourceType = sourceType;
    }

    public String getSourceType() 
    {
        return sourceType;
    }

    public void setPurchaseScope(String purchaseScope) 
    {
        this.purchaseScope = purchaseScope;
    }

    public String getPurchaseScope() 
    {
        return purchaseScope;
    }

    public void setCustomerCode(String customerCode) 
    {
        this.customerCode = customerCode;
    }

    public String getCustomerCode() 
    {
        return customerCode;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setDepartmentGroupCode(String departmentGroupCode) 
    {
        this.departmentGroupCode = departmentGroupCode;
    }

    public String getDepartmentGroupCode() 
    {
        return departmentGroupCode;
    }

    public void setDepartmentGroupName(String departmentGroupName) 
    {
        this.departmentGroupName = departmentGroupName;
    }

    public String getDepartmentGroupName() 
    {
        return departmentGroupName;
    }

    public void setCompanyGroupCode(String companyGroupCode) 
    {
        this.companyGroupCode = companyGroupCode;
    }

    public String getCompanyGroupCode() 
    {
        return companyGroupCode;
    }

    public void setCompanyGroupName(String companyGroupName) 
    {
        this.companyGroupName = companyGroupName;
    }

    public String getCompanyGroupName() 
    {
        return companyGroupName;
    }

    public void setBatchNumber(String batchNumber) 
    {
        this.batchNumber = batchNumber;
    }

    public String getBatchNumber() 
    {
        return batchNumber;
    }

    public void setWarehouse(String warehouse) 
    {
        this.warehouse = warehouse;
    }

    public String getWarehouse() 
    {
        return warehouse;
    }

    public void setStorageLocation(String storageLocation) 
    {
        this.storageLocation = storageLocation;
    }

    public String getStorageLocation() 
    {
        return storageLocation;
    }

    public void setStorageZone(String storageZone) 
    {
        this.storageZone = storageZone;
    }

    public String getStorageZone() 
    {
        return storageZone;
    }

    public void setSourceTable(String sourceTable) 
    {
        this.sourceTable = sourceTable;
    }

    public String getSourceTable() 
    {
        return sourceTable;
    }

    public void setSourceRecordId(Long sourceRecordId) 
    {
        this.sourceRecordId = sourceRecordId;
    }

    public Long getSourceRecordId() 
    {
        return sourceRecordId;
    }

    public void setIssueTime(Date issueTime) 
    {
        this.issueTime = issueTime;
    }

    public Date getIssueTime() 
    {
        return issueTime;
    }

    public void setOperator(String operator) 
    {
        this.operator = operator;
    }

    public String getOperator() 
    {
        return operator;
    }

    public void setAuditor(String auditor) 
    {
        this.auditor = auditor;
    }

    public String getAuditor() 
    {
        return auditor;
    }

    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }

    public void setEncryptedBatch(String encryptedBatch) 
    {
        this.encryptedBatch = encryptedBatch;
    }

    public String getEncryptedBatch() 
    {
        return encryptedBatch;
    }

    public void setDocumentType(String documentType) 
    {
        this.documentType = documentType;
    }

    public String getDocumentType() 
    {
        return documentType;
    }

    public void setAssetOwner(String assetOwner) 
    {
        this.assetOwner = assetOwner;
    }

    public String getAssetOwner() 
    {
        return assetOwner;
    }

    public void setProcessor(String processor) 
    {
        this.processor = processor;
    }

    public String getProcessor() 
    {
        return processor;
    }

    public void setProcessTime(Date processTime) 
    {
        this.processTime = processTime;
    }

    public Date getProcessTime() 
    {
        return processTime;
    }

    public void setDepartment(String department) 
    {
        this.department = department;
    }

    public String getDepartment() 
    {
        return department;
    }

    public void setDepartmentAuditor(String departmentAuditor) 
    {
        this.departmentAuditor = departmentAuditor;
    }

    public String getDepartmentAuditor() 
    {
        return departmentAuditor;
    }

    public void setDepartmentAuditTime(Date departmentAuditTime) 
    {
        this.departmentAuditTime = departmentAuditTime;
    }

    public Date getDepartmentAuditTime() 
    {
        return departmentAuditTime;
    }

    public void setExpenseTransferFlag(String expenseTransferFlag) 
    {
        this.expenseTransferFlag = expenseTransferFlag;
    }

    public String getExpenseTransferFlag() 
    {
        return expenseTransferFlag;
    }

    public void setSiteCode(String siteCode) 
    {
        this.siteCode = siteCode;
    }

    public String getSiteCode() 
    {
        return siteCode;
    }

    public void setProjectCode(String projectCode) 
    {
        this.projectCode = projectCode;
    }

    public String getProjectCode() 
    {
        return projectCode;
    }

    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }

    public void setRdSystemId(String rdSystemId) 
    {
        this.rdSystemId = rdSystemId;
    }

    public String getRdSystemId() 
    {
        return rdSystemId;
    }

    public void setCreatedBy(String createdBy) 
    {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() 
    {
        return createdBy;
    }

    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }

    public void setUpdatedBy(String updatedBy) 
    {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() 
    {
        return updatedBy;
    }

    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }

    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("accountingPeriod", getAccountingPeriod())
            .append("financialAccount", getFinancialAccount())
            .append("sku", getSku())
            .append("quantity", getQuantity())
            .append("amount", getAmount())
            .append("sourceType", getSourceType())
            .append("purchaseScope", getPurchaseScope())
            .append("customerCode", getCustomerCode())
            .append("customerName", getCustomerName())
            .append("departmentGroupCode", getDepartmentGroupCode())
            .append("departmentGroupName", getDepartmentGroupName())
            .append("companyGroupCode", getCompanyGroupCode())
            .append("companyGroupName", getCompanyGroupName())
            .append("batchNumber", getBatchNumber())
            .append("warehouse", getWarehouse())
            .append("storageLocation", getStorageLocation())
            .append("storageZone", getStorageZone())
            .append("sourceTable", getSourceTable())
            .append("sourceRecordId", getSourceRecordId())
            .append("issueTime", getIssueTime())
            .append("operator", getOperator())
            .append("auditor", getAuditor())
            .append("auditTime", getAuditTime())
            .append("encryptedBatch", getEncryptedBatch())
            .append("documentType", getDocumentType())
            .append("assetOwner", getAssetOwner())
            .append("processor", getProcessor())
            .append("processTime", getProcessTime())
            .append("department", getDepartment())
            .append("departmentAuditor", getDepartmentAuditor())
            .append("departmentAuditTime", getDepartmentAuditTime())
            .append("expenseTransferFlag", getExpenseTransferFlag())
            .append("siteCode", getSiteCode())
            .append("projectCode", getProjectCode())
            .append("projectName", getProjectName())
            .append("rdSystemId", getRdSystemId())
            .append("createdBy", getCreatedBy())
            .append("createdTime", getCreatedTime())
            .append("updatedBy", getUpdatedBy())
            .append("updatedTime", getUpdatedTime())
            .append("version", getVersion())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
