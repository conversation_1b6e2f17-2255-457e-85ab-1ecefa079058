<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repackage.mapper.ConversionRuleMapper">
    
    <resultMap type="ConversionRule" id="ConversionRuleResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="description"    column="description"    />
        <result property="conversionType"    column="conversion_type"    />
        <result property="lossRate"    column="loss_rate"    />
        <result property="costMethod"    column="cost_method"    />
        <result property="isActive"    column="is_active"    />
        <result property="cycleFlag"    column="cycle_flag"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="obsoleteDate"    column="obsolete_date"    />
        <result property="assetOwner" column="asset_owner" />
        <result property="VERSION"    column="VERSION"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectConversionRuleVo">
        select rule_id, rule_code, description, conversion_type, loss_rate, cost_method, is_active, cycle_flag, effective_date, obsolete_date, asset_owner, VERSION, created_at, updated_at from conversion_rule
    </sql>

    <select id="selectConversionRuleList" parameterType="ConversionRule" resultMap="ConversionRuleResult">
        <include refid="selectConversionRuleVo"/>
        <where>  
            <if test="ruleCode != null  and ruleCode != ''"> and rule_code = #{ruleCode}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="conversionType != null  and conversionType != ''"> and conversion_type = #{conversionType}</if>
            <if test="lossRate != null "> and loss_rate = #{lossRate}</if>
            <if test="costMethod != null  and costMethod != ''"> and cost_method = #{costMethod}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="cycleFlag != null "> and cycle_flag = #{cycleFlag}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="obsoleteDate != null "> and obsolete_date = #{obsoleteDate}</if>
            <if test="assetOwner != null and assetOwner != ''"> and asset_owner = #{assetOwner}</if>
            <if test="VERSION != null "> and VERSION = #{VERSION}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectConversionRuleByRuleId" parameterType="Long" resultMap="ConversionRuleResult">
        <include refid="selectConversionRuleVo"/>
        where rule_id = #{ruleId}
    </select>

    <insert id="insertConversionRule" parameterType="ConversionRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into conversion_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code,</if>
            <if test="description != null">description,</if>
            <if test="conversionType != null">conversion_type,</if>
            <if test="lossRate != null">loss_rate,</if>
            <if test="costMethod != null">cost_method,</if>
            <if test="isActive != null">is_active,</if>
            <if test="cycleFlag != null">cycle_flag,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="obsoleteDate != null">obsolete_date,</if>
            <if test="assetOwner != null">asset_owner,</if>
            <if test="VERSION != null">VERSION,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">#{ruleCode},</if>
            <if test="description != null">#{description},</if>
            <if test="conversionType != null">#{conversionType},</if>
            <if test="lossRate != null">#{lossRate},</if>
            <if test="costMethod != null">#{costMethod},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="cycleFlag != null">#{cycleFlag},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="obsoleteDate != null">#{obsoleteDate},</if>
            <if test="assetOwner != null">#{assetOwner},</if>
            <if test="VERSION != null">#{VERSION},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateConversionRule" parameterType="ConversionRule">
        update conversion_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code = #{ruleCode},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="conversionType != null and conversionType != ''">conversion_type = #{conversionType},</if>
            <if test="lossRate != null">loss_rate = #{lossRate},</if>
            <if test="costMethod != null and costMethod != ''">cost_method = #{costMethod},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="cycleFlag != null">cycle_flag = #{cycleFlag},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="obsoleteDate != null">obsolete_date = #{obsoleteDate},</if>
            <if test="assetOwner != null">asset_owner = #{assetOwner},</if>
            <if test="VERSION != null">VERSION = #{VERSION},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>
    <update id="resetCycleFlagForActiveRules">
        update conversion_rule
        set cycle_flag = 0, updated_at = NOW()
        where is_active = 1
    </update>

    <delete id="deleteConversionRuleByRuleId" parameterType="Long">
        delete from conversion_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteConversionRuleByRuleIds" parameterType="String">
        delete from conversion_rule where rule_id in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>
</mapper>