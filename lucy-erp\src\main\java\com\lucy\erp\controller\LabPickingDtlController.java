package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LabPickingDtl;
import com.lucy.erp.service.ILabPickingDtlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 研发领用明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/lab/dtl")
public class LabPickingDtlController extends BaseController
{
    @Autowired
    private ILabPickingDtlService labPickingDtlService;

    /**
     * 查询研发领用明细列表
     */
    @PreAuthorize("@ss.hasPermi('lab:dtl:list')")
    @GetMapping("/list")
    public TableDataInfo list(LabPickingDtl labPickingDtl)
    {
        startPage();
        List<LabPickingDtl> list = labPickingDtlService.selectLabPickingDtlList(labPickingDtl);
        return getDataTable(list);
    }

    /**
     * 导出研发领用明细列表
     */
    @PreAuthorize("@ss.hasPermi('lab:dtl:export')")
    @Log(title = "研发领用明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LabPickingDtl labPickingDtl)
    {
        List<LabPickingDtl> list = labPickingDtlService.selectLabPickingDtlList(labPickingDtl);
        ExcelUtil<LabPickingDtl> util = new ExcelUtil<LabPickingDtl>(LabPickingDtl.class);
        util.exportExcel(response, list, "研发领用明细数据");
    }

    /**
     * 获取研发领用明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('lab:dtl:query')")
    @GetMapping(value = "/{lpdCd}")
    public AjaxResult getInfo(@PathVariable("lpdCd") String lpdCd)
    {
        return success(labPickingDtlService.selectLabPickingDtlByLpdCd(lpdCd));
    }

    /**
     * 新增研发领用明细
     */
    @PreAuthorize("@ss.hasPermi('lab:dtl:add')")
    @Log(title = "研发领用明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LabPickingDtl labPickingDtl)
    {
        return toAjax(labPickingDtlService.insertLabPickingDtl(labPickingDtl));
    }

    /**
     * 修改研发领用明细
     */
    @PreAuthorize("@ss.hasPermi('lab:dtl:edit')")
    @Log(title = "研发领用明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LabPickingDtl labPickingDtl)
    {
        return toAjax(labPickingDtlService.updateLabPickingDtl(labPickingDtl));
    }

    /**
     * 删除研发领用明细
     */
    @PreAuthorize("@ss.hasPermi('lab:dtl:remove')")
    @Log(title = "研发领用明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lpdCds}")
    public AjaxResult remove(@PathVariable String[] lpdCds)
    {
        return toAjax(labPickingDtlService.deleteLabPickingDtlByLpdCds(lpdCds));
    }
}
