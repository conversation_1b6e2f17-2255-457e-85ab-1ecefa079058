package com.ruoyi.repackage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 物料依赖图谱对象 conversion_material_dependency_closure
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class ConversionMaterialDependencyClosure extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 祖先节点SKU（起始物料） */
    private String ancestorSku;

    /** 后代节点SKU（最终产物） */
    private String descendantSku;

    /** 最短路径深度 */
    @Excel(name = "最短路径深度")
    private Integer minDepth;

    /** 最长路径深度 */
    @Excel(name = "最长路径深度")
    private Integer maxDepth;

    /** 路径数量 */
    @Excel(name = "路径数量")
    private Integer pathCount;

    public void setAncestorSku(String ancestorSku) 
    {
        this.ancestorSku = ancestorSku;
    }

    public String getAncestorSku() 
    {
        return ancestorSku;
    }

    public void setDescendantSku(String descendantSku) 
    {
        this.descendantSku = descendantSku;
    }

    public String getDescendantSku() 
    {
        return descendantSku;
    }

    public void setMinDepth(Integer minDepth) 
    {
        this.minDepth = minDepth;
    }

    public Integer getMinDepth() 
    {
        return minDepth;
    }

    public void setMaxDepth(Integer maxDepth) 
    {
        this.maxDepth = maxDepth;
    }

    public Integer getMaxDepth() 
    {
        return maxDepth;
    }

    public void setPathCount(Integer pathCount) 
    {
        this.pathCount = pathCount;
    }

    public Integer getPathCount()
    {
        return pathCount;
    }

    /**
     * 构造函数 - 创建依赖关系记录
     */
    public ConversionMaterialDependencyClosure(String ancestorSku, String descendantSku, Integer maxDepth) {
        this.ancestorSku = ancestorSku;
        this.descendantSku = descendantSku;
        this.maxDepth = maxDepth;
    }

    /**
     * 默认构造函数
     */
    public ConversionMaterialDependencyClosure() {
    }

    /**
     * 创建直接依赖关系
     */
    public static ConversionMaterialDependencyClosure createDirectDependency(String ancestorSku, String descendantSku) {
        return new ConversionMaterialDependencyClosure(ancestorSku, descendantSku, 1);
    }

    /**
     * 创建自引用依赖关系（物料到自身的依赖，深度为0）
     */
    public static ConversionMaterialDependencyClosure createSelfReference(String materialSku) {
        return new ConversionMaterialDependencyClosure(materialSku, materialSku, 0);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ancestorSku", getAncestorSku())
            .append("descendantSku", getDescendantSku())
            .append("minDepth", getMinDepth())
            .append("maxDepth", getMaxDepth())
            .append("pathCount", getPathCount())
            .toString();
    }
}
