package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.FinAccAvgCost;
import com.lucy.erp.service.IFinAccAvgCostService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 财务_平均_费Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/system/avgcost")
public class FinAccAvgCostController extends BaseController
{
    @Autowired
    private IFinAccAvgCostService finAccAvgCostService;

    /**
     * 查询财务_平均_费列表
     */
    @PreAuthorize("@ss.hasPermi('system:avgcost:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinAccAvgCost finAccAvgCost)
    {
        startPage();
        List<FinAccAvgCost> list = finAccAvgCostService.selectFinAccAvgCostList(finAccAvgCost);
        return getDataTable(list);
    }

    /**
     * 导出财务_平均_费列表
     */
    @PreAuthorize("@ss.hasPermi('system:avgcost:export')")
    @Log(title = "财务_平均_费", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinAccAvgCost finAccAvgCost)
    {
        List<FinAccAvgCost> list = finAccAvgCostService.selectFinAccAvgCostList(finAccAvgCost);
        ExcelUtil<FinAccAvgCost> util = new ExcelUtil<FinAccAvgCost>(FinAccAvgCost.class);
        util.exportExcel(response, list, "财务_平均_费数据");
    }

    /**
     * 获取财务_平均_费详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:avgcost:query')")
    @GetMapping(value = "/{faacId}")
    public AjaxResult getInfo(FinAccAvgCost finAccAvgCost)
    {
        return success(finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost));
    }

    /**
     * 新增财务_平均_费
     */
    @PreAuthorize("@ss.hasPermi('system:avgcost:add')")
    @Log(title = "财务_平均_费", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinAccAvgCost finAccAvgCost)
    {
        return toAjax(finAccAvgCostService.insertFinAccAvgCost(finAccAvgCost));
    }

    /**
     * 修改财务_平均_费
     */
    @PreAuthorize("@ss.hasPermi('system:avgcost:edit')")
    @Log(title = "财务_平均_费", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinAccAvgCost finAccAvgCost)
    {
        return toAjax(finAccAvgCostService.updateFinAccAvgCost(finAccAvgCost));
    }

    /**
     * 删除财务_平均_费
     */
    @PreAuthorize("@ss.hasPermi('system:avgcost:remove')")
    @Log(title = "财务_平均_费", businessType = BusinessType.DELETE)
	@DeleteMapping("/{faacIds}")
    public AjaxResult remove(@PathVariable String[] faacIds)
    {
        return toAjax(finAccAvgCostService.deleteFinAccAvgCostByFaacIds(faacIds));
    }
}
