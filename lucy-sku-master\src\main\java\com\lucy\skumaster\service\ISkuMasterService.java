package com.lucy.skumaster.service;

import java.util.List;
import com.lucy.skumaster.domain.SkuMaster;

/**
 * SKU主数据 SKU master dataService接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface ISkuMasterService 
{
    /**
     * 查询SKU主数据 SKU master data
     * 
     * @param code SKU主数据 SKU master data主键
     * @return SKU主数据 SKU master data
     */
    public SkuMaster selectSkuMasterByCode(String code);

    /**
     * 查询SKU主数据 SKU master data列表
     * 
     * @param skuMaster SKU主数据 SKU master data
     * @return SKU主数据 SKU master data集合
     */
    public List<SkuMaster> selectSkuMasterList(SkuMaster skuMaster);

    /**
     * 新增SKU主数据 SKU master data
     * 
     * @param skuMaster SKU主数据 SKU master data
     * @return 结果
     */
    public int insertSkuMaster(SkuMaster skuMaster);

    /**
     * 修改SKU主数据 SKU master data
     * 
     * @param skuMaster SKU主数据 SKU master data
     * @return 结果
     */
    public int updateSkuMaster(SkuMaster skuMaster);

    /**
     * 批量删除SKU主数据 SKU master data
     * 
     * @param codes 需要删除的SKU主数据 SKU master data主键集合
     * @return 结果
     */
    public int deleteSkuMasterByCodes(String[] codes);

    /**
     * 删除SKU主数据 SKU master data信息
     * 
     * @param code SKU主数据 SKU master data主键
     * @return 结果
     */
    public int deleteSkuMasterByCode(String code);

    public void insertSkuMasterIfNotExist(String code);
}
