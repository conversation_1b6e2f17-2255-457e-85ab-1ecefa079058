package com.lucy.assetowner.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.lucy.assetowner.service.IAssetOwnerMasterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资产所属主体主 Asset owner master dataController
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/lucy-assetowner/master")
public class AssetOwnerMasterController extends BaseController
{
    @Autowired
    private IAssetOwnerMasterService assetOwnerMasterService;

    /**
     * 查询资产所属主体主 Asset owner master data列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-assetowner:master:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetOwnerMaster assetOwnerMaster)
    {
        startPage();
        List<AssetOwnerMaster> list = assetOwnerMasterService.selectAssetOwnerMasterList(assetOwnerMaster);
        return getDataTable(list);
    }

    /**
     * 导出资产所属主体主 Asset owner master data列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-assetowner:master:export')")
    @Log(title = "资产所属主体主 Asset owner master data", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetOwnerMaster assetOwnerMaster)
    {
        List<AssetOwnerMaster> list = assetOwnerMasterService.selectAssetOwnerMasterList(assetOwnerMaster);
        ExcelUtil<AssetOwnerMaster> util = new ExcelUtil<AssetOwnerMaster>(AssetOwnerMaster.class);
        util.exportExcel(response, list, "资产所属主体主 Asset owner master data数据");
    }

    /**
     * 获取资产所属主体主 Asset owner master data详细信息
     */
    @PreAuthorize("@ss.hasPermi('lucy-assetowner:master:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(assetOwnerMasterService.selectAssetOwnerMasterById(id));
    }

    /**
     * 新增资产所属主体主 Asset owner master data
     */
    @PreAuthorize("@ss.hasPermi('lucy-assetowner:master:add')")
    @Log(title = "资产所属主体主 Asset owner master data", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssetOwnerMaster assetOwnerMaster)
    {
        return toAjax(assetOwnerMasterService.insertAssetOwnerMaster(assetOwnerMaster));
    }

    /**
     * 修改资产所属主体主 Asset owner master data
     */
    @PreAuthorize("@ss.hasPermi('lucy-assetowner:master:edit')")
    @Log(title = "资产所属主体主 Asset owner master data", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssetOwnerMaster assetOwnerMaster)
    {
        return toAjax(assetOwnerMasterService.updateAssetOwnerMaster(assetOwnerMaster));
    }

    /**
     * 删除资产所属主体主 Asset owner master data
     */
    @PreAuthorize("@ss.hasPermi('lucy-assetowner:master:remove')")
    @Log(title = "资产所属主体主 Asset owner master data", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(assetOwnerMasterService.deleteAssetOwnerMasterByIds(ids));
    }
}
