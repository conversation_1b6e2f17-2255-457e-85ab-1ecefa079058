package com.lucy.customer.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 部门组织结构 Department groups对象 customer_company
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public class CustomerCompany extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 公司编号, Group code */
    private String code;

    /** 公司名称, Group name */
    @Excel(name = "公司名称, Group name")
    private String name;

    /** 税号, tax no */
    @Excel(name = "税号, tax no")
    private String taxNo;

    /** 客户类别（直销-终端客户、贸易商、经销商） */
    @Excel(name = "客户类别", readConverterExp = "直=销-终端客户、贸易商、经销商")
    private String category;

    /** 客户分类-小类 */
    @Excel(name = "客户分类-小类")
    private String classificationSubclass;

    /** 客户分类-大类 */
    @Excel(name = "客户分类-大类")
    private String classificationMajor;

    /** 匹配客户档案信息【应用领域、省份等信息】 */
    @Excel(name = "匹配客户档案信息【应用领域、省份等信息】")
    private String profile;

    /** 客户同控合并情况 */
    @Excel(name = "客户同控合并情况")
    private String mergerSituation;

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setTaxNo(String taxNo) 
    {
        this.taxNo = taxNo;
    }

    public String getTaxNo() 
    {
        return taxNo;
    }

    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }

    public void setClassificationSubclass(String classificationSubclass) 
    {
        this.classificationSubclass = classificationSubclass;
    }

    public String getClassificationSubclass() 
    {
        return classificationSubclass;
    }

    public void setClassificationMajor(String classificationMajor) 
    {
        this.classificationMajor = classificationMajor;
    }

    public String getClassificationMajor() 
    {
        return classificationMajor;
    }

    public void setProfile(String profile) 
    {
        this.profile = profile;
    }

    public String getProfile() 
    {
        return profile;
    }

    public void setMergerSituation(String mergerSituation) 
    {
        this.mergerSituation = mergerSituation;
    }

    public String getMergerSituation() 
    {
        return mergerSituation;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("name", getName())
            .append("taxNo", getTaxNo())
            .append("category", getCategory())
            .append("classificationSubclass", getClassificationSubclass())
            .append("classificationMajor", getClassificationMajor())
            .append("profile", getProfile())
            .append("mergerSituation", getMergerSituation())
            .toString();
    }
}
