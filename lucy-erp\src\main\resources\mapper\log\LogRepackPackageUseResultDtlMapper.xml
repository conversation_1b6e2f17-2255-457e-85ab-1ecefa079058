<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogRepackPackageUseResultDtlMapper">
    
    <resultMap type="LogRepackPackageUseResultDtl" id="LogRepackPackageUseResultDtlResult">
        <result property="id"    column="id"    />
        <result property="lrpurdMcd"    column="lrpurd_mcd"    />
        <result property="lrpurdCd"    column="lrpurd_cd"    />
        <result property="lrpurdSkuCd"    column="lrpurd_sku_cd"    />
        <result property="lrpurdSkuDmcd"    column="lrpurd_sku_dmcd"    />
        <result property="lrpurdSkuSlcd"    column="lrpurd_sku_slcd"    />
        <result property="lrpurdSkuTcd"    column="lrpurd_sku_tcd"    />
        <result property="lrpurdSkuOrgmcd"    column="lrpurd_sku_orgmcd"    />
        <result property="lrpurdSkuBn"    column="lrpurd_sku_bn"    />
        <result property="lrpurdSkuBnEncrypt"    column="lrpurd_sku_bn_encrypt"    />
        <result property="lrpurdSkuBnQty"    column="lrpurd_sku_bn_qty"    />
        <result property="lrpurdSkuOutDate"    column="lrpurd_sku_out_date"    />
        <result property="lrpurdTotalPrc"    column="lrpurd_total_prc"    />
        <result property="lrpurdBit"    column="lrpurd_bit"    />
        <result property="lrpurdIspart"    column="lrpurd_ispart"    />
        <result property="lrpurdTotalValue"    column="lrpurd_total_value"    />
        <result property="lrpurdFlag"    column="lrpurd_flag"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <resultMap type="LogRepackResult" id="LogRepackResultResult">
        <result property="id"    column="id"    />
        <result property="lrrCd"    column="lrr_cd"    />
        <result property="lrrTyp"    column="lrr_typ"    />
        <result property="lrrPsn"    column="lrr_psn"    />
        <result property="lrrPsnCd"    column="lrr_psn_cd"    />
        <result property="lrrMainSkuCd"    column="lrr_main_sku_cd"    />
        <result property="lrrMainBatch"    column="lrr_main_batch"    />
        <result property="lrrMainSkuQty"    column="lrr_main_sku_qty"    />
        <result property="lrrMainTotalPrc"    column="lrr_main_total_prc"    />
        <result property="lrrMainDate"    column="lrr_main_date"    />
        <result property="lrrBn"    column="lrr_bn"    />
        <result property="lrrBnEncrypt"    column="lrr_bn_encrypt"    />
        <result property="lrrDmcd"    column="lrr_dmcd"    />
        <result property="lrrSlcd"    column="lrr_slcd"    />
        <result property="lrrTcd"    column="lrr_tcd"    />
        <result property="lrrOrgmcd"    column="lrr_orgmcd"    />
        <result property="flag"    column="flag"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lrpurdMcd"    column="lrpurd_mcd"    />
        <result property="lrpurdSkuCd"    column="lrpurd_sku_cd"    />
        <result property="lrpurdTotalPrc"    column="lrpurd_total_prc"    />
    </resultMap>

    <sql id="selectLogRepackPackageUseResultDtlVo">
        select id, lrpurd_mcd, lrpurd_cd, lrpurd_sku_cd, lrpurd_sku_dmcd, lrpurd_sku_slcd, lrpurd_sku_tcd, lrpurd_sku_orgmcd, lrpurd_sku_bn, lrpurd_sku_bn_encrypt, lrpurd_sku_bn_qty, lrpurd_sku_out_date, lrpurd_total_prc, lrpurd_bit, lrpurd_ispart, lrpurd_total_value, lrpurd_flag, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from log_repack_package_use_result_dtl
    </sql>

    <sql id="selectLogRepackResultVo">
        select id, lrr_cd, lrr_typ, lrr_psn, lrr_psn_cd, lrr_main_sku_cd, lrr_main_batch, lrr_main_sku_qty, lrr_main_total_prc, lrr_main_date, lrr_bn, lrr_bn_encrypt, lrr_dmcd, lrr_slcd, lrr_tcd, lrr_orgmcd, flag, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del,
            lrpurd_mcd,lrpurd_sku_cd,lrpurd_total_prc
        FROM lucyerp_energy_rds.log_repack_package_use_result_dtl  lrpurd
        LEFT JOIN lucyerp_energy_rds.log_repack_result lal ON lrpurd_mcd = lrr_cd
    </sql>

    <select id="selectLogRepackPackageUseResultDtlList" parameterType="LogRepackPackageUseResultDtl" resultMap="LogRepackPackageUseResultDtlResult">
        <include refid="selectLogRepackPackageUseResultDtlVo"/>
        <where>  
            <if test="lrpurdMcd != null  and lrpurdMcd != ''"> and lrpurd_mcd = #{lrpurdMcd}</if>
            <if test="lrpurdSkuCd != null  and lrpurdSkuCd != ''"> and lrpurd_sku_cd = #{lrpurdSkuCd}</if>
            <if test="lrpurdSkuDmcd != null  and lrpurdSkuDmcd != ''"> and lrpurd_sku_dmcd = #{lrpurdSkuDmcd}</if>
            <if test="lrpurdSkuSlcd != null  and lrpurdSkuSlcd != ''"> and lrpurd_sku_slcd = #{lrpurdSkuSlcd}</if>
            <if test="lrpurdSkuTcd != null  and lrpurdSkuTcd != ''"> and lrpurd_sku_tcd = #{lrpurdSkuTcd}</if>
            <if test="lrpurdSkuOrgmcd != null  and lrpurdSkuOrgmcd != ''"> and lrpurd_sku_orgmcd = #{lrpurdSkuOrgmcd}</if>
            <if test="lrpurdSkuBn != null  and lrpurdSkuBn != ''"> and lrpurd_sku_bn = #{lrpurdSkuBn}</if>
            <if test="lrpurdSkuBnEncrypt != null  and lrpurdSkuBnEncrypt != ''"> and lrpurd_sku_bn_encrypt = #{lrpurdSkuBnEncrypt}</if>
            <if test="lrpurdSkuBnQty != null "> and lrpurd_sku_bn_qty = #{lrpurdSkuBnQty}</if>
            <if test="lrpurdSkuOutDate != null "> and lrpurd_sku_out_date = #{lrpurdSkuOutDate}</if>
            <if test="lrpurdTotalPrc != null "> and lrpurd_total_prc = #{lrpurdTotalPrc}</if>
            <if test="lrpurdBit != null "> and lrpurd_bit = #{lrpurdBit}</if>
            <if test="lrpurdIspart != null "> and lrpurd_ispart = #{lrpurdIspart}</if>
            <if test="lrpurdTotalValue != null "> and lrpurd_total_value = #{lrpurdTotalValue}</if>
            <if test="lrpurdFlag != null "> and lrpurd_flag = #{lrpurdFlag}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
        </where>
    </select>

    <select id="selectLogRepackPackageUseResultDtlAndLrrList" parameterType="LogRepackPackageUseResultDtl" resultMap="LogRepackPackageUseResultDtlResult">
        <include refid="selectLogRepackResultVo"/>
            <where>
            <if test="lrrTyp != null  and lrrTyp != ''"> and lrr_typ = #{lrrTyp}</if>
            <if test="lrrPsn != null  and lrrPsn != ''"> and lrr_psn = #{lrrPsn}</if>
            <if test="lrrPsnCd != null  and lrrPsnCd != ''"> and lrr_psn_cd = #{lrrPsnCd}</if>
            <if test="lrrMainSkuCd != null  and lrrMainSkuCd != ''"> and lrr_main_sku_cd = #{lrrMainSkuCd}</if>
            <if test="lrrMainBatch != null  and lrrMainBatch != ''"> and lrr_main_batch = #{lrrMainBatch}</if>
            <if test="lrrMainSkuQty != null "> and lrr_main_sku_qty = #{lrrMainSkuQty}</if>
            <if test="lrrMainTotalPrc != null "> and lrr_main_total_prc = #{lrrMainTotalPrc}</if>
            <if test="lrrMainDate != null "> and lrr_main_date = #{lrrMainDate}</if>
            <if test="lrrBn != null  and lrrBn != ''"> and lrr_bn = #{lrrBn}</if>
            <if test="lrrBnEncrypt != null  and lrrBnEncrypt != ''"> and lrr_bn_encrypt = #{lrrBnEncrypt}</if>
            <if test="lrrDmcd != null  and lrrDmcd != ''"> and lrr_dmcd = #{lrrDmcd}</if>
            <if test="lrrSlcd != null  and lrrSlcd != ''"> and lrr_slcd = #{lrrSlcd}</if>
            <if test="lrrTcd != null  and lrrTcd != ''"> and lrr_tcd = #{lrrTcd}</if>
            <if test="lrrOrgmcd != null  and lrrOrgmcd != ''"> and lrr_orgmcd = #{lrrOrgmcd}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="startDate != null">
                AND r_cre_dt >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                AND r_cre_dt &lt;= DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
            </if>
            </where>
    </select>
    
    <select id="selectLogRepackPackageUseResultDtlByLrpurdMcd" parameterType="String" resultMap="LogRepackPackageUseResultDtlResult">
        <include refid="selectLogRepackPackageUseResultDtlVo"/>
        where lrpurd_mcd = #{lrpurdMcd}
    </select>

    <insert id="insertLogRepackPackageUseResultDtl" parameterType="LogRepackPackageUseResultDtl">
        insert into log_repack_package_use_result_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lrpurdMcd != null">lrpurd_mcd,</if>
            <if test="lrpurdCd != null">lrpurd_cd,</if>
            <if test="lrpurdSkuCd != null and lrpurdSkuCd != ''">lrpurd_sku_cd,</if>
            <if test="lrpurdSkuDmcd != null">lrpurd_sku_dmcd,</if>
            <if test="lrpurdSkuSlcd != null">lrpurd_sku_slcd,</if>
            <if test="lrpurdSkuTcd != null">lrpurd_sku_tcd,</if>
            <if test="lrpurdSkuOrgmcd != null">lrpurd_sku_orgmcd,</if>
            <if test="lrpurdSkuBn != null">lrpurd_sku_bn,</if>
            <if test="lrpurdSkuBnEncrypt != null">lrpurd_sku_bn_encrypt,</if>
            <if test="lrpurdSkuBnQty != null">lrpurd_sku_bn_qty,</if>
            <if test="lrpurdSkuOutDate != null">lrpurd_sku_out_date,</if>
            <if test="lrpurdTotalPrc != null">lrpurd_total_prc,</if>
            <if test="lrpurdBit != null">lrpurd_bit,</if>
            <if test="lrpurdIspart != null">lrpurd_ispart,</if>
            <if test="lrpurdTotalValue != null">lrpurd_total_value,</if>
            <if test="lrpurdFlag != null">lrpurd_flag,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lrpurdMcd != null">#{lrpurdMcd},</if>
            <if test="lrpurdCd != null">#{lrpurdCd},</if>
            <if test="lrpurdSkuCd != null and lrpurdSkuCd != ''">#{lrpurdSkuCd},</if>
            <if test="lrpurdSkuDmcd != null">#{lrpurdSkuDmcd},</if>
            <if test="lrpurdSkuSlcd != null">#{lrpurdSkuSlcd},</if>
            <if test="lrpurdSkuTcd != null">#{lrpurdSkuTcd},</if>
            <if test="lrpurdSkuOrgmcd != null">#{lrpurdSkuOrgmcd},</if>
            <if test="lrpurdSkuBn != null">#{lrpurdSkuBn},</if>
            <if test="lrpurdSkuBnEncrypt != null">#{lrpurdSkuBnEncrypt},</if>
            <if test="lrpurdSkuBnQty != null">#{lrpurdSkuBnQty},</if>
            <if test="lrpurdSkuOutDate != null">#{lrpurdSkuOutDate},</if>
            <if test="lrpurdTotalPrc != null">#{lrpurdTotalPrc},</if>
            <if test="lrpurdBit != null">#{lrpurdBit},</if>
            <if test="lrpurdIspart != null">#{lrpurdIspart},</if>
            <if test="lrpurdTotalValue != null">#{lrpurdTotalValue},</if>
            <if test="lrpurdFlag != null">#{lrpurdFlag},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateLogRepackPackageUseResultDtl" parameterType="LogRepackPackageUseResultDtl">
        update log_repack_package_use_result_dtl
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lrpurdMcd != null">lrpurd_mcd = #{lrpurdMcd},</if>
            <if test="lrpurdSkuCd != null and lrpurdSkuCd != ''">lrpurd_sku_cd = #{lrpurdSkuCd},</if>
            <if test="lrpurdSkuDmcd != null">lrpurd_sku_dmcd = #{lrpurdSkuDmcd},</if>
            <if test="lrpurdSkuSlcd != null">lrpurd_sku_slcd = #{lrpurdSkuSlcd},</if>
            <if test="lrpurdSkuTcd != null">lrpurd_sku_tcd = #{lrpurdSkuTcd},</if>
            <if test="lrpurdSkuOrgmcd != null">lrpurd_sku_orgmcd = #{lrpurdSkuOrgmcd},</if>
            <if test="lrpurdSkuBn != null">lrpurd_sku_bn = #{lrpurdSkuBn},</if>
            <if test="lrpurdSkuBnEncrypt != null">lrpurd_sku_bn_encrypt = #{lrpurdSkuBnEncrypt},</if>
            <if test="lrpurdSkuBnQty != null">lrpurd_sku_bn_qty = #{lrpurdSkuBnQty},</if>
            <if test="lrpurdSkuOutDate != null">lrpurd_sku_out_date = #{lrpurdSkuOutDate},</if>
            <if test="lrpurdTotalPrc != null">lrpurd_total_prc = #{lrpurdTotalPrc},</if>
            <if test="lrpurdBit != null">lrpurd_bit = #{lrpurdBit},</if>
            <if test="lrpurdIspart != null">lrpurd_ispart = #{lrpurdIspart},</if>
            <if test="lrpurdTotalValue != null">lrpurd_total_value = #{lrpurdTotalValue},</if>
            <if test="lrpurdFlag != null">lrpurd_flag = #{lrpurdFlag},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where lrpurd_cd = #{lrpurdCd}
    </update>

    <delete id="deleteLogRepackPackageUseResultDtlByLrpurdCd" parameterType="String">
        delete from log_repack_package_use_result_dtl where lrpurd_cd = #{lrpurdCd}
    </delete>

    <delete id="deleteLogRepackPackageUseResultDtlByLrpurdCds" parameterType="String">
        delete from log_repack_package_use_result_dtl where lrpurd_cd in 
        <foreach item="lrpurdCd" collection="array" open="(" separator="," close=")">
            #{lrpurdCd}
        </foreach>
    </delete>
</mapper>