package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.BasInvoiceTyp;

/**
 * 基础_发票_类型
Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IBasInvoiceTypService 
{
    /**
     * 查询基础_发票_类型

     * 
     * @param bitCd 基础_发票_类型
主键
     * @return 基础_发票_类型

     */
    public BasInvoiceTyp selectBasInvoiceTypByBitCd(String bitCd);

    /**
     * 查询基础_发票_类型
列表
     * 
     * @param basInvoiceTyp 基础_发票_类型

     * @return 基础_发票_类型
集合
     */
    public List<BasInvoiceTyp> selectBasInvoiceTypList(BasInvoiceTyp basInvoiceTyp);

    /**
     * 新增基础_发票_类型

     * 
     * @param basInvoiceTyp 基础_发票_类型

     * @return 结果
     */
    public int insertBasInvoiceTyp(BasInvoiceTyp basInvoiceTyp);

    /**
     * 修改基础_发票_类型

     * 
     * @param basInvoiceTyp 基础_发票_类型

     * @return 结果
     */
    public int updateBasInvoiceTyp(BasInvoiceTyp basInvoiceTyp);

    /**
     * 批量删除基础_发票_类型

     * 
     * @param bitCds 需要删除的基础_发票_类型
主键集合
     * @return 结果
     */
    public int deleteBasInvoiceTypByBitCds(String[] bitCds);

    /**
     * 删除基础_发票_类型
信息
     * 
     * @param bitCd 基础_发票_类型
主键
     * @return 结果
     */
    public int deleteBasInvoiceTypByBitCd(String bitCd);
}
