<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogOsPlanMapper">
    
    <resultMap type="LogOsPlan" id="LogOsPlanResult">
        <result property="id"    column="id"    />
        <result property="lopPsn"    column="lop_psn"    />
        <result property="lopTyp"    column="lop_typ"    />
        <result property="lopIotyp"    column="lop_iotyp"    />
        <result property="lopPcd"    column="lop_pcd"    />
        <result property="lopPpcd"    column="lop_ppcd"    />
        <result property="lopBn"    column="lop_bn"    />
        <result property="lopPbn"    column="lop_pbn"    />
        <result property="lopSbn"    column="lop_sbn"    />
        <result property="lopQty"    column="lop_qty"    />
        <result property="lopDmcd"    column="lop_dmcd"    />
        <result property="lopSlcd"    column="lop_slcd"    />
        <result property="lopTcd"    column="lop_tcd"    />
        <result property="lopIspart"    column="lop_ispart"    />
        <result property="lopBit"    column="lop_bit"    />
        <result property="lopDeliverCost"    column="lop_deliver_cost"    />
        <result property="lopUnitp"    column="lop_unitp"    />
        <result property="lopTotalp"    column="lop_totalp"    />
        <result property="lopCargov"    column="lop_cargov"    />
        <result property="lopNtaxv"    column="lop_ntaxv"    />
        <result property="lopTax"    column="lop_tax"    />
        <result property="logGroupCost"    column="log_group_cost"    />
        <result property="logLogisticsCost"    column="log_logistics_cost"    />
        <result property="lopIspartOld"    column="lop_ispart_old"    />
        <result property="lopBitOld"    column="lop_bit_old"    />
        <result property="lopUnitpOld"    column="lop_unitp_old"    />
        <result property="lopTotalpOld"    column="lop_totalp_old"    />
        <result property="lopCargovOld"    column="lop_cargov_old"    />
        <result property="lopNtaxvOld"    column="lop_ntaxv_old"    />
        <result property="lopTaxOld"    column="lop_tax_old"    />
        <result property="lopOrgmcdOld"    column="lop_orgmcd_old"    />
        <result property="lopBizcd"    column="lop_bizcd"    />
        <result property="lopBizcdMcd"    column="lop_bizcd_mcd"    />
        <result property="lopBizcdRcd"    column="lop_bizcd_rcd"    />
        <result property="lopBizcdRmcd"    column="lop_bizcd_rmcd"    />
        <result property="lopUcd"    column="lop_ucd"    />
        <result property="lopDt"    column="lop_dt"    />
        <result property="lopEps"    column="lop_eps"    />
        <result property="lopTn"    column="lop_tn"    />
        <result property="lopBoxno"    column="lop_boxno"    />
        <result property="lopOutdt"    column="lop_outdt"    />
        <result property="lopSts"    column="lop_sts"    />
        <result property="lopRmk"    column="lop_rmk"    />
        <result property="paramkeysId"    column="paramKeys_id"    />
        <result property="paramKeys"    column="paramKeys"    />
        <result property="paramkeysInsideId"    column="paramKeys_inside_id"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lopBnEncrypt"    column="lop_bn_encrypt"    />
        <result property="lopOrgmcd"    column="lop_orgmcd"    />
        <result property="receiveTime"    column="receive_time"    />
        <result property="lalId"    column="lal_id"    />
        <result property="lopConfirmtime"    column="lop_confirmtime"    />
        <result property="lopFinFlag"    column="lop_fin_flag"    />
        <result property="lopSeparatePacking"    column="lop_separate_packing"    />
        <result property="logNoDeliver"    column="log_no_deliver"    />
        <result property="lopOpcd"    column="lop_opcd"    />
        <result property="lopPickupTime"    column="lop_pickup_time"    />
        <result property="lopInqty"    column="lop_inqty"    />
        <result property="lopBoxnoExport"    column="lop_boxno_export"    />
        <result property="lopPtnoExport"    column="lop_ptno_export"    />
    </resultMap>

    <sql id="selectLogOsPlanVo">
        select id, lop_psn, lop_typ, lop_iotyp, lop_pcd, lop_ppcd, lop_bn, lop_pbn, lop_sbn, lop_qty, lop_dmcd, lop_slcd, lop_tcd, lop_ispart, lop_bit, lop_deliver_cost, lop_unitp, lop_totalp, lop_cargov, lop_ntaxv, lop_tax, log_group_cost, log_logistics_cost, lop_ispart_old, lop_bit_old, lop_unitp_old, lop_totalp_old, lop_cargov_old, lop_ntaxv_old, lop_tax_old, lop_orgmcd_old, lop_bizcd, lop_bizcd_mcd, lop_bizcd_rcd, lop_bizcd_rmcd, lop_ucd, lop_dt, lop_eps, lop_tn, lop_boxno, lop_outdt, lop_sts, lop_rmk, paramKeys_id, paramKeys, paramKeys_inside_id, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, lop_bn_encrypt, lop_orgmcd, receive_time, lal_id, lop_confirmtime, lop_fin_flag, lop_separate_packing, log_no_deliver, lop_opcd, lop_pickup_time, lop_inqty, lop_boxno_export, lop_ptno_export from log_os_plan
    </sql>

    <select id="selectLogOsPlanList" parameterType="LogOsPlan" resultMap="LogOsPlanResult">
        <include refid="selectLogOsPlanVo"/>
        <where>  
            <if test="lopPsn != null  and lopPsn != ''"> and lop_psn = #{lopPsn}</if>
            <if test="lopTyp != null  and lopTyp != ''"> and lop_typ = #{lopTyp}</if>
            <if test="lopIotyp != null  and lopIotyp != ''"> and lop_iotyp = #{lopIotyp}</if>
            <if test="lopPcd != null  and lopPcd != ''"> and lop_pcd = #{lopPcd}</if>
            <if test="lopPpcd != null  and lopPpcd != ''"> and lop_ppcd = #{lopPpcd}</if>
            <if test="lopBn != null  and lopBn != ''"> and lop_bn = #{lopBn}</if>
            <if test="lopPbn != null  and lopPbn != ''"> and lop_pbn = #{lopPbn}</if>
            <if test="lopSbn != null  and lopSbn != ''"> and lop_sbn = #{lopSbn}</if>
            <if test="lopQty != null "> and lop_qty = #{lopQty}</if>
            <if test="lopDmcd != null  and lopDmcd != ''"> and lop_dmcd = #{lopDmcd}</if>
            <if test="lopSlcd != null  and lopSlcd != ''"> and lop_slcd = #{lopSlcd}</if>
            <if test="lopTcd != null  and lopTcd != ''"> and lop_tcd = #{lopTcd}</if>
            <if test="lopIspart != null "> and lop_ispart = #{lopIspart}</if>
            <if test="lopBit != null "> and lop_bit = #{lopBit}</if>
            <if test="lopDeliverCost != null "> and lop_deliver_cost = #{lopDeliverCost}</if>
            <if test="lopUnitp != null "> and lop_unitp = #{lopUnitp}</if>
            <if test="lopTotalp != null "> and lop_totalp = #{lopTotalp}</if>
            <if test="lopCargov != null "> and lop_cargov = #{lopCargov}</if>
            <if test="lopNtaxv != null "> and lop_ntaxv = #{lopNtaxv}</if>
            <if test="lopTax != null "> and lop_tax = #{lopTax}</if>
            <if test="logGroupCost != null "> and log_group_cost = #{logGroupCost}</if>
            <if test="logLogisticsCost != null "> and log_logistics_cost = #{logLogisticsCost}</if>
            <if test="lopIspartOld != null "> and lop_ispart_old = #{lopIspartOld}</if>
            <if test="lopBitOld != null "> and lop_bit_old = #{lopBitOld}</if>
            <if test="lopUnitpOld != null "> and lop_unitp_old = #{lopUnitpOld}</if>
            <if test="lopTotalpOld != null "> and lop_totalp_old = #{lopTotalpOld}</if>
            <if test="lopCargovOld != null "> and lop_cargov_old = #{lopCargovOld}</if>
            <if test="lopNtaxvOld != null "> and lop_ntaxv_old = #{lopNtaxvOld}</if>
            <if test="lopTaxOld != null "> and lop_tax_old = #{lopTaxOld}</if>
            <if test="lopOrgmcdOld != null  and lopOrgmcdOld != ''"> and lop_orgmcd_old = #{lopOrgmcdOld}</if>
            <if test="lopBizcd != null  and lopBizcd != ''"> and lop_bizcd = #{lopBizcd}</if>
            <if test="lopBizcdMcd != null  and lopBizcdMcd != ''"> and lop_bizcd_mcd = #{lopBizcdMcd}</if>
            <if test="lopBizcdRcd != null  and lopBizcdRcd != ''"> and lop_bizcd_rcd = #{lopBizcdRcd}</if>
            <if test="lopBizcdRmcd != null  and lopBizcdRmcd != ''"> and lop_bizcd_rmcd = #{lopBizcdRmcd}</if>
            <if test="lopUcd != null  and lopUcd != ''"> and lop_ucd = #{lopUcd}</if>
            <if test="lopDt != null "> and lop_dt = #{lopDt}</if>
            <if test="lopEps != null  and lopEps != ''"> and lop_eps = #{lopEps}</if>
            <if test="lopTn != null  and lopTn != ''"> and lop_tn = #{lopTn}</if>
            <if test="lopBoxno != null "> and lop_boxno = #{lopBoxno}</if>
            <if test="lopOutdt != null "> and lop_outdt = #{lopOutdt}</if>
            <if test="lopSts != null  and lopSts != ''"> and lop_sts = #{lopSts}</if>
            <if test="lopRmk != null  and lopRmk != ''"> and lop_rmk = #{lopRmk}</if>
            <if test="paramkeysId != null "> and paramKeys_id = #{paramkeysId}</if>
            <if test="paramKeys != null  and paramKeys != ''"> and paramKeys = #{paramKeys}</if>
            <if test="paramkeysInsideId != null "> and paramKeys_inside_id = #{paramkeysInsideId}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="lopBnEncrypt != null  and lopBnEncrypt != ''"> and lop_bn_encrypt = #{lopBnEncrypt}</if>
            <if test="lopOrgmcd != null  and lopOrgmcd != ''"> and lop_orgmcd = #{lopOrgmcd}</if>
            <if test="receiveTime != null  and receiveTime != ''"> and receive_time = #{receiveTime}</if>
            <if test="lalId != null  and lalId != ''"> and lal_id = #{lalId}</if>
            <if test="lopConfirmtime != null  and lopConfirmtime != ''"> and lop_confirmtime = #{lopConfirmtime}</if>
            <if test="lopFinFlag != null "> and lop_fin_flag = #{lopFinFlag}</if>
            <if test="lopSeparatePacking != null  and lopSeparatePacking != ''"> and lop_separate_packing = #{lopSeparatePacking}</if>
            <if test="logNoDeliver != null  and logNoDeliver != ''"> and log_no_deliver = #{logNoDeliver}</if>
            <if test="lopOpcd != null  and lopOpcd != ''"> and lop_opcd = #{lopOpcd}</if>
            <if test="lopPickupTime != null  and lopPickupTime != ''"> and lop_pickup_time = #{lopPickupTime}</if>
            <if test="lopInqty != null "> and lop_inqty = #{lopInqty}</if>
            <if test="lopBoxnoExport != null  and lopBoxnoExport != ''"> and lop_boxno_export = #{lopBoxnoExport}</if>
            <if test="lopPtnoExport != null  and lopPtnoExport != ''"> and lop_ptno_export = #{lopPtnoExport}</if>
        </where>
    </select>
    
    <select id="selectLogOsPlanById" parameterType="String" resultMap="LogOsPlanResult">
        <include refid="selectLogOsPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertLogOsPlan" parameterType="LogOsPlan" useGeneratedKeys="true" keyProperty="id">
        insert into log_os_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lopPsn != null">lop_psn,</if>
            <if test="lopTyp != null">lop_typ,</if>
            <if test="lopIotyp != null">lop_iotyp,</if>
            <if test="lopPcd != null">lop_pcd,</if>
            <if test="lopPpcd != null">lop_ppcd,</if>
            <if test="lopBn != null">lop_bn,</if>
            <if test="lopPbn != null">lop_pbn,</if>
            <if test="lopSbn != null">lop_sbn,</if>
            <if test="lopQty != null">lop_qty,</if>
            <if test="lopDmcd != null">lop_dmcd,</if>
            <if test="lopSlcd != null">lop_slcd,</if>
            <if test="lopTcd != null">lop_tcd,</if>
            <if test="lopIspart != null">lop_ispart,</if>
            <if test="lopBit != null">lop_bit,</if>
            <if test="lopDeliverCost != null">lop_deliver_cost,</if>
            <if test="lopUnitp != null">lop_unitp,</if>
            <if test="lopTotalp != null">lop_totalp,</if>
            <if test="lopCargov != null">lop_cargov,</if>
            <if test="lopNtaxv != null">lop_ntaxv,</if>
            <if test="lopTax != null">lop_tax,</if>
            <if test="logGroupCost != null">log_group_cost,</if>
            <if test="logLogisticsCost != null">log_logistics_cost,</if>
            <if test="lopIspartOld != null">lop_ispart_old,</if>
            <if test="lopBitOld != null">lop_bit_old,</if>
            <if test="lopUnitpOld != null">lop_unitp_old,</if>
            <if test="lopTotalpOld != null">lop_totalp_old,</if>
            <if test="lopCargovOld != null">lop_cargov_old,</if>
            <if test="lopNtaxvOld != null">lop_ntaxv_old,</if>
            <if test="lopTaxOld != null">lop_tax_old,</if>
            <if test="lopOrgmcdOld != null">lop_orgmcd_old,</if>
            <if test="lopBizcd != null">lop_bizcd,</if>
            <if test="lopBizcdMcd != null">lop_bizcd_mcd,</if>
            <if test="lopBizcdRcd != null">lop_bizcd_rcd,</if>
            <if test="lopBizcdRmcd != null">lop_bizcd_rmcd,</if>
            <if test="lopUcd != null">lop_ucd,</if>
            <if test="lopDt != null">lop_dt,</if>
            <if test="lopEps != null">lop_eps,</if>
            <if test="lopTn != null">lop_tn,</if>
            <if test="lopBoxno != null">lop_boxno,</if>
            <if test="lopOutdt != null">lop_outdt,</if>
            <if test="lopSts != null">lop_sts,</if>
            <if test="lopRmk != null">lop_rmk,</if>
            <if test="paramkeysId != null">paramKeys_id,</if>
            <if test="paramKeys != null">paramKeys,</if>
            <if test="paramkeysInsideId != null">paramKeys_inside_id,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="lopBnEncrypt != null">lop_bn_encrypt,</if>
            <if test="lopOrgmcd != null">lop_orgmcd,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="lalId != null">lal_id,</if>
            <if test="lopConfirmtime != null">lop_confirmtime,</if>
            <if test="lopFinFlag != null">lop_fin_flag,</if>
            <if test="lopSeparatePacking != null">lop_separate_packing,</if>
            <if test="logNoDeliver != null">log_no_deliver,</if>
            <if test="lopOpcd != null">lop_opcd,</if>
            <if test="lopPickupTime != null">lop_pickup_time,</if>
            <if test="lopInqty != null">lop_inqty,</if>
            <if test="lopBoxnoExport != null">lop_boxno_export,</if>
            <if test="lopPtnoExport != null">lop_ptno_export,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lopPsn != null">#{lopPsn},</if>
            <if test="lopTyp != null">#{lopTyp},</if>
            <if test="lopIotyp != null">#{lopIotyp},</if>
            <if test="lopPcd != null">#{lopPcd},</if>
            <if test="lopPpcd != null">#{lopPpcd},</if>
            <if test="lopBn != null">#{lopBn},</if>
            <if test="lopPbn != null">#{lopPbn},</if>
            <if test="lopSbn != null">#{lopSbn},</if>
            <if test="lopQty != null">#{lopQty},</if>
            <if test="lopDmcd != null">#{lopDmcd},</if>
            <if test="lopSlcd != null">#{lopSlcd},</if>
            <if test="lopTcd != null">#{lopTcd},</if>
            <if test="lopIspart != null">#{lopIspart},</if>
            <if test="lopBit != null">#{lopBit},</if>
            <if test="lopDeliverCost != null">#{lopDeliverCost},</if>
            <if test="lopUnitp != null">#{lopUnitp},</if>
            <if test="lopTotalp != null">#{lopTotalp},</if>
            <if test="lopCargov != null">#{lopCargov},</if>
            <if test="lopNtaxv != null">#{lopNtaxv},</if>
            <if test="lopTax != null">#{lopTax},</if>
            <if test="logGroupCost != null">#{logGroupCost},</if>
            <if test="logLogisticsCost != null">#{logLogisticsCost},</if>
            <if test="lopIspartOld != null">#{lopIspartOld},</if>
            <if test="lopBitOld != null">#{lopBitOld},</if>
            <if test="lopUnitpOld != null">#{lopUnitpOld},</if>
            <if test="lopTotalpOld != null">#{lopTotalpOld},</if>
            <if test="lopCargovOld != null">#{lopCargovOld},</if>
            <if test="lopNtaxvOld != null">#{lopNtaxvOld},</if>
            <if test="lopTaxOld != null">#{lopTaxOld},</if>
            <if test="lopOrgmcdOld != null">#{lopOrgmcdOld},</if>
            <if test="lopBizcd != null">#{lopBizcd},</if>
            <if test="lopBizcdMcd != null">#{lopBizcdMcd},</if>
            <if test="lopBizcdRcd != null">#{lopBizcdRcd},</if>
            <if test="lopBizcdRmcd != null">#{lopBizcdRmcd},</if>
            <if test="lopUcd != null">#{lopUcd},</if>
            <if test="lopDt != null">#{lopDt},</if>
            <if test="lopEps != null">#{lopEps},</if>
            <if test="lopTn != null">#{lopTn},</if>
            <if test="lopBoxno != null">#{lopBoxno},</if>
            <if test="lopOutdt != null">#{lopOutdt},</if>
            <if test="lopSts != null">#{lopSts},</if>
            <if test="lopRmk != null">#{lopRmk},</if>
            <if test="paramkeysId != null">#{paramkeysId},</if>
            <if test="paramKeys != null">#{paramKeys},</if>
            <if test="paramkeysInsideId != null">#{paramkeysInsideId},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="lopBnEncrypt != null">#{lopBnEncrypt},</if>
            <if test="lopOrgmcd != null">#{lopOrgmcd},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="lalId != null">#{lalId},</if>
            <if test="lopConfirmtime != null">#{lopConfirmtime},</if>
            <if test="lopFinFlag != null">#{lopFinFlag},</if>
            <if test="lopSeparatePacking != null">#{lopSeparatePacking},</if>
            <if test="logNoDeliver != null">#{logNoDeliver},</if>
            <if test="lopOpcd != null">#{lopOpcd},</if>
            <if test="lopPickupTime != null">#{lopPickupTime},</if>
            <if test="lopInqty != null">#{lopInqty},</if>
            <if test="lopBoxnoExport != null">#{lopBoxnoExport},</if>
            <if test="lopPtnoExport != null">#{lopPtnoExport},</if>
         </trim>
    </insert>

    <update id="updateLogOsPlan" parameterType="LogOsPlan">
        update log_os_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="lopPsn != null">lop_psn = #{lopPsn},</if>
            <if test="lopTyp != null">lop_typ = #{lopTyp},</if>
            <if test="lopIotyp != null">lop_iotyp = #{lopIotyp},</if>
            <if test="lopPcd != null">lop_pcd = #{lopPcd},</if>
            <if test="lopPpcd != null">lop_ppcd = #{lopPpcd},</if>
            <if test="lopBn != null">lop_bn = #{lopBn},</if>
            <if test="lopPbn != null">lop_pbn = #{lopPbn},</if>
            <if test="lopSbn != null">lop_sbn = #{lopSbn},</if>
            <if test="lopQty != null">lop_qty = #{lopQty},</if>
            <if test="lopDmcd != null">lop_dmcd = #{lopDmcd},</if>
            <if test="lopSlcd != null">lop_slcd = #{lopSlcd},</if>
            <if test="lopTcd != null">lop_tcd = #{lopTcd},</if>
            <if test="lopIspart != null">lop_ispart = #{lopIspart},</if>
            <if test="lopBit != null">lop_bit = #{lopBit},</if>
            <if test="lopDeliverCost != null">lop_deliver_cost = #{lopDeliverCost},</if>
            <if test="lopUnitp != null">lop_unitp = #{lopUnitp},</if>
            <if test="lopTotalp != null">lop_totalp = #{lopTotalp},</if>
            <if test="lopCargov != null">lop_cargov = #{lopCargov},</if>
            <if test="lopNtaxv != null">lop_ntaxv = #{lopNtaxv},</if>
            <if test="lopTax != null">lop_tax = #{lopTax},</if>
            <if test="logGroupCost != null">log_group_cost = #{logGroupCost},</if>
            <if test="logLogisticsCost != null">log_logistics_cost = #{logLogisticsCost},</if>
            <if test="lopIspartOld != null">lop_ispart_old = #{lopIspartOld},</if>
            <if test="lopBitOld != null">lop_bit_old = #{lopBitOld},</if>
            <if test="lopUnitpOld != null">lop_unitp_old = #{lopUnitpOld},</if>
            <if test="lopTotalpOld != null">lop_totalp_old = #{lopTotalpOld},</if>
            <if test="lopCargovOld != null">lop_cargov_old = #{lopCargovOld},</if>
            <if test="lopNtaxvOld != null">lop_ntaxv_old = #{lopNtaxvOld},</if>
            <if test="lopTaxOld != null">lop_tax_old = #{lopTaxOld},</if>
            <if test="lopOrgmcdOld != null">lop_orgmcd_old = #{lopOrgmcdOld},</if>
            <if test="lopBizcd != null">lop_bizcd = #{lopBizcd},</if>
            <if test="lopBizcdMcd != null">lop_bizcd_mcd = #{lopBizcdMcd},</if>
            <if test="lopBizcdRcd != null">lop_bizcd_rcd = #{lopBizcdRcd},</if>
            <if test="lopBizcdRmcd != null">lop_bizcd_rmcd = #{lopBizcdRmcd},</if>
            <if test="lopUcd != null">lop_ucd = #{lopUcd},</if>
            <if test="lopDt != null">lop_dt = #{lopDt},</if>
            <if test="lopEps != null">lop_eps = #{lopEps},</if>
            <if test="lopTn != null">lop_tn = #{lopTn},</if>
            <if test="lopBoxno != null">lop_boxno = #{lopBoxno},</if>
            <if test="lopOutdt != null">lop_outdt = #{lopOutdt},</if>
            <if test="lopSts != null">lop_sts = #{lopSts},</if>
            <if test="lopRmk != null">lop_rmk = #{lopRmk},</if>
            <if test="paramkeysId != null">paramKeys_id = #{paramkeysId},</if>
            <if test="paramKeys != null">paramKeys = #{paramKeys},</if>
            <if test="paramkeysInsideId != null">paramKeys_inside_id = #{paramkeysInsideId},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="lopBnEncrypt != null">lop_bn_encrypt = #{lopBnEncrypt},</if>
            <if test="lopOrgmcd != null">lop_orgmcd = #{lopOrgmcd},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="lalId != null">lal_id = #{lalId},</if>
            <if test="lopConfirmtime != null">lop_confirmtime = #{lopConfirmtime},</if>
            <if test="lopFinFlag != null">lop_fin_flag = #{lopFinFlag},</if>
            <if test="lopSeparatePacking != null">lop_separate_packing = #{lopSeparatePacking},</if>
            <if test="logNoDeliver != null">log_no_deliver = #{logNoDeliver},</if>
            <if test="lopOpcd != null">lop_opcd = #{lopOpcd},</if>
            <if test="lopPickupTime != null">lop_pickup_time = #{lopPickupTime},</if>
            <if test="lopInqty != null">lop_inqty = #{lopInqty},</if>
            <if test="lopBoxnoExport != null">lop_boxno_export = #{lopBoxnoExport},</if>
            <if test="lopPtnoExport != null">lop_ptno_export = #{lopPtnoExport},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLogOsPlanById" parameterType="Long">
        delete from log_os_plan where id = #{id}
    </delete>

    <delete id="deleteLogOsPlanByIds" parameterType="String">
        delete from log_os_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>