<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.FinAccMasterRecordsUniqMapper">
    
    <resultMap type="FinAccMasterRecordsUniq" id="FinAccMasterRecordsUniqResult">
        <result property="id"    column="id"    />
        <result property="famrProcess"    column="famr_process"    />
        <result property="famrEntity"    column="famr_entity"    />
        <result property="famrEdition"    column="famr_edition"    />
        <result property="famrAddtime"    column="famr_addtime"    />
        <result property="famrEditionRmk"    column="famr_edition_rmk"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
    </resultMap>

    <sql id="selectFinAccMasterRecordsUniqVo">
        select id, famr_process, famr_entity, famr_edition, famr_addtime, famr_edition_rmk, r_cre_kid, r_cre_dt from financial_account_db.fin_acc_master_records_uniq
    </sql>

    <select id="selectFinAccMasterRecordsUniqList" parameterType="FinAccMasterRecordsUniq" resultMap="FinAccMasterRecordsUniqResult">
        <include refid="selectFinAccMasterRecordsUniqVo"/>
        <where>  
            <if test="famrProcess != null  and famrProcess != ''"> and famr_process = #{famrProcess}</if>
            <if test="famrEntity != null  and famrEntity != ''"> and famr_entity = #{famrEntity}</if>
            <if test="famrEdition != null  and famrEdition != ''"> and famr_edition = #{famrEdition}</if>
            <if test="famrAddtime != null "> and famr_addtime = #{famrAddtime}</if>
            <if test="famrEditionRmk != null  and famrEditionRmk != ''"> and famr_edition_rmk = #{famrEditionRmk}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
        </where>
    </select>

    <select id="selectFinAccMasterRecordsById" parameterType="FinAccMasterRecordsUniq" resultMap="FinAccMasterRecordsUniqResult">
        <include refid="selectFinAccMasterRecordsUniqVo"/>
        <where>
            <if test="famrProcess != null and famrProcess != ''"> and famr_process = #{famrProcess}</if>
            <if test="famrEntity != null and famrEntity == 'BM10000050'"> and id like 'ahzs%'</if>
            <if test="famrEntity != null and famrEntity == 'BM10000057'"> and id like 'zssf%'</if>
        </where>
    </select>
    
    <select id="selectFinAccMasterRecordsUniqById" parameterType="String" resultMap="FinAccMasterRecordsUniqResult">
        <include refid="selectFinAccMasterRecordsUniqVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinAccMasterRecordsUniq" parameterType="FinAccMasterRecordsUniq">
        insert into financial_account_db.fin_acc_master_records_uniq
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="famrProcess != null">famr_process,</if>
            <if test="famrEntity != null">famr_entity,</if>
            <if test="famrEdition != null">famr_edition,</if>
            <if test="famrAddtime != null">famr_addtime,</if>
            <if test="famrEditionRmk != null">famr_edition_rmk,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="famrProcess != null">#{famrProcess},</if>
            <if test="famrEntity != null">#{famrEntity},</if>
            <if test="famrEdition != null">#{famrEdition},</if>
            <if test="famrAddtime != null">#{famrAddtime},</if>
            <if test="famrEditionRmk != null">#{famrEditionRmk},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
         </trim>
    </insert>

    <update id="updateFinAccMasterRecordsUniq" parameterType="FinAccMasterRecordsUniq">
        update financial_account_db.fin_acc_master_records_uniq
        <trim prefix="SET" suffixOverrides=",">
            <if test="famrProcess != null">famr_process = #{famrProcess},</if>
            <if test="famrEntity != null">famr_entity = #{famrEntity},</if>
            <if test="famrEdition != null">famr_edition = #{famrEdition},</if>
            <if test="famrAddtime != null">famr_addtime = #{famrAddtime},</if>
            <if test="famrEditionRmk != null">famr_edition_rmk = #{famrEditionRmk},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinAccMasterRecordsUniqById" parameterType="String">
        delete from financial_account_db.fin_acc_master_records_uniq where id = #{id}
    </delete>

    <delete id="deleteFinAccMasterRecordsUniqByIds" parameterType="String">
        delete from financial_account_db.fin_acc_master_records_uniq where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>