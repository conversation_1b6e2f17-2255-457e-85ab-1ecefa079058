package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_客户组织_主对象 bas_custorg_mst
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class BasCustorgMst extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 客户组织编号 */
    private String bcmCd;

    /** 客户组织名称 */
    @Excel(name = "客户组织名称")
    private String bcmNm;

    /** 客户组织全称 */
    @Excel(name = "客户组织全称")
    private String bcmFnm;

    /** 名称缩写 */
    @Excel(name = "名称缩写")
    private String bcmNmabb;

    /** 客户组织名简写 */
    @Excel(name = "客户组织名简写")
    private String bcmXyz;

    /** 上级组织编号 */
    @Excel(name = "上级组织编号")
    private String bcmPcd;

    /** 客户组织类别 */
    @Excel(name = "客户组织类别")
    private String bcmTyp;

    /** 客户组织级别 */
    @Excel(name = "客户组织级别")
    private String bcmLvl;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String bcmFp;

    /** 手机号 */
    @Excel(name = "手机号")
    private String bcmMp;

    /** 传真 */
    @Excel(name = "传真")
    private String bcmFax;

    /** 省份CD */
    @Excel(name = "省份CD")
    private String bcmPrcd;

    /** 省份NM */
    @Excel(name = "省份NM")
    private String bcmPrnm;

    /** 城市CD */
    @Excel(name = "城市CD")
    private String bcmCitycd;

    /** 城市 */
    @Excel(name = "城市")
    private String bcmCity;

    /** 区县CD */
    @Excel(name = "区县CD")
    private String bcmAreacd;

    /** 区县NM */
    @Excel(name = "区县NM")
    private String bcmAreanm;

    /** 街道CD */
    @Excel(name = "街道CD")
    private String bcmJdcd;

    /** 街道NM */
    @Excel(name = "街道NM")
    private String bcmJdnm;

    /** 开票员 */
    @Excel(name = "开票员")
    private String bcmInvcMan;

    /** 联系地址 */
    @Excel(name = "联系地址")
    private String bcmAddr;

    /** 销售员 已弃用 现取销售职能组 */
    @Excel(name = "销售员 已弃用 现取销售职能组")
    private String bcmSmcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bcmSmcd3a;

    /** 客户折扣 */
    @Excel(name = "客户折扣")
    private BigDecimal bcmDc;

    /** 一级组织编号 */
    @Excel(name = "一级组织编号")
    private String bcmFstOrgcd;

    /** 所属区域CD */
    @Excel(name = "所属区域CD")
    private String bcmAreaCd;

    /** 结算方式 */
    @Excel(name = "结算方式")
    private String bcmPaytyp;

    /** 发送邮件 */
    @Excel(name = "发送邮件")
    private String bcmSendyn;

    /** 发票是否寄送 */
    @Excel(name = "发票是否寄送")
    private String bcmIseps;

    /** 发送短信 */
    @Excel(name = "发送短信")
    private String bcmSmsyn;

    /** 随货附COA */
    @Excel(name = "随货附COA")
    private String bcmCoayn;

    /** 是否自动排票 */
    @Excel(name = "是否自动排票")
    private String bcmInvoiceyn;

    /** 排票限额金额 */
    @Excel(name = "排票限额金额")
    private BigDecimal bcmInvoicexe;

    /** 平台单号是否打印条形码 */
    @Excel(name = "平台单号是否打印条形码")
    private String bcmBarcode;

    /** 销售打印类型 */
    @Excel(name = "销售打印类型")
    private String bcmPrintyn;

    /** 销售物流备注 */
    @Excel(name = "销售物流备注")
    private String bcmLrmk;

    /** 所属会员等级CD */
    @Excel(name = "所属会员等级CD")
    private String bcmMbgCd;

    /** 是否叶子节点  默认为否 */
    @Excel(name = "是否叶子节点  默认为否")
    private Integer bcmIsleaf;

    /** 财务记账分类编号 */
    @Excel(name = "财务记账分类编号")
    private String bcmFinanceCode;

    /** 3a组织编号 */
    @Excel(name = "3a组织编号")
    private String aBcmCd;

    /** 职能树表编号 */
    @Excel(name = "职能树表编号")
    private String bcmPscd;

    /** 组织编号关联交易使用 */
    @Excel(name = "组织编号关联交易使用")
    private String bcmOrgcd;

    /** 销售职能树2(用于过渡期) */
    @Excel(name = "销售职能树2(用于过渡期)")
    private String bcmPscdTwo;

    /** 危化品限制 */
    @Excel(name = "危化品限制")
    private String bcmDcls;

    /** 默认的销售合同抬头 */
    @Excel(name = "默认的销售合同抬头")
    private String bcmOcd;

    /** 客户等级 */
    @Excel(name = "客户等级")
    private String bcmRank;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Integer rDel;

    /** 专利产品购买权限 */
    @Excel(name = "专利产品购买权限")
    private String bcmAccredit;

    /** 默认账期 （比如30天） */
    @Excel(name = "默认账期 ", readConverterExp = "比=如30天")
    private Integer bcmCac;

    /** 默认开票周期 （比如30天） */
    @Excel(name = "默认开票周期 ", readConverterExp = "比=如30天")
    private Integer bcmInvoiceCac;

    /** 黑名单客户 00：不是 01：是 */
    @Excel(name = "黑名单客户 00：不是 01：是")
    private String bcmBlaList;

    /** 经销商是否具备购买易制爆产品资格 00：否 01：是 */
    @Excel(name = "经销商是否具备购买易制爆产品资格 00：否 01：是")
    private String bcmBuyExprs;

    /** 票面备注 */
    @Excel(name = "票面备注")
    private String bcmInvcrmk;

    /** 第三方平台名称 */
    @Excel(name = "第三方平台名称")
    private String bcmThirdBoard;

    /** 是否跳过额度、折扣限制 00：否 01：是 */
    @Excel(name = "是否跳过额度、折扣限制 00：否 01：是")
    private String bcmQuotaLimit;

    /** 同控主体 */
    @Excel(name = "同控主体")
    private String bcmConAgent;

    /** 币种 */
    @Excel(name = "币种")
    private String bcmCurrency;

    /** 所属合资公司 */
    @Excel(name = "所属合资公司")
    private String bcmBccd;

    /** 国家CD */
    @Excel(name = "国家CD")
    private String bcmNationcd;

    /** 国家NM */
    @Excel(name = "国家NM")
    private String bcmNationnm;

    /** 客户等级 */
    @Excel(name = "客户等级")
    private String bcmClientgrade;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBcmCd(String bcmCd) 
    {
        this.bcmCd = bcmCd;
    }

    public String getBcmCd() 
    {
        return bcmCd;
    }

    public void setBcmNm(String bcmNm) 
    {
        this.bcmNm = bcmNm;
    }

    public String getBcmNm() 
    {
        return bcmNm;
    }

    public void setBcmFnm(String bcmFnm) 
    {
        this.bcmFnm = bcmFnm;
    }

    public String getBcmFnm() 
    {
        return bcmFnm;
    }

    public void setBcmNmabb(String bcmNmabb) 
    {
        this.bcmNmabb = bcmNmabb;
    }

    public String getBcmNmabb() 
    {
        return bcmNmabb;
    }

    public void setBcmXyz(String bcmXyz) 
    {
        this.bcmXyz = bcmXyz;
    }

    public String getBcmXyz() 
    {
        return bcmXyz;
    }

    public void setBcmPcd(String bcmPcd) 
    {
        this.bcmPcd = bcmPcd;
    }

    public String getBcmPcd() 
    {
        return bcmPcd;
    }

    public void setBcmTyp(String bcmTyp) 
    {
        this.bcmTyp = bcmTyp;
    }

    public String getBcmTyp() 
    {
        return bcmTyp;
    }

    public void setBcmLvl(String bcmLvl) 
    {
        this.bcmLvl = bcmLvl;
    }

    public String getBcmLvl() 
    {
        return bcmLvl;
    }

    public void setBcmFp(String bcmFp) 
    {
        this.bcmFp = bcmFp;
    }

    public String getBcmFp() 
    {
        return bcmFp;
    }

    public void setBcmMp(String bcmMp) 
    {
        this.bcmMp = bcmMp;
    }

    public String getBcmMp() 
    {
        return bcmMp;
    }

    public void setBcmFax(String bcmFax) 
    {
        this.bcmFax = bcmFax;
    }

    public String getBcmFax() 
    {
        return bcmFax;
    }

    public void setBcmPrcd(String bcmPrcd) 
    {
        this.bcmPrcd = bcmPrcd;
    }

    public String getBcmPrcd() 
    {
        return bcmPrcd;
    }

    public void setBcmPrnm(String bcmPrnm) 
    {
        this.bcmPrnm = bcmPrnm;
    }

    public String getBcmPrnm() 
    {
        return bcmPrnm;
    }

    public void setBcmCitycd(String bcmCitycd) 
    {
        this.bcmCitycd = bcmCitycd;
    }

    public String getBcmCitycd() 
    {
        return bcmCitycd;
    }

    public void setBcmCity(String bcmCity) 
    {
        this.bcmCity = bcmCity;
    }

    public String getBcmCity() 
    {
        return bcmCity;
    }

    public void setBcmAreacd(String bcmAreacd) 
    {
        this.bcmAreacd = bcmAreacd;
    }

    public String getBcmAreacd() 
    {
        return bcmAreacd;
    }

    public void setBcmAreanm(String bcmAreanm) 
    {
        this.bcmAreanm = bcmAreanm;
    }

    public String getBcmAreanm() 
    {
        return bcmAreanm;
    }

    public void setBcmJdcd(String bcmJdcd) 
    {
        this.bcmJdcd = bcmJdcd;
    }

    public String getBcmJdcd() 
    {
        return bcmJdcd;
    }

    public void setBcmJdnm(String bcmJdnm) 
    {
        this.bcmJdnm = bcmJdnm;
    }

    public String getBcmJdnm() 
    {
        return bcmJdnm;
    }

    public void setBcmInvcMan(String bcmInvcMan) 
    {
        this.bcmInvcMan = bcmInvcMan;
    }

    public String getBcmInvcMan() 
    {
        return bcmInvcMan;
    }

    public void setBcmAddr(String bcmAddr) 
    {
        this.bcmAddr = bcmAddr;
    }

    public String getBcmAddr() 
    {
        return bcmAddr;
    }

    public void setBcmSmcd(String bcmSmcd) 
    {
        this.bcmSmcd = bcmSmcd;
    }

    public String getBcmSmcd() 
    {
        return bcmSmcd;
    }

    public void setBcmSmcd3a(String bcmSmcd3a) 
    {
        this.bcmSmcd3a = bcmSmcd3a;
    }

    public String getBcmSmcd3a() 
    {
        return bcmSmcd3a;
    }

    public void setBcmDc(BigDecimal bcmDc) 
    {
        this.bcmDc = bcmDc;
    }

    public BigDecimal getBcmDc() 
    {
        return bcmDc;
    }

    public void setBcmFstOrgcd(String bcmFstOrgcd) 
    {
        this.bcmFstOrgcd = bcmFstOrgcd;
    }

    public String getBcmFstOrgcd() 
    {
        return bcmFstOrgcd;
    }

    public void setBcmAreaCd(String bcmAreaCd) 
    {
        this.bcmAreaCd = bcmAreaCd;
    }

    public String getBcmAreaCd() 
    {
        return bcmAreaCd;
    }

    public void setBcmPaytyp(String bcmPaytyp) 
    {
        this.bcmPaytyp = bcmPaytyp;
    }

    public String getBcmPaytyp() 
    {
        return bcmPaytyp;
    }

    public void setBcmSendyn(String bcmSendyn) 
    {
        this.bcmSendyn = bcmSendyn;
    }

    public String getBcmSendyn() 
    {
        return bcmSendyn;
    }

    public void setBcmIseps(String bcmIseps) 
    {
        this.bcmIseps = bcmIseps;
    }

    public String getBcmIseps() 
    {
        return bcmIseps;
    }

    public void setBcmSmsyn(String bcmSmsyn) 
    {
        this.bcmSmsyn = bcmSmsyn;
    }

    public String getBcmSmsyn() 
    {
        return bcmSmsyn;
    }

    public void setBcmCoayn(String bcmCoayn) 
    {
        this.bcmCoayn = bcmCoayn;
    }

    public String getBcmCoayn() 
    {
        return bcmCoayn;
    }

    public void setBcmInvoiceyn(String bcmInvoiceyn) 
    {
        this.bcmInvoiceyn = bcmInvoiceyn;
    }

    public String getBcmInvoiceyn() 
    {
        return bcmInvoiceyn;
    }

    public void setBcmInvoicexe(BigDecimal bcmInvoicexe) 
    {
        this.bcmInvoicexe = bcmInvoicexe;
    }

    public BigDecimal getBcmInvoicexe() 
    {
        return bcmInvoicexe;
    }

    public void setBcmBarcode(String bcmBarcode) 
    {
        this.bcmBarcode = bcmBarcode;
    }

    public String getBcmBarcode() 
    {
        return bcmBarcode;
    }

    public void setBcmPrintyn(String bcmPrintyn) 
    {
        this.bcmPrintyn = bcmPrintyn;
    }

    public String getBcmPrintyn() 
    {
        return bcmPrintyn;
    }

    public void setBcmLrmk(String bcmLrmk) 
    {
        this.bcmLrmk = bcmLrmk;
    }

    public String getBcmLrmk() 
    {
        return bcmLrmk;
    }

    public void setBcmMbgCd(String bcmMbgCd) 
    {
        this.bcmMbgCd = bcmMbgCd;
    }

    public String getBcmMbgCd() 
    {
        return bcmMbgCd;
    }

    public void setBcmIsleaf(Integer bcmIsleaf) 
    {
        this.bcmIsleaf = bcmIsleaf;
    }

    public Integer getBcmIsleaf() 
    {
        return bcmIsleaf;
    }

    public void setBcmFinanceCode(String bcmFinanceCode) 
    {
        this.bcmFinanceCode = bcmFinanceCode;
    }

    public String getBcmFinanceCode() 
    {
        return bcmFinanceCode;
    }

    public void set3aBcmCd(String aBcmCd)
    {
        this.aBcmCd = aBcmCd;
    }

    public String get3aBcmCd() 
    {
        return aBcmCd;
    }

    public void setBcmPscd(String bcmPscd) 
    {
        this.bcmPscd = bcmPscd;
    }

    public String getBcmPscd() 
    {
        return bcmPscd;
    }

    public void setBcmOrgcd(String bcmOrgcd) 
    {
        this.bcmOrgcd = bcmOrgcd;
    }

    public String getBcmOrgcd() 
    {
        return bcmOrgcd;
    }

    public void setBcmPscdTwo(String bcmPscdTwo) 
    {
        this.bcmPscdTwo = bcmPscdTwo;
    }

    public String getBcmPscdTwo() 
    {
        return bcmPscdTwo;
    }

    public void setBcmDcls(String bcmDcls) 
    {
        this.bcmDcls = bcmDcls;
    }

    public String getBcmDcls() 
    {
        return bcmDcls;
    }

    public void setBcmOcd(String bcmOcd) 
    {
        this.bcmOcd = bcmOcd;
    }

    public String getBcmOcd() 
    {
        return bcmOcd;
    }

    public void setBcmRank(String bcmRank) 
    {
        this.bcmRank = bcmRank;
    }

    public String getBcmRank() 
    {
        return bcmRank;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Integer rDel) 
    {
        this.rDel = rDel;
    }

    public Integer getrDel() 
    {
        return rDel;
    }

    public void setBcmAccredit(String bcmAccredit) 
    {
        this.bcmAccredit = bcmAccredit;
    }

    public String getBcmAccredit() 
    {
        return bcmAccredit;
    }

    public void setBcmCac(Integer bcmCac) 
    {
        this.bcmCac = bcmCac;
    }

    public Integer getBcmCac() 
    {
        return bcmCac;
    }

    public void setBcmInvoiceCac(Integer bcmInvoiceCac) 
    {
        this.bcmInvoiceCac = bcmInvoiceCac;
    }

    public Integer getBcmInvoiceCac() 
    {
        return bcmInvoiceCac;
    }

    public void setBcmBlaList(String bcmBlaList) 
    {
        this.bcmBlaList = bcmBlaList;
    }

    public String getBcmBlaList() 
    {
        return bcmBlaList;
    }

    public void setBcmBuyExprs(String bcmBuyExprs) 
    {
        this.bcmBuyExprs = bcmBuyExprs;
    }

    public String getBcmBuyExprs() 
    {
        return bcmBuyExprs;
    }

    public void setBcmInvcrmk(String bcmInvcrmk) 
    {
        this.bcmInvcrmk = bcmInvcrmk;
    }

    public String getBcmInvcrmk() 
    {
        return bcmInvcrmk;
    }

    public void setBcmThirdBoard(String bcmThirdBoard) 
    {
        this.bcmThirdBoard = bcmThirdBoard;
    }

    public String getBcmThirdBoard() 
    {
        return bcmThirdBoard;
    }

    public void setBcmQuotaLimit(String bcmQuotaLimit) 
    {
        this.bcmQuotaLimit = bcmQuotaLimit;
    }

    public String getBcmQuotaLimit() 
    {
        return bcmQuotaLimit;
    }

    public void setBcmConAgent(String bcmConAgent) 
    {
        this.bcmConAgent = bcmConAgent;
    }

    public String getBcmConAgent() 
    {
        return bcmConAgent;
    }

    public void setBcmCurrency(String bcmCurrency) 
    {
        this.bcmCurrency = bcmCurrency;
    }

    public String getBcmCurrency() 
    {
        return bcmCurrency;
    }

    public void setBcmBccd(String bcmBccd) 
    {
        this.bcmBccd = bcmBccd;
    }

    public String getBcmBccd() 
    {
        return bcmBccd;
    }

    public void setBcmNationcd(String bcmNationcd) 
    {
        this.bcmNationcd = bcmNationcd;
    }

    public String getBcmNationcd() 
    {
        return bcmNationcd;
    }

    public void setBcmNationnm(String bcmNationnm) 
    {
        this.bcmNationnm = bcmNationnm;
    }

    public String getBcmNationnm() 
    {
        return bcmNationnm;
    }

    public void setBcmClientgrade(String bcmClientgrade) 
    {
        this.bcmClientgrade = bcmClientgrade;
    }

    public String getBcmClientgrade() 
    {
        return bcmClientgrade;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bcmCd", getBcmCd())
            .append("bcmNm", getBcmNm())
            .append("bcmFnm", getBcmFnm())
            .append("bcmNmabb", getBcmNmabb())
            .append("bcmXyz", getBcmXyz())
            .append("bcmPcd", getBcmPcd())
            .append("bcmTyp", getBcmTyp())
            .append("bcmLvl", getBcmLvl())
            .append("bcmFp", getBcmFp())
            .append("bcmMp", getBcmMp())
            .append("bcmFax", getBcmFax())
            .append("bcmPrcd", getBcmPrcd())
            .append("bcmPrnm", getBcmPrnm())
            .append("bcmCitycd", getBcmCitycd())
            .append("bcmCity", getBcmCity())
            .append("bcmAreacd", getBcmAreacd())
            .append("bcmAreanm", getBcmAreanm())
            .append("bcmJdcd", getBcmJdcd())
            .append("bcmJdnm", getBcmJdnm())
            .append("bcmInvcMan", getBcmInvcMan())
            .append("bcmAddr", getBcmAddr())
            .append("bcmSmcd", getBcmSmcd())
            .append("bcmSmcd3a", getBcmSmcd3a())
            .append("bcmDc", getBcmDc())
            .append("bcmFstOrgcd", getBcmFstOrgcd())
            .append("bcmAreaCd", getBcmAreaCd())
            .append("bcmPaytyp", getBcmPaytyp())
            .append("bcmSendyn", getBcmSendyn())
            .append("bcmIseps", getBcmIseps())
            .append("bcmSmsyn", getBcmSmsyn())
            .append("bcmCoayn", getBcmCoayn())
            .append("bcmInvoiceyn", getBcmInvoiceyn())
            .append("bcmInvoicexe", getBcmInvoicexe())
            .append("bcmBarcode", getBcmBarcode())
            .append("bcmPrintyn", getBcmPrintyn())
            .append("bcmLrmk", getBcmLrmk())
            .append("bcmMbgCd", getBcmMbgCd())
            .append("bcmIsleaf", getBcmIsleaf())
            .append("bcmFinanceCode", getBcmFinanceCode())
            .append("3aBcmCd", get3aBcmCd())
            .append("bcmPscd", getBcmPscd())
            .append("bcmOrgcd", getBcmOrgcd())
            .append("bcmPscdTwo", getBcmPscdTwo())
            .append("bcmDcls", getBcmDcls())
            .append("bcmOcd", getBcmOcd())
            .append("bcmRank", getBcmRank())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("bcmAccredit", getBcmAccredit())
            .append("bcmCac", getBcmCac())
            .append("bcmInvoiceCac", getBcmInvoiceCac())
            .append("bcmBlaList", getBcmBlaList())
            .append("bcmBuyExprs", getBcmBuyExprs())
            .append("bcmInvcrmk", getBcmInvcrmk())
            .append("bcmThirdBoard", getBcmThirdBoard())
            .append("bcmQuotaLimit", getBcmQuotaLimit())
            .append("bcmConAgent", getBcmConAgent())
            .append("bcmCurrency", getBcmCurrency())
            .append("bcmBccd", getBcmBccd())
            .append("bcmNationcd", getBcmNationcd())
            .append("bcmNationnm", getBcmNationnm())
            .append("bcmClientgrade", getBcmClientgrade())
            .toString();
    }
}
