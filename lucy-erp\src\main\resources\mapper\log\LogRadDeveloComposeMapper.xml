<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogRadDeveloComposeMapper">
    
    <resultMap type="LogRadDeveloCompose" id="LogRadDeveloComposeResult">
        <result property="id"    column="id"    />
        <result property="lrdcCd"    column="lrdc_cd"    />
        <result property="lrdcMcd"    column="lrdc_mcd"    />
        <result property="lrdcBn"    column="lrdc_bn"    />
        <result property="lrdcBnEncrypt"    column="lrdc_bn_encrypt"    />
        <result property="lrdcPcd"    column="lrdc_pcd"    />
        <result property="lrdcPpcd"    column="lrdc_ppcd"    />
        <result property="lrdcSlcd"    column="lrdc_slcd"    />
        <result property="lrdcTcd"    column="lrdc_tcd"    />
        <result property="lrdcDmcd"    column="lrdc_dmcd"    />
        <result property="lrdcOrgmcd"    column="lrdc_orgmcd"    />
        <result property="lrdcPbn"    column="lrdc_pbn"    />
        <result property="lrdcSbn"    column="lrdc_sbn"    />
        <result property="lrdcQty"    column="lrdc_qty"    />
        <result property="lrdcUnitp"    column="lrdc_unitp"    />
        <result property="lrdcBit"    column="lrdc_bit"    />
        <result property="lrdcIspart"    column="lrdc_ispart"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectLogRadDeveloComposeVo">
        select id, lrdc_cd, lrdc_mcd, lrdc_bn, lrdc_bn_encrypt, lrdc_pcd, lrdc_ppcd, lrdc_slcd, lrdc_tcd, lrdc_dmcd, lrdc_orgmcd, lrdc_pbn, lrdc_sbn, lrdc_qty, lrdc_unitp, lrdc_bit, lrdc_ispart, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from log_rad_develo_compose
    </sql>

    <select id="selectLogRadDeveloComposeList" parameterType="LogRadDeveloCompose" resultMap="LogRadDeveloComposeResult">
        <include refid="selectLogRadDeveloComposeVo"/>
        <where>  
            <if test="lrdcMcd != null  and lrdcMcd != ''"> and lrdc_mcd = #{lrdcMcd}</if>
            <if test="lrdcBn != null  and lrdcBn != ''"> and lrdc_bn = #{lrdcBn}</if>
            <if test="lrdcBnEncrypt != null  and lrdcBnEncrypt != ''"> and lrdc_bn_encrypt = #{lrdcBnEncrypt}</if>
            <if test="lrdcPcd != null  and lrdcPcd != ''"> and lrdc_pcd = #{lrdcPcd}</if>
            <if test="lrdcPpcd != null  and lrdcPpcd != ''"> and lrdc_ppcd = #{lrdcPpcd}</if>
            <if test="lrdcSlcd != null  and lrdcSlcd != ''"> and lrdc_slcd = #{lrdcSlcd}</if>
            <if test="lrdcTcd != null  and lrdcTcd != ''"> and lrdc_tcd = #{lrdcTcd}</if>
            <if test="lrdcDmcd != null  and lrdcDmcd != ''"> and lrdc_dmcd = #{lrdcDmcd}</if>
            <if test="lrdcOrgmcd != null  and lrdcOrgmcd != ''"> and lrdc_orgmcd = #{lrdcOrgmcd}</if>
            <if test="lrdcPbn != null  and lrdcPbn != ''"> and lrdc_pbn = #{lrdcPbn}</if>
            <if test="lrdcSbn != null  and lrdcSbn != ''"> and lrdc_sbn = #{lrdcSbn}</if>
            <if test="lrdcQty != null "> and lrdc_qty = #{lrdcQty}</if>
            <if test="lrdcUnitp != null "> and lrdc_unitp = #{lrdcUnitp}</if>
            <if test="lrdcBit != null "> and lrdc_bit = #{lrdcBit}</if>
            <if test="lrdcIspart != null "> and lrdc_ispart = #{lrdcIspart}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
        </where>
    </select>
    
    <select id="selectLogRadDeveloComposeByLrdcCd" parameterType="String" resultMap="LogRadDeveloComposeResult">
        <include refid="selectLogRadDeveloComposeVo"/>
        where lrdc_cd = #{lrdcCd}
    </select>

    <insert id="insertLogRadDeveloCompose" parameterType="LogRadDeveloCompose">
        insert into log_rad_develo_compose
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lrdcCd != null">lrdc_cd,</if>
            <if test="lrdcMcd != null">lrdc_mcd,</if>
            <if test="lrdcBn != null">lrdc_bn,</if>
            <if test="lrdcBnEncrypt != null">lrdc_bn_encrypt,</if>
            <if test="lrdcPcd != null">lrdc_pcd,</if>
            <if test="lrdcPpcd != null">lrdc_ppcd,</if>
            <if test="lrdcSlcd != null">lrdc_slcd,</if>
            <if test="lrdcTcd != null">lrdc_tcd,</if>
            <if test="lrdcDmcd != null">lrdc_dmcd,</if>
            <if test="lrdcOrgmcd != null">lrdc_orgmcd,</if>
            <if test="lrdcPbn != null">lrdc_pbn,</if>
            <if test="lrdcSbn != null">lrdc_sbn,</if>
            <if test="lrdcQty != null">lrdc_qty,</if>
            <if test="lrdcUnitp != null">lrdc_unitp,</if>
            <if test="lrdcBit != null">lrdc_bit,</if>
            <if test="lrdcIspart != null">lrdc_ispart,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lrdcCd != null">#{lrdcCd},</if>
            <if test="lrdcMcd != null">#{lrdcMcd},</if>
            <if test="lrdcBn != null">#{lrdcBn},</if>
            <if test="lrdcBnEncrypt != null">#{lrdcBnEncrypt},</if>
            <if test="lrdcPcd != null">#{lrdcPcd},</if>
            <if test="lrdcPpcd != null">#{lrdcPpcd},</if>
            <if test="lrdcSlcd != null">#{lrdcSlcd},</if>
            <if test="lrdcTcd != null">#{lrdcTcd},</if>
            <if test="lrdcDmcd != null">#{lrdcDmcd},</if>
            <if test="lrdcOrgmcd != null">#{lrdcOrgmcd},</if>
            <if test="lrdcPbn != null">#{lrdcPbn},</if>
            <if test="lrdcSbn != null">#{lrdcSbn},</if>
            <if test="lrdcQty != null">#{lrdcQty},</if>
            <if test="lrdcUnitp != null">#{lrdcUnitp},</if>
            <if test="lrdcBit != null">#{lrdcBit},</if>
            <if test="lrdcIspart != null">#{lrdcIspart},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateLogRadDeveloCompose" parameterType="LogRadDeveloCompose">
        update log_rad_develo_compose
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lrdcMcd != null">lrdc_mcd = #{lrdcMcd},</if>
            <if test="lrdcBn != null">lrdc_bn = #{lrdcBn},</if>
            <if test="lrdcBnEncrypt != null">lrdc_bn_encrypt = #{lrdcBnEncrypt},</if>
            <if test="lrdcPcd != null">lrdc_pcd = #{lrdcPcd},</if>
            <if test="lrdcPpcd != null">lrdc_ppcd = #{lrdcPpcd},</if>
            <if test="lrdcSlcd != null">lrdc_slcd = #{lrdcSlcd},</if>
            <if test="lrdcTcd != null">lrdc_tcd = #{lrdcTcd},</if>
            <if test="lrdcDmcd != null">lrdc_dmcd = #{lrdcDmcd},</if>
            <if test="lrdcOrgmcd != null">lrdc_orgmcd = #{lrdcOrgmcd},</if>
            <if test="lrdcPbn != null">lrdc_pbn = #{lrdcPbn},</if>
            <if test="lrdcSbn != null">lrdc_sbn = #{lrdcSbn},</if>
            <if test="lrdcQty != null">lrdc_qty = #{lrdcQty},</if>
            <if test="lrdcUnitp != null">lrdc_unitp = #{lrdcUnitp},</if>
            <if test="lrdcBit != null">lrdc_bit = #{lrdcBit},</if>
            <if test="lrdcIspart != null">lrdc_ispart = #{lrdcIspart},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where lrdc_cd = #{lrdcCd}
    </update>

    <delete id="deleteLogRadDeveloComposeByLrdcCd" parameterType="String">
        delete from log_rad_develo_compose where lrdc_cd = #{lrdcCd}
    </delete>

    <delete id="deleteLogRadDeveloComposeByLrdcCds" parameterType="String">
        delete from log_rad_develo_compose where lrdc_cd in 
        <foreach item="lrdcCd" collection="array" open="(" separator="," close=")">
            #{lrdcCd}
        </foreach>
    </delete>
</mapper>