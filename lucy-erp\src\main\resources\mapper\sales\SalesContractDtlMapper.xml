<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.SalesContractDtlMapper">
    
    <resultMap type="SalesContractDtl" id="SalesContractDtlResult">
        <result property="id"    column="id"    />
        <result property="scdMid"    column="scd_mid"    />
        <result property="scdMcd"    column="scd_mcd"    />
        <result property="scdCd"    column="scd_cd"    />
        <result property="scdOpcd"    column="scd_opcd"    />
        <result property="scdOscd"    column="scd_oscd"    />
        <result property="scdTyp"    column="scd_typ"    />
        <result property="scdPrinttyp"    column="scd_printtyp"    />
        <result property="scdPrecd"    column="scd_precd"    />
        <result property="scdDcd"    column="scd_dcd"    />
        <result property="scdBr"    column="scd_br"    />
        <result property="scdPmid"    column="scd_pmid"    />
        <result property="scdPmsort"    column="scd_pmsort"    />
        <result property="scdPorgcd"    column="scd_porgcd"    />
        <result property="scdPmcd"    column="scd_pmcd"    />
        <result property="scdPmnm"    column="scd_pmnm"    />
        <result property="scdPmenm"    column="scd_pmenm"    />
        <result property="scdCas"    column="scd_cas"    />
        <result property="scdPurity"    column="scd_purity"    />
        <result property="scdDcls"    column="scd_dcls"    />
        <result property="scdSprmk"    column="scd_sprmk"    />
        <result property="scdSprmkPoison"    column="scd_sprmk_poison"    />
        <result property="scdStg"    column="scd_stg"    />
        <result property="scdPpid"    column="scd_ppid"    />
        <result property="scdPpcd"    column="scd_ppcd"    />
        <result property="scdPack"    column="scd_pack"    />
        <result property="scdLstprc"    column="scd_lstprc"    />
        <result property="scdPprc"    column="scd_pprc"    />
        <result property="scdDcttyp"    column="scd_dcttyp"    />
        <result property="scdOrgdct"    column="scd_orgdct"    />
        <result property="scdDct"    column="scd_dct"    />
        <result property="scdPrc"    column="scd_prc"    />
        <result property="scdPrcAdjust"    column="scd_prc_adjust"    />
        <result property="scdQty"    column="scd_qty"    />
        <result property="scdQtyReserved"    column="scd_qty_reserved"    />
        <result property="scdPlanqty"    column="scd_planqty"    />
        <result property="scdOutqty"    column="scd_outqty"    />
        <result property="scdIqty"    column="scd_iqty"    />
        <result property="scdTotprcAdjust"    column="scd_totprc_adjust"    />
        <result property="scdFullCutPrice"    column="scd_full_cut_price"    />
        <result property="scdMallCouponAmount"    column="scd_mall_coupon_amount"    />
        <result property="scdDisCountPrice"    column="scd_dis_count_price"    />
        <result property="scdTotprc"    column="scd_totprc"    />
        <result property="scdGold"    column="scd_gold"    />
        <result property="scdPnt"    column="scd_pnt"    />
        <result property="scdPuryn"    column="scd_puryn"    />
        <result property="scdDd"    column="scd_dd"    />
        <result property="scdIsss"    column="scd_isss"    />
        <result property="scdStscd"    column="scd_stscd"    />
        <result property="scdStsnm"    column="scd_stsnm"    />
        <result property="scdSodt"    column="scd_sodt"    />
        <result property="scdShdt"    column="scd_shdt"    />
        <result property="scdZcsd"    column="scd_zcsd"    />
        <result property="scdSd"    column="scd_sd"    />
        <result property="scdOsdt"    column="scd_osdt"    />
        <result property="scdRmk"    column="scd_rmk"    />
        <result property="scdSrmk"    column="scd_srmk"    />
        <result property="scdLrmk"    column="scd_lrmk"    />
        <result property="scdDymemo"    column="scd_dymemo"    />
        <result property="scdFrmk"    column="scd_frmk"    />
        <result property="scdEplnm"    column="scd_eplnm"    />
        <result property="scdStsrmk"    column="scd_stsrmk"    />
        <result property="scdStyp"    column="scd_styp"    />
        <result property="scdTicketno"    column="scd_ticketNo"    />
        <result property="scdPntused"    column="scd_pntUsed"    />
        <result property="scdIntegralSharePrice"    column="scd_integral_share_price"    />
        <result property="scdGoldused"    column="scd_goldused"    />
        <result property="scdGoldusedPrice"    column="scd_goldused_price"    />
        <result property="scdTransportway"    column="scd_transportWay"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="scdProdBn"    column="scd_prod_bn"    />
        <result property="scdSlcdBn"    column="scd_slcd_bn"    />
        <result property="scdInvoiceMethod"    column="scd_invoice_method"    />
        <result property="scdClassiimdlRmk"    column="scd_classiimdl_rmk"    />
        <result property="scdVirtual"    column="scd_virtual"    />
        <result property="scdRawMaterial"    column="scd_raw_material"    />
        <result property="scdIsrisk"    column="scd_isrisk"    />
        <result property="scdMaterialcode"    column="scd_materialCode"    />
        <result property="scdImcd"    column="scd_imcd"    />
        <result property="scdKfrmk"    column="scd_kfrmk"    />
        <result property="scdPackageService"    column="scd_package_service"    />
        <result property="scdZamk"    column="scd_zamk"    />
        <result property="scdBdmType"    column="scd_bdm_type"    />
        <result property="scdPcdcd"    column="scd_pcdcd"    />
        <result property="scdRemarks"    column="scd_remarks"    />
    </resultMap>

    <sql id="selectSalesContractDtlVo">
        select id, scd_mid, scd_mcd, scd_cd, scd_opcd, scd_oscd, scd_typ, scd_printtyp, scd_precd, scd_dcd, scd_br, scd_pmid, scd_pmsort, scd_porgcd, scd_pmcd, scd_pmnm, scd_pmenm, scd_cas, scd_purity, scd_dcls, scd_sprmk, scd_sprmk_poison, scd_stg, scd_ppid, scd_ppcd, scd_pack, scd_lstprc, scd_pprc, scd_dcttyp, scd_orgdct, scd_dct, scd_prc, scd_prc_adjust, scd_qty, scd_qty_reserved, scd_planqty, scd_outqty, scd_iqty, scd_totprc_adjust, scd_full_cut_price, scd_mall_coupon_amount, scd_dis_count_price, scd_totprc, scd_gold, scd_pnt, scd_puryn, scd_dd, scd_isss, scd_stscd, scd_stsnm, scd_sodt, scd_shdt, scd_zcsd, scd_sd, scd_osdt, scd_rmk, scd_srmk, scd_lrmk, scd_dymemo, scd_frmk, scd_eplnm, scd_stsrmk, scd_styp, scd_ticketNo, scd_pntUsed, scd_integral_share_price, scd_goldused, scd_goldused_price, scd_transportWay, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, scd_prod_bn, scd_slcd_bn, scd_invoice_method, scd_classiimdl_rmk, scd_virtual, scd_raw_material, scd_isrisk, scd_materialCode, scd_imcd, scd_kfrmk, scd_package_service, scd_zamk, scd_bdm_type, scd_pcdcd, scd_remarks from sales_contract_dtl
    </sql>

    <select id="selectSalesContractDtlList" parameterType="SalesContractDtl" resultMap="SalesContractDtlResult">
        <include refid="selectSalesContractDtlVo"/>
        <where>  
            <if test="scdMid != null "> and scd_mid = #{scdMid}</if>
            <if test="scdOpcd != null  and scdOpcd != ''"> and scd_opcd = #{scdOpcd}</if>
            <if test="scdOscd != null  and scdOscd != ''"> and scd_oscd = #{scdOscd}</if>
            <if test="scdTyp != null  and scdTyp != ''"> and scd_typ = #{scdTyp}</if>
            <if test="scdPrinttyp != null  and scdPrinttyp != ''"> and scd_printtyp = #{scdPrinttyp}</if>
            <if test="scdPrecd != null  and scdPrecd != ''"> and scd_precd = #{scdPrecd}</if>
            <if test="scdDcd != null  and scdDcd != ''"> and scd_dcd = #{scdDcd}</if>
            <if test="scdBr != null  and scdBr != ''"> and scd_br = #{scdBr}</if>
            <if test="scdPmid != null "> and scd_pmid = #{scdPmid}</if>
            <if test="scdPmsort != null  and scdPmsort != ''"> and scd_pmsort = #{scdPmsort}</if>
            <if test="scdPorgcd != null  and scdPorgcd != ''"> and scd_porgcd = #{scdPorgcd}</if>
            <if test="scdPmcd != null  and scdPmcd != ''"> and scd_pmcd = #{scdPmcd}</if>
            <if test="scdPmnm != null  and scdPmnm != ''"> and scd_pmnm = #{scdPmnm}</if>
            <if test="scdPmenm != null  and scdPmenm != ''"> and scd_pmenm = #{scdPmenm}</if>
            <if test="scdCas != null  and scdCas != ''"> and scd_cas = #{scdCas}</if>
            <if test="scdPurity != null  and scdPurity != ''"> and scd_purity = #{scdPurity}</if>
            <if test="scdDcls != null  and scdDcls != ''"> and scd_dcls = #{scdDcls}</if>
            <if test="scdSprmk != null  and scdSprmk != ''"> and scd_sprmk = #{scdSprmk}</if>
            <if test="scdSprmkPoison != null  and scdSprmkPoison != ''"> and scd_sprmk_poison = #{scdSprmkPoison}</if>
            <if test="scdStg != null  and scdStg != ''"> and scd_stg = #{scdStg}</if>
            <if test="scdPpid != null "> and scd_ppid = #{scdPpid}</if>
            <if test="scdPpcd != null  and scdPpcd != ''"> and scd_ppcd = #{scdPpcd}</if>
            <if test="scdPack != null  and scdPack != ''"> and scd_pack = #{scdPack}</if>
            <if test="scdLstprc != null "> and scd_lstprc = #{scdLstprc}</if>
            <if test="scdPprc != null  and scdPprc != ''"> and scd_pprc = #{scdPprc}</if>
            <if test="scdDcttyp != null  and scdDcttyp != ''"> and scd_dcttyp = #{scdDcttyp}</if>
            <if test="scdOrgdct != null "> and scd_orgdct = #{scdOrgdct}</if>
            <if test="scdDct != null "> and scd_dct = #{scdDct}</if>
            <if test="scdPrc != null "> and scd_prc = #{scdPrc}</if>
            <if test="scdPrcAdjust != null "> and scd_prc_adjust = #{scdPrcAdjust}</if>
            <if test="scdQty != null "> and scd_qty = #{scdQty}</if>
            <if test="scdQtyReserved != null "> and scd_qty_reserved = #{scdQtyReserved}</if>
            <if test="scdPlanqty != null "> and scd_planqty = #{scdPlanqty}</if>
            <if test="scdOutqty != null "> and scd_outqty = #{scdOutqty}</if>
            <if test="scdIqty != null "> and scd_iqty = #{scdIqty}</if>
            <if test="scdTotprcAdjust != null "> and scd_totprc_adjust = #{scdTotprcAdjust}</if>
            <if test="scdFullCutPrice != null "> and scd_full_cut_price = #{scdFullCutPrice}</if>
            <if test="scdMallCouponAmount != null "> and scd_mall_coupon_amount = #{scdMallCouponAmount}</if>
            <if test="scdDisCountPrice != null "> and scd_dis_count_price = #{scdDisCountPrice}</if>
            <if test="scdTotprc != null "> and scd_totprc = #{scdTotprc}</if>
            <if test="scdGold != null "> and scd_gold = #{scdGold}</if>
            <if test="scdPnt != null "> and scd_pnt = #{scdPnt}</if>
            <if test="scdPuryn != null  and scdPuryn != ''"> and scd_puryn = #{scdPuryn}</if>
            <if test="scdDd != null  and scdDd != ''"> and scd_dd = #{scdDd}</if>
            <if test="scdIsss != null  and scdIsss != ''"> and scd_isss = #{scdIsss}</if>
            <if test="scdStscd != null  and scdStscd != ''"> and scd_stscd = #{scdStscd}</if>
            <if test="scdStsnm != null  and scdStsnm != ''"> and scd_stsnm = #{scdStsnm}</if>
            <if test="scdSodt != null "> and scd_sodt = #{scdSodt}</if>
            <if test="scdShdt != null "> and scd_shdt = #{scdShdt}</if>
            <if test="scdZcsd != null  and scdZcsd != ''"> and scd_zcsd = #{scdZcsd}</if>
            <if test="scdSd != null  and scdSd != ''"> and scd_sd = #{scdSd}</if>
            <if test="scdOsdt != null "> and scd_osdt = #{scdOsdt}</if>
            <if test="scdRmk != null  and scdRmk != ''"> and scd_rmk = #{scdRmk}</if>
            <if test="scdSrmk != null  and scdSrmk != ''"> and scd_srmk = #{scdSrmk}</if>
            <if test="scdLrmk != null  and scdLrmk != ''"> and scd_lrmk = #{scdLrmk}</if>
            <if test="scdDymemo != null  and scdDymemo != ''"> and scd_dymemo = #{scdDymemo}</if>
            <if test="scdFrmk != null  and scdFrmk != ''"> and scd_frmk = #{scdFrmk}</if>
            <if test="scdEplnm != null  and scdEplnm != ''"> and scd_eplnm = #{scdEplnm}</if>
            <if test="scdStsrmk != null  and scdStsrmk != ''"> and scd_stsrmk = #{scdStsrmk}</if>
            <if test="scdStyp != null  and scdStyp != ''"> and scd_styp = #{scdStyp}</if>
            <if test="scdTicketno != null  and scdTicketno != ''"> and scd_ticketNo = #{scdTicketno}</if>
            <if test="scdPntused != null "> and scd_pntUsed = #{scdPntused}</if>
            <if test="scdIntegralSharePrice != null "> and scd_integral_share_price = #{scdIntegralSharePrice}</if>
            <if test="scdGoldused != null "> and scd_goldused = #{scdGoldused}</if>
            <if test="scdGoldusedPrice != null "> and scd_goldused_price = #{scdGoldusedPrice}</if>
            <if test="scdTransportway != null "> and scd_transportWay = #{scdTransportway}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="scdProdBn != null  and scdProdBn != ''"> and scd_prod_bn = #{scdProdBn}</if>
            <if test="scdSlcdBn != null  and scdSlcdBn != ''"> and scd_slcd_bn = #{scdSlcdBn}</if>
            <if test="scdInvoiceMethod != null  and scdInvoiceMethod != ''"> and scd_invoice_method = #{scdInvoiceMethod}</if>
            <if test="scdClassiimdlRmk != null  and scdClassiimdlRmk != ''"> and scd_classiimdl_rmk = #{scdClassiimdlRmk}</if>
            <if test="scdVirtual != null  and scdVirtual != ''"> and scd_virtual = #{scdVirtual}</if>
            <if test="scdRawMaterial != null  and scdRawMaterial != ''"> and scd_raw_material = #{scdRawMaterial}</if>
            <if test="scdIsrisk != null  and scdIsrisk != ''"> and scd_isrisk = #{scdIsrisk}</if>
            <if test="scdMaterialcode != null  and scdMaterialcode != ''"> and scd_materialCode = #{scdMaterialcode}</if>
            <if test="scdImcd != null  and scdImcd != ''"> and scd_imcd = #{scdImcd}</if>
            <if test="scdKfrmk != null  and scdKfrmk != ''"> and scd_kfrmk = #{scdKfrmk}</if>
            <if test="scdPackageService != null  and scdPackageService != ''"> and scd_package_service = #{scdPackageService}</if>
            <if test="scdZamk != null  and scdZamk != ''"> and scd_zamk = #{scdZamk}</if>
            <if test="scdBdmType != null  and scdBdmType != ''"> and scd_bdm_type = #{scdBdmType}</if>
            <if test="scdPcdcd != null  and scdPcdcd != ''"> and scd_pcdcd = #{scdPcdcd}</if>
            <if test="scdRemarks != null  and scdRemarks != ''"> and scd_remarks = #{scdRemarks}</if>
        </where>
    </select>
    
    <select id="selectSalesContractDtlByScdcd" parameterType="String" resultMap="SalesContractDtlResult">
        <include refid="selectSalesContractDtlVo"/>
        where scd_cd = #{scdcd}
    </select>

    <insert id="insertSalesContractDtl" parameterType="SalesContractDtl">
        insert into sales_contract_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="scdMid != null">scd_mid,</if>
            <if test="scdMcd != null">scd_mcd,</if>
            <if test="scdCd != null">scd_cd,</if>
            <if test="scdOpcd != null">scd_opcd,</if>
            <if test="scdOscd != null">scd_oscd,</if>
            <if test="scdTyp != null">scd_typ,</if>
            <if test="scdPrinttyp != null">scd_printtyp,</if>
            <if test="scdPrecd != null">scd_precd,</if>
            <if test="scdDcd != null">scd_dcd,</if>
            <if test="scdBr != null">scd_br,</if>
            <if test="scdPmid != null">scd_pmid,</if>
            <if test="scdPmsort != null">scd_pmsort,</if>
            <if test="scdPorgcd != null">scd_porgcd,</if>
            <if test="scdPmcd != null">scd_pmcd,</if>
            <if test="scdPmnm != null">scd_pmnm,</if>
            <if test="scdPmenm != null">scd_pmenm,</if>
            <if test="scdCas != null">scd_cas,</if>
            <if test="scdPurity != null">scd_purity,</if>
            <if test="scdDcls != null">scd_dcls,</if>
            <if test="scdSprmk != null">scd_sprmk,</if>
            <if test="scdSprmkPoison != null">scd_sprmk_poison,</if>
            <if test="scdStg != null">scd_stg,</if>
            <if test="scdPpid != null">scd_ppid,</if>
            <if test="scdPpcd != null">scd_ppcd,</if>
            <if test="scdPack != null">scd_pack,</if>
            <if test="scdLstprc != null">scd_lstprc,</if>
            <if test="scdPprc != null">scd_pprc,</if>
            <if test="scdDcttyp != null">scd_dcttyp,</if>
            <if test="scdOrgdct != null">scd_orgdct,</if>
            <if test="scdDct != null">scd_dct,</if>
            <if test="scdPrc != null">scd_prc,</if>
            <if test="scdPrcAdjust != null">scd_prc_adjust,</if>
            <if test="scdQty != null">scd_qty,</if>
            <if test="scdQtyReserved != null">scd_qty_reserved,</if>
            <if test="scdPlanqty != null">scd_planqty,</if>
            <if test="scdOutqty != null">scd_outqty,</if>
            <if test="scdIqty != null">scd_iqty,</if>
            <if test="scdTotprcAdjust != null">scd_totprc_adjust,</if>
            <if test="scdFullCutPrice != null">scd_full_cut_price,</if>
            <if test="scdMallCouponAmount != null">scd_mall_coupon_amount,</if>
            <if test="scdDisCountPrice != null">scd_dis_count_price,</if>
            <if test="scdTotprc != null">scd_totprc,</if>
            <if test="scdGold != null">scd_gold,</if>
            <if test="scdPnt != null">scd_pnt,</if>
            <if test="scdPuryn != null">scd_puryn,</if>
            <if test="scdDd != null">scd_dd,</if>
            <if test="scdIsss != null">scd_isss,</if>
            <if test="scdStscd != null">scd_stscd,</if>
            <if test="scdStsnm != null">scd_stsnm,</if>
            <if test="scdSodt != null">scd_sodt,</if>
            <if test="scdShdt != null">scd_shdt,</if>
            <if test="scdZcsd != null">scd_zcsd,</if>
            <if test="scdSd != null">scd_sd,</if>
            <if test="scdOsdt != null">scd_osdt,</if>
            <if test="scdRmk != null">scd_rmk,</if>
            <if test="scdSrmk != null">scd_srmk,</if>
            <if test="scdLrmk != null">scd_lrmk,</if>
            <if test="scdDymemo != null">scd_dymemo,</if>
            <if test="scdFrmk != null">scd_frmk,</if>
            <if test="scdEplnm != null">scd_eplnm,</if>
            <if test="scdStsrmk != null">scd_stsrmk,</if>
            <if test="scdStyp != null">scd_styp,</if>
            <if test="scdTicketno != null">scd_ticketNo,</if>
            <if test="scdPntused != null">scd_pntUsed,</if>
            <if test="scdIntegralSharePrice != null">scd_integral_share_price,</if>
            <if test="scdGoldused != null">scd_goldused,</if>
            <if test="scdGoldusedPrice != null">scd_goldused_price,</if>
            <if test="scdTransportway != null">scd_transportWay,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="scdProdBn != null">scd_prod_bn,</if>
            <if test="scdSlcdBn != null">scd_slcd_bn,</if>
            <if test="scdInvoiceMethod != null">scd_invoice_method,</if>
            <if test="scdClassiimdlRmk != null">scd_classiimdl_rmk,</if>
            <if test="scdVirtual != null">scd_virtual,</if>
            <if test="scdRawMaterial != null">scd_raw_material,</if>
            <if test="scdIsrisk != null">scd_isrisk,</if>
            <if test="scdMaterialcode != null">scd_materialCode,</if>
            <if test="scdImcd != null">scd_imcd,</if>
            <if test="scdKfrmk != null">scd_kfrmk,</if>
            <if test="scdPackageService != null">scd_package_service,</if>
            <if test="scdZamk != null">scd_zamk,</if>
            <if test="scdBdmType != null">scd_bdm_type,</if>
            <if test="scdPcdcd != null">scd_pcdcd,</if>
            <if test="scdRemarks != null">scd_remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="scdMid != null">#{scdMid},</if>
            <if test="scdMcd != null">#{scdMcd},</if>
            <if test="scdCd != null">#{scdCd},</if>
            <if test="scdOpcd != null">#{scdOpcd},</if>
            <if test="scdOscd != null">#{scdOscd},</if>
            <if test="scdTyp != null">#{scdTyp},</if>
            <if test="scdPrinttyp != null">#{scdPrinttyp},</if>
            <if test="scdPrecd != null">#{scdPrecd},</if>
            <if test="scdDcd != null">#{scdDcd},</if>
            <if test="scdBr != null">#{scdBr},</if>
            <if test="scdPmid != null">#{scdPmid},</if>
            <if test="scdPmsort != null">#{scdPmsort},</if>
            <if test="scdPorgcd != null">#{scdPorgcd},</if>
            <if test="scdPmcd != null">#{scdPmcd},</if>
            <if test="scdPmnm != null">#{scdPmnm},</if>
            <if test="scdPmenm != null">#{scdPmenm},</if>
            <if test="scdCas != null">#{scdCas},</if>
            <if test="scdPurity != null">#{scdPurity},</if>
            <if test="scdDcls != null">#{scdDcls},</if>
            <if test="scdSprmk != null">#{scdSprmk},</if>
            <if test="scdSprmkPoison != null">#{scdSprmkPoison},</if>
            <if test="scdStg != null">#{scdStg},</if>
            <if test="scdPpid != null">#{scdPpid},</if>
            <if test="scdPpcd != null">#{scdPpcd},</if>
            <if test="scdPack != null">#{scdPack},</if>
            <if test="scdLstprc != null">#{scdLstprc},</if>
            <if test="scdPprc != null">#{scdPprc},</if>
            <if test="scdDcttyp != null">#{scdDcttyp},</if>
            <if test="scdOrgdct != null">#{scdOrgdct},</if>
            <if test="scdDct != null">#{scdDct},</if>
            <if test="scdPrc != null">#{scdPrc},</if>
            <if test="scdPrcAdjust != null">#{scdPrcAdjust},</if>
            <if test="scdQty != null">#{scdQty},</if>
            <if test="scdQtyReserved != null">#{scdQtyReserved},</if>
            <if test="scdPlanqty != null">#{scdPlanqty},</if>
            <if test="scdOutqty != null">#{scdOutqty},</if>
            <if test="scdIqty != null">#{scdIqty},</if>
            <if test="scdTotprcAdjust != null">#{scdTotprcAdjust},</if>
            <if test="scdFullCutPrice != null">#{scdFullCutPrice},</if>
            <if test="scdMallCouponAmount != null">#{scdMallCouponAmount},</if>
            <if test="scdDisCountPrice != null">#{scdDisCountPrice},</if>
            <if test="scdTotprc != null">#{scdTotprc},</if>
            <if test="scdGold != null">#{scdGold},</if>
            <if test="scdPnt != null">#{scdPnt},</if>
            <if test="scdPuryn != null">#{scdPuryn},</if>
            <if test="scdDd != null">#{scdDd},</if>
            <if test="scdIsss != null">#{scdIsss},</if>
            <if test="scdStscd != null">#{scdStscd},</if>
            <if test="scdStsnm != null">#{scdStsnm},</if>
            <if test="scdSodt != null">#{scdSodt},</if>
            <if test="scdShdt != null">#{scdShdt},</if>
            <if test="scdZcsd != null">#{scdZcsd},</if>
            <if test="scdSd != null">#{scdSd},</if>
            <if test="scdOsdt != null">#{scdOsdt},</if>
            <if test="scdRmk != null">#{scdRmk},</if>
            <if test="scdSrmk != null">#{scdSrmk},</if>
            <if test="scdLrmk != null">#{scdLrmk},</if>
            <if test="scdDymemo != null">#{scdDymemo},</if>
            <if test="scdFrmk != null">#{scdFrmk},</if>
            <if test="scdEplnm != null">#{scdEplnm},</if>
            <if test="scdStsrmk != null">#{scdStsrmk},</if>
            <if test="scdStyp != null">#{scdStyp},</if>
            <if test="scdTicketno != null">#{scdTicketno},</if>
            <if test="scdPntused != null">#{scdPntused},</if>
            <if test="scdIntegralSharePrice != null">#{scdIntegralSharePrice},</if>
            <if test="scdGoldused != null">#{scdGoldused},</if>
            <if test="scdGoldusedPrice != null">#{scdGoldusedPrice},</if>
            <if test="scdTransportway != null">#{scdTransportway},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="scdProdBn != null">#{scdProdBn},</if>
            <if test="scdSlcdBn != null">#{scdSlcdBn},</if>
            <if test="scdInvoiceMethod != null">#{scdInvoiceMethod},</if>
            <if test="scdClassiimdlRmk != null">#{scdClassiimdlRmk},</if>
            <if test="scdVirtual != null">#{scdVirtual},</if>
            <if test="scdRawMaterial != null">#{scdRawMaterial},</if>
            <if test="scdIsrisk != null">#{scdIsrisk},</if>
            <if test="scdMaterialcode != null">#{scdMaterialcode},</if>
            <if test="scdImcd != null">#{scdImcd},</if>
            <if test="scdKfrmk != null">#{scdKfrmk},</if>
            <if test="scdPackageService != null">#{scdPackageService},</if>
            <if test="scdZamk != null">#{scdZamk},</if>
            <if test="scdBdmType != null">#{scdBdmType},</if>
            <if test="scdPcdcd != null">#{scdPcdcd},</if>
            <if test="scdRemarks != null">#{scdRemarks},</if>
         </trim>
    </insert>

    <update id="updateSalesContractDtl" parameterType="SalesContractDtl">
        update sales_contract_dtl
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="scdMid != null">scd_mid = #{scdMid},</if>
            <if test="scdCd != null">scd_cd = #{scdCd},</if>
            <if test="scdOpcd != null">scd_opcd = #{scdOpcd},</if>
            <if test="scdOscd != null">scd_oscd = #{scdOscd},</if>
            <if test="scdTyp != null">scd_typ = #{scdTyp},</if>
            <if test="scdPrinttyp != null">scd_printtyp = #{scdPrinttyp},</if>
            <if test="scdPrecd != null">scd_precd = #{scdPrecd},</if>
            <if test="scdDcd != null">scd_dcd = #{scdDcd},</if>
            <if test="scdBr != null">scd_br = #{scdBr},</if>
            <if test="scdPmid != null">scd_pmid = #{scdPmid},</if>
            <if test="scdPmsort != null">scd_pmsort = #{scdPmsort},</if>
            <if test="scdPorgcd != null">scd_porgcd = #{scdPorgcd},</if>
            <if test="scdPmcd != null">scd_pmcd = #{scdPmcd},</if>
            <if test="scdPmnm != null">scd_pmnm = #{scdPmnm},</if>
            <if test="scdPmenm != null">scd_pmenm = #{scdPmenm},</if>
            <if test="scdCas != null">scd_cas = #{scdCas},</if>
            <if test="scdPurity != null">scd_purity = #{scdPurity},</if>
            <if test="scdDcls != null">scd_dcls = #{scdDcls},</if>
            <if test="scdSprmk != null">scd_sprmk = #{scdSprmk},</if>
            <if test="scdSprmkPoison != null">scd_sprmk_poison = #{scdSprmkPoison},</if>
            <if test="scdStg != null">scd_stg = #{scdStg},</if>
            <if test="scdPpid != null">scd_ppid = #{scdPpid},</if>
            <if test="scdPpcd != null">scd_ppcd = #{scdPpcd},</if>
            <if test="scdPack != null">scd_pack = #{scdPack},</if>
            <if test="scdLstprc != null">scd_lstprc = #{scdLstprc},</if>
            <if test="scdPprc != null">scd_pprc = #{scdPprc},</if>
            <if test="scdDcttyp != null">scd_dcttyp = #{scdDcttyp},</if>
            <if test="scdOrgdct != null">scd_orgdct = #{scdOrgdct},</if>
            <if test="scdDct != null">scd_dct = #{scdDct},</if>
            <if test="scdPrc != null">scd_prc = #{scdPrc},</if>
            <if test="scdPrcAdjust != null">scd_prc_adjust = #{scdPrcAdjust},</if>
            <if test="scdQty != null">scd_qty = #{scdQty},</if>
            <if test="scdQtyReserved != null">scd_qty_reserved = #{scdQtyReserved},</if>
            <if test="scdPlanqty != null">scd_planqty = #{scdPlanqty},</if>
            <if test="scdOutqty != null">scd_outqty = #{scdOutqty},</if>
            <if test="scdIqty != null">scd_iqty = #{scdIqty},</if>
            <if test="scdTotprcAdjust != null">scd_totprc_adjust = #{scdTotprcAdjust},</if>
            <if test="scdFullCutPrice != null">scd_full_cut_price = #{scdFullCutPrice},</if>
            <if test="scdMallCouponAmount != null">scd_mall_coupon_amount = #{scdMallCouponAmount},</if>
            <if test="scdDisCountPrice != null">scd_dis_count_price = #{scdDisCountPrice},</if>
            <if test="scdTotprc != null">scd_totprc = #{scdTotprc},</if>
            <if test="scdGold != null">scd_gold = #{scdGold},</if>
            <if test="scdPnt != null">scd_pnt = #{scdPnt},</if>
            <if test="scdPuryn != null">scd_puryn = #{scdPuryn},</if>
            <if test="scdDd != null">scd_dd = #{scdDd},</if>
            <if test="scdIsss != null">scd_isss = #{scdIsss},</if>
            <if test="scdStscd != null">scd_stscd = #{scdStscd},</if>
            <if test="scdStsnm != null">scd_stsnm = #{scdStsnm},</if>
            <if test="scdSodt != null">scd_sodt = #{scdSodt},</if>
            <if test="scdShdt != null">scd_shdt = #{scdShdt},</if>
            <if test="scdZcsd != null">scd_zcsd = #{scdZcsd},</if>
            <if test="scdSd != null">scd_sd = #{scdSd},</if>
            <if test="scdOsdt != null">scd_osdt = #{scdOsdt},</if>
            <if test="scdRmk != null">scd_rmk = #{scdRmk},</if>
            <if test="scdSrmk != null">scd_srmk = #{scdSrmk},</if>
            <if test="scdLrmk != null">scd_lrmk = #{scdLrmk},</if>
            <if test="scdDymemo != null">scd_dymemo = #{scdDymemo},</if>
            <if test="scdFrmk != null">scd_frmk = #{scdFrmk},</if>
            <if test="scdEplnm != null">scd_eplnm = #{scdEplnm},</if>
            <if test="scdStsrmk != null">scd_stsrmk = #{scdStsrmk},</if>
            <if test="scdStyp != null">scd_styp = #{scdStyp},</if>
            <if test="scdTicketno != null">scd_ticketNo = #{scdTicketno},</if>
            <if test="scdPntused != null">scd_pntUsed = #{scdPntused},</if>
            <if test="scdIntegralSharePrice != null">scd_integral_share_price = #{scdIntegralSharePrice},</if>
            <if test="scdGoldused != null">scd_goldused = #{scdGoldused},</if>
            <if test="scdGoldusedPrice != null">scd_goldused_price = #{scdGoldusedPrice},</if>
            <if test="scdTransportway != null">scd_transportWay = #{scdTransportway},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="scdProdBn != null">scd_prod_bn = #{scdProdBn},</if>
            <if test="scdSlcdBn != null">scd_slcd_bn = #{scdSlcdBn},</if>
            <if test="scdInvoiceMethod != null">scd_invoice_method = #{scdInvoiceMethod},</if>
            <if test="scdClassiimdlRmk != null">scd_classiimdl_rmk = #{scdClassiimdlRmk},</if>
            <if test="scdVirtual != null">scd_virtual = #{scdVirtual},</if>
            <if test="scdRawMaterial != null">scd_raw_material = #{scdRawMaterial},</if>
            <if test="scdIsrisk != null">scd_isrisk = #{scdIsrisk},</if>
            <if test="scdMaterialcode != null">scd_materialCode = #{scdMaterialcode},</if>
            <if test="scdImcd != null">scd_imcd = #{scdImcd},</if>
            <if test="scdKfrmk != null">scd_kfrmk = #{scdKfrmk},</if>
            <if test="scdPackageService != null">scd_package_service = #{scdPackageService},</if>
            <if test="scdZamk != null">scd_zamk = #{scdZamk},</if>
            <if test="scdBdmType != null">scd_bdm_type = #{scdBdmType},</if>
            <if test="scdPcdcd != null">scd_pcdcd = #{scdPcdcd},</if>
            <if test="scdRemarks != null">scd_remarks = #{scdRemarks},</if>
        </trim>
        where scd_mcd = #{scdMcd}
    </update>

    <delete id="deleteSalesContractDtlByScdMcd" parameterType="String">
        delete from sales_contract_dtl where scd_mcd = #{scdMcd}
    </delete>

    <delete id="deleteSalesContractDtlByScdMcds" parameterType="String">
        delete from sales_contract_dtl where scd_mcd in 
        <foreach item="scdMcd" collection="array" open="(" separator="," close=")">
            #{scdMcd}
        </foreach>
    </delete>
</mapper>