package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LabProject;

/**
 * 研发项目Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ILabProjectService 
{
    /**
     * 查询研发项目
     * 
     * @param lpCd 研发项目主键
     * @return 研发项目
     */
    public LabProject selectLabProjectByLpCd(String lpCd);

    /**
     * 查询研发项目列表
     * 
     * @param labProject 研发项目
     * @return 研发项目集合
     */
    public List<LabProject> selectLabProjectList(LabProject labProject);

    /**
     * 新增研发项目
     * 
     * @param labProject 研发项目
     * @return 结果
     */
    public int insertLabProject(LabProject labProject);

    /**
     * 修改研发项目
     * 
     * @param labProject 研发项目
     * @return 结果
     */
    public int updateLabProject(LabProject labProject);

    /**
     * 批量删除研发项目
     * 
     * @param lpCds 需要删除的研发项目主键集合
     * @return 结果
     */
    public int deleteLabProjectByLpCds(String[] lpCds);

    /**
     * 删除研发项目信息
     * 
     * @param lpCd 研发项目主键
     * @return 结果
     */
    public int deleteLabProjectByLpCd(String lpCd);
}
