package com.lucy.assetowner.service;

import java.util.List;
import com.lucy.assetowner.domain.AssetOwnerMaster;

/**
 * 资产所属主体主 Asset owner master dataService接口
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface IAssetOwnerMasterService 
{
    /**
     * 查询资产所属主体主 Asset owner master data
     * 
     * @param id 资产所属主体主 Asset owner master data主键
     * @return 资产所属主体主 Asset owner master data
     */
    public AssetOwnerMaster selectAssetOwnerMasterById(Long id);

    /**
     * 查询资产所属主体主 Asset owner master data列表
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 资产所属主体主 Asset owner master data集合
     */
    public List<AssetOwnerMaster> selectAssetOwnerMasterList(AssetOwnerMaster assetOwnerMaster);

    /**
     * 新增资产所属主体主 Asset owner master data
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 结果
     */
    public int insertAssetOwnerMaster(AssetOwnerMaster assetOwnerMaster);

    /**
     * 修改资产所属主体主 Asset owner master data
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 结果
     */
    public int updateAssetOwnerMaster(AssetOwnerMaster assetOwnerMaster);

    /**
     * 批量删除资产所属主体主 Asset owner master data
     * 
     * @param ids 需要删除的资产所属主体主 Asset owner master data主键集合
     * @return 结果
     */
    public int deleteAssetOwnerMasterByIds(Long[] ids);

    /**
     * 删除资产所属主体主 Asset owner master data信息
     * 
     * @param id 资产所属主体主 Asset owner master data主键
     * @return 结果
     */
    public int deleteAssetOwnerMasterById(Long id);

    public  void loadData();

    public AssetOwnerMaster get(String code);

    }
