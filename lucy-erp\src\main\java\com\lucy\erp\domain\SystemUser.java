package com.lucy.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 系统_用户对象 system_user
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class SystemUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 用户类型 */
    @Excel(name = "用户类型")
    private String suTyp;

    /** 用户编号 */
    @Excel(name = "用户编号")
    private String suEno;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String suEnm;

    /** 登入帐号 */
    @Excel(name = "登入帐号")
    private String suLogno;

    /** 登录帐号随机码 */
    @Excel(name = "登录帐号随机码")
    private Long suRnum;

    /** 密码 */
    @Excel(name = "密码")
    private String suPwd;

    /** 最后登陆IP */
    @Excel(name = "最后登陆IP")
    private String suLogip;

    /** 最后登陆时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后登陆时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date suLogdt;

    /** 是否可登陆 */
    @Excel(name = "是否可登陆")
    private String suLogflag;

    /** 企业微信账号 */
    @Excel(name = "企业微信账号")
    private String suQywxEno;

    /** 创建用户ID */
    @Excel(name = "创建用户ID")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 修改用户ID */
    @Excel(name = "修改用户ID")
    private String rUpdKid;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本号 */
    @Excel(name = "行版本号")
    private Long rV;

    /** 是否禁用/删除 */
    @Excel(name = "是否禁用/删除")
    private Integer rDel;

    /** 用于查询下拉框时排序 */
    @Excel(name = "用于查询下拉框时排序")
    private Long suSort;

    /** 企业微信用户验证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "企业微信用户验证时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date suQywxCheckdt;

    private String dept;

    private String deptName;
    public String getDept() {
		return dept;
	}

	public void setDept(String dept) {
		this.dept = dept;
	}

    public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setSuTyp(String suTyp) 
    {
        this.suTyp = suTyp;
    }

    public String getSuTyp() 
    {
        return suTyp;
    }

    public void setSuEno(String suEno) 
    {
        this.suEno = suEno;
    }

    public String getSuEno() 
    {
        return suEno;
    }

    public void setSuEnm(String suEnm) 
    {
        this.suEnm = suEnm;
    }

    public String getSuEnm() 
    {
        return suEnm;
    }

    public void setSuLogno(String suLogno) 
    {
        this.suLogno = suLogno;
    }

    public String getSuLogno() 
    {
        return suLogno;
    }

    public void setSuRnum(Long suRnum) 
    {
        this.suRnum = suRnum;
    }

    public Long getSuRnum() 
    {
        return suRnum;
    }

    public void setSuPwd(String suPwd) 
    {
        this.suPwd = suPwd;
    }

    public String getSuPwd() 
    {
        return suPwd;
    }

    public void setSuLogip(String suLogip) 
    {
        this.suLogip = suLogip;
    }

    public String getSuLogip() 
    {
        return suLogip;
    }

    public void setSuLogdt(Date suLogdt) 
    {
        this.suLogdt = suLogdt;
    }

    public Date getSuLogdt() 
    {
        return suLogdt;
    }

    public void setSuLogflag(String suLogflag) 
    {
        this.suLogflag = suLogflag;
    }

    public String getSuLogflag() 
    {
        return suLogflag;
    }

    public void setSuQywxEno(String suQywxEno) 
    {
        this.suQywxEno = suQywxEno;
    }

    public String getSuQywxEno() 
    {
        return suQywxEno;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Integer rDel) 
    {
        this.rDel = rDel;
    }

    public Integer getrDel() 
    {
        return rDel;
    }

    public void setSuSort(Long suSort) 
    {
        this.suSort = suSort;
    }

    public Long getSuSort() 
    {
        return suSort;
    }

    public void setSuQywxCheckdt(Date suQywxCheckdt) 
    {
        this.suQywxCheckdt = suQywxCheckdt;
    }

    public Date getSuQywxCheckdt() 
    {
        return suQywxCheckdt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("suTyp", getSuTyp())
            .append("suEno", getSuEno())
            .append("suEnm", getSuEnm())
            .append("suLogno", getSuLogno())
            .append("suRnum", getSuRnum())
            .append("suPwd", getSuPwd())
            .append("suLogip", getSuLogip())
            .append("suLogdt", getSuLogdt())
            .append("suLogflag", getSuLogflag())
            .append("suQywxEno", getSuQywxEno())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("suSort", getSuSort())
            .append("suQywxCheckdt", getSuQywxCheckdt())
            .toString();
    }
}
