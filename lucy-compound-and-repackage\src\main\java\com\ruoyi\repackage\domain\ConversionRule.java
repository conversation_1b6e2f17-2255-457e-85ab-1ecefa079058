package com.ruoyi.repackage.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分装规则主对象 conversion_rule
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class ConversionRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则唯一标识，示例值: 101 */
    private Long ruleId;

    /** 规则编码（可读标识），示例值: "DIL-ETH-95-TO-75" */
    @Excel(name = "规则编码", readConverterExp = "可=读标识")
    private String ruleCode;

    /** 规则描述，示例值: "95%乙醇稀释至75%" */
    @Excel(name = "规则描述，示例值: 95%乙醇稀释至75%")
    private String description;

    /** 转换类型：分装/DILUTION、混合/MIX、纯化/PURIFICATION，示例值: "DILUTION" */
    @Excel(name = "转换类型：分装/DILUTION、混合/MIX、纯化/PURIFICATION，示例值: DILUTION")
    private String conversionType;

    /** 全局损耗率（0-1），示例值: 0.02 (2%损耗) */
    @Excel(name = "全局损耗率", readConverterExp = "0=-1")
    private BigDecimal lossRate;

    /** 成本分配方法：按数量/QTY、按有效成分/EFFECTIVE_COMPONENT，示例值: "EFFECTIVE_COMPONENT" */
    @Excel(name = "成本分配方法：按数量/QTY、按有效成分/EFFECTIVE_COMPONENT，示例值: EFFECTIVE_COMPONENT")
    private String costMethod;

    /** 是否启用，示例值: 1 (启用) */
    @Excel(name = "是否启用，示例值: 1 (启用)")
    private Integer isActive;

    /** 是否被检测到环状依赖，示例值: 0 (无环) */
    @Excel(name = "是否被检测到环状依赖，示例值: 0 (无环)")
    private Integer cycleFlag;

    /** 规则生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "规则生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 规则失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "规则失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date obsoleteDate;

    /** 资产所属主体 */
    @Excel(name = "资产所属主体")
    private String assetOwner;

    /** 版本号 */
    @Excel(name = "版本号")
    private Integer VERSION;

    /** 创建时间，示例值: 2025-06-25 10:00:00 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间，示例值: 2025-06-25 10:00:00", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public String getAssetOwner() {
        return assetOwner;
    }

    public void setAssetOwner(String assetOwner) {
        this.assetOwner = assetOwner;
    }

    public void setRuleId(Long ruleId)
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }

    public void setRuleCode(String ruleCode) 
    {
        this.ruleCode = ruleCode;
    }

    public String getRuleCode() 
    {
        return ruleCode;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setConversionType(String conversionType) 
    {
        this.conversionType = conversionType;
    }

    public String getConversionType() 
    {
        return conversionType;
    }

    public void setLossRate(BigDecimal lossRate) 
    {
        this.lossRate = lossRate;
    }

    public BigDecimal getLossRate() 
    {
        return lossRate;
    }

    public void setCostMethod(String costMethod) 
    {
        this.costMethod = costMethod;
    }

    public String getCostMethod() 
    {
        return costMethod;
    }

    public void setIsActive(Integer isActive) 
    {
        this.isActive = isActive;
    }

    public Integer getIsActive() 
    {
        return isActive;
    }

    public void setCycleFlag(Integer cycleFlag) 
    {
        this.cycleFlag = cycleFlag;
    }

    public Integer getCycleFlag() 
    {
        return cycleFlag;
    }

    public void setEffectiveDate(Date effectiveDate) 
    {
        this.effectiveDate = effectiveDate;
    }

    public Date getEffectiveDate() 
    {
        return effectiveDate;
    }

    public void setObsoleteDate(Date obsoleteDate) 
    {
        this.obsoleteDate = obsoleteDate;
    }

    public Date getObsoleteDate() 
    {
        return obsoleteDate;
    }

    public void setVERSION(Integer VERSION) 
    {
        this.VERSION = VERSION;
    }

    public Integer getVERSION() 
    {
        return VERSION;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }

    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleId", getRuleId())
            .append("ruleCode", getRuleCode())
            .append("description", getDescription())
            .append("conversionType", getConversionType())
            .append("lossRate", getLossRate())
            .append("costMethod", getCostMethod())
            .append("isActive", getIsActive())
            .append("cycleFlag", getCycleFlag())
            .append("effectiveDate", getEffectiveDate())
            .append("obsoleteDate", getObsoleteDate())
            .append("assetOwner", getAssetOwner())
            .append("VERSION", getVERSION())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
