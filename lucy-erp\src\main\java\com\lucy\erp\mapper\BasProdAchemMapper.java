package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.BasProdAchem;

/**
 * 基础_化学_主
Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface BasProdAchemMapper 
{
    /**
     * 查询基础_化学_主

     * 
     * @param bpacCd 基础_化学_主
主键
     * @return 基础_化学_主

     */
    public BasProdAchem selectBasProdAchemByBpacCd(String bpacCd);

    /**
     * 查询基础_化学_主
列表
     * 
     * @param basProdAchem 基础_化学_主

     * @return 基础_化学_主
集合
     */
    public List<BasProdAchem> selectBasProdAchemList(BasProdAchem basProdAchem);

    /**
     * 新增基础_化学_主

     * 
     * @param basProdAchem 基础_化学_主

     * @return 结果
     */
    public int insertBasProdAchem(BasProdAchem basProdAchem);

    /**
     * 修改基础_化学_主

     * 
     * @param basProdAchem 基础_化学_主

     * @return 结果
     */
    public int updateBasProdAchem(BasProdAchem basProdAchem);

    /**
     * 删除基础_化学_主

     * 
     * @param bpacCd 基础_化学_主
主键
     * @return 结果
     */
    public int deleteBasProdAchemByBpacCd(String bpacCd);

    /**
     * 批量删除基础_化学_主

     * 
     * @param bpacCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasProdAchemByBpacCds(String[] bpacCds);
}
