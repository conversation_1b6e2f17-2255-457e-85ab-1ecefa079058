<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repackage.mapper.ConversionMaterialDependencyClosureMapper">
    
    <resultMap type="ConversionMaterialDependencyClosure" id="ConversionMaterialDependencyClosureResult">
        <result property="ancestorSku"    column="ancestor_sku"    />
        <result property="descendantSku"    column="descendant_sku"    />
        <result property="minDepth"    column="min_depth"    />
        <result property="maxDepth"    column="max_depth"    />
        <result property="pathCount"    column="path_count"    />
    </resultMap>

    <sql id="selectConversionMaterialDependencyClosureVo">
        select ancestor_sku, descendant_sku, min_depth, max_depth, path_count from conversion_material_dependency_closure
    </sql>

    <select id="selectConversionMaterialDependencyClosureList" parameterType="ConversionMaterialDependencyClosure" resultMap="ConversionMaterialDependencyClosureResult">
        <include refid="selectConversionMaterialDependencyClosureVo"/>
        <where>  
            <if test="minDepth != null "> and min_depth = #{minDepth}</if>
            <if test="maxDepth != null "> and max_depth = #{maxDepth}</if>
            <if test="pathCount != null "> and path_count = #{pathCount}</if>
        </where>
    </select>
    
    <select id="selectConversionMaterialDependencyClosureByAncestorSku" parameterType="String" resultMap="ConversionMaterialDependencyClosureResult">
        <include refid="selectConversionMaterialDependencyClosureVo"/>
        where ancestor_sku = #{ancestorSku}
    </select>

    <insert id="insertConversionMaterialDependencyClosure" parameterType="ConversionMaterialDependencyClosure">
        insert into conversion_material_dependency_closure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ancestorSku != null">ancestor_sku,</if>
            <if test="descendantSku != null">descendant_sku,</if>
            <if test="minDepth != null">min_depth,</if>
            <if test="maxDepth != null">max_depth,</if>
            <if test="pathCount != null">path_count,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ancestorSku != null">#{ancestorSku},</if>
            <if test="descendantSku != null">#{descendantSku},</if>
            <if test="minDepth != null">#{minDepth},</if>
            <if test="maxDepth != null">#{maxDepth},</if>
            <if test="pathCount != null">#{pathCount},</if>
         </trim>
    </insert>

    <update id="updateConversionMaterialDependencyClosure" parameterType="ConversionMaterialDependencyClosure">
        update conversion_material_dependency_closure
        <trim prefix="SET" suffixOverrides=",">
            <if test="descendantSku != null">descendant_sku = #{descendantSku},</if>
            <if test="minDepth != null">min_depth = #{minDepth},</if>
            <if test="maxDepth != null">max_depth = #{maxDepth},</if>
            <if test="pathCount != null">path_count = #{pathCount},</if>
        </trim>
        where ancestor_sku = #{ancestorSku}
    </update>

    <delete id="deleteConversionMaterialDependencyClosureByAncestorSku" parameterType="String">
        delete from conversion_material_dependency_closure where ancestor_sku = #{ancestorSku}
    </delete>

    <delete id="deleteConversionMaterialDependencyClosureByAncestorSkus" parameterType="String">
        delete from conversion_material_dependency_closure where ancestor_sku in
        <foreach item="ancestorSku" collection="array" open="(" separator="," close=")">
            #{ancestorSku}
        </foreach>
    </delete>

    <delete id="deleteAllConversionMaterialDependencyClosure">
        truncate table conversion_material_dependency_closure
    </delete>

    <!-- 批量插入依赖关系 -->
    <insert id="batchInsertConversionMaterialDependencyClosure" parameterType="java.util.List">
        insert into conversion_material_dependency_closure
        (ancestor_sku, descendant_sku, max_depth)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.ancestorSku}, #{item.descendantSku}, #{item.maxDepth})
        </foreach>
    </insert>
</mapper>