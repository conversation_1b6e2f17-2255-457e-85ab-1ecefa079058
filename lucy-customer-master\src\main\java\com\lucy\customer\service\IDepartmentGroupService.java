package com.lucy.customer.service;

import java.util.List;
import com.lucy.customer.domain.DepartmentGroup;

/**
 * 部门组织结构 Department groupsService接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface IDepartmentGroupService 
{
    /**
     * 查询部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 部门组织结构 Department groups
     */
    public DepartmentGroup selectDepartmentGroupByCode(String code);

    /**
     * 查询部门组织结构 Department groups列表
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 部门组织结构 Department groups集合
     */
    public List<DepartmentGroup> selectDepartmentGroupList(DepartmentGroup departmentGroup);

    /**
     * 新增部门组织结构 Department groups
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 结果
     */
    public int insertDepartmentGroup(DepartmentGroup departmentGroup);

    /**
     * 修改部门组织结构 Department groups
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 结果
     */
    public int updateDepartmentGroup(DepartmentGroup departmentGroup);

    /**
     * 批量删除部门组织结构 Department groups
     * 
     * @param codes 需要删除的部门组织结构 Department groups主键集合
     * @return 结果
     */
    public int deleteDepartmentGroupByCodes(String[] codes);

    /**
     * 删除部门组织结构 Department groups信息
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 结果
     */
    public int deleteDepartmentGroupByCode(String code);
    
    public void insertDepartmentGroupIfNotExist(String code);
}
