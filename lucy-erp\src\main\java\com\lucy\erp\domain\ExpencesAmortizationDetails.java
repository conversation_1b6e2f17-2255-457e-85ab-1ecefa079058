package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 费用分摊明细对象 expences_amortization_details
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class ExpencesAmortizationDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 费用编号 */
    @Excel(name = "费用编号")
    private String eadEmcd;

    /** 费用明细编号 */
    @Excel(name = "费用明细编号")
    private String eadEmccd;

    /** 费用sku编号 */
    @Excel(name = "费用sku编号")
    private String eadSkuCd;

    /** 费用sku数量 */
    @Excel(name = "费用sku数量")
    private BigDecimal eadSkuQty;

    /** 分摊金额（本币） */
    @Excel(name = "分摊金额", readConverterExp = "本=币")
    private BigDecimal eadAmt;

    /** 费用发票类型 */
    @Excel(name = "费用发票类型")
    private String eadInvcTyp;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal eadBit;

    /** 是否价税分离 */
    @Excel(name = "是否价税分离")
    private Integer eadIsPart;

    /** 付汇订单号 */
    @Excel(name = "付汇订单号")
    private String eadPmcd;

    /** 付汇订单明细编号 */
    @Excel(name = "付汇订单明细编号")
    private String eadPcd;

    /** 付汇货物sku编号 */
    @Excel(name = "付汇货物sku编号")
    private String eadPskuCd;

    /** 付汇货物数量 */
    @Excel(name = "付汇货物数量")
    private BigDecimal eadPskuQty;

    /** 费用类目 */
    @Excel(name = "费用类目")
    private String eadPtyp;

    /** 分摊方式 */
    @Excel(name = "分摊方式")
    private String eadTyp;

    /** 差额调整方式 */
    @Excel(name = "差额调整方式")
    private String eadAdpter;

    /** 差额 */
    @Excel(name = "差额")
    private BigDecimal eadAdpterMargin;

    /** 是否入库存资产 */
    @Excel(name = "是否入库存资产")
    private Integer eadIsAsset;

    /** 分摊产生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "分摊产生时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date eadDt;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long rV;

    /** 删除标志 */
    @Excel(name = "删除标志")
    private Integer rDel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setEadEmcd(String eadEmcd) 
    {
        this.eadEmcd = eadEmcd;
    }

    public String getEadEmcd() 
    {
        return eadEmcd;
    }

    public void setEadEmccd(String eadEmccd) 
    {
        this.eadEmccd = eadEmccd;
    }

    public String getEadEmccd() 
    {
        return eadEmccd;
    }

    public void setEadSkuCd(String eadSkuCd) 
    {
        this.eadSkuCd = eadSkuCd;
    }

    public String getEadSkuCd() 
    {
        return eadSkuCd;
    }

    public void setEadSkuQty(BigDecimal eadSkuQty) 
    {
        this.eadSkuQty = eadSkuQty;
    }

    public BigDecimal getEadSkuQty() 
    {
        return eadSkuQty;
    }

    public void setEadAmt(BigDecimal eadAmt) 
    {
        this.eadAmt = eadAmt;
    }

    public BigDecimal getEadAmt() 
    {
        return eadAmt;
    }

    public void setEadInvcTyp(String eadInvcTyp) 
    {
        this.eadInvcTyp = eadInvcTyp;
    }

    public String getEadInvcTyp() 
    {
        return eadInvcTyp;
    }

    public void setEadBit(BigDecimal eadBit) 
    {
        this.eadBit = eadBit;
    }

    public BigDecimal getEadBit() 
    {
        return eadBit;
    }

    public void setEadIsPart(Integer eadIsPart) 
    {
        this.eadIsPart = eadIsPart;
    }

    public Integer getEadIsPart() 
    {
        return eadIsPart;
    }

    public void setEadPmcd(String eadPmcd) 
    {
        this.eadPmcd = eadPmcd;
    }

    public String getEadPmcd() 
    {
        return eadPmcd;
    }

    public void setEadPcd(String eadPcd) 
    {
        this.eadPcd = eadPcd;
    }

    public String getEadPcd() 
    {
        return eadPcd;
    }

    public void setEadPskuCd(String eadPskuCd) 
    {
        this.eadPskuCd = eadPskuCd;
    }

    public String getEadPskuCd() 
    {
        return eadPskuCd;
    }

    public void setEadPskuQty(BigDecimal eadPskuQty) 
    {
        this.eadPskuQty = eadPskuQty;
    }

    public BigDecimal getEadPskuQty() 
    {
        return eadPskuQty;
    }

    public void setEadPtyp(String eadPtyp) 
    {
        this.eadPtyp = eadPtyp;
    }

    public String getEadPtyp() 
    {
        return eadPtyp;
    }

    public void setEadTyp(String eadTyp) 
    {
        this.eadTyp = eadTyp;
    }

    public String getEadTyp() 
    {
        return eadTyp;
    }

    public void setEadAdpter(String eadAdpter) 
    {
        this.eadAdpter = eadAdpter;
    }

    public String getEadAdpter() 
    {
        return eadAdpter;
    }

    public void setEadAdpterMargin(BigDecimal eadAdpterMargin) 
    {
        this.eadAdpterMargin = eadAdpterMargin;
    }

    public BigDecimal getEadAdpterMargin() 
    {
        return eadAdpterMargin;
    }

    public void setEadIsAsset(Integer eadIsAsset) 
    {
        this.eadIsAsset = eadIsAsset;
    }

    public Integer getEadIsAsset() 
    {
        return eadIsAsset;
    }

    public void setEadDt(Date eadDt) 
    {
        this.eadDt = eadDt;
    }

    public Date getEadDt() 
    {
        return eadDt;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Integer rDel) 
    {
        this.rDel = rDel;
    }

    public Integer getrDel() 
    {
        return rDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eadEmcd", getEadEmcd())
            .append("eadEmccd", getEadEmccd())
            .append("eadSkuCd", getEadSkuCd())
            .append("eadSkuQty", getEadSkuQty())
            .append("eadAmt", getEadAmt())
            .append("eadInvcTyp", getEadInvcTyp())
            .append("eadBit", getEadBit())
            .append("eadIsPart", getEadIsPart())
            .append("eadPmcd", getEadPmcd())
            .append("eadPcd", getEadPcd())
            .append("eadPskuCd", getEadPskuCd())
            .append("eadPskuQty", getEadPskuQty())
            .append("eadPtyp", getEadPtyp())
            .append("eadTyp", getEadTyp())
            .append("eadAdpter", getEadAdpter())
            .append("eadAdpterMargin", getEadAdpterMargin())
            .append("eadIsAsset", getEadIsAsset())
            .append("eadDt", getEadDt())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
