package com.lucy.erp.service.impl;

import java.util.List;

import com.lucy.erp.domain.BasEmployee;
import com.lucy.erp.domain.SystemCodeDtl;
import com.lucy.erp.service.IBasEmployeeService;
import com.lucy.erp.service.ISystemCodeDtlService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.framework.datasource.DynamicDataSourceContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.SystemUserMapper;
import com.lucy.erp.domain.SystemUser;
import com.lucy.erp.service.ISystemUserService;

/**
 * 系统_用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@DataSource(DataSourceType.ERP)
public class SystemUserServiceImpl implements ISystemUserService
{
    @Autowired
    private SystemUserMapper systemUserMapper;

    @Autowired
    private IBasEmployeeService basEmployeeService;

    @Autowired
    private ISystemCodeDtlService systemCodeDtlService;

    /**
     * 查询系统_用户
     * 
     * @param id 系统_用户主键
     * @return 系统_用户
     */
    @Override
    public SystemUser selectSystemUserById(Long id)
    {
        return systemUserMapper.selectSystemUserById(id);
    }

    @Override
    public SystemUser selectSystemUserByLogno(String logno) {
        SystemUser user =  systemUserMapper.selectSystemUserByLogno(logno);
        BasEmployee basEmployee = basEmployeeService.selectBasEmployeeByBeNo(user.getSuEno());
        user.setDept(basEmployee.getBeDept());
        SystemCodeDtl systemCodeDtl = new SystemCodeDtl();
        systemCodeDtl.setScdTcd("DEPT");
        systemCodeDtl.setScdCd(basEmployee.getBeDept());
        List<SystemCodeDtl> systemCodeDtls = systemCodeDtlService.selectSystemCodeDtlList(systemCodeDtl);
        if(!systemCodeDtls.isEmpty()) {
            user.setDept(systemCodeDtls.get(0).getScdNm());
        }
        return user;
    }

    @Override
    public SystemUser selectSystemUserByEno(String eno) {
        return systemUserMapper.selectSystemUserByEno(eno);
    }

    @Override
    public SystemUser selectSystemUserManager(String logno) {
        SystemUser systemUser = selectSystemUserByLogno(logno);
        BasEmployee basEmployee = basEmployeeService.selectBasEmployeeByBeNo(systemUser.getSuEno());
        // 必须手动指定数据源，否则下面的SQL无法查出数据
        DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.ERP.name());
        if (StringUtils.isBlank(basEmployee.getBeMng())) {
            return null;
        }
        SystemUser manager = selectSystemUserByEno(basEmployee.getBeMng());
        if (manager == null) {
            return null;
        }
        if (StringUtils.isNotBlank(basEmployee.getBeDept())) {
            manager.setDept(basEmployee.getBeDept());
        }
        DynamicDataSourceContextHolder.clearDataSourceType();
        return manager;
    }

    /**
     * 查询系统_用户列表
     * 
     * @param systemUser 系统_用户
     * @return 系统_用户
     */
    @Override
    public List<SystemUser> selectSystemUserList(SystemUser systemUser)
    {
        return systemUserMapper.selectSystemUserList(systemUser);
    }

    /**
     * 新增系统_用户
     * 
     * @param systemUser 系统_用户
     * @return 结果
     */
    @Override
    public int insertSystemUser(SystemUser systemUser)
    {
        return systemUserMapper.insertSystemUser(systemUser);
    }

    /**
     * 修改系统_用户
     * 
     * @param systemUser 系统_用户
     * @return 结果
     */
    @Override
    public int updateSystemUser(SystemUser systemUser)
    {
        return systemUserMapper.updateSystemUser(systemUser);
    }

    /**
     * 批量删除系统_用户
     * 
     * @param ids 需要删除的系统_用户主键
     * @return 结果
     */
    @Override
    public int deleteSystemUserByIds(Long[] ids)
    {
        return systemUserMapper.deleteSystemUserByIds(ids);
    }

    /**
     * 删除系统_用户信息
     * 
     * @param id 系统_用户主键
     * @return 结果
     */
    @Override
    public int deleteSystemUserById(Long id)
    {
        return systemUserMapper.deleteSystemUserById(id);
    }
}
