<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LabProjectMapper">
    
    <resultMap type="LabProject" id="LabProjectResult">
        <result property="id"    column="id"    />
        <result property="lpCd"    column="lp_cd"    />
        <result property="lpPcd"    column="lp_pcd"    />
        <result property="lpNm"    column="lp_nm"    />
        <result property="lpContents"    column="lp_contents"    />
        <result property="lpPlanTimeStart"    column="lp_plan_time_start"    />
        <result property="lpPlanTimeEnd"    column="lp_plan_time_end"    />
        <result property="lpElapsedTime"    column="lp_elapsed_time"    />
        <result property="lpTimeStart"    column="lp_time_start"    />
        <result property="lpTimeEnd"    column="lp_time_end"    />
        <result property="lpSts"    column="lp_sts"    />
        <result property="lpCreateTime"    column="lp_create_time"    />
        <result property="lpUser"    column="lp_user"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lpFinalReport"    column="lp_final_report"    />
        <result property="lpFinalReportDt"    column="lp_final_report_dt"    />
        <result property="lpDemander"    column="lp_demander"    />
        <result property="lpSource"    column="lp_source"    />
        <result property="lpCas"    column="lp_cas"    />
        <result property="lpProdSource"    column="lp_prod_source"    />
        <result property="lpQuantityDemanded"    column="lp_quantity_demanded"    />
        <result property="lpRemark"    column="lp_remark"    />
        <result property="lpProdRemark"    column="lp_prod_remark"    />
        <result property="lpFinalConfirm"    column="lp_final_confirm"    />
        <result property="lpIsApproval"    column="lp_is_approval"    />
        <result property="lpQty"    column="lp_qty"    />
        <result property="lpQtyUnit"    column="lp_qty_unit"    />
    </resultMap>

    <sql id="selectLabProjectVo">
        select id, lp_cd, lp_pcd, lp_nm, lp_contents, lp_plan_time_start, lp_plan_time_end, lp_elapsed_time, lp_time_start, lp_time_end, lp_sts, lp_create_time, lp_user, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, lp_final_report, lp_final_report_dt, lp_demander, lp_source, lp_cas, lp_prod_source, lp_quantity_demanded, lp_remark, lp_prod_remark, lp_final_confirm, lp_is_approval, lp_qty, lp_qty_unit from lab_project
    </sql>

    <select id="selectLabProjectList" parameterType="LabProject" resultMap="LabProjectResult">
        <include refid="selectLabProjectVo"/>
        <where>  
            <if test="lpPcd != null  and lpPcd != ''"> and lp_pcd = #{lpPcd}</if>
            <if test="lpNm != null  and lpNm != ''"> and lp_nm = #{lpNm}</if>
            <if test="lpContents != null  and lpContents != ''"> and lp_contents = #{lpContents}</if>
            <if test="lpPlanTimeStart != null "> and lp_plan_time_start = #{lpPlanTimeStart}</if>
            <if test="lpPlanTimeEnd != null "> and lp_plan_time_end = #{lpPlanTimeEnd}</if>
            <if test="lpElapsedTime != null  and lpElapsedTime != ''"> and lp_elapsed_time = #{lpElapsedTime}</if>
            <if test="lpTimeStart != null "> and lp_time_start = #{lpTimeStart}</if>
            <if test="lpTimeEnd != null "> and lp_time_end = #{lpTimeEnd}</if>
            <if test="lpSts != null  and lpSts != ''"> and lp_sts = #{lpSts}</if>
            <if test="lpCreateTime != null "> and lp_create_time = #{lpCreateTime}</if>
            <if test="lpUser != null  and lpUser != ''"> and lp_user = #{lpUser}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="lpFinalReport != null  and lpFinalReport != ''"> and lp_final_report = #{lpFinalReport}</if>
            <if test="lpFinalReportDt != null "> and lp_final_report_dt = #{lpFinalReportDt}</if>
            <if test="lpDemander != null  and lpDemander != ''"> and lp_demander = #{lpDemander}</if>
            <if test="lpSource != null  and lpSource != ''"> and lp_source = #{lpSource}</if>
            <if test="lpCas != null  and lpCas != ''"> and lp_cas = #{lpCas}</if>
            <if test="lpProdSource != null  and lpProdSource != ''"> and lp_prod_source = #{lpProdSource}</if>
            <if test="lpQuantityDemanded != null  and lpQuantityDemanded != ''"> and lp_quantity_demanded = #{lpQuantityDemanded}</if>
            <if test="lpRemark != null  and lpRemark != ''"> and lp_remark = #{lpRemark}</if>
            <if test="lpProdRemark != null  and lpProdRemark != ''"> and lp_prod_remark = #{lpProdRemark}</if>
            <if test="lpFinalConfirm != null  and lpFinalConfirm != ''"> and lp_final_confirm = #{lpFinalConfirm}</if>
            <if test="lpIsApproval != null  and lpIsApproval != ''"> and lp_is_approval = #{lpIsApproval}</if>
            <if test="lpQty != null "> and lp_qty = #{lpQty}</if>
            <if test="lpQtyUnit != null  and lpQtyUnit != ''"> and lp_qty_unit = #{lpQtyUnit}</if>
        </where>
    </select>
    
    <select id="selectLabProjectByLpCd" parameterType="String" resultMap="LabProjectResult">
        <include refid="selectLabProjectVo"/>
        where lp_cd = #{lpCd}
    </select>

    <insert id="insertLabProject" parameterType="LabProject">
        insert into lab_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lpCd != null">lp_cd,</if>
            <if test="lpPcd != null">lp_pcd,</if>
            <if test="lpNm != null">lp_nm,</if>
            <if test="lpContents != null">lp_contents,</if>
            <if test="lpPlanTimeStart != null">lp_plan_time_start,</if>
            <if test="lpPlanTimeEnd != null">lp_plan_time_end,</if>
            <if test="lpElapsedTime != null">lp_elapsed_time,</if>
            <if test="lpTimeStart != null">lp_time_start,</if>
            <if test="lpTimeEnd != null">lp_time_end,</if>
            <if test="lpSts != null">lp_sts,</if>
            <if test="lpCreateTime != null">lp_create_time,</if>
            <if test="lpUser != null">lp_user,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="lpFinalReport != null">lp_final_report,</if>
            <if test="lpFinalReportDt != null">lp_final_report_dt,</if>
            <if test="lpDemander != null">lp_demander,</if>
            <if test="lpSource != null">lp_source,</if>
            <if test="lpCas != null">lp_cas,</if>
            <if test="lpProdSource != null">lp_prod_source,</if>
            <if test="lpQuantityDemanded != null">lp_quantity_demanded,</if>
            <if test="lpRemark != null">lp_remark,</if>
            <if test="lpProdRemark != null">lp_prod_remark,</if>
            <if test="lpFinalConfirm != null">lp_final_confirm,</if>
            <if test="lpIsApproval != null">lp_is_approval,</if>
            <if test="lpQty != null">lp_qty,</if>
            <if test="lpQtyUnit != null">lp_qty_unit,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lpCd != null">#{lpCd},</if>
            <if test="lpPcd != null">#{lpPcd},</if>
            <if test="lpNm != null">#{lpNm},</if>
            <if test="lpContents != null">#{lpContents},</if>
            <if test="lpPlanTimeStart != null">#{lpPlanTimeStart},</if>
            <if test="lpPlanTimeEnd != null">#{lpPlanTimeEnd},</if>
            <if test="lpElapsedTime != null">#{lpElapsedTime},</if>
            <if test="lpTimeStart != null">#{lpTimeStart},</if>
            <if test="lpTimeEnd != null">#{lpTimeEnd},</if>
            <if test="lpSts != null">#{lpSts},</if>
            <if test="lpCreateTime != null">#{lpCreateTime},</if>
            <if test="lpUser != null">#{lpUser},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="lpFinalReport != null">#{lpFinalReport},</if>
            <if test="lpFinalReportDt != null">#{lpFinalReportDt},</if>
            <if test="lpDemander != null">#{lpDemander},</if>
            <if test="lpSource != null">#{lpSource},</if>
            <if test="lpCas != null">#{lpCas},</if>
            <if test="lpProdSource != null">#{lpProdSource},</if>
            <if test="lpQuantityDemanded != null">#{lpQuantityDemanded},</if>
            <if test="lpRemark != null">#{lpRemark},</if>
            <if test="lpProdRemark != null">#{lpProdRemark},</if>
            <if test="lpFinalConfirm != null">#{lpFinalConfirm},</if>
            <if test="lpIsApproval != null">#{lpIsApproval},</if>
            <if test="lpQty != null">#{lpQty},</if>
            <if test="lpQtyUnit != null">#{lpQtyUnit},</if>
         </trim>
    </insert>

    <update id="updateLabProject" parameterType="LabProject">
        update lab_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lpPcd != null">lp_pcd = #{lpPcd},</if>
            <if test="lpNm != null">lp_nm = #{lpNm},</if>
            <if test="lpContents != null">lp_contents = #{lpContents},</if>
            <if test="lpPlanTimeStart != null">lp_plan_time_start = #{lpPlanTimeStart},</if>
            <if test="lpPlanTimeEnd != null">lp_plan_time_end = #{lpPlanTimeEnd},</if>
            <if test="lpElapsedTime != null">lp_elapsed_time = #{lpElapsedTime},</if>
            <if test="lpTimeStart != null">lp_time_start = #{lpTimeStart},</if>
            <if test="lpTimeEnd != null">lp_time_end = #{lpTimeEnd},</if>
            <if test="lpSts != null">lp_sts = #{lpSts},</if>
            <if test="lpCreateTime != null">lp_create_time = #{lpCreateTime},</if>
            <if test="lpUser != null">lp_user = #{lpUser},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="lpFinalReport != null">lp_final_report = #{lpFinalReport},</if>
            <if test="lpFinalReportDt != null">lp_final_report_dt = #{lpFinalReportDt},</if>
            <if test="lpDemander != null">lp_demander = #{lpDemander},</if>
            <if test="lpSource != null">lp_source = #{lpSource},</if>
            <if test="lpCas != null">lp_cas = #{lpCas},</if>
            <if test="lpProdSource != null">lp_prod_source = #{lpProdSource},</if>
            <if test="lpQuantityDemanded != null">lp_quantity_demanded = #{lpQuantityDemanded},</if>
            <if test="lpRemark != null">lp_remark = #{lpRemark},</if>
            <if test="lpProdRemark != null">lp_prod_remark = #{lpProdRemark},</if>
            <if test="lpFinalConfirm != null">lp_final_confirm = #{lpFinalConfirm},</if>
            <if test="lpIsApproval != null">lp_is_approval = #{lpIsApproval},</if>
            <if test="lpQty != null">lp_qty = #{lpQty},</if>
            <if test="lpQtyUnit != null">lp_qty_unit = #{lpQtyUnit},</if>
        </trim>
        where lp_cd = #{lpCd}
    </update>

    <delete id="deleteLabProjectByLpCd" parameterType="String">
        delete from lab_project where lp_cd = #{lpCd}
    </delete>

    <delete id="deleteLabProjectByLpCds" parameterType="String">
        delete from lab_project where lp_cd in 
        <foreach item="lpCd" collection="array" open="(" separator="," close=")">
            #{lpCd}
        </foreach>
    </delete>
</mapper>