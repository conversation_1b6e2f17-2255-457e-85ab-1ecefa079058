package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.FinAccMasterRecordsUniq;
import com.lucy.erp.service.IFinAccMasterRecordsUniqService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 账套Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/fin/uniq")
public class FinAccMasterRecordsUniqController extends BaseController
{
    @Autowired
    private IFinAccMasterRecordsUniqService finAccMasterRecordsUniqService;

    /**
     * 查询账套列表
     */
    @PreAuthorize("@ss.hasPermi('fin:uniq:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        startPage();
        List<FinAccMasterRecordsUniq> list = finAccMasterRecordsUniqService.selectFinAccMasterRecordsUniqList(finAccMasterRecordsUniq);
        return getDataTable(list);
    }

    /**
     * 导出账套列表
     */
    @PreAuthorize("@ss.hasPermi('fin:uniq:export')")
    @Log(title = "账套", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        List<FinAccMasterRecordsUniq> list = finAccMasterRecordsUniqService.selectFinAccMasterRecordsUniqList(finAccMasterRecordsUniq);
        ExcelUtil<FinAccMasterRecordsUniq> util = new ExcelUtil<FinAccMasterRecordsUniq>(FinAccMasterRecordsUniq.class);
        util.exportExcel(response, list, "账套数据");
    }

    /**
     * 获取账套详细信息
     */
    @PreAuthorize("@ss.hasPermi('fin:uniq:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(finAccMasterRecordsUniqService.selectFinAccMasterRecordsUniqById(id));
    }

    /**
     * 新增账套
     */
    @PreAuthorize("@ss.hasPermi('fin:uniq:add')")
    @Log(title = "账套", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        return toAjax(finAccMasterRecordsUniqService.insertFinAccMasterRecordsUniq(finAccMasterRecordsUniq));
    }

    /**
     * 修改账套
     */
    @PreAuthorize("@ss.hasPermi('fin:uniq:edit')")
    @Log(title = "账套", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        return toAjax(finAccMasterRecordsUniqService.updateFinAccMasterRecordsUniq(finAccMasterRecordsUniq));
    }

    /**
     * 删除账套
     */
    @PreAuthorize("@ss.hasPermi('fin:uniq:remove')")
    @Log(title = "账套", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(finAccMasterRecordsUniqService.deleteFinAccMasterRecordsUniqByIds(ids));
    }
}
