package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.SystemCodeDtl;
import com.lucy.erp.service.ISystemCodeDtlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 系统_字典Controller
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/system/code")
public class SystemCodeDtlController extends BaseController
{
    @Autowired
    private ISystemCodeDtlService systemCodeDtlService;

    /**
     * 查询系统_字典列表
     */
    @PreAuthorize("@ss.hasPermi('system:code:list')")
    @GetMapping("/list")
    public TableDataInfo list(SystemCodeDtl systemCodeDtl)
    {
        startPage();
        List<SystemCodeDtl> list = systemCodeDtlService.selectSystemCodeDtlList(systemCodeDtl);
        return getDataTable(list);
    }

    /**
     * 导出系统_字典列表
     */
    @PreAuthorize("@ss.hasPermi('system:code:export')")
    @Log(title = "系统_字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SystemCodeDtl systemCodeDtl)
    {
        List<SystemCodeDtl> list = systemCodeDtlService.selectSystemCodeDtlList(systemCodeDtl);
        ExcelUtil<SystemCodeDtl> util = new ExcelUtil<SystemCodeDtl>(SystemCodeDtl.class);
        util.exportExcel(response, list, "系统_字典数据");
    }

    /**
     * 获取系统_字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:code:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(systemCodeDtlService.selectSystemCodeDtlById(id));
    }

    /**
     * 新增系统_字典
     */
    @PreAuthorize("@ss.hasPermi('system:code:add')")
    @Log(title = "系统_字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SystemCodeDtl systemCodeDtl)
    {
        return toAjax(systemCodeDtlService.insertSystemCodeDtl(systemCodeDtl));
    }

    /**
     * 修改系统_字典
     */
    @PreAuthorize("@ss.hasPermi('system:code:edit')")
    @Log(title = "系统_字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SystemCodeDtl systemCodeDtl)
    {
        return toAjax(systemCodeDtlService.updateSystemCodeDtl(systemCodeDtl));
    }

    /**
     * 删除系统_字典
     */
    @PreAuthorize("@ss.hasPermi('system:code:remove')")
    @Log(title = "系统_字典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(systemCodeDtlService.deleteSystemCodeDtlByIds(ids));
    }
}
