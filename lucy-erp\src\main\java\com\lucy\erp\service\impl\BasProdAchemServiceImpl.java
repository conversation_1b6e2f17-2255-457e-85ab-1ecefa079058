package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasProdAchemMapper;
import com.lucy.erp.domain.BasProdAchem;
import com.lucy.erp.service.IBasProdAchemService;

/**
 * 基础_化学_主
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasProdAchemServiceImpl implements IBasProdAchemService 
{
    @Autowired
    private BasProdAchemMapper basProdAchemMapper;

    /**
     * 查询基础_化学_主

     * 
     * @param bpacCd 基础_化学_主
主键
     * @return 基础_化学_主

     */
    @Override
    public BasProdAchem selectBasProdAchemByBpacCd(String bpacCd)
    {
        return basProdAchemMapper.selectBasProdAchemByBpacCd(bpacCd);
    }

    /**
     * 查询基础_化学_主
列表
     * 
     * @param basProdAchem 基础_化学_主

     * @return 基础_化学_主

     */
    @Override
    public List<BasProdAchem> selectBasProdAchemList(BasProdAchem basProdAchem)
    {
        return basProdAchemMapper.selectBasProdAchemList(basProdAchem);
    }

    /**
     * 新增基础_化学_主

     * 
     * @param basProdAchem 基础_化学_主

     * @return 结果
     */
    @Override
    public int insertBasProdAchem(BasProdAchem basProdAchem)
    {
        return basProdAchemMapper.insertBasProdAchem(basProdAchem);
    }

    /**
     * 修改基础_化学_主

     * 
     * @param basProdAchem 基础_化学_主

     * @return 结果
     */
    @Override
    public int updateBasProdAchem(BasProdAchem basProdAchem)
    {
        return basProdAchemMapper.updateBasProdAchem(basProdAchem);
    }

    /**
     * 批量删除基础_化学_主

     * 
     * @param bpacCds 需要删除的基础_化学_主
主键
     * @return 结果
     */
    @Override
    public int deleteBasProdAchemByBpacCds(String[] bpacCds)
    {
        return basProdAchemMapper.deleteBasProdAchemByBpacCds(bpacCds);
    }

    /**
     * 删除基础_化学_主
信息
     * 
     * @param bpacCd 基础_化学_主
主键
     * @return 结果
     */
    @Override
    public int deleteBasProdAchemByBpacCd(String bpacCd)
    {
        return basProdAchemMapper.deleteBasProdAchemByBpacCd(bpacCd);
    }
}
