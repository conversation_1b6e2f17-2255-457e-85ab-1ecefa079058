package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasProdAchem;
import com.lucy.erp.service.IBasProdAchemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_化学_主
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/prod/achem")
public class BasProdAchemController extends BaseController
{
    @Autowired
    private IBasProdAchemService basProdAchemService;

    /**
     * 查询基础_化学_主
列表
     */
    @PreAuthorize("@ss.hasPermi('prod:achem:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasProdAchem basProdAchem)
    {
        startPage();
        List<BasProdAchem> list = basProdAchemService.selectBasProdAchemList(basProdAchem);
        return getDataTable(list);
    }

    /**
     * 导出基础_化学_主
列表
     */
    @PreAuthorize("@ss.hasPermi('prod:achem:export')")
    @Log(title = "基础_化学_主 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasProdAchem basProdAchem)
    {
        List<BasProdAchem> list = basProdAchemService.selectBasProdAchemList(basProdAchem);
        ExcelUtil<BasProdAchem> util = new ExcelUtil<BasProdAchem>(BasProdAchem.class);
        util.exportExcel(response, list, "基础_化学_主数据");
    }

    /**
     * 获取基础_化学_主
详细信息
     */
    @PreAuthorize("@ss.hasPermi('prod:achem:query')")
    @GetMapping(value = "/{bpacCd}")
    public AjaxResult getInfo(@PathVariable("bpacCd") String bpacCd)
    {
        return success(basProdAchemService.selectBasProdAchemByBpacCd(bpacCd));
    }

    /**
     * 新增基础_化学_主

     */
    @PreAuthorize("@ss.hasPermi('prod:achem:add')")
    @Log(title = "基础_化学_主 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasProdAchem basProdAchem)
    {
        return toAjax(basProdAchemService.insertBasProdAchem(basProdAchem));
    }

    /**
     * 修改基础_化学_主

     */
    @PreAuthorize("@ss.hasPermi('prod:achem:edit')")
    @Log(title = "基础_化学_主 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasProdAchem basProdAchem)
    {
        return toAjax(basProdAchemService.updateBasProdAchem(basProdAchem));
    }

    /**
     * 删除基础_化学_主

     */
    @PreAuthorize("@ss.hasPermi('prod:achem:remove')")
    @Log(title = "基础_化学_主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bpacCds}")
    public AjaxResult remove(@PathVariable String[] bpacCds)
    {
        return toAjax(basProdAchemService.deleteBasProdAchemByBpacCds(bpacCds));
    }
}
