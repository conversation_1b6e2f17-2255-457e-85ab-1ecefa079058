package com.lucy.inventoryoe.mapper;

import java.util.List;
import com.lucy.inventoryoe.domain.LogInventoryList2025010101;

/**
 * 库存清单快照Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface LogInventoryList2025010101Mapper 
{
    /**
     * 查询库存清单快照
     * 
     * @param id 库存清单快照主键
     * @return 库存清单快照
     */
    public LogInventoryList2025010101 selectLogInventoryList2025010101ById(Long id);

    /**
     * 查询库存清单快照列表
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 库存清单快照集合
     */
    public List<LogInventoryList2025010101> selectLogInventoryList2025010101List(LogInventoryList2025010101 logInventoryList2025010101);

    /**
     * 新增库存清单快照
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 结果
     */
    public int insertLogInventoryList2025010101(LogInventoryList2025010101 logInventoryList2025010101);

    /**
     * 修改库存清单快照
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 结果
     */
    public int updateLogInventoryList2025010101(LogInventoryList2025010101 logInventoryList2025010101);

    /**
     * 删除库存清单快照
     * 
     * @param id 库存清单快照主键
     * @return 结果
     */
    public int deleteLogInventoryList2025010101ById(Long id);

    /**
     * 批量删除库存清单快照
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLogInventoryList2025010101ByIds(Long[] ids);
}
