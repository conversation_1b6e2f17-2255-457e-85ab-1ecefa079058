package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasProdLine;
import com.lucy.erp.service.IBasProdLineService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产线数据Controller
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/prod/line")
public class BasProdLineController extends BaseController
{
    @Autowired
    private IBasProdLineService basProdLineService;

    /**
     * 查询产线数据列表
     */
    @PreAuthorize("@ss.hasPermi('prod:line:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasProdLine basProdLine)
    {
        startPage();
        List<BasProdLine> list = basProdLineService.selectBasProdLineList(basProdLine);
        return getDataTable(list);
    }

    /**
     * 导出产线数据列表
     */
    @PreAuthorize("@ss.hasPermi('prod:line:export')")
    @Log(title = "产线数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasProdLine basProdLine)
    {
        List<BasProdLine> list = basProdLineService.selectBasProdLineList(basProdLine);
        ExcelUtil<BasProdLine> util = new ExcelUtil<BasProdLine>(BasProdLine.class);
        util.exportExcel(response, list, "产线数据数据");
    }

    /**
     * 获取产线数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('prod:line:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(basProdLineService.selectBasProdLineById(id));
    }

    /**
     * 新增产线数据
     */
    @PreAuthorize("@ss.hasPermi('prod:line:add')")
    @Log(title = "产线数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasProdLine basProdLine)
    {
        return toAjax(basProdLineService.insertBasProdLine(basProdLine));
    }

    /**
     * 修改产线数据
     */
    @PreAuthorize("@ss.hasPermi('prod:line:edit')")
    @Log(title = "产线数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasProdLine basProdLine)
    {
        return toAjax(basProdLineService.updateBasProdLine(basProdLine));
    }

    /**
     * 删除产线数据
     */
    @PreAuthorize("@ss.hasPermi('prod:line:remove')")
    @Log(title = "产线数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(basProdLineService.deleteBasProdLineByIds(ids));
    }
}
