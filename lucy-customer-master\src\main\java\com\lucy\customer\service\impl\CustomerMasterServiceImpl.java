package com.lucy.customer.service.impl;

import java.util.List;

import com.lucy.customer.domain.CustomerCompany;
import com.lucy.erp.domain.BasCompany;
import com.lucy.erp.domain.BasCustorgMst;
import com.lucy.erp.service.IBasCompanyService;
import com.lucy.erp.service.IBasCustorgMstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.customer.mapper.CustomerMasterMapper;
import com.lucy.customer.domain.CustomerMaster;
import com.lucy.customer.service.ICustomerMasterService;

/**
 * 客户主数据 Customer masterService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
public class CustomerMasterServiceImpl implements ICustomerMasterService 
{
    @Autowired
    private CustomerMasterMapper customerMasterMapper;

    @Autowired
    private IBasCustorgMstService basCustorgMstService;

    @Autowired
    private IBasCompanyService basCompanyService;

    /**
     * 查询客户主数据 Customer master
     * 
     * @param code 客户主数据 Customer master主键
     * @return 客户主数据 Customer master
     */
    @Override
    public CustomerMaster selectCustomerMasterByCode(String code)
    {
        return customerMasterMapper.selectCustomerMasterByCode(code);
    }

    /**
     * 查询客户主数据 Customer master列表
     * 
     * @param customerMaster 客户主数据 Customer master
     * @return 客户主数据 Customer master
     */
    @Override
    public List<CustomerMaster> selectCustomerMasterList(CustomerMaster customerMaster)
    {
        return customerMasterMapper.selectCustomerMasterList(customerMaster);
    }

    /**
     * 新增客户主数据 Customer master
     * 
     * @param customerMaster 客户主数据 Customer master
     * @return 结果
     */
    @Override
    public int insertCustomerMaster(CustomerMaster customerMaster)
    {
        return customerMasterMapper.insertCustomerMaster(customerMaster);
    }

    /**
     * 修改客户主数据 Customer master
     * 
     * @param customerMaster 客户主数据 Customer master
     * @return 结果
     */
    @Override
    public int updateCustomerMaster(CustomerMaster customerMaster)
    {
        return customerMasterMapper.updateCustomerMaster(customerMaster);
    }

    /**
     * 批量删除客户主数据 Customer master
     * 
     * @param codes 需要删除的客户主数据 Customer master主键
     * @return 结果
     */
    @Override
    public int deleteCustomerMasterByCodes(String[] codes)
    {
        return customerMasterMapper.deleteCustomerMasterByCodes(codes);
    }

    /**
     * 删除客户主数据 Customer master信息
     * 
     * @param code 客户主数据 Customer master主键
     * @return 结果
     */
    @Override
    public int deleteCustomerMasterByCode(String code)
    {
        return customerMasterMapper.deleteCustomerMasterByCode(code);
    }

    /**
     * 验证一级组织是否存在
     * 如果不存在则新增
     * @param code 一级组织编号
     */
    @Override
    public void insertCustomerMasterIfNotExist(String code, String companyCode) {
        CustomerMaster customerMaster = selectCustomerMasterByCode(code);
        if (customerMaster == null) {
            BasCustorgMst basCustorgMst = basCustorgMstService.selectBasCustorgMstByBcmCd(code);
            BasCompany basCompany = basCompanyService.selectBasCompanyByBcCd(companyCode);
            CustomerMaster customerMasterNew = new CustomerMaster();
            customerMasterNew.setCode(code);
            customerMasterNew.setName(basCustorgMst.getBcmNm());
            // TODO
            // 信用等级
//            customerMasterNew.setCreditRating("");
            // 付款条件
            customerMasterNew.setPaymentTerms("");
            customerMasterNew.setCompanyCode(companyCode);
            customerMasterNew.setCompanyName(basCompany.getBcNm());
            customerMasterNew.setTaxNo(basCompany.getBcTrcno());
            // 客户类别
            customerMasterNew.setCategory("");
            // 客户分类-小类
            customerMasterNew.setClassificationSubclass("");
            // 客户分类-大类
            customerMasterNew.setClassificationMajor("");
            // 匹配客户档案信息【应用领域、省份等信息】
            customerMasterNew.setProfile("");
            // 客户同控合并情况
            customerMasterNew.setMergerSituation("");
            insertCustomerMaster(customerMasterNew);
        }
    }

}
