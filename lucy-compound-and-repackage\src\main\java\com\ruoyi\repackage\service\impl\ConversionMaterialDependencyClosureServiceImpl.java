package com.ruoyi.repackage.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionMaterialDependencyClosureMapper;
import com.ruoyi.repackage.domain.ConversionMaterialDependencyClosure;
import com.ruoyi.repackage.service.IConversionMaterialDependencyClosureService;

/**
 * 物料依赖图谱Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionMaterialDependencyClosureServiceImpl implements IConversionMaterialDependencyClosureService 
{
    @Autowired
    private ConversionMaterialDependencyClosureMapper conversionMaterialDependencyClosureMapper;

    /**
     * 查询物料依赖图谱
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 物料依赖图谱
     */
    @Override
    public ConversionMaterialDependencyClosure selectConversionMaterialDependencyClosureByAncestorSku(String ancestorSku)
    {
        return conversionMaterialDependencyClosureMapper.selectConversionMaterialDependencyClosureByAncestorSku(ancestorSku);
    }

    /**
     * 查询物料依赖图谱列表
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 物料依赖图谱
     */
    @Override
    public List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureList(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return conversionMaterialDependencyClosureMapper.selectConversionMaterialDependencyClosureList(conversionMaterialDependencyClosure);
    }

    /**
     * 新增物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    @Override
    public int insertConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return conversionMaterialDependencyClosureMapper.insertConversionMaterialDependencyClosure(conversionMaterialDependencyClosure);
    }

    /**
     * 修改物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    @Override
    public int updateConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return conversionMaterialDependencyClosureMapper.updateConversionMaterialDependencyClosure(conversionMaterialDependencyClosure);
    }

    /**
     * 批量删除物料依赖图谱
     * 
     * @param ancestorSkus 需要删除的物料依赖图谱主键
     * @return 结果
     */
    @Override
    public int deleteConversionMaterialDependencyClosureByAncestorSkus(String[] ancestorSkus)
    {
        return conversionMaterialDependencyClosureMapper.deleteConversionMaterialDependencyClosureByAncestorSkus(ancestorSkus);
    }

    /**
     * 删除物料依赖图谱信息
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 结果
     */
    @Override
    public int deleteConversionMaterialDependencyClosureByAncestorSku(String ancestorSku)
    {
        return conversionMaterialDependencyClosureMapper.deleteConversionMaterialDependencyClosureByAncestorSku(ancestorSku);
    }
}
