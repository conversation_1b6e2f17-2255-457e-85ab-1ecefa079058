package com.lucy.inventoryoe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.inventoryoe.domain.Zw20241231FinAccInventoryOe2;
import com.lucy.inventoryoe.service.IZw20241231FinAccInventoryOe2Service;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 24期初库存初始化Controller
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@RestController
@RequestMapping("/lucyinventoryoe/inventoryOE")
public class Zw20241231FinAccInventoryOe2Controller extends BaseController
{
    @Autowired
    private IZw20241231FinAccInventoryOe2Service zw20241231FinAccInventoryOe2Service;

    /**
     * 查询24期初库存初始化列表
     */
    @PreAuthorize("@ss.hasPermi('lucyinventoryoe:inventoryOE:list')")
    @GetMapping("/list")
    public TableDataInfo list(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2)
    {
        startPage();
        List<Zw20241231FinAccInventoryOe2> list = zw20241231FinAccInventoryOe2Service.selectZw20241231FinAccInventoryOe2List(zw20241231FinAccInventoryOe2);
        return getDataTable(list);
    }

    /**
     * 导出24期初库存初始化列表
     */
    @PreAuthorize("@ss.hasPermi('lucyinventoryoe:inventoryOE:export')")
    @Log(title = "24期初库存初始化", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2)
    {
        List<Zw20241231FinAccInventoryOe2> list = zw20241231FinAccInventoryOe2Service.selectZw20241231FinAccInventoryOe2List(zw20241231FinAccInventoryOe2);
        ExcelUtil<Zw20241231FinAccInventoryOe2> util = new ExcelUtil<Zw20241231FinAccInventoryOe2>(Zw20241231FinAccInventoryOe2.class);
        util.exportExcel(response, list, "24期初库存初始化数据");
    }

    /**
     * 获取24期初库存初始化详细信息
     */
    @PreAuthorize("@ss.hasPermi('lucyinventoryoe:inventoryOE:query')")
    @GetMapping(value = "/{orgcd}")
    public AjaxResult getInfo(@PathVariable("orgcd") String orgcd)
    {
        return success(zw20241231FinAccInventoryOe2Service.selectZw20241231FinAccInventoryOe2ByOrgcd(orgcd));
    }

    /**
     * 新增24期初库存初始化
     */
    @PreAuthorize("@ss.hasPermi('lucyinventoryoe:inventoryOE:add')")
    @Log(title = "24期初库存初始化", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2)
    {
        return toAjax(zw20241231FinAccInventoryOe2Service.insertZw20241231FinAccInventoryOe2(zw20241231FinAccInventoryOe2));
    }

    /**
     * 修改24期初库存初始化
     */
    @PreAuthorize("@ss.hasPermi('lucyinventoryoe:inventoryOE:edit')")
    @Log(title = "24期初库存初始化", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2)
    {
        return toAjax(zw20241231FinAccInventoryOe2Service.updateZw20241231FinAccInventoryOe2(zw20241231FinAccInventoryOe2));
    }

    /**
     * 删除24期初库存初始化
     */
    @PreAuthorize("@ss.hasPermi('lucyinventoryoe:inventoryOE:remove')")
    @Log(title = "24期初库存初始化", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orgcds}")
    public AjaxResult remove(@PathVariable String[] orgcds)
    {
        return toAjax(zw20241231FinAccInventoryOe2Service.deleteZw20241231FinAccInventoryOe2ByOrgcds(orgcds));
    }
}
