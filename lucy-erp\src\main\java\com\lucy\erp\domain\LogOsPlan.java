package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 物流_出库_计划
对象 log_os_plan
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public class LogOsPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 计划批号 */
    @Excel(name = "计划批号")
    private String lopPsn;

    /** 计划类别 */
    @Excel(name = "计划类别")
    private String lopTyp;

    /** 出入库类别 */
    @Excel(name = "出入库类别")
    private String lopIotyp;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String lopPcd;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String lopPpcd;

    /** 批号 */
    @Excel(name = "批号")
    private String lopBn;

    /** 批号 */
    @Excel(name = "批号")
    private String lopPbn;

    /** 批号 */
    @Excel(name = "批号")
    private String lopSbn;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal lopQty;

    /** 仓库 */
    @Excel(name = "仓库")
    private String lopDmcd;

    /** 库位 */
    @Excel(name = "库位")
    private String lopSlcd;

    /** 库位类别 */
    @Excel(name = "库位类别")
    private String lopTcd;

    /** 是否价税分离_成本 */
    @Excel(name = "是否价税分离_成本")
    private Long lopIspart;

    /** 税率_成本 */
    @Excel(name = "税率_成本")
    private BigDecimal lopBit;

    /** 其他出库成本-不含税 */
    @Excel(name = "其他出库成本-不含税")
    private BigDecimal lopDeliverCost;

    /** 单价_成本 */
    @Excel(name = "单价_成本")
    private BigDecimal lopUnitp;

    /** 总价含税_成本 */
    @Excel(name = "总价含税_成本")
    private BigDecimal lopTotalp;

    /** 总货值_成本 */
    @Excel(name = "总货值_成本")
    private BigDecimal lopCargov;

    /** 总税额_成本 */
    @Excel(name = "总税额_成本")
    private BigDecimal lopNtaxv;

    /** 税额_成本 */
    @Excel(name = "税额_成本")
    private BigDecimal lopTax;

    /** 集团成本_不含税 */
    @Excel(name = "集团成本_不含税")
    private BigDecimal logGroupCost;

    /** 物流成本_不含税 */
    @Excel(name = "物流成本_不含税")
    private BigDecimal logLogisticsCost;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long lopIspartOld;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal lopBitOld;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal lopUnitpOld;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal lopTotalpOld;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal lopCargovOld;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal lopNtaxvOld;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal lopTaxOld;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String lopOrgmcdOld;

    /** 业务单号_销售合同明细编号 */
    @Excel(name = "业务单号_销售合同明细编号")
    private String lopBizcd;

    /** 业务单号_销售合同编号 */
    @Excel(name = "业务单号_销售合同编号")
    private String lopBizcdMcd;

    /** 业务单号_销售发货记录编号 */
    @Excel(name = "业务单号_销售发货记录编号")
    private String lopBizcdRcd;

    /** 业务单号_出库箱单编号 */
    @Excel(name = "业务单号_出库箱单编号")
    private String lopBizcdRmcd;

    /** 用户编号 */
    @Excel(name = "用户编号")
    private String lopUcd;

    /** 计划制作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划制作时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lopDt;

    /** 快递公司 */
    @Excel(name = "快递公司")
    private String lopEps;

    /** 运单号 */
    @Excel(name = "运单号")
    private String lopTn;

    /** 箱号 */
    @Excel(name = "箱号")
    private Long lopBoxno;

    /** 发运时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发运时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lopOutdt;

    /** 状态 */
    @Excel(name = "状态")
    private String lopSts;

    /** 销售合同库存备注 */
    @Excel(name = "销售合同库存备注")
    private String lopRmk;

    /** 关联交易对应业务的出库计划id */
    @Excel(name = "关联交易对应业务的出库计划id")
    private Long paramkeysId;

    /** paramKeys */
    @Excel(name = "paramKeys")
    private String paramKeys;

    /** 关联交易销售出库id对应关联交易采购入到货列表ID */
    @Excel(name = "关联交易销售出库id对应关联交易采购入到货列表ID")
    private Long paramkeysInsideId;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 库存批次加密 */
    @Excel(name = "库存批次加密")
    private String lopBnEncrypt;

    /** 资产所属组织编号 */
    @Excel(name = "资产所属组织编号")
    private String lopOrgmcd;

    /** 签收时间 */
    @Excel(name = "签收时间")
    private String receiveTime;

    /** 到货列表cd */
    @Excel(name = "到货列表cd")
    private String lalId;

    /** 确认收入时间 */
    @Excel(name = "确认收入时间")
    private String lopConfirmtime;

    /** 记账标记 0 是未刷,1 是标记 */
    @Excel(name = "记账标记 0 是未刷,1 是标记")
    private Integer lopFinFlag;

    /** 是否单独装箱 01：是 */
    @Excel(name = "是否单独装箱 01：是")
    private String lopSeparatePacking;

    /** 01：不能发货 */
    @Excel(name = "01：不能发货")
    private String logNoDeliver;

    /** 平台单号 */
    @Excel(name = "平台单号")
    private String lopOpcd;

    /** 取货完成时间 */
    @Excel(name = "取货完成时间")
    private String lopPickupTime;

    /** 合资公司采购入库数量 */
    @Excel(name = "合资公司采购入库数量")
    private BigDecimal lopInqty;

    /** 出口货物装箱号 */
    @Excel(name = "出口货物装箱号")
    private String lopBoxnoExport;

    /** 出口货物装托盘号 */
    @Excel(name = "出口货物装托盘号")
    private String lopPtnoExport;

    private String startDate;
    private String endDate;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLopPsn(String lopPsn) 
    {
        this.lopPsn = lopPsn;
    }

    public String getLopPsn() 
    {
        return lopPsn;
    }

    public void setLopTyp(String lopTyp) 
    {
        this.lopTyp = lopTyp;
    }

    public String getLopTyp() 
    {
        return lopTyp;
    }

    public void setLopIotyp(String lopIotyp) 
    {
        this.lopIotyp = lopIotyp;
    }

    public String getLopIotyp() 
    {
        return lopIotyp;
    }

    public void setLopPcd(String lopPcd) 
    {
        this.lopPcd = lopPcd;
    }

    public String getLopPcd() 
    {
        return lopPcd;
    }

    public void setLopPpcd(String lopPpcd) 
    {
        this.lopPpcd = lopPpcd;
    }

    public String getLopPpcd() 
    {
        return lopPpcd;
    }

    public void setLopBn(String lopBn) 
    {
        this.lopBn = lopBn;
    }

    public String getLopBn() 
    {
        return lopBn;
    }

    public void setLopPbn(String lopPbn) 
    {
        this.lopPbn = lopPbn;
    }

    public String getLopPbn() 
    {
        return lopPbn;
    }

    public void setLopSbn(String lopSbn) 
    {
        this.lopSbn = lopSbn;
    }

    public String getLopSbn() 
    {
        return lopSbn;
    }

    public void setLopQty(BigDecimal lopQty) 
    {
        this.lopQty = lopQty;
    }

    public BigDecimal getLopQty() 
    {
        return lopQty;
    }

    public void setLopDmcd(String lopDmcd) 
    {
        this.lopDmcd = lopDmcd;
    }

    public String getLopDmcd() 
    {
        return lopDmcd;
    }

    public void setLopSlcd(String lopSlcd) 
    {
        this.lopSlcd = lopSlcd;
    }

    public String getLopSlcd() 
    {
        return lopSlcd;
    }

    public void setLopTcd(String lopTcd) 
    {
        this.lopTcd = lopTcd;
    }

    public String getLopTcd() 
    {
        return lopTcd;
    }

    public void setLopIspart(Long lopIspart) 
    {
        this.lopIspart = lopIspart;
    }

    public Long getLopIspart() 
    {
        return lopIspart;
    }

    public void setLopBit(BigDecimal lopBit) 
    {
        this.lopBit = lopBit;
    }

    public BigDecimal getLopBit() 
    {
        return lopBit;
    }

    public void setLopDeliverCost(BigDecimal lopDeliverCost) 
    {
        this.lopDeliverCost = lopDeliverCost;
    }

    public BigDecimal getLopDeliverCost() 
    {
        return lopDeliverCost;
    }

    public void setLopUnitp(BigDecimal lopUnitp) 
    {
        this.lopUnitp = lopUnitp;
    }

    public BigDecimal getLopUnitp() 
    {
        return lopUnitp;
    }

    public void setLopTotalp(BigDecimal lopTotalp) 
    {
        this.lopTotalp = lopTotalp;
    }

    public BigDecimal getLopTotalp() 
    {
        return lopTotalp;
    }

    public void setLopCargov(BigDecimal lopCargov) 
    {
        this.lopCargov = lopCargov;
    }

    public BigDecimal getLopCargov() 
    {
        return lopCargov;
    }

    public void setLopNtaxv(BigDecimal lopNtaxv) 
    {
        this.lopNtaxv = lopNtaxv;
    }

    public BigDecimal getLopNtaxv() 
    {
        return lopNtaxv;
    }

    public void setLopTax(BigDecimal lopTax) 
    {
        this.lopTax = lopTax;
    }

    public BigDecimal getLopTax() 
    {
        return lopTax;
    }

    public void setLogGroupCost(BigDecimal logGroupCost) 
    {
        this.logGroupCost = logGroupCost;
    }

    public BigDecimal getLogGroupCost() 
    {
        return logGroupCost;
    }

    public void setLogLogisticsCost(BigDecimal logLogisticsCost) 
    {
        this.logLogisticsCost = logLogisticsCost;
    }

    public BigDecimal getLogLogisticsCost() 
    {
        return logLogisticsCost;
    }

    public void setLopIspartOld(Long lopIspartOld) 
    {
        this.lopIspartOld = lopIspartOld;
    }

    public Long getLopIspartOld() 
    {
        return lopIspartOld;
    }

    public void setLopBitOld(BigDecimal lopBitOld) 
    {
        this.lopBitOld = lopBitOld;
    }

    public BigDecimal getLopBitOld() 
    {
        return lopBitOld;
    }

    public void setLopUnitpOld(BigDecimal lopUnitpOld) 
    {
        this.lopUnitpOld = lopUnitpOld;
    }

    public BigDecimal getLopUnitpOld() 
    {
        return lopUnitpOld;
    }

    public void setLopTotalpOld(BigDecimal lopTotalpOld) 
    {
        this.lopTotalpOld = lopTotalpOld;
    }

    public BigDecimal getLopTotalpOld() 
    {
        return lopTotalpOld;
    }

    public void setLopCargovOld(BigDecimal lopCargovOld) 
    {
        this.lopCargovOld = lopCargovOld;
    }

    public BigDecimal getLopCargovOld() 
    {
        return lopCargovOld;
    }

    public void setLopNtaxvOld(BigDecimal lopNtaxvOld) 
    {
        this.lopNtaxvOld = lopNtaxvOld;
    }

    public BigDecimal getLopNtaxvOld() 
    {
        return lopNtaxvOld;
    }

    public void setLopTaxOld(BigDecimal lopTaxOld) 
    {
        this.lopTaxOld = lopTaxOld;
    }

    public BigDecimal getLopTaxOld() 
    {
        return lopTaxOld;
    }

    public void setLopOrgmcdOld(String lopOrgmcdOld) 
    {
        this.lopOrgmcdOld = lopOrgmcdOld;
    }

    public String getLopOrgmcdOld() 
    {
        return lopOrgmcdOld;
    }

    public void setLopBizcd(String lopBizcd) 
    {
        this.lopBizcd = lopBizcd;
    }

    public String getLopBizcd() 
    {
        return lopBizcd;
    }

    public void setLopBizcdMcd(String lopBizcdMcd) 
    {
        this.lopBizcdMcd = lopBizcdMcd;
    }

    public String getLopBizcdMcd() 
    {
        return lopBizcdMcd;
    }

    public void setLopBizcdRcd(String lopBizcdRcd) 
    {
        this.lopBizcdRcd = lopBizcdRcd;
    }

    public String getLopBizcdRcd() 
    {
        return lopBizcdRcd;
    }

    public void setLopBizcdRmcd(String lopBizcdRmcd) 
    {
        this.lopBizcdRmcd = lopBizcdRmcd;
    }

    public String getLopBizcdRmcd() 
    {
        return lopBizcdRmcd;
    }

    public void setLopUcd(String lopUcd) 
    {
        this.lopUcd = lopUcd;
    }

    public String getLopUcd() 
    {
        return lopUcd;
    }

    public void setLopDt(Date lopDt) 
    {
        this.lopDt = lopDt;
    }

    public Date getLopDt() 
    {
        return lopDt;
    }

    public void setLopEps(String lopEps) 
    {
        this.lopEps = lopEps;
    }

    public String getLopEps() 
    {
        return lopEps;
    }

    public void setLopTn(String lopTn) 
    {
        this.lopTn = lopTn;
    }

    public String getLopTn() 
    {
        return lopTn;
    }

    public void setLopBoxno(Long lopBoxno) 
    {
        this.lopBoxno = lopBoxno;
    }

    public Long getLopBoxno() 
    {
        return lopBoxno;
    }

    public void setLopOutdt(Date lopOutdt) 
    {
        this.lopOutdt = lopOutdt;
    }

    public Date getLopOutdt() 
    {
        return lopOutdt;
    }

    public void setLopSts(String lopSts) 
    {
        this.lopSts = lopSts;
    }

    public String getLopSts() 
    {
        return lopSts;
    }

    public void setLopRmk(String lopRmk) 
    {
        this.lopRmk = lopRmk;
    }

    public String getLopRmk() 
    {
        return lopRmk;
    }

    public void setParamkeysId(Long paramkeysId) 
    {
        this.paramkeysId = paramkeysId;
    }

    public Long getParamkeysId() 
    {
        return paramkeysId;
    }

    public void setParamKeys(String paramKeys) 
    {
        this.paramKeys = paramKeys;
    }

    public String getParamKeys() 
    {
        return paramKeys;
    }

    public void setParamkeysInsideId(Long paramkeysInsideId) 
    {
        this.paramkeysInsideId = paramkeysInsideId;
    }

    public Long getParamkeysInsideId() 
    {
        return paramkeysInsideId;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setLopBnEncrypt(String lopBnEncrypt) 
    {
        this.lopBnEncrypt = lopBnEncrypt;
    }

    public String getLopBnEncrypt() 
    {
        return lopBnEncrypt;
    }

    public void setLopOrgmcd(String lopOrgmcd) 
    {
        this.lopOrgmcd = lopOrgmcd;
    }

    public String getLopOrgmcd() 
    {
        return lopOrgmcd;
    }

    public void setReceiveTime(String receiveTime) 
    {
        this.receiveTime = receiveTime;
    }

    public String getReceiveTime() 
    {
        return receiveTime;
    }

    public void setLalId(String lalId) 
    {
        this.lalId = lalId;
    }

    public String getLalId() 
    {
        return lalId;
    }

    public void setLopConfirmtime(String lopConfirmtime) 
    {
        this.lopConfirmtime = lopConfirmtime;
    }

    public String getLopConfirmtime() 
    {
        return lopConfirmtime;
    }

    public void setLopFinFlag(Integer lopFinFlag) 
    {
        this.lopFinFlag = lopFinFlag;
    }

    public Integer getLopFinFlag() 
    {
        return lopFinFlag;
    }

    public void setLopSeparatePacking(String lopSeparatePacking) 
    {
        this.lopSeparatePacking = lopSeparatePacking;
    }

    public String getLopSeparatePacking() 
    {
        return lopSeparatePacking;
    }

    public void setLogNoDeliver(String logNoDeliver) 
    {
        this.logNoDeliver = logNoDeliver;
    }

    public String getLogNoDeliver() 
    {
        return logNoDeliver;
    }

    public void setLopOpcd(String lopOpcd) 
    {
        this.lopOpcd = lopOpcd;
    }

    public String getLopOpcd() 
    {
        return lopOpcd;
    }

    public void setLopPickupTime(String lopPickupTime) 
    {
        this.lopPickupTime = lopPickupTime;
    }

    public String getLopPickupTime() 
    {
        return lopPickupTime;
    }

    public void setLopInqty(BigDecimal lopInqty) 
    {
        this.lopInqty = lopInqty;
    }

    public BigDecimal getLopInqty() 
    {
        return lopInqty;
    }

    public void setLopBoxnoExport(String lopBoxnoExport) 
    {
        this.lopBoxnoExport = lopBoxnoExport;
    }

    public String getLopBoxnoExport() 
    {
        return lopBoxnoExport;
    }

    public void setLopPtnoExport(String lopPtnoExport) 
    {
        this.lopPtnoExport = lopPtnoExport;
    }

    public String getLopPtnoExport() 
    {
        return lopPtnoExport;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lopPsn", getLopPsn())
            .append("lopTyp", getLopTyp())
            .append("lopIotyp", getLopIotyp())
            .append("lopPcd", getLopPcd())
            .append("lopPpcd", getLopPpcd())
            .append("lopBn", getLopBn())
            .append("lopPbn", getLopPbn())
            .append("lopSbn", getLopSbn())
            .append("lopQty", getLopQty())
            .append("lopDmcd", getLopDmcd())
            .append("lopSlcd", getLopSlcd())
            .append("lopTcd", getLopTcd())
            .append("lopIspart", getLopIspart())
            .append("lopBit", getLopBit())
            .append("lopDeliverCost", getLopDeliverCost())
            .append("lopUnitp", getLopUnitp())
            .append("lopTotalp", getLopTotalp())
            .append("lopCargov", getLopCargov())
            .append("lopNtaxv", getLopNtaxv())
            .append("lopTax", getLopTax())
            .append("logGroupCost", getLogGroupCost())
            .append("logLogisticsCost", getLogLogisticsCost())
            .append("lopIspartOld", getLopIspartOld())
            .append("lopBitOld", getLopBitOld())
            .append("lopUnitpOld", getLopUnitpOld())
            .append("lopTotalpOld", getLopTotalpOld())
            .append("lopCargovOld", getLopCargovOld())
            .append("lopNtaxvOld", getLopNtaxvOld())
            .append("lopTaxOld", getLopTaxOld())
            .append("lopOrgmcdOld", getLopOrgmcdOld())
            .append("lopBizcd", getLopBizcd())
            .append("lopBizcdMcd", getLopBizcdMcd())
            .append("lopBizcdRcd", getLopBizcdRcd())
            .append("lopBizcdRmcd", getLopBizcdRmcd())
            .append("lopUcd", getLopUcd())
            .append("lopDt", getLopDt())
            .append("lopEps", getLopEps())
            .append("lopTn", getLopTn())
            .append("lopBoxno", getLopBoxno())
            .append("lopOutdt", getLopOutdt())
            .append("lopSts", getLopSts())
            .append("lopRmk", getLopRmk())
            .append("paramkeysId", getParamkeysId())
            .append("paramKeys", getParamKeys())
            .append("paramkeysInsideId", getParamkeysInsideId())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("lopBnEncrypt", getLopBnEncrypt())
            .append("lopOrgmcd", getLopOrgmcd())
            .append("receiveTime", getReceiveTime())
            .append("lalId", getLalId())
            .append("lopConfirmtime", getLopConfirmtime())
            .append("lopFinFlag", getLopFinFlag())
            .append("lopSeparatePacking", getLopSeparatePacking())
            .append("logNoDeliver", getLogNoDeliver())
            .append("lopOpcd", getLopOpcd())
            .append("lopPickupTime", getLopPickupTime())
            .append("lopInqty", getLopInqty())
            .append("lopBoxnoExport", getLopBoxnoExport())
            .append("lopPtnoExport", getLopPtnoExport())
            .toString();
    }
}
