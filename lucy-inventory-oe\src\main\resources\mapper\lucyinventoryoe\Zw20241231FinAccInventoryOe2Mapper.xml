<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.inventoryoe.mapper.Zw20241231FinAccInventoryOe2Mapper">
    
    <resultMap type="Zw20241231FinAccInventoryOe2" id="Zw20241231FinAccInventoryOe2Result">
        <result property="orgcd"    column="orgcd"    />
        <result property="faiSku"    column="fai_sku"    />
        <result property="amt"    column="amt"    />
        <result property="qty"    column="qty"    />
        <result property="faiSubject"    column="fai_subject"    />
        <result property="faiEid"    column="fai_eid"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectZw20241231FinAccInventoryOe2Vo">
        select orgcd, fai_sku, amt, qty, fai_subject, fai_eid, type from zw_20241231_fin_acc_inventory_oe
    </sql>

    <select id="selectZw20241231FinAccInventoryOe2List" parameterType="Zw20241231FinAccInventoryOe2" resultMap="Zw20241231FinAccInventoryOe2Result">
        <include refid="selectZw20241231FinAccInventoryOe2Vo"/>
        <where>  
            <if test="orgcd != null  and orgcd != ''"> and orgcd = #{orgcd}</if>
            <if test="faiSku != null  and faiSku != ''"> and fai_sku = #{faiSku}</if>
            <if test="amt != null "> and amt = #{amt}</if>
            <if test="qty != null "> and qty = #{qty}</if>
            <if test="faiSubject != null  and faiSubject != ''"> and fai_subject = #{faiSubject}</if>
            <if test="faiEid != null  and faiEid != ''"> and fai_eid = #{faiEid}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectZw20241231FinAccInventoryOe2ByOrgcd" parameterType="String" resultMap="Zw20241231FinAccInventoryOe2Result">
        <include refid="selectZw20241231FinAccInventoryOe2Vo"/>
        where orgcd = #{orgcd}
    </select>

    <select id="selectZw20241231FinAccInventoryOe2ByType" parameterType="String" resultMap="Zw20241231FinAccInventoryOe2Result">
        <include refid="selectZw20241231FinAccInventoryOe2Vo"/>
        where
        type = #{type}
        and  (amt!=0 OR qty !=0)
--         and  ( !( qty>=0 AND amt>=0)   or
--         (qty =0 AND amt>=0))
-- limit 1

    </select>

    <select id="selectZw20241231FinAccInventoryOe2ByTypeAndOrgcd"  parameterType="Zw20241231FinAccInventoryOe2"   resultMap="Zw20241231FinAccInventoryOe2Result">
        <include refid="selectZw20241231FinAccInventoryOe2Vo"/>
        where type = #{type}
            and  orgcd = #{orgcd}
        -- limit 1

    </select>


    <insert id="insertZw20241231FinAccInventoryOe2" parameterType="Zw20241231FinAccInventoryOe2">
        insert into zw_20241231_fin_acc_inventory_oe
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgcd != null and orgcd != ''">orgcd,</if>
            <if test="faiSku != null and faiSku != ''">fai_sku,</if>
            <if test="amt != null">amt,</if>
            <if test="qty != null">qty,</if>
            <if test="faiSubject != null and faiSubject != ''">fai_subject,</if>
            <if test="faiEid != null and faiEid != ''">fai_eid,</if>
            <if test="type != null and type != ''">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgcd != null and orgcd != ''">#{orgcd},</if>
            <if test="faiSku != null and faiSku != ''">#{faiSku},</if>
            <if test="amt != null">#{amt},</if>
            <if test="qty != null">#{qty},</if>
            <if test="faiSubject != null and faiSubject != ''">#{faiSubject},</if>
            <if test="faiEid != null and faiEid != ''">#{faiEid},</if>
            <if test="type != null and type != ''">#{type},</if>
         </trim>
    </insert>

    <update id="updateZw20241231FinAccInventoryOe2" parameterType="Zw20241231FinAccInventoryOe2">
        update zw_20241231_fin_acc_inventory_oe
        <trim prefix="SET" suffixOverrides=",">
            <if test="faiSku != null and faiSku != ''">fai_sku = #{faiSku},</if>
            <if test="amt != null">amt = #{amt},</if>
            <if test="qty != null">qty = #{qty},</if>
            <if test="faiSubject != null and faiSubject != ''">fai_subject = #{faiSubject},</if>
            <if test="faiEid != null and faiEid != ''">fai_eid = #{faiEid},</if>
            <if test="type != null and type != ''">type = #{type},</if>
        </trim>
        where orgcd = #{orgcd}
        and  fai_sku = #{faiSku}
    </update>

    <delete id="deleteZw20241231FinAccInventoryOe2ByOrgcd" parameterType="String">
        delete from zw_20241231_fin_acc_inventory_oe where orgcd = #{orgcd}
    </delete>

    <delete id="deleteZw20241231FinAccInventoryOe2ByOrgcds" parameterType="String">
        delete from zw_20241231_fin_acc_inventory_oe where orgcd in
        <foreach item="orgcd" collection="array" open="(" separator="," close=")">
            #{orgcd}
        </foreach>
    </delete>
</mapper>