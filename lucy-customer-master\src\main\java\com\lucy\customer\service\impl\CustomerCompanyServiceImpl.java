package com.lucy.customer.service.impl;

import java.util.List;

import com.lucy.erp.domain.BasCompany;
import com.lucy.erp.service.IBasCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.customer.mapper.CustomerCompanyMapper;
import com.lucy.customer.domain.CustomerCompany;
import com.lucy.customer.service.ICustomerCompanyService;

/**
 * 部门组织结构 Department groupsService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class CustomerCompanyServiceImpl implements ICustomerCompanyService 
{
    @Autowired
    private CustomerCompanyMapper customerCompanyMapper;

    @Autowired
    private IBasCompanyService basCompanyService;

    /**
     * 查询部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 部门组织结构 Department groups
     */
    @Override
    public CustomerCompany selectCustomerCompanyByCode(String code)
    {
        return customerCompanyMapper.selectCustomerCompanyByCode(code);
    }

    /**
     * 查询部门组织结构 Department groups列表
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 部门组织结构 Department groups
     */
    @Override
    public List<CustomerCompany> selectCustomerCompanyList(CustomerCompany customerCompany)
    {
        return customerCompanyMapper.selectCustomerCompanyList(customerCompany);
    }

    /**
     * 新增部门组织结构 Department groups
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 结果
     */
    @Override
    public int insertCustomerCompany(CustomerCompany customerCompany)
    {
        return customerCompanyMapper.insertCustomerCompany(customerCompany);
    }

    /**
     * 修改部门组织结构 Department groups
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 结果
     */
    @Override
    public int updateCustomerCompany(CustomerCompany customerCompany)
    {
        return customerCompanyMapper.updateCustomerCompany(customerCompany);
    }

    /**
     * 批量删除部门组织结构 Department groups
     * 
     * @param codes 需要删除的部门组织结构 Department groups主键
     * @return 结果
     */
    @Override
    public int deleteCustomerCompanyByCodes(String[] codes)
    {
        return customerCompanyMapper.deleteCustomerCompanyByCodes(codes);
    }

    /**
     * 删除部门组织结构 Department groups信息
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 结果
     */
    @Override
    public int deleteCustomerCompanyByCode(String code)
    {
        return customerCompanyMapper.deleteCustomerCompanyByCode(code);
    }
    
    @Override
    public void insertCustomerCompanyIfNotExist(String code) {
        CustomerCompany customerCompany = selectCustomerCompanyByCode(code);
        if (customerCompany == null) {
            BasCompany basCompany = basCompanyService.selectBasCompanyByBcCd(code);
            CustomerCompany customerCompanyNew = new CustomerCompany();
            customerCompanyNew.setCode(code);
            customerCompanyNew.setName(basCompany.getBcNm());
            // 税号
            customerCompanyNew.setTaxNo(basCompany.getBcTrcno());
            // TODO
            // 客户类别
            customerCompanyNew.setCategory("");
            // 客户分类-小类
            customerCompanyNew.setClassificationSubclass("");
            // 客户分类-大类
            customerCompanyNew.setClassificationMajor("");
            // 匹配客户档案信息【应用领域、省份等信息】
            customerCompanyNew.setProfile("");
            // 客户同控合并情况
            customerCompanyNew.setMergerSituation("");
            insertCustomerCompany(customerCompanyNew);           
        }
    }
}
