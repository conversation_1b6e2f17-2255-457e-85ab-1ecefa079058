package com.lucy.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_供应商对象 bas_sup_mst
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class BasSupMst extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 公司编号 */
    private String bsmCcd;

    /** 供应商编号 */
    private String bsmCd;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String bsmNm;

    /** 供应商全称 */
    @Excel(name = "供应商全称")
    private String bsmFnm;

    /** 简称 */
    @Excel(name = "简称")
    private String bsmNmabb;

    /** 供应商XYZ */
    @Excel(name = "供应商XYZ")
    private String bsmXyz;

    /** 上级供应商编号 */
    @Excel(name = "上级供应商编号")
    private String bsmPcd;

    /** 类别CD */
    @Excel(name = "类别CD")
    private String bsmTcd;

    /** 国内/国外 */
    @Excel(name = "国内/国外")
    private String bsmIsos;

    /** 国家简写 */
    @Excel(name = "国家简写")
    private String bsmNationcd;

    /** 国家名称 */
    @Excel(name = "国家名称")
    private String bsmNation;

    /** 付款方式 */
    @Excel(name = "付款方式")
    private String bsmPaytyp;

    /** 是否免检 */
    @Excel(name = "是否免检")
    private String bsmFreeqc;

    /** 级别(低级别用户做出不同相应) */
    @Excel(name = "级别(低级别用户做出不同相应)")
    private String bsmLvl;

    /** 固定电话 */
    @Excel(name = "固定电话")
    private String bsmFp;

    /** 传真 */
    @Excel(name = "传真")
    private String bsmFax;

    /** 省份CD */
    @Excel(name = "省份CD")
    private String bsmPrcd;

    /** 省份NM */
    @Excel(name = "省份NM")
    private String bsmPrnm;

    /** 城市 */
    @Excel(name = "城市")
    private String bsmCity;

    /** 地址 */
    @Excel(name = "地址")
    private String bsmAddr;

    /** 是否启用（0X禁用，01正常） */
    @Excel(name = "是否启用", readConverterExp = "0=X禁用，01正常")
    private String bsmSts;

    /** 供应商类别 */
    @Excel(name = "供应商类别")
    private String bsmTyp;

    /** 供应商一级编号 */
    @Excel(name = "供应商一级编号")
    private String bsmFstOrgcd;

    /** 供应商发票类型 */
    @Excel(name = "供应商发票类型")
    private String bsmInvtyp;

    /** 财务记账分类编号 */
    @Excel(name = "财务记账分类编号")
    private String bsmFinanceCode;

    /** 是否为进口（0为否） */
    @Excel(name = "是否为进口", readConverterExp = "0=为否")
    private String bsmImport;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 原供应商编号 */
    @Excel(name = "原供应商编号")
    private String bsmInitialcd;

    /** 组织编号 */
    @Excel(name = "组织编号")
    private String bsmOrgcd;

    /** 买方组织编号 */
    @Excel(name = "买方组织编号")
    private String bsmBuyer;

    /** 是否质检合格 */
    @Excel(name = "是否质检合格")
    private String bsmAql;

    /** 是否录票 */
    @Excel(name = "是否录票")
    private String bsmInvoice;

    /** 账期 */
    @Excel(name = "账期")
    private String bsmDd;

    /** 营业执照截止日期 */
    @Excel(name = "营业执照截止日期")
    private String bsmYyzzDt;

    /** 危化品经营许可证截止日期 */
    @Excel(name = "危化品经营许可证截止日期")
    private String bsmWhpDt;

    /** 营业执照上传人 */
    @Excel(name = "营业执照上传人")
    private String bsmYyzzNm;

    /** 危化品经营上传人 */
    @Excel(name = "危化品经营上传人")
    private String bsmWhpNm;

    /** 营业执照上传日期 */
    @Excel(name = "营业执照上传日期")
    private String bsmYyzzupDt;

    /** 危化品经营许可证上传日期 */
    @Excel(name = "危化品经营许可证上传日期")
    private String bsmWhpupDt;

    /** 经营范围上传人 */
    @Excel(name = "经营范围上传人")
    private String bsmJyfwNm;

    /** 经营范围上传日期 */
    @Excel(name = "经营范围上传日期")
    private String bsmJyfwupDt;

    /** 供应商首评得分 */
    @Excel(name = "供应商首评得分")
    private String bsmFirstScore;

    /** 供应商评定年份 */
    @Excel(name = "供应商评定年份")
    private String bsmAssessYear;

    /** 供应商等级 */
    @Excel(name = "供应商等级")
    private String bsmGrade;

    /** 供应商评定有效期 */
    @Excel(name = "供应商评定有效期")
    private String bsmAssessExpiry;

    /** 同控主体 */
    @Excel(name = "同控主体")
    private String bsmConAgent;

    /** 实缴资本 */
    @Excel(name = "实缴资本")
    private String bsmSjzb;

    /** 所属城市 */
    @Excel(name = "所属城市")
    private String bsmHisCity;

    /** 所属区县 */
    @Excel(name = "所属区县")
    private String bsmHisArea;

    /** 公司类型 */
    @Excel(name = "公司类型")
    private String bsmCompType;

    /** 通信地址 */
    @Excel(name = "通信地址")
    private String bsmMailAddress;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private String bsmAduitSts;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBsmCcd(String bsmCcd) 
    {
        this.bsmCcd = bsmCcd;
    }

    public String getBsmCcd() 
    {
        return bsmCcd;
    }

    public void setBsmCd(String bsmCd) 
    {
        this.bsmCd = bsmCd;
    }

    public String getBsmCd() 
    {
        return bsmCd;
    }

    public void setBsmNm(String bsmNm) 
    {
        this.bsmNm = bsmNm;
    }

    public String getBsmNm() 
    {
        return bsmNm;
    }

    public void setBsmFnm(String bsmFnm) 
    {
        this.bsmFnm = bsmFnm;
    }

    public String getBsmFnm() 
    {
        return bsmFnm;
    }

    public void setBsmNmabb(String bsmNmabb) 
    {
        this.bsmNmabb = bsmNmabb;
    }

    public String getBsmNmabb() 
    {
        return bsmNmabb;
    }

    public void setBsmXyz(String bsmXyz) 
    {
        this.bsmXyz = bsmXyz;
    }

    public String getBsmXyz() 
    {
        return bsmXyz;
    }

    public void setBsmPcd(String bsmPcd) 
    {
        this.bsmPcd = bsmPcd;
    }

    public String getBsmPcd() 
    {
        return bsmPcd;
    }

    public void setBsmTcd(String bsmTcd) 
    {
        this.bsmTcd = bsmTcd;
    }

    public String getBsmTcd() 
    {
        return bsmTcd;
    }

    public void setBsmIsos(String bsmIsos) 
    {
        this.bsmIsos = bsmIsos;
    }

    public String getBsmIsos() 
    {
        return bsmIsos;
    }

    public void setBsmNationcd(String bsmNationcd) 
    {
        this.bsmNationcd = bsmNationcd;
    }

    public String getBsmNationcd() 
    {
        return bsmNationcd;
    }

    public void setBsmNation(String bsmNation) 
    {
        this.bsmNation = bsmNation;
    }

    public String getBsmNation() 
    {
        return bsmNation;
    }

    public void setBsmPaytyp(String bsmPaytyp) 
    {
        this.bsmPaytyp = bsmPaytyp;
    }

    public String getBsmPaytyp() 
    {
        return bsmPaytyp;
    }

    public void setBsmFreeqc(String bsmFreeqc) 
    {
        this.bsmFreeqc = bsmFreeqc;
    }

    public String getBsmFreeqc() 
    {
        return bsmFreeqc;
    }

    public void setBsmLvl(String bsmLvl) 
    {
        this.bsmLvl = bsmLvl;
    }

    public String getBsmLvl() 
    {
        return bsmLvl;
    }

    public void setBsmFp(String bsmFp) 
    {
        this.bsmFp = bsmFp;
    }

    public String getBsmFp() 
    {
        return bsmFp;
    }

    public void setBsmFax(String bsmFax) 
    {
        this.bsmFax = bsmFax;
    }

    public String getBsmFax() 
    {
        return bsmFax;
    }

    public void setBsmPrcd(String bsmPrcd) 
    {
        this.bsmPrcd = bsmPrcd;
    }

    public String getBsmPrcd() 
    {
        return bsmPrcd;
    }

    public void setBsmPrnm(String bsmPrnm) 
    {
        this.bsmPrnm = bsmPrnm;
    }

    public String getBsmPrnm() 
    {
        return bsmPrnm;
    }

    public void setBsmCity(String bsmCity) 
    {
        this.bsmCity = bsmCity;
    }

    public String getBsmCity() 
    {
        return bsmCity;
    }

    public void setBsmAddr(String bsmAddr) 
    {
        this.bsmAddr = bsmAddr;
    }

    public String getBsmAddr() 
    {
        return bsmAddr;
    }

    public void setBsmSts(String bsmSts) 
    {
        this.bsmSts = bsmSts;
    }

    public String getBsmSts() 
    {
        return bsmSts;
    }

    public void setBsmTyp(String bsmTyp) 
    {
        this.bsmTyp = bsmTyp;
    }

    public String getBsmTyp() 
    {
        return bsmTyp;
    }

    public void setBsmFstOrgcd(String bsmFstOrgcd) 
    {
        this.bsmFstOrgcd = bsmFstOrgcd;
    }

    public String getBsmFstOrgcd() 
    {
        return bsmFstOrgcd;
    }

    public void setBsmInvtyp(String bsmInvtyp) 
    {
        this.bsmInvtyp = bsmInvtyp;
    }

    public String getBsmInvtyp() 
    {
        return bsmInvtyp;
    }

    public void setBsmFinanceCode(String bsmFinanceCode) 
    {
        this.bsmFinanceCode = bsmFinanceCode;
    }

    public String getBsmFinanceCode() 
    {
        return bsmFinanceCode;
    }

    public void setBsmImport(String bsmImport) 
    {
        this.bsmImport = bsmImport;
    }

    public String getBsmImport() 
    {
        return bsmImport;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setBsmInitialcd(String bsmInitialcd) 
    {
        this.bsmInitialcd = bsmInitialcd;
    }

    public String getBsmInitialcd() 
    {
        return bsmInitialcd;
    }

    public void setBsmOrgcd(String bsmOrgcd) 
    {
        this.bsmOrgcd = bsmOrgcd;
    }

    public String getBsmOrgcd() 
    {
        return bsmOrgcd;
    }

    public void setBsmBuyer(String bsmBuyer) 
    {
        this.bsmBuyer = bsmBuyer;
    }

    public String getBsmBuyer() 
    {
        return bsmBuyer;
    }

    public void setBsmAql(String bsmAql) 
    {
        this.bsmAql = bsmAql;
    }

    public String getBsmAql() 
    {
        return bsmAql;
    }

    public void setBsmInvoice(String bsmInvoice) 
    {
        this.bsmInvoice = bsmInvoice;
    }

    public String getBsmInvoice() 
    {
        return bsmInvoice;
    }

    public void setBsmDd(String bsmDd) 
    {
        this.bsmDd = bsmDd;
    }

    public String getBsmDd() 
    {
        return bsmDd;
    }

    public void setBsmYyzzDt(String bsmYyzzDt) 
    {
        this.bsmYyzzDt = bsmYyzzDt;
    }

    public String getBsmYyzzDt() 
    {
        return bsmYyzzDt;
    }

    public void setBsmWhpDt(String bsmWhpDt) 
    {
        this.bsmWhpDt = bsmWhpDt;
    }

    public String getBsmWhpDt() 
    {
        return bsmWhpDt;
    }

    public void setBsmYyzzNm(String bsmYyzzNm) 
    {
        this.bsmYyzzNm = bsmYyzzNm;
    }

    public String getBsmYyzzNm() 
    {
        return bsmYyzzNm;
    }

    public void setBsmWhpNm(String bsmWhpNm) 
    {
        this.bsmWhpNm = bsmWhpNm;
    }

    public String getBsmWhpNm() 
    {
        return bsmWhpNm;
    }

    public void setBsmYyzzupDt(String bsmYyzzupDt) 
    {
        this.bsmYyzzupDt = bsmYyzzupDt;
    }

    public String getBsmYyzzupDt() 
    {
        return bsmYyzzupDt;
    }

    public void setBsmWhpupDt(String bsmWhpupDt) 
    {
        this.bsmWhpupDt = bsmWhpupDt;
    }

    public String getBsmWhpupDt() 
    {
        return bsmWhpupDt;
    }

    public void setBsmJyfwNm(String bsmJyfwNm) 
    {
        this.bsmJyfwNm = bsmJyfwNm;
    }

    public String getBsmJyfwNm() 
    {
        return bsmJyfwNm;
    }

    public void setBsmJyfwupDt(String bsmJyfwupDt) 
    {
        this.bsmJyfwupDt = bsmJyfwupDt;
    }

    public String getBsmJyfwupDt() 
    {
        return bsmJyfwupDt;
    }

    public void setBsmFirstScore(String bsmFirstScore) 
    {
        this.bsmFirstScore = bsmFirstScore;
    }

    public String getBsmFirstScore() 
    {
        return bsmFirstScore;
    }

    public void setBsmAssessYear(String bsmAssessYear) 
    {
        this.bsmAssessYear = bsmAssessYear;
    }

    public String getBsmAssessYear() 
    {
        return bsmAssessYear;
    }

    public void setBsmGrade(String bsmGrade) 
    {
        this.bsmGrade = bsmGrade;
    }

    public String getBsmGrade() 
    {
        return bsmGrade;
    }

    public void setBsmAssessExpiry(String bsmAssessExpiry) 
    {
        this.bsmAssessExpiry = bsmAssessExpiry;
    }

    public String getBsmAssessExpiry() 
    {
        return bsmAssessExpiry;
    }

    public void setBsmConAgent(String bsmConAgent) 
    {
        this.bsmConAgent = bsmConAgent;
    }

    public String getBsmConAgent() 
    {
        return bsmConAgent;
    }

    public void setBsmSjzb(String bsmSjzb) 
    {
        this.bsmSjzb = bsmSjzb;
    }

    public String getBsmSjzb() 
    {
        return bsmSjzb;
    }

    public void setBsmHisCity(String bsmHisCity) 
    {
        this.bsmHisCity = bsmHisCity;
    }

    public String getBsmHisCity() 
    {
        return bsmHisCity;
    }

    public void setBsmHisArea(String bsmHisArea) 
    {
        this.bsmHisArea = bsmHisArea;
    }

    public String getBsmHisArea() 
    {
        return bsmHisArea;
    }

    public void setBsmCompType(String bsmCompType) 
    {
        this.bsmCompType = bsmCompType;
    }

    public String getBsmCompType() 
    {
        return bsmCompType;
    }

    public void setBsmMailAddress(String bsmMailAddress) 
    {
        this.bsmMailAddress = bsmMailAddress;
    }

    public String getBsmMailAddress() 
    {
        return bsmMailAddress;
    }

    public void setBsmAduitSts(String bsmAduitSts) 
    {
        this.bsmAduitSts = bsmAduitSts;
    }

    public String getBsmAduitSts() 
    {
        return bsmAduitSts;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bsmCcd", getBsmCcd())
            .append("bsmCd", getBsmCd())
            .append("bsmNm", getBsmNm())
            .append("bsmFnm", getBsmFnm())
            .append("bsmNmabb", getBsmNmabb())
            .append("bsmXyz", getBsmXyz())
            .append("bsmPcd", getBsmPcd())
            .append("bsmTcd", getBsmTcd())
            .append("bsmIsos", getBsmIsos())
            .append("bsmNationcd", getBsmNationcd())
            .append("bsmNation", getBsmNation())
            .append("bsmPaytyp", getBsmPaytyp())
            .append("bsmFreeqc", getBsmFreeqc())
            .append("bsmLvl", getBsmLvl())
            .append("bsmFp", getBsmFp())
            .append("bsmFax", getBsmFax())
            .append("bsmPrcd", getBsmPrcd())
            .append("bsmPrnm", getBsmPrnm())
            .append("bsmCity", getBsmCity())
            .append("bsmAddr", getBsmAddr())
            .append("bsmSts", getBsmSts())
            .append("bsmTyp", getBsmTyp())
            .append("bsmFstOrgcd", getBsmFstOrgcd())
            .append("bsmInvtyp", getBsmInvtyp())
            .append("bsmFinanceCode", getBsmFinanceCode())
            .append("bsmImport", getBsmImport())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("bsmInitialcd", getBsmInitialcd())
            .append("bsmOrgcd", getBsmOrgcd())
            .append("bsmBuyer", getBsmBuyer())
            .append("bsmAql", getBsmAql())
            .append("bsmInvoice", getBsmInvoice())
            .append("bsmDd", getBsmDd())
            .append("bsmYyzzDt", getBsmYyzzDt())
            .append("bsmWhpDt", getBsmWhpDt())
            .append("bsmYyzzNm", getBsmYyzzNm())
            .append("bsmWhpNm", getBsmWhpNm())
            .append("bsmYyzzupDt", getBsmYyzzupDt())
            .append("bsmWhpupDt", getBsmWhpupDt())
            .append("bsmJyfwNm", getBsmJyfwNm())
            .append("bsmJyfwupDt", getBsmJyfwupDt())
            .append("bsmFirstScore", getBsmFirstScore())
            .append("bsmAssessYear", getBsmAssessYear())
            .append("bsmGrade", getBsmGrade())
            .append("bsmAssessExpiry", getBsmAssessExpiry())
            .append("bsmConAgent", getBsmConAgent())
            .append("bsmSjzb", getBsmSjzb())
            .append("bsmHisCity", getBsmHisCity())
            .append("bsmHisArea", getBsmHisArea())
            .append("bsmCompType", getBsmCompType())
            .append("bsmMailAddress", getBsmMailAddress())
            .append("bsmAduitSts", getBsmAduitSts())
            .toString();
    }
}
