package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogRepackPackageUsePlan;

/**
 * 产成品包材领料主Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ILogRepackPackageUsePlanService 
{
    /**
     * 查询产成品包材领料主
     * 
     * @param lrpupCd 产成品包材领料主主键
     * @return 产成品包材领料主
     */
    public LogRepackPackageUsePlan selectLogRepackPackageUsePlanByLrpupCd(String lrpupCd);

    /**
     * 查询产成品包材领料主
     *
     * @param lrpup_psn 产成品包材领料主工单号
     * @return 产成品包材领料主
     */
    public LogRepackPackageUsePlan selectLogRepackPackageUsePlanByLrpupPsn(String lrpup_psn);

    /**
     * 查询产成品包材领料主列表
     * 
     * @param logRepackPackageUsePlan 产成品包材领料主
     * @return 产成品包材领料主集合
     */
    public List<LogRepackPackageUsePlan> selectLogRepackPackageUsePlanList(LogRepackPackageUsePlan logRepackPackageUsePlan);

    /**
     * 新增产成品包材领料主
     * 
     * @param logRepackPackageUsePlan 产成品包材领料主
     * @return 结果
     */
    public int insertLogRepackPackageUsePlan(LogRepackPackageUsePlan logRepackPackageUsePlan);

    /**
     * 修改产成品包材领料主
     * 
     * @param logRepackPackageUsePlan 产成品包材领料主
     * @return 结果
     */
    public int updateLogRepackPackageUsePlan(LogRepackPackageUsePlan logRepackPackageUsePlan);

    /**
     * 批量删除产成品包材领料主
     * 
     * @param lrpupCds 需要删除的产成品包材领料主主键集合
     * @return 结果
     */
    public int deleteLogRepackPackageUsePlanByLrpupCds(String[] lrpupCds);

    /**
     * 删除产成品包材领料主信息
     * 
     * @param lrpupCd 产成品包材领料主主键
     * @return 结果
     */
    public int deleteLogRepackPackageUsePlanByLrpupCd(String lrpupCd);
}
