package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.BasCustorgMst;

/**
 * 基础_客户组织_主Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface BasCustorgMstMapper 
{
    /**
     * 查询基础_客户组织_主
     * 
     * @param bcmCd 基础_客户组织_主主键
     * @return 基础_客户组织_主
     */
    public BasCustorgMst selectBasCustorgMstByBcmCd(String bcmCd);

    /**
     * 查询基础_客户组织_主列表
     * 
     * @param basCustorgMst 基础_客户组织_主
     * @return 基础_客户组织_主集合
     */
    public List<BasCustorgMst> selectBasCustorgMstList(BasCustorgMst basCustorgMst);

    /**
     * 新增基础_客户组织_主
     * 
     * @param basCustorgMst 基础_客户组织_主
     * @return 结果
     */
    public int insertBasCustorgMst(BasCustorgMst basCustorgMst);

    /**
     * 修改基础_客户组织_主
     * 
     * @param basCustorgMst 基础_客户组织_主
     * @return 结果
     */
    public int updateBasCustorgMst(BasCustorgMst basCustorgMst);

    /**
     * 删除基础_客户组织_主
     * 
     * @param bcmCd 基础_客户组织_主主键
     * @return 结果
     */
    public int deleteBasCustorgMstByBcmCd(String bcmCd);

    /**
     * 批量删除基础_客户组织_主
     * 
     * @param bcmCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasCustorgMstByBcmCds(String[] bcmCds);
}
