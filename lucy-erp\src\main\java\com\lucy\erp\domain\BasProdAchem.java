package com.lucy.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_化学_主
对象 bas_prod_achem
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public class BasProdAchem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 产品编号 */
    private String bpacCd;

    /** 物质编号 */
    private String bpacMcd;

    /** CAS */
    @Excel(name = "CAS")
    private String bpacCas;

    /** IUPAC NAME */
    @Excel(name = "IUPAC NAME")
    private String bpacIupac;

    /** MDL */
    @Excel(name = "MDL")
    private String bpacMdl;

    /** EINECS */
    @Excel(name = "EINECS")
    private String bpacEinecs;

    /** SMILES */
    @Excel(name = "SMILES")
    private String bpacSmiles;

    /** 纯度 */
    @Excel(name = "纯度")
    private String bpacPurity;

    /** 中文别名 */
    @Excel(name = "中文别名")
    private String bpacFnm;

    /** 英文别名 */
    @Excel(name = "英文别名")
    private String bpacEfnm;

    /** 中文别名1 */
    @Excel(name = "中文别名1")
    private String bpacFnm1;

    /** 中文别名2 */
    @Excel(name = "中文别名2")
    private String bpacFnm2;

    /** 分子式 */
    @Excel(name = "分子式")
    private String bpacMf;

    /** 精确质量 */
    @Excel(name = "精确质量")
    private String bpacEm;

    /** 分子量 */
    @Excel(name = "分子量")
    private String bpacMw;

    /** 颜色备注 */
    @Excel(name = "颜色备注")
    private String bpacColor;

    /** 外观 */
    @Excel(name = "外观")
    private String bpacAppear;

    /** 备货注意事项 */
    @Excel(name = "备货注意事项")
    private String bpacLos;

    /** 性状备注 */
    @Excel(name = "性状备注")
    private String bpacCharacter;

    /** 单位 开发票用 */
    @Excel(name = "单位 开发票用")
    private String bpacUnit;

    /** 商品税收分类编码 */
    @Excel(name = "商品税收分类编码")
    private String bpacTaxonomy;

    /** 气味 */
    @Excel(name = "气味")
    private String bpacSmell;

    /** 折射率 */
    @Excel(name = "折射率")
    private String bpacRi;

    /** 蒸汽密度 */
    @Excel(name = "蒸汽密度")
    private String bpacVade;

    /** 闪点 */
    @Excel(name = "闪点")
    private String bpacFp;

    /** 沸点 */
    @Excel(name = "沸点")
    private String bpacBp;

    /** 熔点 */
    @Excel(name = "熔点")
    private String bpacMp;

    /** 自燃点 */
    @Excel(name = "自燃点")
    private String bpacAutoign;

    /** 溶解性 */
    @Excel(name = "溶解性")
    private String bpacSolu;

    /** 蒸汽压 */
    @Excel(name = "蒸汽压")
    private String bpacVapp;

    /** 密度 */
    @Excel(name = "密度")
    private String bpacDens;

    /** 危险类别_危险品分类 */
    @Excel(name = "危险类别_危险品分类")
    private String bpacDgcls;

    /** 危险品标志 */
    @Excel(name = "危险品标志")
    private String bpacDgsign;

    /** 危险品运输码_运输条件 */
    @Excel(name = "危险品运输码_运输条件")
    private String bpacDgtno;

    /** 危险品类别码 */
    @Excel(name = "危险品类别码")
    private String bpacRiskcd;

    /** 安全说明_危险说明 */
    @Excel(name = "安全说明_危险说明")
    private String bpacSafetydesc;

    /** 包装等级 */
    @Excel(name = "包装等级")
    private String bpacPackgrp;

    /** WGK */
    @Excel(name = "WGK")
    private String bpacWgk;

    /** RTECS */
    @Excel(name = "RTECS")
    private String bpacRtecs;

    /** F-代码 */
    @Excel(name = "F-代码")
    private String bpacF;

    /** 存储条件（标签） */
    @Excel(name = "存储条件", readConverterExp = "标=签")
    private String bpacScLabelPrint;

    /** 储存条件 */
    @Excel(name = "储存条件")
    private String bpacSc;

    /** 存储-光敏 */
    @Excel(name = "存储-光敏")
    private String bpacScPtst;

    /** 化学性质 */
    @Excel(name = "化学性质")
    private String bpacCp;

    /** 化学性质备注 */
    @Excel(name = "化学性质备注")
    private String bpacUsage;

    /** 包装备注 */
    @Excel(name = "包装备注")
    private String bpacIsal;

    /** 分装备注 */
    @Excel(name = "分装备注")
    private String bpacIsmp;

    /** 包装要求-外包装（网站显示） */
    @Excel(name = "包装要求-外包装", readConverterExp = "网=站显示")
    private String bpacPackinstr;

    /** 禁配物 */
    @Excel(name = "禁配物")
    private String bpacFprod;

    /** 易制爆 */
    @Excel(name = "易制爆")
    private String bpacEp;

    /** 存储备注 */
    @Excel(name = "存储备注")
    private String bpacSnote;

    /** 监管条件 */
    @Excel(name = "监管条件")
    private String bpacSprmk;

    /** 易涉毒备注(暂时关闭) */
    @Excel(name = "易涉毒备注(暂时关闭)")
    private String bpacSprmkPoisonod;

    /** 易制爆备注 */
    @Excel(name = "易制爆备注")
    private String bpacSprmkDanger;

    /** 结构图 */
    @Excel(name = "结构图")
    private String bpacImg;

    /** UNNumber */
    @Excel(name = "UNNumber")
    private String bpacUnn;

    /** GHSSymbolsGlobal */
    @Excel(name = "GHSSymbolsGlobal")
    private String bpacGhssg;

    /** GHSSignalWordsGlobal */
    @Excel(name = "GHSSignalWordsGlobal")
    private String bpacGhsswg;

    /** GHSSignalWordsGlobal2 */
    @Excel(name = "GHSSignalWordsGlobal2")
    private String bpacGhsswg2;

    /** GHSHazardStatementsGlobal */
    @Excel(name = "GHSHazardStatementsGlobal")
    private String bpacGhshsg;

    /** GHSPrecautionaryStmntsGlobal */
    @Excel(name = "GHSPrecautionaryStmntsGlobal")
    private String bpacGhspsg;

    /** 物质描述 */
    @Excel(name = "物质描述")
    private String bpacInfo;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String rCreKid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date rCreDt;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String rUpdKid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date rUpdDt;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 分装注意事项 */
    @Excel(name = "分装注意事项")
    private String bpacRpknote;

    /** 使用注意事项 */
    @Excel(name = "使用注意事项")
    private String bpacUseprecautions;

    /** 二类医疗器械备注 */
    @Excel(name = "二类医疗器械备注")
    private String bpacClassiimdlRmk;

    /** 原料药备注 */
    @Excel(name = "原料药备注")
    private String bpacRawMaterial;

    /** 易涉毒备注 */
    @Excel(name = "易涉毒备注")
    private String bpacSprmkPoison;

    /** 产品用途 */
    @Excel(name = "产品用途")
    private String bpacProductUse;

    /** 文献来源 */
    @Excel(name = "文献来源")
    private String bpacLiteratureSource;

    /** 警示词 */
    @Excel(name = "警示词")
    private String bpacWarning;

    /** 象形图-Pictogram */
    @Excel(name = "象形图-Pictogram")
    private String bpacPictogram;

    /** 预防措施-Precautionary statement(s) */
    @Excel(name = "预防措施-Precautionary statement(s)")
    private String bpacPrecautionary;

    /** 事故响应 */
    @Excel(name = "事故响应")
    private String bpacAccident;

    /** 储存 */
    @Excel(name = "储存")
    private String bpacStorage;

    /** 废弃处置 */
    @Excel(name = "废弃处置")
    private String bpacDisposal;

    /** 进出口分类编码 */
    @Excel(name = "进出口分类编码")
    private String bpacHscode;

    /** 危险性类别，GHS分类 */
    @Excel(name = "危险性类别，GHS分类")
    private String bpacRiskcate;

    /** 物理和化学危害 physical chemical hazards */
    @Excel(name = "物理和化学危害 physical chemical hazards")
    private String bpacPch;

    /** 健康危害-Health hazard */
    @Excel(name = "健康危害-Health hazard")
    private String bpacHealth;

    /** 环境危害code-Environmental hazards */
    @Excel(name = "环境危害code-Environmental hazards")
    private String bpacEnvironmentalCode;

    /** 合适的灭火剂-Suitable extinguishing media */
    @Excel(name = "合适的灭火剂-Suitable extinguishing media")
    private String bpacExtinguish;

    /** 源于此物质或混合物的特别的危害-Special hazards arising from the substance or mixture */
    @Excel(name = "源于此物质或混合物的特别的危害-Special hazards arising from the substance or mixture")
    private String bpacSpecialhazard;

    /** pH值 */
    @Excel(name = "pH值")
    private String bpacPh;

    /** 燃烧上下极限及爆炸极限-Upper/lower flammability or explosive limits */
    @Excel(name = "燃烧上下极限及爆炸极限-Upper/lower flammability or explosive limits")
    private String bpacLimit;

    /** 蒸发速率-Evaporation rate */
    @Excel(name = "蒸发速率-Evaporation rate")
    private String bpacEvaporation;

    /** 正辛醇/水分配系数-Partition coefficient:n-octanol/water */
    @Excel(name = "正辛醇/水分配系数-Partition coefficient:n-octanol/water")
    private String bpacPartition;

    /** 分解温度-Decomposition  temperature */
    @Excel(name = "分解温度-Decomposition  temperature")
    private String bpacDt;

    /** 易燃性-Flammability */
    @Excel(name = "易燃性-Flammability")
    private String bpacFlammability;

    /** LD50经口-LD50 Oral */
    @Excel(name = "LD50经口-LD50 Oral")
    private String bpacOral;

    /** LC50吸入-LC50 Inhalation */
    @Excel(name = "LC50吸入-LC50 Inhalation")
    private String bpacInhalation;

    /** LD50经皮-LD50 Dermal */
    @Excel(name = "LD50经皮-LD50 Dermal")
    private String bpacDermal;

    /** 皮肤刺激及腐蚀-Skin corrosion/irritation */
    @Excel(name = "皮肤刺激及腐蚀-Skin corrosion/irritation")
    private String bpacSkin;

    /** 眼睛刺激及腐蚀-Serious eye damage/eye irritation */
    @Excel(name = "眼睛刺激及腐蚀-Serious eye damage/eye irritation")
    private String bpacEye;

    /** 呼吸及皮肤过敏-Respiratory or skin sensitization */
    @Excel(name = "呼吸及皮肤过敏-Respiratory or skin sensitization")
    private String bpacRespiratory;

    /** 生殖细胞突变性-Germ cell mutagenicity */
    @Excel(name = "生殖细胞突变性-Germ cell mutagenicity")
    private String bpacMutagenicity;

    /** 致癌性-Carcinogenicity */
    @Excel(name = "致癌性-Carcinogenicity")
    private String bpacCarcinogenicity;

    /** 生殖毒性-Reproductive toxicity */
    @Excel(name = "生殖毒性-Reproductive toxicity")
    private String bpacReproductive;

    /** 特异性靶器官系统毒性（一次接触）-Specific target organ toxicity - single exposure */
    @Excel(name = "特异性靶器官系统毒性", readConverterExp = "一=次接触")
    private String bpacSingle;

    /** 特异性靶器官系统毒性（反复接触）-Specific target organ toxicity - repeated exposure */
    @Excel(name = "特异性靶器官系统毒性", readConverterExp = "反=复接触")
    private String bpacRepeated;

    /** 附加说明-Additional Information */
    @Excel(name = "附加说明-Additional Information")
    private String bpacAddinfo;

    /** 对鱼类的毒性-Toxicity to fish */
    @Excel(name = "对鱼类的毒性-Toxicity to fish")
    private String bpacTofish;

    /** 对水溞和其他水生无脊椎动物的毒性-Toxicity to daphnia  and other aquatic  invertebrates */
    @Excel(name = "对水溞和其他水生无脊椎动物的毒性-Toxicity to daphnia  and other aquatic  invertebrates")
    private String bpacTodaphnia;

    /** 对藻类的毒性-Toxicity to algae */
    @Excel(name = "对藻类的毒性-Toxicity to algae")
    private String bpacToalgae;

    /** 对细菌的毒性-Toxicity to bacteria */
    @Excel(name = "对细菌的毒性-Toxicity to bacteria")
    private String bpacTobacteria;

    /** 持久性及降解性-Persistence and degradability */
    @Excel(name = "持久性及降解性-Persistence and degradability")
    private String bpacPersistence;

    /** 潜在的生物积累性-Bioaccumulative potential */
    @Excel(name = "潜在的生物积累性-Bioaccumulative potential")
    private String bpacBioaccumulative;

    /** 土壤中的迁移性-Mobility in soil */
    @Excel(name = "土壤中的迁移性-Mobility in soil")
    private String bpacMobility;

    /** 其他不利的影响-Other adverse effects */
    @Excel(name = "其他不利的影响-Other adverse effects")
    private String bpacEffects;

    /** 环境危害-Environmental hazards */
    @Excel(name = "环境危害-Environmental hazards")
    private String bpacEnvironmental;

    /** msds文件的oss路径 */
    @Excel(name = "msds文件的oss路径")
    private String bpacMsdsUrl;

    /** 采购发单放行 */
    @Excel(name = "采购发单放行")
    private String bpacPermitThrough;

    /** SN资质 */
    @Excel(name = "SN资质")
    private String bpacSnQualifications;

    /** 风险管控 */
    @Excel(name = "风险管控")
    private String bpacIsrisk;

    /** HS编码 */
    @Excel(name = "HS编码")
    private String bpacCodehs;

    /** 危险货物(01:是,02:否,03:待确认) */
    @Excel(name = "危险货物(01:是,02:否,03:待确认)")
    private String bpacDgcargo;

    /** 危险来源 */
    @Excel(name = "危险来源")
    private String bpacDgsource;

    /** 外贸-进口限制 */
    @Excel(name = "外贸-进口限制")
    private String bpacInportlimit;

    /** 外贸-出口限制 */
    @Excel(name = "外贸-出口限制")
    private String bpacExportlimit;

    /** 3ABio-ph适用范围 */
    @Excel(name = "3ABio-ph适用范围")
    private String bpacPhscope;

    /** 3ABio-浓度 */
    @Excel(name = "3ABio-浓度")
    private String bpacDensity;

    /** 国危次属性 */
    @Excel(name = "国危次属性")
    private String bpacDgclsNext;

    /** 非国危分类 */
    @Excel(name = "非国危分类")
    private String bpacNdgclsClass;

    /** 是否已维护 01:已维护 02 未维护 */
    @Excel(name = "是否已维护 01:已维护 02 未维护")
    private String bpacMaintain;

    /** 纯度(英文) */
    @Excel(name = "纯度(英文)")
    private String bpacPurityEn;

    /** 是否新物资(1:是;0:否) */
    @Excel(name = "是否新物资(1:是;0:否)")
    private String bpacIsnewsubstances;

    /** SN码 */
    @Excel(name = "SN码")
    private String bpacSn;

    /** 特殊标记 */
    @Excel(name = "特殊标记")
    private String bpacSpecialmarking;

    /** 国危序号 */
    @Excel(name = "国危序号")
    private Long bpacDgclsNo;

    /** 回执编号 */
    @Excel(name = "回执编号")
    private String bpacSnReturnCode;

    /** 备案日期 */
    @Excel(name = "备案日期")
    private String bpacSnDt;

    /** 外贸业务已维护01;未维护02 */
    @Excel(name = "外贸业务已维护01;未维护02")
    private String bpacForeignlock;

    /** 新加坡HS */
    @Excel(name = "新加坡HS")
    private String bpacSingaporehs;

    /** 顺丰备案号 */
    @Excel(name = "顺丰备案号")
    private String bpacRecordNumberSf;

    /** CIQ编码 */
    @Excel(name = "CIQ编码")
    private String bpacCiqcode;

    /** 质量 */
    @Excel(name = "质量")
    private String bpacQuality;

    /** 供应链 */
    @Excel(name = "供应链")
    private String bpacSupplyChain;

    /** 产品 */
    @Excel(name = "产品")
    private String bpacProductInfo;

    /** 市场 */
    @Excel(name = "市场")
    private String bpacProductMarket;

    /** 运输名称(Transport name) */
    @Excel(name = "运输名称(Transport name)")
    private String bpacTransportName;

    /** 有限数量(LQ) */
    @Excel(name = "有限数量(LQ)")
    private String bpacLq;

    /** 例外数量(EQ) */
    @Excel(name = "例外数量(EQ)")
    private String bpacEq;

    /** 危险性识别号(HN) */
    @Excel(name = "危险性识别号(HN)")
    private String bpacHn;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBpacCd(String bpacCd) 
    {
        this.bpacCd = bpacCd;
    }

    public String getBpacCd() 
    {
        return bpacCd;
    }

    public void setBpacMcd(String bpacMcd) 
    {
        this.bpacMcd = bpacMcd;
    }

    public String getBpacMcd() 
    {
        return bpacMcd;
    }

    public void setBpacCas(String bpacCas) 
    {
        this.bpacCas = bpacCas;
    }

    public String getBpacCas() 
    {
        return bpacCas;
    }

    public void setBpacIupac(String bpacIupac) 
    {
        this.bpacIupac = bpacIupac;
    }

    public String getBpacIupac() 
    {
        return bpacIupac;
    }

    public void setBpacMdl(String bpacMdl) 
    {
        this.bpacMdl = bpacMdl;
    }

    public String getBpacMdl() 
    {
        return bpacMdl;
    }

    public void setBpacEinecs(String bpacEinecs) 
    {
        this.bpacEinecs = bpacEinecs;
    }

    public String getBpacEinecs() 
    {
        return bpacEinecs;
    }

    public void setBpacSmiles(String bpacSmiles) 
    {
        this.bpacSmiles = bpacSmiles;
    }

    public String getBpacSmiles() 
    {
        return bpacSmiles;
    }

    public void setBpacPurity(String bpacPurity) 
    {
        this.bpacPurity = bpacPurity;
    }

    public String getBpacPurity() 
    {
        return bpacPurity;
    }

    public void setBpacFnm(String bpacFnm) 
    {
        this.bpacFnm = bpacFnm;
    }

    public String getBpacFnm() 
    {
        return bpacFnm;
    }

    public void setBpacEfnm(String bpacEfnm) 
    {
        this.bpacEfnm = bpacEfnm;
    }

    public String getBpacEfnm() 
    {
        return bpacEfnm;
    }

    public void setBpacFnm1(String bpacFnm1) 
    {
        this.bpacFnm1 = bpacFnm1;
    }

    public String getBpacFnm1() 
    {
        return bpacFnm1;
    }

    public void setBpacFnm2(String bpacFnm2) 
    {
        this.bpacFnm2 = bpacFnm2;
    }

    public String getBpacFnm2() 
    {
        return bpacFnm2;
    }

    public void setBpacMf(String bpacMf) 
    {
        this.bpacMf = bpacMf;
    }

    public String getBpacMf() 
    {
        return bpacMf;
    }

    public void setBpacEm(String bpacEm) 
    {
        this.bpacEm = bpacEm;
    }

    public String getBpacEm() 
    {
        return bpacEm;
    }

    public void setBpacMw(String bpacMw) 
    {
        this.bpacMw = bpacMw;
    }

    public String getBpacMw() 
    {
        return bpacMw;
    }

    public void setBpacColor(String bpacColor) 
    {
        this.bpacColor = bpacColor;
    }

    public String getBpacColor() 
    {
        return bpacColor;
    }

    public void setBpacAppear(String bpacAppear) 
    {
        this.bpacAppear = bpacAppear;
    }

    public String getBpacAppear() 
    {
        return bpacAppear;
    }

    public void setBpacLos(String bpacLos) 
    {
        this.bpacLos = bpacLos;
    }

    public String getBpacLos() 
    {
        return bpacLos;
    }

    public void setBpacCharacter(String bpacCharacter) 
    {
        this.bpacCharacter = bpacCharacter;
    }

    public String getBpacCharacter() 
    {
        return bpacCharacter;
    }

    public void setBpacUnit(String bpacUnit) 
    {
        this.bpacUnit = bpacUnit;
    }

    public String getBpacUnit() 
    {
        return bpacUnit;
    }

    public void setBpacTaxonomy(String bpacTaxonomy) 
    {
        this.bpacTaxonomy = bpacTaxonomy;
    }

    public String getBpacTaxonomy() 
    {
        return bpacTaxonomy;
    }

    public void setBpacSmell(String bpacSmell) 
    {
        this.bpacSmell = bpacSmell;
    }

    public String getBpacSmell() 
    {
        return bpacSmell;
    }

    public void setBpacRi(String bpacRi) 
    {
        this.bpacRi = bpacRi;
    }

    public String getBpacRi() 
    {
        return bpacRi;
    }

    public void setBpacVade(String bpacVade) 
    {
        this.bpacVade = bpacVade;
    }

    public String getBpacVade() 
    {
        return bpacVade;
    }

    public void setBpacFp(String bpacFp) 
    {
        this.bpacFp = bpacFp;
    }

    public String getBpacFp() 
    {
        return bpacFp;
    }

    public void setBpacBp(String bpacBp) 
    {
        this.bpacBp = bpacBp;
    }

    public String getBpacBp() 
    {
        return bpacBp;
    }

    public void setBpacMp(String bpacMp) 
    {
        this.bpacMp = bpacMp;
    }

    public String getBpacMp() 
    {
        return bpacMp;
    }

    public void setBpacAutoign(String bpacAutoign) 
    {
        this.bpacAutoign = bpacAutoign;
    }

    public String getBpacAutoign() 
    {
        return bpacAutoign;
    }

    public void setBpacSolu(String bpacSolu) 
    {
        this.bpacSolu = bpacSolu;
    }

    public String getBpacSolu() 
    {
        return bpacSolu;
    }

    public void setBpacVapp(String bpacVapp) 
    {
        this.bpacVapp = bpacVapp;
    }

    public String getBpacVapp() 
    {
        return bpacVapp;
    }

    public void setBpacDens(String bpacDens) 
    {
        this.bpacDens = bpacDens;
    }

    public String getBpacDens() 
    {
        return bpacDens;
    }

    public void setBpacDgcls(String bpacDgcls) 
    {
        this.bpacDgcls = bpacDgcls;
    }

    public String getBpacDgcls() 
    {
        return bpacDgcls;
    }

    public void setBpacDgsign(String bpacDgsign) 
    {
        this.bpacDgsign = bpacDgsign;
    }

    public String getBpacDgsign() 
    {
        return bpacDgsign;
    }

    public void setBpacDgtno(String bpacDgtno) 
    {
        this.bpacDgtno = bpacDgtno;
    }

    public String getBpacDgtno() 
    {
        return bpacDgtno;
    }

    public void setBpacRiskcd(String bpacRiskcd) 
    {
        this.bpacRiskcd = bpacRiskcd;
    }

    public String getBpacRiskcd() 
    {
        return bpacRiskcd;
    }

    public void setBpacSafetydesc(String bpacSafetydesc) 
    {
        this.bpacSafetydesc = bpacSafetydesc;
    }

    public String getBpacSafetydesc() 
    {
        return bpacSafetydesc;
    }

    public void setBpacPackgrp(String bpacPackgrp) 
    {
        this.bpacPackgrp = bpacPackgrp;
    }

    public String getBpacPackgrp() 
    {
        return bpacPackgrp;
    }

    public void setBpacWgk(String bpacWgk) 
    {
        this.bpacWgk = bpacWgk;
    }

    public String getBpacWgk() 
    {
        return bpacWgk;
    }

    public void setBpacRtecs(String bpacRtecs) 
    {
        this.bpacRtecs = bpacRtecs;
    }

    public String getBpacRtecs() 
    {
        return bpacRtecs;
    }

    public void setBpacF(String bpacF) 
    {
        this.bpacF = bpacF;
    }

    public String getBpacF() 
    {
        return bpacF;
    }

    public void setBpacScLabelPrint(String bpacScLabelPrint) 
    {
        this.bpacScLabelPrint = bpacScLabelPrint;
    }

    public String getBpacScLabelPrint() 
    {
        return bpacScLabelPrint;
    }

    public void setBpacSc(String bpacSc) 
    {
        this.bpacSc = bpacSc;
    }

    public String getBpacSc() 
    {
        return bpacSc;
    }

    public void setBpacScPtst(String bpacScPtst) 
    {
        this.bpacScPtst = bpacScPtst;
    }

    public String getBpacScPtst() 
    {
        return bpacScPtst;
    }

    public void setBpacCp(String bpacCp) 
    {
        this.bpacCp = bpacCp;
    }

    public String getBpacCp() 
    {
        return bpacCp;
    }

    public void setBpacUsage(String bpacUsage) 
    {
        this.bpacUsage = bpacUsage;
    }

    public String getBpacUsage() 
    {
        return bpacUsage;
    }

    public void setBpacIsal(String bpacIsal) 
    {
        this.bpacIsal = bpacIsal;
    }

    public String getBpacIsal() 
    {
        return bpacIsal;
    }

    public void setBpacIsmp(String bpacIsmp) 
    {
        this.bpacIsmp = bpacIsmp;
    }

    public String getBpacIsmp() 
    {
        return bpacIsmp;
    }

    public void setBpacPackinstr(String bpacPackinstr) 
    {
        this.bpacPackinstr = bpacPackinstr;
    }

    public String getBpacPackinstr() 
    {
        return bpacPackinstr;
    }

    public void setBpacFprod(String bpacFprod) 
    {
        this.bpacFprod = bpacFprod;
    }

    public String getBpacFprod() 
    {
        return bpacFprod;
    }

    public void setBpacEp(String bpacEp) 
    {
        this.bpacEp = bpacEp;
    }

    public String getBpacEp() 
    {
        return bpacEp;
    }

    public void setBpacSnote(String bpacSnote) 
    {
        this.bpacSnote = bpacSnote;
    }

    public String getBpacSnote() 
    {
        return bpacSnote;
    }

    public void setBpacSprmk(String bpacSprmk) 
    {
        this.bpacSprmk = bpacSprmk;
    }

    public String getBpacSprmk() 
    {
        return bpacSprmk;
    }

    public void setBpacSprmkPoisonod(String bpacSprmkPoisonod) 
    {
        this.bpacSprmkPoisonod = bpacSprmkPoisonod;
    }

    public String getBpacSprmkPoisonod() 
    {
        return bpacSprmkPoisonod;
    }

    public void setBpacSprmkDanger(String bpacSprmkDanger) 
    {
        this.bpacSprmkDanger = bpacSprmkDanger;
    }

    public String getBpacSprmkDanger() 
    {
        return bpacSprmkDanger;
    }

    public void setBpacImg(String bpacImg) 
    {
        this.bpacImg = bpacImg;
    }

    public String getBpacImg() 
    {
        return bpacImg;
    }

    public void setBpacUnn(String bpacUnn) 
    {
        this.bpacUnn = bpacUnn;
    }

    public String getBpacUnn() 
    {
        return bpacUnn;
    }

    public void setBpacGhssg(String bpacGhssg) 
    {
        this.bpacGhssg = bpacGhssg;
    }

    public String getBpacGhssg() 
    {
        return bpacGhssg;
    }

    public void setBpacGhsswg(String bpacGhsswg) 
    {
        this.bpacGhsswg = bpacGhsswg;
    }

    public String getBpacGhsswg() 
    {
        return bpacGhsswg;
    }

    public void setBpacGhsswg2(String bpacGhsswg2) 
    {
        this.bpacGhsswg2 = bpacGhsswg2;
    }

    public String getBpacGhsswg2() 
    {
        return bpacGhsswg2;
    }

    public void setBpacGhshsg(String bpacGhshsg) 
    {
        this.bpacGhshsg = bpacGhshsg;
    }

    public String getBpacGhshsg() 
    {
        return bpacGhshsg;
    }

    public void setBpacGhspsg(String bpacGhspsg) 
    {
        this.bpacGhspsg = bpacGhspsg;
    }

    public String getBpacGhspsg() 
    {
        return bpacGhspsg;
    }

    public void setBpacInfo(String bpacInfo) 
    {
        this.bpacInfo = bpacInfo;
    }

    public String getBpacInfo() 
    {
        return bpacInfo;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setBpacRpknote(String bpacRpknote) 
    {
        this.bpacRpknote = bpacRpknote;
    }

    public String getBpacRpknote() 
    {
        return bpacRpknote;
    }

    public void setBpacUseprecautions(String bpacUseprecautions) 
    {
        this.bpacUseprecautions = bpacUseprecautions;
    }

    public String getBpacUseprecautions() 
    {
        return bpacUseprecautions;
    }

    public void setBpacClassiimdlRmk(String bpacClassiimdlRmk) 
    {
        this.bpacClassiimdlRmk = bpacClassiimdlRmk;
    }

    public String getBpacClassiimdlRmk() 
    {
        return bpacClassiimdlRmk;
    }

    public void setBpacRawMaterial(String bpacRawMaterial) 
    {
        this.bpacRawMaterial = bpacRawMaterial;
    }

    public String getBpacRawMaterial() 
    {
        return bpacRawMaterial;
    }

    public void setBpacSprmkPoison(String bpacSprmkPoison) 
    {
        this.bpacSprmkPoison = bpacSprmkPoison;
    }

    public String getBpacSprmkPoison() 
    {
        return bpacSprmkPoison;
    }

    public void setBpacProductUse(String bpacProductUse) 
    {
        this.bpacProductUse = bpacProductUse;
    }

    public String getBpacProductUse() 
    {
        return bpacProductUse;
    }

    public void setBpacLiteratureSource(String bpacLiteratureSource) 
    {
        this.bpacLiteratureSource = bpacLiteratureSource;
    }

    public String getBpacLiteratureSource() 
    {
        return bpacLiteratureSource;
    }

    public void setBpacWarning(String bpacWarning) 
    {
        this.bpacWarning = bpacWarning;
    }

    public String getBpacWarning() 
    {
        return bpacWarning;
    }

    public void setBpacPictogram(String bpacPictogram) 
    {
        this.bpacPictogram = bpacPictogram;
    }

    public String getBpacPictogram() 
    {
        return bpacPictogram;
    }

    public void setBpacPrecautionary(String bpacPrecautionary) 
    {
        this.bpacPrecautionary = bpacPrecautionary;
    }

    public String getBpacPrecautionary() 
    {
        return bpacPrecautionary;
    }

    public void setBpacAccident(String bpacAccident) 
    {
        this.bpacAccident = bpacAccident;
    }

    public String getBpacAccident() 
    {
        return bpacAccident;
    }

    public void setBpacStorage(String bpacStorage) 
    {
        this.bpacStorage = bpacStorage;
    }

    public String getBpacStorage() 
    {
        return bpacStorage;
    }

    public void setBpacDisposal(String bpacDisposal) 
    {
        this.bpacDisposal = bpacDisposal;
    }

    public String getBpacDisposal() 
    {
        return bpacDisposal;
    }

    public void setBpacHscode(String bpacHscode) 
    {
        this.bpacHscode = bpacHscode;
    }

    public String getBpacHscode() 
    {
        return bpacHscode;
    }

    public void setBpacRiskcate(String bpacRiskcate) 
    {
        this.bpacRiskcate = bpacRiskcate;
    }

    public String getBpacRiskcate() 
    {
        return bpacRiskcate;
    }

    public void setBpacPch(String bpacPch) 
    {
        this.bpacPch = bpacPch;
    }

    public String getBpacPch() 
    {
        return bpacPch;
    }

    public void setBpacHealth(String bpacHealth) 
    {
        this.bpacHealth = bpacHealth;
    }

    public String getBpacHealth() 
    {
        return bpacHealth;
    }

    public void setBpacEnvironmentalCode(String bpacEnvironmentalCode) 
    {
        this.bpacEnvironmentalCode = bpacEnvironmentalCode;
    }

    public String getBpacEnvironmentalCode() 
    {
        return bpacEnvironmentalCode;
    }

    public void setBpacExtinguish(String bpacExtinguish) 
    {
        this.bpacExtinguish = bpacExtinguish;
    }

    public String getBpacExtinguish() 
    {
        return bpacExtinguish;
    }

    public void setBpacSpecialhazard(String bpacSpecialhazard) 
    {
        this.bpacSpecialhazard = bpacSpecialhazard;
    }

    public String getBpacSpecialhazard() 
    {
        return bpacSpecialhazard;
    }

    public void setBpacPh(String bpacPh) 
    {
        this.bpacPh = bpacPh;
    }

    public String getBpacPh() 
    {
        return bpacPh;
    }

    public void setBpacLimit(String bpacLimit) 
    {
        this.bpacLimit = bpacLimit;
    }

    public String getBpacLimit() 
    {
        return bpacLimit;
    }

    public void setBpacEvaporation(String bpacEvaporation) 
    {
        this.bpacEvaporation = bpacEvaporation;
    }

    public String getBpacEvaporation() 
    {
        return bpacEvaporation;
    }

    public void setBpacPartition(String bpacPartition) 
    {
        this.bpacPartition = bpacPartition;
    }

    public String getBpacPartition() 
    {
        return bpacPartition;
    }

    public void setBpacDt(String bpacDt) 
    {
        this.bpacDt = bpacDt;
    }

    public String getBpacDt() 
    {
        return bpacDt;
    }

    public void setBpacFlammability(String bpacFlammability) 
    {
        this.bpacFlammability = bpacFlammability;
    }

    public String getBpacFlammability() 
    {
        return bpacFlammability;
    }

    public void setBpacOral(String bpacOral) 
    {
        this.bpacOral = bpacOral;
    }

    public String getBpacOral() 
    {
        return bpacOral;
    }

    public void setBpacInhalation(String bpacInhalation) 
    {
        this.bpacInhalation = bpacInhalation;
    }

    public String getBpacInhalation() 
    {
        return bpacInhalation;
    }

    public void setBpacDermal(String bpacDermal) 
    {
        this.bpacDermal = bpacDermal;
    }

    public String getBpacDermal() 
    {
        return bpacDermal;
    }

    public void setBpacSkin(String bpacSkin) 
    {
        this.bpacSkin = bpacSkin;
    }

    public String getBpacSkin() 
    {
        return bpacSkin;
    }

    public void setBpacEye(String bpacEye) 
    {
        this.bpacEye = bpacEye;
    }

    public String getBpacEye() 
    {
        return bpacEye;
    }

    public void setBpacRespiratory(String bpacRespiratory) 
    {
        this.bpacRespiratory = bpacRespiratory;
    }

    public String getBpacRespiratory() 
    {
        return bpacRespiratory;
    }

    public void setBpacMutagenicity(String bpacMutagenicity) 
    {
        this.bpacMutagenicity = bpacMutagenicity;
    }

    public String getBpacMutagenicity() 
    {
        return bpacMutagenicity;
    }

    public void setBpacCarcinogenicity(String bpacCarcinogenicity) 
    {
        this.bpacCarcinogenicity = bpacCarcinogenicity;
    }

    public String getBpacCarcinogenicity() 
    {
        return bpacCarcinogenicity;
    }

    public void setBpacReproductive(String bpacReproductive) 
    {
        this.bpacReproductive = bpacReproductive;
    }

    public String getBpacReproductive() 
    {
        return bpacReproductive;
    }

    public void setBpacSingle(String bpacSingle) 
    {
        this.bpacSingle = bpacSingle;
    }

    public String getBpacSingle() 
    {
        return bpacSingle;
    }

    public void setBpacRepeated(String bpacRepeated) 
    {
        this.bpacRepeated = bpacRepeated;
    }

    public String getBpacRepeated() 
    {
        return bpacRepeated;
    }

    public void setBpacAddinfo(String bpacAddinfo) 
    {
        this.bpacAddinfo = bpacAddinfo;
    }

    public String getBpacAddinfo() 
    {
        return bpacAddinfo;
    }

    public void setBpacTofish(String bpacTofish) 
    {
        this.bpacTofish = bpacTofish;
    }

    public String getBpacTofish() 
    {
        return bpacTofish;
    }

    public void setBpacTodaphnia(String bpacTodaphnia) 
    {
        this.bpacTodaphnia = bpacTodaphnia;
    }

    public String getBpacTodaphnia() 
    {
        return bpacTodaphnia;
    }

    public void setBpacToalgae(String bpacToalgae) 
    {
        this.bpacToalgae = bpacToalgae;
    }

    public String getBpacToalgae() 
    {
        return bpacToalgae;
    }

    public void setBpacTobacteria(String bpacTobacteria) 
    {
        this.bpacTobacteria = bpacTobacteria;
    }

    public String getBpacTobacteria() 
    {
        return bpacTobacteria;
    }

    public void setBpacPersistence(String bpacPersistence) 
    {
        this.bpacPersistence = bpacPersistence;
    }

    public String getBpacPersistence() 
    {
        return bpacPersistence;
    }

    public void setBpacBioaccumulative(String bpacBioaccumulative) 
    {
        this.bpacBioaccumulative = bpacBioaccumulative;
    }

    public String getBpacBioaccumulative() 
    {
        return bpacBioaccumulative;
    }

    public void setBpacMobility(String bpacMobility) 
    {
        this.bpacMobility = bpacMobility;
    }

    public String getBpacMobility() 
    {
        return bpacMobility;
    }

    public void setBpacEffects(String bpacEffects) 
    {
        this.bpacEffects = bpacEffects;
    }

    public String getBpacEffects() 
    {
        return bpacEffects;
    }

    public void setBpacEnvironmental(String bpacEnvironmental) 
    {
        this.bpacEnvironmental = bpacEnvironmental;
    }

    public String getBpacEnvironmental() 
    {
        return bpacEnvironmental;
    }

    public void setBpacMsdsUrl(String bpacMsdsUrl) 
    {
        this.bpacMsdsUrl = bpacMsdsUrl;
    }

    public String getBpacMsdsUrl() 
    {
        return bpacMsdsUrl;
    }

    public void setBpacPermitThrough(String bpacPermitThrough) 
    {
        this.bpacPermitThrough = bpacPermitThrough;
    }

    public String getBpacPermitThrough() 
    {
        return bpacPermitThrough;
    }

    public void setBpacSnQualifications(String bpacSnQualifications) 
    {
        this.bpacSnQualifications = bpacSnQualifications;
    }

    public String getBpacSnQualifications() 
    {
        return bpacSnQualifications;
    }

    public void setBpacIsrisk(String bpacIsrisk) 
    {
        this.bpacIsrisk = bpacIsrisk;
    }

    public String getBpacIsrisk() 
    {
        return bpacIsrisk;
    }

    public void setBpacCodehs(String bpacCodehs) 
    {
        this.bpacCodehs = bpacCodehs;
    }

    public String getBpacCodehs() 
    {
        return bpacCodehs;
    }

    public void setBpacDgcargo(String bpacDgcargo) 
    {
        this.bpacDgcargo = bpacDgcargo;
    }

    public String getBpacDgcargo() 
    {
        return bpacDgcargo;
    }

    public void setBpacDgsource(String bpacDgsource) 
    {
        this.bpacDgsource = bpacDgsource;
    }

    public String getBpacDgsource() 
    {
        return bpacDgsource;
    }

    public void setBpacInportlimit(String bpacInportlimit) 
    {
        this.bpacInportlimit = bpacInportlimit;
    }

    public String getBpacInportlimit() 
    {
        return bpacInportlimit;
    }

    public void setBpacExportlimit(String bpacExportlimit) 
    {
        this.bpacExportlimit = bpacExportlimit;
    }

    public String getBpacExportlimit() 
    {
        return bpacExportlimit;
    }

    public void setBpacPhscope(String bpacPhscope) 
    {
        this.bpacPhscope = bpacPhscope;
    }

    public String getBpacPhscope() 
    {
        return bpacPhscope;
    }

    public void setBpacDensity(String bpacDensity) 
    {
        this.bpacDensity = bpacDensity;
    }

    public String getBpacDensity() 
    {
        return bpacDensity;
    }

    public void setBpacDgclsNext(String bpacDgclsNext) 
    {
        this.bpacDgclsNext = bpacDgclsNext;
    }

    public String getBpacDgclsNext() 
    {
        return bpacDgclsNext;
    }

    public void setBpacNdgclsClass(String bpacNdgclsClass) 
    {
        this.bpacNdgclsClass = bpacNdgclsClass;
    }

    public String getBpacNdgclsClass() 
    {
        return bpacNdgclsClass;
    }

    public void setBpacMaintain(String bpacMaintain) 
    {
        this.bpacMaintain = bpacMaintain;
    }

    public String getBpacMaintain() 
    {
        return bpacMaintain;
    }

    public void setBpacPurityEn(String bpacPurityEn) 
    {
        this.bpacPurityEn = bpacPurityEn;
    }

    public String getBpacPurityEn() 
    {
        return bpacPurityEn;
    }

    public void setBpacIsnewsubstances(String bpacIsnewsubstances) 
    {
        this.bpacIsnewsubstances = bpacIsnewsubstances;
    }

    public String getBpacIsnewsubstances() 
    {
        return bpacIsnewsubstances;
    }

    public void setBpacSn(String bpacSn) 
    {
        this.bpacSn = bpacSn;
    }

    public String getBpacSn() 
    {
        return bpacSn;
    }

    public void setBpacSpecialmarking(String bpacSpecialmarking) 
    {
        this.bpacSpecialmarking = bpacSpecialmarking;
    }

    public String getBpacSpecialmarking() 
    {
        return bpacSpecialmarking;
    }

    public void setBpacDgclsNo(Long bpacDgclsNo) 
    {
        this.bpacDgclsNo = bpacDgclsNo;
    }

    public Long getBpacDgclsNo() 
    {
        return bpacDgclsNo;
    }

    public void setBpacSnReturnCode(String bpacSnReturnCode) 
    {
        this.bpacSnReturnCode = bpacSnReturnCode;
    }

    public String getBpacSnReturnCode() 
    {
        return bpacSnReturnCode;
    }

    public void setBpacSnDt(String bpacSnDt) 
    {
        this.bpacSnDt = bpacSnDt;
    }

    public String getBpacSnDt() 
    {
        return bpacSnDt;
    }

    public void setBpacForeignlock(String bpacForeignlock) 
    {
        this.bpacForeignlock = bpacForeignlock;
    }

    public String getBpacForeignlock() 
    {
        return bpacForeignlock;
    }

    public void setBpacSingaporehs(String bpacSingaporehs) 
    {
        this.bpacSingaporehs = bpacSingaporehs;
    }

    public String getBpacSingaporehs() 
    {
        return bpacSingaporehs;
    }

    public void setBpacRecordNumberSf(String bpacRecordNumberSf) 
    {
        this.bpacRecordNumberSf = bpacRecordNumberSf;
    }

    public String getBpacRecordNumberSf() 
    {
        return bpacRecordNumberSf;
    }

    public void setBpacCiqcode(String bpacCiqcode) 
    {
        this.bpacCiqcode = bpacCiqcode;
    }

    public String getBpacCiqcode() 
    {
        return bpacCiqcode;
    }

    public void setBpacQuality(String bpacQuality) 
    {
        this.bpacQuality = bpacQuality;
    }

    public String getBpacQuality() 
    {
        return bpacQuality;
    }

    public void setBpacSupplyChain(String bpacSupplyChain) 
    {
        this.bpacSupplyChain = bpacSupplyChain;
    }

    public String getBpacSupplyChain() 
    {
        return bpacSupplyChain;
    }

    public void setBpacProductInfo(String bpacProductInfo) 
    {
        this.bpacProductInfo = bpacProductInfo;
    }

    public String getBpacProductInfo() 
    {
        return bpacProductInfo;
    }

    public void setBpacProductMarket(String bpacProductMarket) 
    {
        this.bpacProductMarket = bpacProductMarket;
    }

    public String getBpacProductMarket() 
    {
        return bpacProductMarket;
    }

    public void setBpacTransportName(String bpacTransportName) 
    {
        this.bpacTransportName = bpacTransportName;
    }

    public String getBpacTransportName() 
    {
        return bpacTransportName;
    }

    public void setBpacLq(String bpacLq) 
    {
        this.bpacLq = bpacLq;
    }

    public String getBpacLq() 
    {
        return bpacLq;
    }

    public void setBpacEq(String bpacEq) 
    {
        this.bpacEq = bpacEq;
    }

    public String getBpacEq() 
    {
        return bpacEq;
    }

    public void setBpacHn(String bpacHn) 
    {
        this.bpacHn = bpacHn;
    }

    public String getBpacHn() 
    {
        return bpacHn;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bpacCd", getBpacCd())
            .append("bpacMcd", getBpacMcd())
            .append("bpacCas", getBpacCas())
            .append("bpacIupac", getBpacIupac())
            .append("bpacMdl", getBpacMdl())
            .append("bpacEinecs", getBpacEinecs())
            .append("bpacSmiles", getBpacSmiles())
            .append("bpacPurity", getBpacPurity())
            .append("bpacFnm", getBpacFnm())
            .append("bpacEfnm", getBpacEfnm())
            .append("bpacFnm1", getBpacFnm1())
            .append("bpacFnm2", getBpacFnm2())
            .append("bpacMf", getBpacMf())
            .append("bpacEm", getBpacEm())
            .append("bpacMw", getBpacMw())
            .append("bpacColor", getBpacColor())
            .append("bpacAppear", getBpacAppear())
            .append("bpacLos", getBpacLos())
            .append("bpacCharacter", getBpacCharacter())
            .append("bpacUnit", getBpacUnit())
            .append("bpacTaxonomy", getBpacTaxonomy())
            .append("bpacSmell", getBpacSmell())
            .append("bpacRi", getBpacRi())
            .append("bpacVade", getBpacVade())
            .append("bpacFp", getBpacFp())
            .append("bpacBp", getBpacBp())
            .append("bpacMp", getBpacMp())
            .append("bpacAutoign", getBpacAutoign())
            .append("bpacSolu", getBpacSolu())
            .append("bpacVapp", getBpacVapp())
            .append("bpacDens", getBpacDens())
            .append("bpacDgcls", getBpacDgcls())
            .append("bpacDgsign", getBpacDgsign())
            .append("bpacDgtno", getBpacDgtno())
            .append("bpacRiskcd", getBpacRiskcd())
            .append("bpacSafetydesc", getBpacSafetydesc())
            .append("bpacPackgrp", getBpacPackgrp())
            .append("bpacWgk", getBpacWgk())
            .append("bpacRtecs", getBpacRtecs())
            .append("bpacF", getBpacF())
            .append("bpacScLabelPrint", getBpacScLabelPrint())
            .append("bpacSc", getBpacSc())
            .append("bpacScPtst", getBpacScPtst())
            .append("bpacCp", getBpacCp())
            .append("bpacUsage", getBpacUsage())
            .append("bpacIsal", getBpacIsal())
            .append("bpacIsmp", getBpacIsmp())
            .append("bpacPackinstr", getBpacPackinstr())
            .append("bpacFprod", getBpacFprod())
            .append("bpacEp", getBpacEp())
            .append("bpacSnote", getBpacSnote())
            .append("bpacSprmk", getBpacSprmk())
            .append("bpacSprmkPoisonod", getBpacSprmkPoisonod())
            .append("bpacSprmkDanger", getBpacSprmkDanger())
            .append("bpacImg", getBpacImg())
            .append("bpacUnn", getBpacUnn())
            .append("bpacGhssg", getBpacGhssg())
            .append("bpacGhsswg", getBpacGhsswg())
            .append("bpacGhsswg2", getBpacGhsswg2())
            .append("bpacGhshsg", getBpacGhshsg())
            .append("bpacGhspsg", getBpacGhspsg())
            .append("bpacInfo", getBpacInfo())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("bpacRpknote", getBpacRpknote())
            .append("bpacUseprecautions", getBpacUseprecautions())
            .append("bpacClassiimdlRmk", getBpacClassiimdlRmk())
            .append("bpacRawMaterial", getBpacRawMaterial())
            .append("bpacSprmkPoison", getBpacSprmkPoison())
            .append("bpacProductUse", getBpacProductUse())
            .append("bpacLiteratureSource", getBpacLiteratureSource())
            .append("bpacWarning", getBpacWarning())
            .append("bpacPictogram", getBpacPictogram())
            .append("bpacPrecautionary", getBpacPrecautionary())
            .append("bpacAccident", getBpacAccident())
            .append("bpacStorage", getBpacStorage())
            .append("bpacDisposal", getBpacDisposal())
            .append("bpacHscode", getBpacHscode())
            .append("bpacRiskcate", getBpacRiskcate())
            .append("bpacPch", getBpacPch())
            .append("bpacHealth", getBpacHealth())
            .append("bpacEnvironmentalCode", getBpacEnvironmentalCode())
            .append("bpacExtinguish", getBpacExtinguish())
            .append("bpacSpecialhazard", getBpacSpecialhazard())
            .append("bpacPh", getBpacPh())
            .append("bpacLimit", getBpacLimit())
            .append("bpacEvaporation", getBpacEvaporation())
            .append("bpacPartition", getBpacPartition())
            .append("bpacDt", getBpacDt())
            .append("bpacFlammability", getBpacFlammability())
            .append("bpacOral", getBpacOral())
            .append("bpacInhalation", getBpacInhalation())
            .append("bpacDermal", getBpacDermal())
            .append("bpacSkin", getBpacSkin())
            .append("bpacEye", getBpacEye())
            .append("bpacRespiratory", getBpacRespiratory())
            .append("bpacMutagenicity", getBpacMutagenicity())
            .append("bpacCarcinogenicity", getBpacCarcinogenicity())
            .append("bpacReproductive", getBpacReproductive())
            .append("bpacSingle", getBpacSingle())
            .append("bpacRepeated", getBpacRepeated())
            .append("bpacAddinfo", getBpacAddinfo())
            .append("bpacTofish", getBpacTofish())
            .append("bpacTodaphnia", getBpacTodaphnia())
            .append("bpacToalgae", getBpacToalgae())
            .append("bpacTobacteria", getBpacTobacteria())
            .append("bpacPersistence", getBpacPersistence())
            .append("bpacBioaccumulative", getBpacBioaccumulative())
            .append("bpacMobility", getBpacMobility())
            .append("bpacEffects", getBpacEffects())
            .append("bpacEnvironmental", getBpacEnvironmental())
            .append("bpacMsdsUrl", getBpacMsdsUrl())
            .append("bpacPermitThrough", getBpacPermitThrough())
            .append("bpacSnQualifications", getBpacSnQualifications())
            .append("bpacIsrisk", getBpacIsrisk())
            .append("bpacCodehs", getBpacCodehs())
            .append("bpacDgcargo", getBpacDgcargo())
            .append("bpacDgsource", getBpacDgsource())
            .append("bpacInportlimit", getBpacInportlimit())
            .append("bpacExportlimit", getBpacExportlimit())
            .append("bpacPhscope", getBpacPhscope())
            .append("bpacDensity", getBpacDensity())
            .append("bpacDgclsNext", getBpacDgclsNext())
            .append("bpacNdgclsClass", getBpacNdgclsClass())
            .append("bpacMaintain", getBpacMaintain())
            .append("bpacPurityEn", getBpacPurityEn())
            .append("bpacIsnewsubstances", getBpacIsnewsubstances())
            .append("bpacSn", getBpacSn())
            .append("bpacSpecialmarking", getBpacSpecialmarking())
            .append("bpacDgclsNo", getBpacDgclsNo())
            .append("bpacSnReturnCode", getBpacSnReturnCode())
            .append("bpacSnDt", getBpacSnDt())
            .append("bpacForeignlock", getBpacForeignlock())
            .append("bpacSingaporehs", getBpacSingaporehs())
            .append("bpacRecordNumberSf", getBpacRecordNumberSf())
            .append("bpacCiqcode", getBpacCiqcode())
            .append("bpacQuality", getBpacQuality())
            .append("bpacSupplyChain", getBpacSupplyChain())
            .append("bpacProductInfo", getBpacProductInfo())
            .append("bpacProductMarket", getBpacProductMarket())
            .append("bpacTransportName", getBpacTransportName())
            .append("bpacLq", getBpacLq())
            .append("bpacEq", getBpacEq())
            .append("bpacHn", getBpacHn())
            .toString();
    }
}
