package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogInventoryIoTransaction;
import com.lucy.erp.service.ILogInventoryIoTransactionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 物流_库存_清单Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/inventory/transaction")
public class LogInventoryIoTransactionController extends BaseController
{
    @Autowired
    private ILogInventoryIoTransactionService logInventoryIoTransactionService;

    /**
     * 查询物流_库存_清单列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:transaction:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogInventoryIoTransaction logInventoryIoTransaction)
    {
        startPage();
        List<LogInventoryIoTransaction> list = logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);
        return getDataTable(list);
    }

    /**
     * 导出物流_库存_清单列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:transaction:export')")
    @Log(title = "物流_库存_清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogInventoryIoTransaction logInventoryIoTransaction)
    {
        List<LogInventoryIoTransaction> list = logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);
        ExcelUtil<LogInventoryIoTransaction> util = new ExcelUtil<LogInventoryIoTransaction>(LogInventoryIoTransaction.class);
        util.exportExcel(response, list, "物流_库存_清单数据");
    }

    /**
     * 获取物流_库存_清单详细信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:transaction:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(logInventoryIoTransactionService.selectLogInventoryIoTransactionById(id));
    }

    /**
     * 新增物流_库存_清单
     */
    @PreAuthorize("@ss.hasPermi('inventory:transaction:add')")
    @Log(title = "物流_库存_清单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogInventoryIoTransaction logInventoryIoTransaction)
    {
        return toAjax(logInventoryIoTransactionService.insertLogInventoryIoTransaction(logInventoryIoTransaction));
    }

    /**
     * 修改物流_库存_清单
     */
    @PreAuthorize("@ss.hasPermi('inventory:transaction:edit')")
    @Log(title = "物流_库存_清单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogInventoryIoTransaction logInventoryIoTransaction)
    {
        return toAjax(logInventoryIoTransactionService.updateLogInventoryIoTransaction(logInventoryIoTransaction));
    }

    /**
     * 删除物流_库存_清单
     */
    @PreAuthorize("@ss.hasPermi('inventory:transaction:remove')")
    @Log(title = "物流_库存_清单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(logInventoryIoTransactionService.deleteLogInventoryIoTransactionByIds(ids));
    }
}
