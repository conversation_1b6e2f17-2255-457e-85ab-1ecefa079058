package com.lucy.inventoryexit.controller;

import com.lucy.inventoryexit.service.InventoryExitService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = {"/inventoryexit"})
public class InventoryExitController extends BaseController {

    @Autowired
    InventoryExitService inventoryExitService;

    @Anonymous
    @GetMapping("/test")
    public AjaxResult testDatasource() {
        try {
            AjaxResult ajax = AjaxResult.success();
            inventoryExitService.testDatasource();
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
