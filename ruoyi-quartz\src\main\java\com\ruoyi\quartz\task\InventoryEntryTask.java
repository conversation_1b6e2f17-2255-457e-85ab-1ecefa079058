package com.ruoyi.quartz.task;

import com.lucy.assetowner.domain.AssetOwner;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.WXUtils;
import com.ruoyi.inventoryentry.service.IInventoryCurrentPeriodEntryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 库存入库定时任务
 *
 * <AUTHOR>
 */
@Component("inventoryEntryTask")
public class InventoryEntryTask {
    private static final Logger log = LoggerFactory.getLogger(InventoryEntryTask.class);

    @Autowired
    private IInventoryCurrentPeriodEntryService inventoryCurrentPeriodEntryService;
    /**
     * 多参数库存入库数据同步任务 全部资产主体
     *
     * @param dateStart 开始日期（格式：yyyy-MM-dd）
     * @param dateEnd 结束日期（格式：yyyy-MM-dd）
     */
    public void syncInventoryEntryWithMultiParams(String dateStart, String dateEnd) {
        // 参数校验
        if (StringUtils.isEmpty(dateStart) || StringUtils.isEmpty(dateEnd)) {
            throw new IllegalArgumentException("日期参数不能为空");
        }

        String userid_zc = "13294181129";

        try {
            for (AssetOwner assetOwner : AssetOwner.values()) {
                WXUtils.invokeWorkNotification(userid_zc, "入库数据同步" + assetOwner.entity().getName()
                        + "开始执行，日期：" + dateStart + "至" + dateEnd);
                inventoryCurrentPeriodEntryService.loadBQRK(dateStart, dateEnd, assetOwner);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
