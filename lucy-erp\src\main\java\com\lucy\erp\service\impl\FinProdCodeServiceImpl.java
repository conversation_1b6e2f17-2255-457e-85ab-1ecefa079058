package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.FinProdCodeMapper;
import com.lucy.erp.domain.FinProdCode;
import com.lucy.erp.service.IFinProdCodeService;

/**
 * 记账科目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class FinProdCodeServiceImpl implements IFinProdCodeService
{
    @Autowired
    private FinProdCodeMapper finProdCodeMapper;

    /**
     * 查询记账科目
     * 
     * @param id 记账科目主键
     * @return 记账科目
     */
    @Override
    public FinProdCode selectFinProdCodeById(Long id)
    {
        return finProdCodeMapper.selectFinProdCodeById(id);
    }

    /**
     * 查询记账科目列表
     * 
     * @param finProdCode 记账科目
     * @return 记账科目
     */
    @Override
    public List<FinProdCode> selectFinProdCodeList(FinProdCode finProdCode)
    {
        return finProdCodeMapper.selectFinProdCodeList(finProdCode);
    }

    /**
     * 新增记账科目
     * 
     * @param finProdCode 记账科目
     * @return 结果
     */
    @Override
    public int insertFinProdCode(FinProdCode finProdCode)
    {
        return finProdCodeMapper.insertFinProdCode(finProdCode);
    }

    /**
     * 修改记账科目
     * 
     * @param finProdCode 记账科目
     * @return 结果
     */
    @Override
    public int updateFinProdCode(FinProdCode finProdCode)
    {
        return finProdCodeMapper.updateFinProdCode(finProdCode);
    }

    /**
     * 批量删除记账科目
     * 
     * @param ids 需要删除的记账科目主键
     * @return 结果
     */
    @Override
    public int deleteFinProdCodeByIds(Long[] ids)
    {
        return finProdCodeMapper.deleteFinProdCodeByIds(ids);
    }

    /**
     * 删除记账科目信息
     * 
     * @param id 记账科目主键
     * @return 结果
     */
    @Override
    public int deleteFinProdCodeById(Long id)
    {
        return finProdCodeMapper.deleteFinProdCodeById(id);
    }
}
