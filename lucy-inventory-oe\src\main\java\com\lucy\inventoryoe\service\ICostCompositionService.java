package com.lucy.inventoryoe.service;

import java.util.List;
import com.lucy.inventoryoe.domain.CostComposition;

/**
 * Cost composition structureService接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface ICostCompositionService 
{
    /**
     * 查询Cost composition structure
     * 
     * @param id Cost composition structure主键
     * @return Cost composition structure
     */
    public CostComposition selectCostCompositionById(Long id);

    /**
     * 查询Cost composition structure列表
     * 
     * @param costComposition Cost composition structure
     * @return Cost composition structure集合
     */
    public List<CostComposition> selectCostCompositionList(CostComposition costComposition);

    /**
     * 新增Cost composition structure
     * 
     * @param costComposition Cost composition structure
     * @return 结果
     */
    public int insertCostComposition(CostComposition costComposition);

    /**
     * 修改Cost composition structure
     * 
     * @param costComposition Cost composition structure
     * @return 结果
     */
    public int updateCostComposition(CostComposition costComposition);

    /**
     * 批量删除Cost composition structure
     * 
     * @param ids 需要删除的Cost composition structure主键集合
     * @return 结果
     */
    public int deleteCostCompositionByIds(Long[] ids);

    /**
     * 删除Cost composition structure信息
     * 
     * @param id Cost composition structure主键
     * @return 结果
     */
    public int deleteCostCompositionById(Long id);
}
