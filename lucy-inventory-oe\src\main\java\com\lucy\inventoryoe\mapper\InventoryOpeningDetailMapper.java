package com.lucy.inventoryoe.mapper;

import java.util.List;
import com.lucy.inventoryoe.domain.InventoryOpeningDetail;

/**
 * 库存期初明细 Inventory opening detail recordsMapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface InventoryOpeningDetailMapper 
{
    /**
     * 查询库存期初明细 Inventory opening detail records
     * 
     * @param id 库存期初明细 Inventory opening detail records主键
     * @return 库存期初明细 Inventory opening detail records
     */
    public InventoryOpeningDetail selectInventoryOpeningDetailById(Long id);

    /**
     * 查询库存期初明细 Inventory opening detail records列表
     * 
     * @param inventoryOpeningDetail 库存期初明细 Inventory opening detail records
     * @return 库存期初明细 Inventory opening detail records集合
     */
    public List<InventoryOpeningDetail> selectInventoryOpeningDetailList(InventoryOpeningDetail inventoryOpeningDetail);

    /**
     * 新增库存期初明细 Inventory opening detail records
     * 
     * @param inventoryOpeningDetail 库存期初明细 Inventory opening detail records
     * @return 结果
     */
    public int insertInventoryOpeningDetail(InventoryOpeningDetail inventoryOpeningDetail);

    /**
     * 修改库存期初明细 Inventory opening detail records
     * 
     * @param inventoryOpeningDetail 库存期初明细 Inventory opening detail records
     * @return 结果
     */
    public int updateInventoryOpeningDetail(InventoryOpeningDetail inventoryOpeningDetail);

    /**
     * 删除库存期初明细 Inventory opening detail records
     * 
     * @param id 库存期初明细 Inventory opening detail records主键
     * @return 结果
     */
    public int deleteInventoryOpeningDetailById(Long id);

    /**
     * 批量删除库存期初明细 Inventory opening detail records
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryOpeningDetailByIds(Long[] ids);
}
