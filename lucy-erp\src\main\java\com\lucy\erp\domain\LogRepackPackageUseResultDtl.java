package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产成品投料明细对象 log_repack_package_use_result_dtl
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public class LogRepackPackageUseResultDtl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 领料明细表id */
    private Long id;

    /** 投料单编号 */
    @Excel(name = "投料单编号")
    private String lrpurdMcd;

    /** 投料单明细编号 */
    private String lrpurdCd;

    /** 包装材料SKU编号 */
    @Excel(name = "包装材料SKU编号")
    private String lrpurdSkuCd;

    /** 仓库 */
    @Excel(name = "仓库")
    private String lrpurdSkuDmcd;

    /** 库位 */
    @Excel(name = "库位")
    private String lrpurdSkuSlcd;

    /** 库位类型 */
    @Excel(name = "库位类型")
    private String lrpurdSkuTcd;

    /** 资产所属组织 */
    @Excel(name = "资产所属组织")
    private String lrpurdSkuOrgmcd;

    /** 包装材料批次 */
    @Excel(name = "包装材料批次")
    private String lrpurdSkuBn;

    /** 包装材料加密批次 */
    @Excel(name = "包装材料加密批次")
    private String lrpurdSkuBnEncrypt;

    /** 包装材料投料数量 */
    @Excel(name = "包装材料投料数量")
    private BigDecimal lrpurdSkuBnQty;

    /** 投料时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "投料时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lrpurdSkuOutDate;

    /** 包材总金额 */
    @Excel(name = "包材总金额")
    private BigDecimal lrpurdTotalPrc;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal lrpurdBit;

    /** 是否价税分离 */
    @Excel(name = "是否价税分离")
    private Integer lrpurdIspart;

    /** 包材单位含税成本 */
    @Excel(name = "包材单位含税成本")
    private BigDecimal lrpurdTotalValue;

    /** 记账标记 */
    @Excel(name = "记账标记")
    private Integer lrpurdFlag;

    /** r_cre_kid */
    @Excel(name = "r_cre_kid")
    private String rCreKid;

    /** r_cre_dt */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "r_cre_dt", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** r_upd_kid */
    @Excel(name = "r_upd_kid")
    private String rUpdKid;

    /** r_upd_dt */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "r_upd_dt", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** r_v */
    @Excel(name = "r_v")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLrpurdMcd(String lrpurdMcd) 
    {
        this.lrpurdMcd = lrpurdMcd;
    }

    public String getLrpurdMcd() 
    {
        return lrpurdMcd;
    }

    public void setLrpurdCd(String lrpurdCd) 
    {
        this.lrpurdCd = lrpurdCd;
    }

    public String getLrpurdCd() 
    {
        return lrpurdCd;
    }

    public void setLrpurdSkuCd(String lrpurdSkuCd) 
    {
        this.lrpurdSkuCd = lrpurdSkuCd;
    }

    public String getLrpurdSkuCd() 
    {
        return lrpurdSkuCd;
    }

    public void setLrpurdSkuDmcd(String lrpurdSkuDmcd) 
    {
        this.lrpurdSkuDmcd = lrpurdSkuDmcd;
    }

    public String getLrpurdSkuDmcd() 
    {
        return lrpurdSkuDmcd;
    }

    public void setLrpurdSkuSlcd(String lrpurdSkuSlcd) 
    {
        this.lrpurdSkuSlcd = lrpurdSkuSlcd;
    }

    public String getLrpurdSkuSlcd() 
    {
        return lrpurdSkuSlcd;
    }

    public void setLrpurdSkuTcd(String lrpurdSkuTcd) 
    {
        this.lrpurdSkuTcd = lrpurdSkuTcd;
    }

    public String getLrpurdSkuTcd() 
    {
        return lrpurdSkuTcd;
    }

    public void setLrpurdSkuOrgmcd(String lrpurdSkuOrgmcd) 
    {
        this.lrpurdSkuOrgmcd = lrpurdSkuOrgmcd;
    }

    public String getLrpurdSkuOrgmcd() 
    {
        return lrpurdSkuOrgmcd;
    }

    public void setLrpurdSkuBn(String lrpurdSkuBn) 
    {
        this.lrpurdSkuBn = lrpurdSkuBn;
    }

    public String getLrpurdSkuBn() 
    {
        return lrpurdSkuBn;
    }

    public void setLrpurdSkuBnEncrypt(String lrpurdSkuBnEncrypt) 
    {
        this.lrpurdSkuBnEncrypt = lrpurdSkuBnEncrypt;
    }

    public String getLrpurdSkuBnEncrypt() 
    {
        return lrpurdSkuBnEncrypt;
    }

    public void setLrpurdSkuBnQty(BigDecimal lrpurdSkuBnQty) 
    {
        this.lrpurdSkuBnQty = lrpurdSkuBnQty;
    }

    public BigDecimal getLrpurdSkuBnQty() 
    {
        return lrpurdSkuBnQty;
    }

    public void setLrpurdSkuOutDate(Date lrpurdSkuOutDate) 
    {
        this.lrpurdSkuOutDate = lrpurdSkuOutDate;
    }

    public Date getLrpurdSkuOutDate() 
    {
        return lrpurdSkuOutDate;
    }

    public void setLrpurdTotalPrc(BigDecimal lrpurdTotalPrc) 
    {
        this.lrpurdTotalPrc = lrpurdTotalPrc;
    }

    public BigDecimal getLrpurdTotalPrc() 
    {
        return lrpurdTotalPrc;
    }

    public void setLrpurdBit(BigDecimal lrpurdBit) 
    {
        this.lrpurdBit = lrpurdBit;
    }

    public BigDecimal getLrpurdBit() 
    {
        return lrpurdBit;
    }

    public void setLrpurdIspart(Integer lrpurdIspart) 
    {
        this.lrpurdIspart = lrpurdIspart;
    }

    public Integer getLrpurdIspart() 
    {
        return lrpurdIspart;
    }

    public void setLrpurdTotalValue(BigDecimal lrpurdTotalValue) 
    {
        this.lrpurdTotalValue = lrpurdTotalValue;
    }

    public BigDecimal getLrpurdTotalValue() 
    {
        return lrpurdTotalValue;
    }

    public void setLrpurdFlag(Integer lrpurdFlag) 
    {
        this.lrpurdFlag = lrpurdFlag;
    }

    public Integer getLrpurdFlag() 
    {
        return lrpurdFlag;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lrpurdMcd", getLrpurdMcd())
            .append("lrpurdCd", getLrpurdCd())
            .append("lrpurdSkuCd", getLrpurdSkuCd())
            .append("lrpurdSkuDmcd", getLrpurdSkuDmcd())
            .append("lrpurdSkuSlcd", getLrpurdSkuSlcd())
            .append("lrpurdSkuTcd", getLrpurdSkuTcd())
            .append("lrpurdSkuOrgmcd", getLrpurdSkuOrgmcd())
            .append("lrpurdSkuBn", getLrpurdSkuBn())
            .append("lrpurdSkuBnEncrypt", getLrpurdSkuBnEncrypt())
            .append("lrpurdSkuBnQty", getLrpurdSkuBnQty())
            .append("lrpurdSkuOutDate", getLrpurdSkuOutDate())
            .append("lrpurdTotalPrc", getLrpurdTotalPrc())
            .append("lrpurdBit", getLrpurdBit())
            .append("lrpurdIspart", getLrpurdIspart())
            .append("lrpurdTotalValue", getLrpurdTotalValue())
            .append("lrpurdFlag", getLrpurdFlag())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
