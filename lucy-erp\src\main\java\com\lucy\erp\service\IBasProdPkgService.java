package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.BasProdPkg;

/**
 * 基础_产品_包装
Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IBasProdPkgService 
{
    /**
     * 查询基础_产品_包装

     * 
     * @param bppCd 基础_产品_包装
主键
     * @return 基础_产品_包装

     */
    public BasProdPkg selectBasProdPkgByBppCd(String bppCd);

    /**
     * 查询基础_产品_包装
列表
     * 
     * @param basProdPkg 基础_产品_包装

     * @return 基础_产品_包装
集合
     */
    public List<BasProdPkg> selectBasProdPkgList(BasProdPkg basProdPkg);

    /**
     * 新增基础_产品_包装

     * 
     * @param basProdPkg 基础_产品_包装

     * @return 结果
     */
    public int insertBasProdPkg(BasProdPkg basProdPkg);

    /**
     * 修改基础_产品_包装

     * 
     * @param basProdPkg 基础_产品_包装

     * @return 结果
     */
    public int updateBasProdPkg(BasProdPkg basProdPkg);

    /**
     * 批量删除基础_产品_包装

     * 
     * @param bppCds 需要删除的基础_产品_包装
主键集合
     * @return 结果
     */
    public int deleteBasProdPkgByBppCds(String[] bppCds);

    /**
     * 删除基础_产品_包装
信息
     * 
     * @param bppCd 基础_产品_包装
主键
     * @return 结果
     */
    public int deleteBasProdPkgByBppCd(String bppCd);
}
