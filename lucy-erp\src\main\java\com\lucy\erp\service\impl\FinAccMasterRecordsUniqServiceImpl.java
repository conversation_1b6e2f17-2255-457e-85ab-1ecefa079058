package com.lucy.erp.service.impl;

import java.util.Arrays;
import java.util.List;

import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.FinAccMasterRecordsUniqMapper;
import com.lucy.erp.domain.FinAccMasterRecordsUniq;
import com.lucy.erp.service.IFinAccMasterRecordsUniqService;

/**
 * 账套Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@DataSource(DataSourceType.ERP)
public class FinAccMasterRecordsUniqServiceImpl implements IFinAccMasterRecordsUniqService 
{
    @Autowired
    private FinAccMasterRecordsUniqMapper finAccMasterRecordsUniqMapper;

    /**
     * 查询账套
     * 
     * @param id 账套主键
     * @return 账套
     */
    @Override
    public FinAccMasterRecordsUniq selectFinAccMasterRecordsUniqById(String id)
    {
        return finAccMasterRecordsUniqMapper.selectFinAccMasterRecordsUniqById(id);
    }

    /**
     * 查询账套列表
     * 
     * @param finAccMasterRecordsUniq 账套
     * @return 账套
     */
    @Override
    public List<FinAccMasterRecordsUniq> selectFinAccMasterRecordsUniqList(FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        return finAccMasterRecordsUniqMapper.selectFinAccMasterRecordsUniqList(finAccMasterRecordsUniq);
    }

    /**
     * 查询账套列表
     *
     * @param finAccMasterRecordsUniq 账套
     * @return 账套
     */
    @Override
    public List<FinAccMasterRecordsUniq> selectFinAccMasterRecordsById(FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        return finAccMasterRecordsUniqMapper.selectFinAccMasterRecordsById(finAccMasterRecordsUniq);
    }

    /**
     * 新增账套
     * 
     * @param finAccMasterRecordsUniq 账套
     * @return 结果
     */
    @Override
    public int insertFinAccMasterRecordsUniq(FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        return finAccMasterRecordsUniqMapper.insertFinAccMasterRecordsUniq(finAccMasterRecordsUniq);
    }

    /**
     * 修改账套
     * 
     * @param finAccMasterRecordsUniq 账套
     * @return 结果
     */
    @Override
    public int updateFinAccMasterRecordsUniq(FinAccMasterRecordsUniq finAccMasterRecordsUniq)
    {
        return finAccMasterRecordsUniqMapper.updateFinAccMasterRecordsUniq(finAccMasterRecordsUniq);
    }

    /**
     * 批量删除账套
     * 
     * @param ids 需要删除的账套主键
     * @return 结果
     */
    @Override
    public int deleteFinAccMasterRecordsUniqByIds(String[] ids)
    {
        return finAccMasterRecordsUniqMapper.deleteFinAccMasterRecordsUniqByIds(ids);
    }

    /**
     * 删除账套信息
     * 
     * @param id 账套主键
     * @return 结果
     */
    @Override
    public int deleteFinAccMasterRecordsUniqById(String id)
    {
        return finAccMasterRecordsUniqMapper.deleteFinAccMasterRecordsUniqById(id);
    }

    /**
     * 获取账套ID
     * @param zti
     * @param month
     * @return
     */
    public String getFaacId(String zti,String month){
        FinAccMasterRecordsUniq finAccMasterRecordsUniq = new FinAccMasterRecordsUniq();
        AssetOwnerMaster assetOwnerMaster = AssetOwner.byCode(zti);
        finAccMasterRecordsUniq.setFamrEntity(assetOwnerMaster.getName());
        finAccMasterRecordsUniq.setFamrProcess(month);
        List<FinAccMasterRecordsUniq> finAccMasterRecordsUniqs;
        if (Arrays.asList("BM10000050","BM10000057").contains(zti)){
            finAccMasterRecordsUniq.setFamrEntity(zti);
            finAccMasterRecordsUniqs = selectFinAccMasterRecordsById(finAccMasterRecordsUniq);
        }else {
            finAccMasterRecordsUniqs = selectFinAccMasterRecordsUniqList(finAccMasterRecordsUniq);
        }
        finAccMasterRecordsUniq = finAccMasterRecordsUniqs.get(0);
        return finAccMasterRecordsUniq.getId();
    }
}
