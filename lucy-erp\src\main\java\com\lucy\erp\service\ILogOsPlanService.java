package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogOsPlan;

/**
 * 物流_出库_计划
Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface ILogOsPlanService 
{
    /**
     * 查询物流_出库_计划

     * 
     * @param id 物流_出库_计划
主键
     * @return 物流_出库_计划

     */
    public LogOsPlan selectLogOsPlanById(String id);

    /**
     * 查询物流_出库_计划
列表
     * 
     * @param logOsPlan 物流_出库_计划

     * @return 物流_出库_计划
集合
     */
    public List<LogOsPlan> selectLogOsPlanList(LogOsPlan logOsPlan);

    /**
     * 新增物流_出库_计划

     * 
     * @param logOsPlan 物流_出库_计划

     * @return 结果
     */
    public int insertLogOsPlan(LogOsPlan logOsPlan);

    /**
     * 修改物流_出库_计划

     * 
     * @param logOsPlan 物流_出库_计划

     * @return 结果
     */
    public int updateLogOsPlan(LogOsPlan logOsPlan);

    /**
     * 批量删除物流_出库_计划

     * 
     * @param ids 需要删除的物流_出库_计划
主键集合
     * @return 结果
     */
    public int deleteLogOsPlanByIds(Long[] ids);

    /**
     * 删除物流_出库_计划
信息
     * 
     * @param id 物流_出库_计划
主键
     * @return 结果
     */
    public int deleteLogOsPlanById(Long id);
}
