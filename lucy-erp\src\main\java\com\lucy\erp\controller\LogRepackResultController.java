package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogRepackResult;
import com.lucy.erp.service.ILogRepackResultService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产成品Controller
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/log/result")
public class LogRepackResultController extends BaseController
{
    @Autowired
    private ILogRepackResultService logRepackResultService;

    /**
     * 查询产成品列表
     */
    @PreAuthorize("@ss.hasPermi('log:result:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogRepackResult logRepackResult)
    {
        startPage();
        List<LogRepackResult> list = logRepackResultService.selectLogRepackResultList(logRepackResult);
        return getDataTable(list);
    }

    /**
     * 导出产成品列表
     */
    @PreAuthorize("@ss.hasPermi('log:result:export')")
    @Log(title = "产成品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogRepackResult logRepackResult)
    {
        List<LogRepackResult> list = logRepackResultService.selectLogRepackResultList(logRepackResult);
        ExcelUtil<LogRepackResult> util = new ExcelUtil<LogRepackResult>(LogRepackResult.class);
        util.exportExcel(response, list, "产成品数据");
    }

    /**
     * 获取产成品详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:result:query')")
    @GetMapping(value = "/{lrrCd}")
    public AjaxResult getInfo(@PathVariable("lrrCd") String lrrCd)
    {
        return success(logRepackResultService.selectLogRepackResultByLrrCd(lrrCd));
    }

    /**
     * 新增产成品
     */
    @PreAuthorize("@ss.hasPermi('log:result:add')")
    @Log(title = "产成品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogRepackResult logRepackResult)
    {
        return toAjax(logRepackResultService.insertLogRepackResult(logRepackResult));
    }

    /**
     * 修改产成品
     */
    @PreAuthorize("@ss.hasPermi('log:result:edit')")
    @Log(title = "产成品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogRepackResult logRepackResult)
    {
        return toAjax(logRepackResultService.updateLogRepackResult(logRepackResult));
    }

    /**
     * 删除产成品
     */
    @PreAuthorize("@ss.hasPermi('log:result:remove')")
    @Log(title = "产成品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lrrCds}")
    public AjaxResult remove(@PathVariable String[] lrrCds)
    {
        return toAjax(logRepackResultService.deleteLogRepackResultByLrrCds(lrrCds));
    }
}
