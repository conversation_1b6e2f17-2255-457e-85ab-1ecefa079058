package com.lucy.inventoryoe.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.inventoryoe.mapper.LogInventoryList2025010101Mapper;
import com.lucy.inventoryoe.domain.LogInventoryList2025010101;
import com.lucy.inventoryoe.service.ILogInventoryList2025010101Service;

/**
 * 库存清单快照Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class LogInventoryList2025010101ServiceImpl implements ILogInventoryList2025010101Service 
{
    @Autowired
    private LogInventoryList2025010101Mapper logInventoryList2025010101Mapper;

    /**
     * 查询库存清单快照
     * 
     * @param id 库存清单快照主键
     * @return 库存清单快照
     */
    @Override
    public LogInventoryList2025010101 selectLogInventoryList2025010101ById(Long id)
    {
        return logInventoryList2025010101Mapper.selectLogInventoryList2025010101ById(id);
    }

    /**
     * 查询库存清单快照列表
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 库存清单快照
     */
    @Override
    public List<LogInventoryList2025010101> selectLogInventoryList2025010101List(LogInventoryList2025010101 logInventoryList2025010101)
    {
        return logInventoryList2025010101Mapper.selectLogInventoryList2025010101List(logInventoryList2025010101);
    }

    /**
     * 新增库存清单快照
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 结果
     */
    @Override
    public int insertLogInventoryList2025010101(LogInventoryList2025010101 logInventoryList2025010101)
    {
        return logInventoryList2025010101Mapper.insertLogInventoryList2025010101(logInventoryList2025010101);
    }

    /**
     * 修改库存清单快照
     * 
     * @param logInventoryList2025010101 库存清单快照
     * @return 结果
     */
    @Override
    public int updateLogInventoryList2025010101(LogInventoryList2025010101 logInventoryList2025010101)
    {
        return logInventoryList2025010101Mapper.updateLogInventoryList2025010101(logInventoryList2025010101);
    }

    /**
     * 批量删除库存清单快照
     * 
     * @param ids 需要删除的库存清单快照主键
     * @return 结果
     */
    @Override
    public int deleteLogInventoryList2025010101ByIds(Long[] ids)
    {
        return logInventoryList2025010101Mapper.deleteLogInventoryList2025010101ByIds(ids);
    }

    /**
     * 删除库存清单快照信息
     * 
     * @param id 库存清单快照主键
     * @return 结果
     */
    @Override
    public int deleteLogInventoryList2025010101ById(Long id)
    {
        return logInventoryList2025010101Mapper.deleteLogInventoryList2025010101ById(id);
    }
}
