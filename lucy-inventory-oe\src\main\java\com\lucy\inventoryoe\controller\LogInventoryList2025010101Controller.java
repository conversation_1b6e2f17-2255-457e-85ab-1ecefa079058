package com.lucy.inventoryoe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.inventoryoe.domain.LogInventoryList2025010101;
import com.lucy.inventoryoe.service.ILogInventoryList2025010101Service;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 库存清单快照Controller
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@RestController
@RequestMapping("/lucy-inventoryoe/***********")
public class LogInventoryList2025010101Controller extends BaseController
{
    @Autowired
    private ILogInventoryList2025010101Service logInventoryList2025010101Service;

    /**
     * 查询库存清单快照列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:***********:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogInventoryList2025010101 logInventoryList2025010101)
    {
        startPage();
        List<LogInventoryList2025010101> list = logInventoryList2025010101Service.selectLogInventoryList2025010101List(logInventoryList2025010101);
        return getDataTable(list);
    }

    /**
     * 导出库存清单快照列表
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:***********:export')")
    @Log(title = "库存清单快照", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogInventoryList2025010101 logInventoryList2025010101)
    {
        List<LogInventoryList2025010101> list = logInventoryList2025010101Service.selectLogInventoryList2025010101List(logInventoryList2025010101);
        ExcelUtil<LogInventoryList2025010101> util = new ExcelUtil<LogInventoryList2025010101>(LogInventoryList2025010101.class);
        util.exportExcel(response, list, "库存清单快照数据");
    }

    /**
     * 获取库存清单快照详细信息
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:***********:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(logInventoryList2025010101Service.selectLogInventoryList2025010101ById(id));
    }

    /**
     * 新增库存清单快照
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:***********:add')")
    @Log(title = "库存清单快照", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogInventoryList2025010101 logInventoryList2025010101)
    {
        return toAjax(logInventoryList2025010101Service.insertLogInventoryList2025010101(logInventoryList2025010101));
    }

    /**
     * 修改库存清单快照
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:***********:edit')")
    @Log(title = "库存清单快照", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogInventoryList2025010101 logInventoryList2025010101)
    {
        return toAjax(logInventoryList2025010101Service.updateLogInventoryList2025010101(logInventoryList2025010101));
    }

    /**
     * 删除库存清单快照
     */
    @PreAuthorize("@ss.hasPermi('lucy-inventoryoe:***********:remove')")
    @Log(title = "库存清单快照", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(logInventoryList2025010101Service.deleteLogInventoryList2025010101ByIds(ids));
    }
}
