package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogRadDeveloProduct;
import com.lucy.erp.service.ILogRadDeveloProductService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小样或者产成品结果Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/system/product")
public class LogRadDeveloProductController extends BaseController
{
    @Autowired
    private ILogRadDeveloProductService logRadDeveloProductService;

    /**
     * 查询小样或者产成品结果列表
     */
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogRadDeveloProduct logRadDeveloProduct)
    {
        startPage();
        List<LogRadDeveloProduct> list = logRadDeveloProductService.selectLogRadDeveloProductList(logRadDeveloProduct);
        return getDataTable(list);
    }

    /**
     * 导出小样或者产成品结果列表
     */
    @PreAuthorize("@ss.hasPermi('system:product:export')")
    @Log(title = "小样或者产成品结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogRadDeveloProduct logRadDeveloProduct)
    {
        List<LogRadDeveloProduct> list = logRadDeveloProductService.selectLogRadDeveloProductList(logRadDeveloProduct);
        ExcelUtil<LogRadDeveloProduct> util = new ExcelUtil<LogRadDeveloProduct>(LogRadDeveloProduct.class);
        util.exportExcel(response, list, "小样或者产成品结果数据");
    }

    /**
     * 获取小样或者产成品结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:product:query')")
    @GetMapping(value = "/{lrdpCd}")
    public AjaxResult getInfo(@PathVariable("lrdpCd") String lrdpCd)
    {
        return success(logRadDeveloProductService.selectLogRadDeveloProductByLrdpCd(lrdpCd));
    }

    /**
     * 新增小样或者产成品结果
     */
    @PreAuthorize("@ss.hasPermi('system:product:add')")
    @Log(title = "小样或者产成品结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogRadDeveloProduct logRadDeveloProduct)
    {
        return toAjax(logRadDeveloProductService.insertLogRadDeveloProduct(logRadDeveloProduct));
    }

    /**
     * 修改小样或者产成品结果
     */
    @PreAuthorize("@ss.hasPermi('system:product:edit')")
    @Log(title = "小样或者产成品结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogRadDeveloProduct logRadDeveloProduct)
    {
        return toAjax(logRadDeveloProductService.updateLogRadDeveloProduct(logRadDeveloProduct));
    }

    /**
     * 删除小样或者产成品结果
     */
    @PreAuthorize("@ss.hasPermi('system:product:remove')")
    @Log(title = "小样或者产成品结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lrdpCds}")
    public AjaxResult remove(@PathVariable String[] lrdpCds)
    {
        return toAjax(logRadDeveloProductService.deleteLogRadDeveloProductByLrdpCds(lrdpCds));
    }
}
