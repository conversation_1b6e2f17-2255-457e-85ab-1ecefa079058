package com.lucy.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.customer.domain.DepartmentGroup;
import com.lucy.customer.service.IDepartmentGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 部门组织结构 Department groupsController
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@RequestMapping("/customer/department")
public class DepartmentGroupController extends BaseController
{
    @Autowired
    private IDepartmentGroupService departmentGroupService;

    /**
     * 查询部门组织结构 Department groups列表
     */
    @PreAuthorize("@ss.hasPermi('customer:department:list')")
    @GetMapping("/list")
    public TableDataInfo list(DepartmentGroup departmentGroup)
    {
        startPage();
        List<DepartmentGroup> list = departmentGroupService.selectDepartmentGroupList(departmentGroup);
        return getDataTable(list);
    }

    /**
     * 导出部门组织结构 Department groups列表
     */
    @PreAuthorize("@ss.hasPermi('customer:department:export')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DepartmentGroup departmentGroup)
    {
        List<DepartmentGroup> list = departmentGroupService.selectDepartmentGroupList(departmentGroup);
        ExcelUtil<DepartmentGroup> util = new ExcelUtil<DepartmentGroup>(DepartmentGroup.class);
        util.exportExcel(response, list, "部门组织结构 Department groups数据");
    }

    /**
     * 获取部门组织结构 Department groups详细信息
     */
    @PreAuthorize("@ss.hasPermi('customer:department:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(departmentGroupService.selectDepartmentGroupByCode(code));
    }

    /**
     * 新增部门组织结构 Department groups
     */
    @PreAuthorize("@ss.hasPermi('customer:department:add')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DepartmentGroup departmentGroup)
    {
        return toAjax(departmentGroupService.insertDepartmentGroup(departmentGroup));
    }

    /**
     * 修改部门组织结构 Department groups
     */
    @PreAuthorize("@ss.hasPermi('customer:department:edit')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DepartmentGroup departmentGroup)
    {
        return toAjax(departmentGroupService.updateDepartmentGroup(departmentGroup));
    }

    /**
     * 删除部门组织结构 Department groups
     */
    @PreAuthorize("@ss.hasPermi('customer:department:remove')")
    @Log(title = "部门组织结构 Department groups", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(departmentGroupService.deleteDepartmentGroupByCodes(codes));
    }
}
