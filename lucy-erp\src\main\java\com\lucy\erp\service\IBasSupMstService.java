package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.BasSupMst;

/**
 * 基础_供应商Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface IBasSupMstService 
{
    /**
     * 查询基础_供应商
     * 
     * @param bsmCcd 基础_供应商主键
     * @return 基础_供应商
     */
    public BasSupMst selectBasSupMstByBsmCcd(String bsmCcd);

    /**
     * 查询基础_供应商
     *
     * @param bsmCd 基础_供应商CD
     * @return 基础_供应商
     */
    public BasSupMst selectBasSupMstByBsmCd(String bsmCd);

    /**
     * 查询基础_供应商列表
     * 
     * @param basSupMst 基础_供应商
     * @return 基础_供应商集合
     */
    public List<BasSupMst> selectBasSupMstList(BasSupMst basSupMst);

    /**
     * 新增基础_供应商
     * 
     * @param basSupMst 基础_供应商
     * @return 结果
     */
    public int insertBasSupMst(BasSupMst basSupMst);

    /**
     * 修改基础_供应商
     * 
     * @param basSupMst 基础_供应商
     * @return 结果
     */
    public int updateBasSupMst(BasSupMst basSupMst);

    /**
     * 批量删除基础_供应商
     * 
     * @param bsmCcds 需要删除的基础_供应商主键集合
     * @return 结果
     */
    public int deleteBasSupMstByBsmCcds(String[] bsmCcds);

    /**
     * 删除基础_供应商信息
     * 
     * @param bsmCcd 基础_供应商主键
     * @return 结果
     */
    public int deleteBasSupMstByBsmCcd(String bsmCcd);
}
