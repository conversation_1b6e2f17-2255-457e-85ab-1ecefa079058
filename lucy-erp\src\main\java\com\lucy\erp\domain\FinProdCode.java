package com.lucy.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 记账科目对象 fin_prod_code
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class FinProdCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bpmCnm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bpmCcd;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bpmSortnm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bpmSort;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String code;

    /** 财务科目 */
    @Excel(name = "财务科目")
    private String lgcode;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBpmCnm(String bpmCnm) 
    {
        this.bpmCnm = bpmCnm;
    }

    public String getBpmCnm() 
    {
        return bpmCnm;
    }

    public void setBpmCcd(String bpmCcd) 
    {
        this.bpmCcd = bpmCcd;
    }

    public String getBpmCcd() 
    {
        return bpmCcd;
    }

    public void setBpmSortnm(String bpmSortnm) 
    {
        this.bpmSortnm = bpmSortnm;
    }

    public String getBpmSortnm() 
    {
        return bpmSortnm;
    }

    public void setBpmSort(String bpmSort) 
    {
        this.bpmSort = bpmSort;
    }

    public String getBpmSort() 
    {
        return bpmSort;
    }

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setLgcode(String lgcode) 
    {
        this.lgcode = lgcode;
    }

    public String getLgcode() 
    {
        return lgcode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bpmCnm", getBpmCnm())
            .append("bpmCcd", getBpmCcd())
            .append("bpmSortnm", getBpmSortnm())
            .append("bpmSort", getBpmSort())
            .append("code", getCode())
            .append("lgcode", getLgcode())
            .toString();
    }
}
