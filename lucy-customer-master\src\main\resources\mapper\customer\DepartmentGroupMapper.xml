<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.customer.mapper.DepartmentGroupMapper">
    
    <resultMap type="DepartmentGroup" id="DepartmentGroupResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="manager"    column="manager"    />
        <result property="costCenter"    column="cost_center"    />
    </resultMap>

    <sql id="selectDepartmentGroupVo">
        select code, name, manager, cost_center from department_group
    </sql>

    <select id="selectDepartmentGroupList" parameterType="DepartmentGroup" resultMap="DepartmentGroupResult">
        <include refid="selectDepartmentGroupVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="manager != null  and manager != ''"> and manager = #{manager}</if>
            <if test="costCenter != null  and costCenter != ''"> and cost_center = #{costCenter}</if>
        </where>
    </select>
    
    <select id="selectDepartmentGroupByCode" parameterType="String" resultMap="DepartmentGroupResult">
        <include refid="selectDepartmentGroupVo"/>
        where code = #{code}
    </select>

    <insert id="insertDepartmentGroup" parameterType="DepartmentGroup">
        insert into department_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="manager != null">manager,</if>
            <if test="costCenter != null">cost_center,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="manager != null">#{manager},</if>
            <if test="costCenter != null">#{costCenter},</if>
         </trim>
    </insert>

    <update id="updateDepartmentGroup" parameterType="DepartmentGroup">
        update department_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="manager != null and manager != ''">manager = #{manager},</if>
            <if test="costCenter != null">cost_center = #{costCenter},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteDepartmentGroupByCode" parameterType="String">
        delete from department_group where code = #{code}
    </delete>

    <delete id="deleteDepartmentGroupByCodes" parameterType="String">
        delete from department_group where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>