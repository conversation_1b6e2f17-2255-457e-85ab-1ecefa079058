package com.ruoyi.repackage.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.repackage.domain.ConversionDependencyCycle;
import com.ruoyi.repackage.domain.ConversionMaterialDependencyClosure;
import com.ruoyi.repackage.domain.ConversionRule;
import com.ruoyi.repackage.domain.ConversionRuleDetail;
import com.ruoyi.repackage.mapper.ConversionDependencyCycleMapper;
import com.ruoyi.repackage.mapper.ConversionMaterialDependencyClosureMapper;
import com.ruoyi.repackage.mapper.ConversionRuleDetailMapper;
import com.ruoyi.repackage.mapper.ConversionRuleMapper;
import com.ruoyi.repackage.service.IConversionDependencyGraphService;

@Service
public class ConversionDependencyGraphServiceImpl implements IConversionDependencyGraphService {

    private static final Logger log = LoggerFactory.getLogger(ConversionDependencyGraphServiceImpl.class);

    @Autowired
    private ConversionRuleMapper conversionRuleMapper;

    @Autowired
    private ConversionRuleDetailMapper conversionRuleDetailMapper;

    @Autowired
    private ConversionMaterialDependencyClosureMapper closureMapper;

    @Autowired
    private ConversionDependencyCycleMapper cycleMapper;

    /**
     * 构建物料依赖图谱
     * 1. 清空现有依赖图谱
     * 2. 从规则表提取直接依赖关系
     * 3. 使用DFS遍历构建传递闭包(支持环状依赖)
     * 4. 自动检测环状依赖并标记最佳断开点
     */
    @Override
    @Transactional
    public void buildDependencyGraph(String assetOwner) {
        // 1. 清空现有依赖图谱
        clearExistingDependencyGraph();

        // 2. 提取直接依赖关系
        List<ConversionMaterialDependencyClosure> directDependencies = extractDirectDependencies(assetOwner);

        // 3. 构建传递闭包
        List<ConversionMaterialDependencyClosure> closure = buildTransitiveClosure(directDependencies);

        // 4. 保存依赖图谱到数据库
        saveDependencyGraph(closure);
    }

    /**
     * 清空现有依赖图谱
     */
    private void clearExistingDependencyGraph() {
        closureMapper.deleteAllConversionMaterialDependencyClosure();
        cycleMapper.deleteAllConversionDependencyCycle();
        conversionRuleMapper.resetCycleFlagForActiveRules();
    }

    /**
     * 从规则表提取直接依赖关系
     */
    private List<ConversionMaterialDependencyClosure> extractDirectDependencies(String assetOwner) {
        List<ConversionMaterialDependencyClosure> directDeps = new ArrayList<>();

        // 查询所有活跃的规则
        ConversionRule ruleQuery = new ConversionRule();
        ruleQuery.setIsActive(1);
        ruleQuery.setAssetOwner(assetOwner);
        List<ConversionRule> activeRules = conversionRuleMapper.selectConversionRuleList(ruleQuery);

        for (ConversionRule rule : activeRules) {
            // 查询规则的输入和输出物料
            ConversionRuleDetail detailQuery = new ConversionRuleDetail();
            detailQuery.setRuleId(rule.getRuleId());
            List<ConversionRuleDetail> details = conversionRuleDetailMapper.selectConversionRuleDetailList(detailQuery);

            // 分离输入和输出物料
            List<ConversionRuleDetail> inputs = details.stream()
                    .filter(d -> "INPUT".equals(d.getRole()))
                    .collect(Collectors.toList());

            List<ConversionRuleDetail> outputs = details.stream()
                    .filter(d -> "OUTPUT".equals(d.getRole()))
                    .collect(Collectors.toList());

            if (!inputs.isEmpty() && !outputs.isEmpty()) {
                String ancestor = inputs.get(0).getMaterialSku();
                String descendant = outputs.get(0).getMaterialSku();

                // 创建直接依赖关系
                directDeps.add(ConversionMaterialDependencyClosure.createDirectDependency(ancestor, descendant));
            }
        }

        return directDeps;
    }

    /**
     * 使用DFS遍历构建传递闭包
     */
    private List<ConversionMaterialDependencyClosure> buildTransitiveClosure(List<ConversionMaterialDependencyClosure> directDeps) {
        // 1. 先检测环（第一次DFS遍历）
        List<ConversionDependencyCycle> detectedCycles = detectCyclesOnly(directDeps);

        // 2. 记录检测到的环
        for (ConversionDependencyCycle cycle : detectedCycles) {
            recordDetectedCycle(cycle);
        }

        // 3. 建立环状节点集合
        Set<String> cyclicNodes = buildCyclicNodesSet(detectedCycles);

        // 4. 标记涉及环状节点的规则
        markCyclicRules(directDeps, cyclicNodes, detectedCycles);

        // 5. 根据环断开建议移除环状边
        List<ConversionMaterialDependencyClosure> acyclicDeps = removeBreakEdges(directDeps, detectedCycles);

        // 6. 基于无环图构建传递闭包
        List<ConversionMaterialDependencyClosure> closureRelations = buildClosureFromAcyclicGraph(acyclicDeps);

        log.info("传递闭包构建完成，共{}条关系，检测到{}个环",
            closureRelations.size(), detectedCycles.size());

        return closureRelations;
    }


    /**
     * 仅用于检测环，不构建传递闭包
     * 修复版本：正确处理复杂环形依赖，包括复合环和跨环连接
     */
    private List<ConversionDependencyCycle> detectCyclesOnly(List<ConversionMaterialDependencyClosure> directDeps) {
        // 转换为邻接表格式
        Map<String, Set<String>> adjacencyList = convertToAdjacencyList(directDeps);
        // 获取所有节点
        Set<String> allNodes = getAllNodesFromGraph(adjacencyList);
        // 存储检测到的环
        List<ConversionDependencyCycle> detectedCycles = new ArrayList<>();
        // 全局访问记录，避免重复遍历已处理的环
        Set<String> globalVisited = new HashSet<>();

        for (String startNode : allNodes) {
            // 跳过已经在其他环中处理过的节点
            if (globalVisited.contains(startNode)) {
                continue;
            }

            // 每次DFS使用独立的状态
            Set<String> visited = new HashSet<>();
            Set<String> recursionStack = new HashSet<>();
            List<String> currentPath = new ArrayList<>();

            dfsForCycleDetectionOnly(startNode, adjacencyList, visited, recursionStack,
                    currentPath, detectedCycles, globalVisited, 0);
        }

        return detectedCycles;
    }

    /**
     * 仅用于环检测的DFS，不记录传递闭包
     * 修复版本：正确管理访问状态，支持复杂环形结构检测
     */
    private void dfsForCycleDetectionOnly(String currentNode, Map<String, Set<String>> adjacencyList,
                                        Set<String> visited, Set<String> recursionStack,
                                        List<String> currentPath, List<ConversionDependencyCycle> detectedCycles,
                                        Set<String> globalVisited, int currentDepth) {
        // 防止无限递归
        if (currentDepth > 100) {
            log.warn("DFS深度超过限制，当前路径: {}", String.join(" -> ", currentPath));
            return;
        }

        // 标记当前节点已访问
        visited.add(currentNode);
        recursionStack.add(currentNode);
        currentPath.add(currentNode);
        globalVisited.add(currentNode);

        // 遍历邻居
        Set<String> neighbors = adjacencyList.getOrDefault(currentNode, Collections.emptySet());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                // 递归访问邻居
                dfsForCycleDetectionOnly(neighbor, adjacencyList, visited, recursionStack,
                                       currentPath, detectedCycles, globalVisited, currentDepth + 1);
            } else if (recursionStack.contains(neighbor)) {
                // 检测到环 - 使用改进的环检测逻辑
                handleCycleDetection(neighbor, currentPath, detectedCycles);
            }
        }

        // 回溯
        recursionStack.remove(currentNode);
        currentPath.remove(currentPath.size() - 1);
    }

    /**
     * 根据环断开建议移除环状边
     */
    private List<ConversionMaterialDependencyClosure> removeBreakEdges(
            List<ConversionMaterialDependencyClosure> directDeps,
            List<ConversionDependencyCycle> cycles) {

        Set<String> edgesToRemove = new HashSet<>();

        for (ConversionDependencyCycle cycle : cycles) {
            String resolvedMethod = cycle.getResolvedMethod();

            // 1. 移除进入环的边
            if (resolvedMethod != null && resolvedMethod.startsWith("断开子环:")) {
                String breakEdge = resolvedMethod.split(":")[1];
                edgesToRemove.add(breakEdge);
            }

            // 2. 移除环内所有边
            Set<String> cycleEdges = extractCycleEdges(cycle.getCyclePath());
            edgesToRemove.addAll(cycleEdges);
        }

        return directDeps.stream()
                .filter(dep -> {
                    String edge = dep.getAncestorSku() + "->" + dep.getDescendantSku();
                    return !edgesToRemove.contains(edge);
                })
                .collect(Collectors.toList());
    }

    /**
     * 从环路径中提取所有边
     * 例如：D -> E -> D 提取出 [D->E, E->D]
     */
    private Set<String> extractCycleEdges(String cyclePath) {
        Set<String> edges = new HashSet<>();

        if (cyclePath == null || cyclePath.trim().isEmpty()) {
            return edges;
        }

        String[] nodes = cyclePath.split(" -> ");
        if (nodes.length < 2) {
            return edges;
        }

        // 提取环中的所有边
        for (int i = 0; i < nodes.length - 1; i++) {
            String from = nodes[i].trim();
            String to = nodes[i + 1].trim();
            edges.add(from + "->" + to);
        }

        return edges;
    }

    /**
     * 基于无环图构建传递闭包
     */
    private List<ConversionMaterialDependencyClosure> buildClosureFromAcyclicGraph(
            List<ConversionMaterialDependencyClosure> acyclicDeps) {

        Map<String, Set<String>> adjacencyList = convertToAdjacencyList(acyclicDeps);
        Set<String> allNodes = getAllNodesFromGraph(adjacencyList);
        Map<String, Map<String, ConversionMaterialDependencyClosure>> closureMap = new HashMap<>();

        for (String startNode : allNodes) {
            Set<String> visited = new HashSet<>();
            List<String> currentPath = new ArrayList<>();

            dfsForAcyclicClosure(startNode, startNode, adjacencyList, acyclicDeps,
                               closureMap, visited, currentPath, 0);
        }

        // 转换为列表格式
        List<ConversionMaterialDependencyClosure> closureRelations = new ArrayList<>();
        for (Map<String, ConversionMaterialDependencyClosure> ancestorMap : closureMap.values()) {
            closureRelations.addAll(ancestorMap.values());
        }

        // 添加自引用记录
        for (String node : allNodes) {
            closureRelations.add(ConversionMaterialDependencyClosure.createSelfReference(node));
        }

        return closureRelations;
    }

    /**
     * 无环图的DFS遍历
     */
    private void dfsForAcyclicClosure(String originalStart, String currentNode,
                                    Map<String, Set<String>> adjacencyList,
                                    List<ConversionMaterialDependencyClosure> directDeps,
                                    Map<String, Map<String, ConversionMaterialDependencyClosure>> closureMap,
                                    Set<String> visited, List<String> currentPath, int currentDepth) {

        if (currentDepth > 100) {
            log.warn("DFS深度超过限制: {}", String.join(" -> ", currentPath));
            return;
        }

        visited.add(currentNode);
        currentPath.add(currentNode);

        // 记录传递闭包关系
        if (!currentNode.equals(originalStart)) {
            recordTransitiveRelation(originalStart, currentNode, currentPath, directDeps, closureMap);
        }

        // 遍历邻居
        Set<String> neighbors = adjacencyList.getOrDefault(currentNode, Collections.emptySet());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                dfsForAcyclicClosure(originalStart, neighbor, adjacencyList, directDeps,
                                   closureMap, visited, currentPath, currentDepth + 1);
            }
        }

        currentPath.remove(currentPath.size() - 1);
    }

    /**
     * 记录传递闭包关系
     */
    private void recordTransitiveRelation(String ancestor, String descendant, List<String> path,
                                        List<ConversionMaterialDependencyClosure> directDeps,
                                        Map<String, Map<String, ConversionMaterialDependencyClosure>> closureMap) {

        // 构建传递闭包关系
        ConversionMaterialDependencyClosure relation = buildTransitiveRelation(ancestor, descendant, path, directDeps);

        if (relation != null) {
            Map<String, ConversionMaterialDependencyClosure> ancestorMap =
                closureMap.computeIfAbsent(ancestor, k -> new HashMap<>());

            // 如果关系已存在，合并路径信息
            if (ancestorMap.containsKey(descendant)) {
                ConversionMaterialDependencyClosure existing = ancestorMap.get(descendant);
                // 简化合并：只更新最大深度
                existing.setMaxDepth(Math.max(
                    existing.getMaxDepth() != null ? existing.getMaxDepth() : 0,
                    relation.getMaxDepth() != null ? relation.getMaxDepth() : 0));
            } else {
                ancestorMap.put(descendant, relation);
            }
        }
    }

    /**
     * 构建传递闭包关系对象
     */
    private ConversionMaterialDependencyClosure buildTransitiveRelation(String ancestor, String descendant,
                                                                       List<String> path,
                                                                       List<ConversionMaterialDependencyClosure> directDeps) {

        // 计算路径深度(排除起始节点)
        int depth = path.size() - 1;

        // 查找路径中的直接依赖关系
        List<ConversionMaterialDependencyClosure> pathDependencies = new ArrayList<>();
        for (int i = 0; i < path.size() - 1; i++) {
            String from = path.get(i);
            String to = path.get(i + 1);

            ConversionMaterialDependencyClosure directDep = findDirectPath(directDeps, from, to);
            if (directDep != null) {
                pathDependencies.add(directDep);
            }
        }

        if (pathDependencies.isEmpty()) {
            return null;
        }

        // 通过组合路径依赖构建传递闭包关系
        ConversionMaterialDependencyClosure result = new ConversionMaterialDependencyClosure();
        result.setAncestorSku(ancestor);
        result.setDescendantSku(descendant);

        // 计算组合深度
        int totalDepth = pathDependencies.stream()
                .mapToInt(dep -> dep.getMaxDepth() != null ? dep.getMaxDepth() : 1)
                .sum();
        result.setMaxDepth(totalDepth);

        return result;
    }

    /**
     * 处理环检测
     */
    private void handleCycleDetection(String cycleStartNode, List<String> currentPath,
                                            List<ConversionDependencyCycle> detectedCycles) {

        // 找到环的起始位置
        int cycleStartIndex = currentPath.indexOf(cycleStartNode);
        if (cycleStartIndex == -1) {
            return;
        }

        // 构建环路径
        List<String> cyclePath = new ArrayList<>(currentPath.subList(cycleStartIndex, currentPath.size()));
        cyclePath.add(cycleStartNode); // 闭合环

        // 创建环状依赖对象
        ConversionDependencyCycle cycle = createCycleFromPath(cyclePath);

        // 处理子环断开
        if (cycleStartIndex > 0) {
            // 找到进入子环的边
            String breakFrom = currentPath.get(cycleStartIndex - 1);  // 子环前的节点
            String breakTo = currentPath.get(cycleStartIndex);        // 子环起始节点

            // 设置断开信息
            cycle.setResolvedMethod(String.format("断开子环:%s->%s", breakFrom, breakTo));
        }

        // 检查是否为重复环
        if (!isDuplicateCycle(cycle, detectedCycles)) {
            detectedCycles.add(cycle);
            log.info("检测到环状依赖: {}", String.join(" -> ", cyclePath));
        }
    }

    /**
     * 构建环状节点集合
     * 收集所有参与环状依赖的节点
     */
    private Set<String> buildCyclicNodesSet(List<ConversionDependencyCycle> detectedCycles) {
        Set<String> cyclicNodes = new HashSet<>();

        for (ConversionDependencyCycle cycle : detectedCycles) {
            String cyclePath = cycle.getCyclePath();
            if (cyclePath != null && !cyclePath.trim().isEmpty()) {
                // 解析环路径，提取所有节点
                String[] nodes = cyclePath.split(" -> ");
                for (String node : nodes) {
                    String trimmedNode = node.trim();
                    if (!trimmedNode.isEmpty()) {
                        cyclicNodes.add(trimmedNode);
                    }
                }
            }
        }

        return cyclicNodes;
    }

    /**
     * 标记涉及环状节点的规则
     * 环状节点向外的依赖需要标记cycle_flag=1，环内的依赖不标记
     */
    private void markCyclicRules(List<ConversionMaterialDependencyClosure> directDeps,
                               Set<String> cyclicNodes,
                               List<ConversionDependencyCycle> detectedCycles) {

        // 1. 收集所有环内的边（这些边不需要标记cycle_flag）
        Set<String> cyclicEdges = new HashSet<>();
        // 2. 收集所有被断开的边（这些边也不需要标记cycle_flag，因为会被移除）
        Set<String> brokenEdges = new HashSet<>();

        for (ConversionDependencyCycle cycle : detectedCycles) {
            // 收集环内边
            Set<String> edges = extractCycleEdges(cycle.getCyclePath());
            cyclicEdges.addAll(edges);

            // 收集被断开的边
            String resolvedMethod = cycle.getResolvedMethod();
            if (resolvedMethod != null && resolvedMethod.startsWith("断开子环:")) {
                String breakEdge = resolvedMethod.split(":")[1];
                brokenEdges.add(breakEdge);
            }
        }

        // 3. 遍历所有直接依赖，标记需要cycle_flag的规则
        for (ConversionMaterialDependencyClosure dep : directDeps) {
            String fromNode = dep.getAncestorSku();
            String toNode = dep.getDescendantSku();
            String edge = fromNode + "->" + toNode;

            // 环状节点向外的依赖（fromNode在环中，但这条边不是环内边且不是被断开的边）
            if (cyclicNodes.contains(fromNode) &&
                !cyclicEdges.contains(edge) &&
                !brokenEdges.contains(edge)) {
                // 更新对应的conversion_rule的cycle_flag
                updateRuleCycleFlag(fromNode, toNode);
            }
        }
    }

    /**
     * 更新conversion_rule的cycle_flag
     */
    private void updateRuleCycleFlag(String inputSku, String outputSku) {
        try {
            String ruleCode = inputSku + "__" + outputSku;
            // 查找对应的规则
            ConversionRule ruleQuery = new ConversionRule();
            ruleQuery.setIsActive(1);
            ruleQuery.setRuleCode(ruleCode);
            List<ConversionRule> activeRules = conversionRuleMapper.selectConversionRuleList(ruleQuery);

            for (ConversionRule rule : activeRules) {
                // 更新cycle_flag
                rule.setCycleFlag(1);
                conversionRuleMapper.updateConversionRule(rule);
            }
        } catch (Exception e) {
            log.error("更新规则cycle_flag失败: {} -> {}", inputSku, outputSku, e);
        }
    }


    /**
     * 转换为邻接表格式
     */
    private Map<String, Set<String>> convertToAdjacencyList(List<ConversionMaterialDependencyClosure> directDeps) {
        Map<String, Set<String>> adjacencyList = new HashMap<>();

        for (ConversionMaterialDependencyClosure dep : directDeps) {
            String from = dep.getAncestorSku();
            String to = dep.getDescendantSku();
            adjacencyList.computeIfAbsent(from, k -> new HashSet<>()).add(to);
        }

        return adjacencyList;
    }

    /**
     * 获取图中所有节点
     */
    private Set<String> getAllNodesFromGraph(Map<String, Set<String>> graph) {
        Set<String> allNodes = new HashSet<>();
        for (String from : graph.keySet()) {
            allNodes.add(from);
            allNodes.addAll(graph.get(from));
        }
        return allNodes;
    }

    /**
     * 查找直接路径
     */
    private ConversionMaterialDependencyClosure findDirectPath(List<ConversionMaterialDependencyClosure> directDeps,
                                                              String ancestor, String descendant) {
        return directDeps.stream()
                .filter(dep -> dep.getAncestorSku().equals(ancestor) && dep.getDescendantSku().equals(descendant))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查是否为重复环
     */
    private boolean isDuplicateCycle(ConversionDependencyCycle newCycle, List<ConversionDependencyCycle> existingCycles) {
        String newMaterialList = newCycle.getMaterialList();
        if (newMaterialList == null) {
            return false;
        }

        for (ConversionDependencyCycle existing : existingCycles) {
            String existingMaterialList = existing.getMaterialList();

            if (newMaterialList.equals(existingMaterialList)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从路径创建环状依赖对象
     */
    private ConversionDependencyCycle createCycleFromPath(List<String> cyclePath) {
        ConversionDependencyCycle cycle = new ConversionDependencyCycle();

        // 设置路径
        cycle.setCyclePath(String.join(" -> ", cyclePath));
        // 设置物料列表(去重)
        Set<String> materials = new LinkedHashSet<>(cyclePath);
        cycle.setMaterialList(JSONObject.toJSONString(materials));

        return cycle;
    }

    /**
     * 记录检测到的环状依赖
     */
    private void recordDetectedCycle(ConversionDependencyCycle cycle) {
        try {
            // 保存新的环状依赖记录
            cycleMapper.insertConversionDependencyCycle(cycle);
        } catch (Exception e) {
            log.error("记录环状依赖失败: {}", cycle.getCyclePath(), e);
        }
    }

    /**
     * 保存依赖图谱到数据库
     */
    private void saveDependencyGraph(List<ConversionMaterialDependencyClosure> closure) {
        // 批量插入记录
        if (!closure.isEmpty()) {
            int batchSize = 1000;
            for (int i = 0; i < closure.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, closure.size());
                List<ConversionMaterialDependencyClosure> batch = closure.subList(i, endIndex);
                closureMapper.batchInsertConversionMaterialDependencyClosure(batch);
            }
        }
    }
}
