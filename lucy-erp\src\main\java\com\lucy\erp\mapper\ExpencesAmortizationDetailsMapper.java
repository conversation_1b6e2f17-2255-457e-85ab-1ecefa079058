package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.ExpencesAmortizationDetails;

/**
 * 费用分摊明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface ExpencesAmortizationDetailsMapper 
{
    /**
     * 查询费用分摊明细
     * 
     * @param ead_pcd 费用分摊明细主键
     * @return 费用分摊明细
     */
    public ExpencesAmortizationDetails selectExpencesAmortizationDetailsByEcdPcd(String ead_pcd);

    /**
     * 查询费用分摊明细列表
     * 
     * @param expencesAmortizationDetails 费用分摊明细
     * @return 费用分摊明细集合
     */
    public List<ExpencesAmortizationDetails> selectExpencesAmortizationDetailsList(ExpencesAmortizationDetails expencesAmortizationDetails);

    /**
     * 新增费用分摊明细
     * 
     * @param expencesAmortizationDetails 费用分摊明细
     * @return 结果
     */
    public int insertExpencesAmortizationDetails(ExpencesAmortizationDetails expencesAmortizationDetails);

    /**
     * 修改费用分摊明细
     * 
     * @param expencesAmortizationDetails 费用分摊明细
     * @return 结果
     */
    public int updateExpencesAmortizationDetails(ExpencesAmortizationDetails expencesAmortizationDetails);

    /**
     * 删除费用分摊明细
     * 
     * @param id 费用分摊明细主键
     * @return 结果
     */
    public int deleteExpencesAmortizationDetailsById(Long id);

    /**
     * 批量删除费用分摊明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExpencesAmortizationDetailsByIds(Long[] ids);
}
