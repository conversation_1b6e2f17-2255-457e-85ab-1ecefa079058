<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogRepackResultMapper">
    
    <resultMap type="LogRepackResult" id="LogRepackResultResult">
        <result property="id"    column="id"    />
        <result property="lrrCd"    column="lrr_cd"    />
        <result property="lrrTyp"    column="lrr_typ"    />
        <result property="lrrPsn"    column="lrr_psn"    />
        <result property="lrrPsnCd"    column="lrr_psn_cd"    />
        <result property="lrrMainSkuCd"    column="lrr_main_sku_cd"    />
        <result property="lrrMainBatch"    column="lrr_main_batch"    />
        <result property="lrrMainSkuQty"    column="lrr_main_sku_qty"    />
        <result property="lrrMainTotalPrc"    column="lrr_main_total_prc"    />
        <result property="lrrMainDate"    column="lrr_main_date"    />
        <result property="lrrBn"    column="lrr_bn"    />
        <result property="lrrBnEncrypt"    column="lrr_bn_encrypt"    />
        <result property="lrrDmcd"    column="lrr_dmcd"    />
        <result property="lrrSlcd"    column="lrr_slcd"    />
        <result property="lrrTcd"    column="lrr_tcd"    />
        <result property="lrrOrgmcd"    column="lrr_orgmcd"    />
        <result property="flag"    column="flag"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectLogRepackResultVo">
        select id, lrr_cd, lrr_typ, lrr_psn, lrr_psn_cd, lrr_main_sku_cd, lrr_main_batch, lrr_main_sku_qty, lrr_main_total_prc, lrr_main_date, lrr_bn, lrr_bn_encrypt, lrr_dmcd, lrr_slcd, lrr_tcd, lrr_orgmcd, flag, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from log_repack_result
    </sql>

    <select id="selectLogRepackResultList" parameterType="LogRepackResult" resultMap="LogRepackResultResult">
        <include refid="selectLogRepackResultVo"/>
        <where>  
            <if test="lrrTyp != null  and lrrTyp != ''"> and lrr_typ = #{lrrTyp}</if>
            <if test="lrrPsn != null  and lrrPsn != ''"> and lrr_psn = #{lrrPsn}</if>
            <if test="lrrPsnCd != null  and lrrPsnCd != ''"> and lrr_psn_cd = #{lrrPsnCd}</if>
            <if test="lrrMainSkuCd != null  and lrrMainSkuCd != ''"> and lrr_main_sku_cd = #{lrrMainSkuCd}</if>
            <if test="lrrMainBatch != null  and lrrMainBatch != ''"> and lrr_main_batch = #{lrrMainBatch}</if>
            <if test="lrrMainSkuQty != null "> and lrr_main_sku_qty = #{lrrMainSkuQty}</if>
            <if test="lrrMainTotalPrc != null "> and lrr_main_total_prc = #{lrrMainTotalPrc}</if>
            <if test="lrrMainDate != null "> and lrr_main_date = #{lrrMainDate}</if>
            <if test="lrrBn != null  and lrrBn != ''"> and lrr_bn = #{lrrBn}</if>
            <if test="lrrBnEncrypt != null  and lrrBnEncrypt != ''"> and lrr_bn_encrypt = #{lrrBnEncrypt}</if>
            <if test="lrrDmcd != null  and lrrDmcd != ''"> and lrr_dmcd = #{lrrDmcd}</if>
            <if test="lrrSlcd != null  and lrrSlcd != ''"> and lrr_slcd = #{lrrSlcd}</if>
            <if test="lrrTcd != null  and lrrTcd != ''"> and lrr_tcd = #{lrrTcd}</if>
            <if test="lrrOrgmcd != null  and lrrOrgmcd != ''"> and lrr_orgmcd = #{lrrOrgmcd}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="startDate != null">
                AND r_cre_dt >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                AND r_cre_dt &lt;= DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
            </if>
        </where>
    </select>
    
    <select id="selectLogRepackResultByLrrCd" parameterType="String" resultMap="LogRepackResultResult">
        <include refid="selectLogRepackResultVo"/>
        where lrr_cd = #{lrrCd}
    </select>

    <insert id="insertLogRepackResult" parameterType="LogRepackResult">
        insert into log_repack_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lrrCd != null">lrr_cd,</if>
            <if test="lrrTyp != null">lrr_typ,</if>
            <if test="lrrPsn != null and lrrPsn != ''">lrr_psn,</if>
            <if test="lrrPsnCd != null and lrrPsnCd != ''">lrr_psn_cd,</if>
            <if test="lrrMainSkuCd != null and lrrMainSkuCd != ''">lrr_main_sku_cd,</if>
            <if test="lrrMainBatch != null">lrr_main_batch,</if>
            <if test="lrrMainSkuQty != null">lrr_main_sku_qty,</if>
            <if test="lrrMainTotalPrc != null">lrr_main_total_prc,</if>
            <if test="lrrMainDate != null">lrr_main_date,</if>
            <if test="lrrBn != null">lrr_bn,</if>
            <if test="lrrBnEncrypt != null">lrr_bn_encrypt,</if>
            <if test="lrrDmcd != null">lrr_dmcd,</if>
            <if test="lrrSlcd != null">lrr_slcd,</if>
            <if test="lrrTcd != null">lrr_tcd,</if>
            <if test="lrrOrgmcd != null">lrr_orgmcd,</if>
            <if test="flag != null">flag,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lrrCd != null">#{lrrCd},</if>
            <if test="lrrTyp != null">#{lrrTyp},</if>
            <if test="lrrPsn != null and lrrPsn != ''">#{lrrPsn},</if>
            <if test="lrrPsnCd != null and lrrPsnCd != ''">#{lrrPsnCd},</if>
            <if test="lrrMainSkuCd != null and lrrMainSkuCd != ''">#{lrrMainSkuCd},</if>
            <if test="lrrMainBatch != null">#{lrrMainBatch},</if>
            <if test="lrrMainSkuQty != null">#{lrrMainSkuQty},</if>
            <if test="lrrMainTotalPrc != null">#{lrrMainTotalPrc},</if>
            <if test="lrrMainDate != null">#{lrrMainDate},</if>
            <if test="lrrBn != null">#{lrrBn},</if>
            <if test="lrrBnEncrypt != null">#{lrrBnEncrypt},</if>
            <if test="lrrDmcd != null">#{lrrDmcd},</if>
            <if test="lrrSlcd != null">#{lrrSlcd},</if>
            <if test="lrrTcd != null">#{lrrTcd},</if>
            <if test="lrrOrgmcd != null">#{lrrOrgmcd},</if>
            <if test="flag != null">#{flag},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateLogRepackResult" parameterType="LogRepackResult">
        update log_repack_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lrrTyp != null">lrr_typ = #{lrrTyp},</if>
            <if test="lrrPsn != null and lrrPsn != ''">lrr_psn = #{lrrPsn},</if>
            <if test="lrrPsnCd != null and lrrPsnCd != ''">lrr_psn_cd = #{lrrPsnCd},</if>
            <if test="lrrMainSkuCd != null and lrrMainSkuCd != ''">lrr_main_sku_cd = #{lrrMainSkuCd},</if>
            <if test="lrrMainBatch != null">lrr_main_batch = #{lrrMainBatch},</if>
            <if test="lrrMainSkuQty != null">lrr_main_sku_qty = #{lrrMainSkuQty},</if>
            <if test="lrrMainTotalPrc != null">lrr_main_total_prc = #{lrrMainTotalPrc},</if>
            <if test="lrrMainDate != null">lrr_main_date = #{lrrMainDate},</if>
            <if test="lrrBn != null">lrr_bn = #{lrrBn},</if>
            <if test="lrrBnEncrypt != null">lrr_bn_encrypt = #{lrrBnEncrypt},</if>
            <if test="lrrDmcd != null">lrr_dmcd = #{lrrDmcd},</if>
            <if test="lrrSlcd != null">lrr_slcd = #{lrrSlcd},</if>
            <if test="lrrTcd != null">lrr_tcd = #{lrrTcd},</if>
            <if test="lrrOrgmcd != null">lrr_orgmcd = #{lrrOrgmcd},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where lrr_cd = #{lrrCd}
    </update>

    <delete id="deleteLogRepackResultByLrrCd" parameterType="String">
        delete from log_repack_result where lrr_cd = #{lrrCd}
    </delete>

    <delete id="deleteLogRepackResultByLrrCds" parameterType="String">
        delete from log_repack_result where lrr_cd in 
        <foreach item="lrrCd" collection="array" open="(" separator="," close=")">
            #{lrrCd}
        </foreach>
    </delete>
</mapper>