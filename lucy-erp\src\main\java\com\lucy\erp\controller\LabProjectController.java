package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LabProject;
import com.lucy.erp.service.ILabProjectService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 研发项目Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/lab/project")
public class LabProjectController extends BaseController
{
    @Autowired
    private ILabProjectService labProjectService;

    /**
     * 查询研发项目列表
     */
    @PreAuthorize("@ss.hasPermi('lab:project:list')")
    @GetMapping("/list")
    public TableDataInfo list(LabProject labProject)
    {
        startPage();
        List<LabProject> list = labProjectService.selectLabProjectList(labProject);
        return getDataTable(list);
    }

    /**
     * 导出研发项目列表
     */
    @PreAuthorize("@ss.hasPermi('lab:project:export')")
    @Log(title = "研发项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LabProject labProject)
    {
        List<LabProject> list = labProjectService.selectLabProjectList(labProject);
        ExcelUtil<LabProject> util = new ExcelUtil<LabProject>(LabProject.class);
        util.exportExcel(response, list, "研发项目数据");
    }

    /**
     * 获取研发项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('lab:project:query')")
    @GetMapping(value = "/{lpCd}")
    public AjaxResult getInfo(@PathVariable("lpCd") String lpCd)
    {
        return success(labProjectService.selectLabProjectByLpCd(lpCd));
    }

    /**
     * 新增研发项目
     */
    @PreAuthorize("@ss.hasPermi('lab:project:add')")
    @Log(title = "研发项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LabProject labProject)
    {
        return toAjax(labProjectService.insertLabProject(labProject));
    }

    /**
     * 修改研发项目
     */
    @PreAuthorize("@ss.hasPermi('lab:project:edit')")
    @Log(title = "研发项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LabProject labProject)
    {
        return toAjax(labProjectService.updateLabProject(labProject));
    }

    /**
     * 删除研发项目
     */
    @PreAuthorize("@ss.hasPermi('lab:project:remove')")
    @Log(title = "研发项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lpCds}")
    public AjaxResult remove(@PathVariable String[] lpCds)
    {
        return toAjax(labProjectService.deleteLabProjectByLpCds(lpCds));
    }
}
