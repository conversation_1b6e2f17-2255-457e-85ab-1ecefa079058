package com.lucy.customer.service.impl;

import java.util.List;

import com.lucy.erp.domain.BasCustorgMst;
import com.lucy.erp.service.IBasCustorgMstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.customer.mapper.DepartmentGroupMapper;
import com.lucy.customer.domain.DepartmentGroup;
import com.lucy.customer.service.IDepartmentGroupService;

/**
 * 部门组织结构 Department groupsService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class DepartmentGroupServiceImpl implements IDepartmentGroupService 
{
    @Autowired
    private DepartmentGroupMapper departmentGroupMapper;

    @Autowired
    private IBasCustorgMstService basCustorgMstService;

    /**
     * 查询部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 部门组织结构 Department groups
     */
    @Override
    public DepartmentGroup selectDepartmentGroupByCode(String code)
    {
        return departmentGroupMapper.selectDepartmentGroupByCode(code);
    }

    /**
     * 查询部门组织结构 Department groups列表
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 部门组织结构 Department groups
     */
    @Override
    public List<DepartmentGroup> selectDepartmentGroupList(DepartmentGroup departmentGroup)
    {
        return departmentGroupMapper.selectDepartmentGroupList(departmentGroup);
    }

    /**
     * 新增部门组织结构 Department groups
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 结果
     */
    @Override
    public int insertDepartmentGroup(DepartmentGroup departmentGroup)
    {
        return departmentGroupMapper.insertDepartmentGroup(departmentGroup);
    }

    /**
     * 修改部门组织结构 Department groups
     * 
     * @param departmentGroup 部门组织结构 Department groups
     * @return 结果
     */
    @Override
    public int updateDepartmentGroup(DepartmentGroup departmentGroup)
    {
        return departmentGroupMapper.updateDepartmentGroup(departmentGroup);
    }

    /**
     * 批量删除部门组织结构 Department groups
     * 
     * @param codes 需要删除的部门组织结构 Department groups主键
     * @return 结果
     */
    @Override
    public int deleteDepartmentGroupByCodes(String[] codes)
    {
        return departmentGroupMapper.deleteDepartmentGroupByCodes(codes);
    }

    /**
     * 删除部门组织结构 Department groups信息
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 结果
     */
    @Override
    public int deleteDepartmentGroupByCode(String code)
    {
        return departmentGroupMapper.deleteDepartmentGroupByCode(code);
    }
    
    @Override
    public void insertDepartmentGroupIfNotExist(String code) {
        DepartmentGroup departmentGroup = selectDepartmentGroupByCode(code);
        if (departmentGroup == null) {
            BasCustorgMst basCustorgMst = basCustorgMstService.selectBasCustorgMstByBcmCd(code);
            DepartmentGroup departmentGroupNew = new DepartmentGroup();
            departmentGroupNew.setCode(code);
            departmentGroupNew.setName(basCustorgMst.getBcmNm());
            // TODO
            // 负责人
            departmentGroupNew.setManager("");
            // 成本中心
            departmentGroupNew.setCostCenter("");
            departmentGroupMapper.insertDepartmentGroup(departmentGroupNew);
        }
    }
}
