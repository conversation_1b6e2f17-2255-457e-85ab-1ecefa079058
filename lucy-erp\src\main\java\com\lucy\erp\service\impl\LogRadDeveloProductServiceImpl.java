package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogRadDeveloProductMapper;
import com.lucy.erp.domain.LogRadDeveloProduct;
import com.lucy.erp.service.ILogRadDeveloProductService;

/**
 * 小样或者产成品结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogRadDeveloProductServiceImpl implements ILogRadDeveloProductService 
{
    @Autowired
    private LogRadDeveloProductMapper logRadDeveloProductMapper;

    /**
     * 查询小样或者产成品结果
     * 
     * @param lrdpCd 小样或者产成品结果主键
     * @return 小样或者产成品结果
     */
    @Override
    public LogRadDeveloProduct selectLogRadDeveloProductByLrdpCd(String lrdpCd)
    {
        return logRadDeveloProductMapper.selectLogRadDeveloProductByLrdpCd(lrdpCd);
    }

    /**
     * 查询小样或者产成品结果列表
     * 
     * @param logRadDeveloProduct 小样或者产成品结果
     * @return 小样或者产成品结果
     */
    @Override
    public List<LogRadDeveloProduct> selectLogRadDeveloProductList(LogRadDeveloProduct logRadDeveloProduct)
    {
        return logRadDeveloProductMapper.selectLogRadDeveloProductList(logRadDeveloProduct);
    }

    /**
     * 新增小样或者产成品结果
     * 
     * @param logRadDeveloProduct 小样或者产成品结果
     * @return 结果
     */
    @Override
    public int insertLogRadDeveloProduct(LogRadDeveloProduct logRadDeveloProduct)
    {
        return logRadDeveloProductMapper.insertLogRadDeveloProduct(logRadDeveloProduct);
    }

    /**
     * 修改小样或者产成品结果
     * 
     * @param logRadDeveloProduct 小样或者产成品结果
     * @return 结果
     */
    @Override
    public int updateLogRadDeveloProduct(LogRadDeveloProduct logRadDeveloProduct)
    {
        return logRadDeveloProductMapper.updateLogRadDeveloProduct(logRadDeveloProduct);
    }

    /**
     * 批量删除小样或者产成品结果
     * 
     * @param lrdpCds 需要删除的小样或者产成品结果主键
     * @return 结果
     */
    @Override
    public int deleteLogRadDeveloProductByLrdpCds(String[] lrdpCds)
    {
        return logRadDeveloProductMapper.deleteLogRadDeveloProductByLrdpCds(lrdpCds);
    }

    /**
     * 删除小样或者产成品结果信息
     * 
     * @param lrdpCd 小样或者产成品结果主键
     * @return 结果
     */
    @Override
    public int deleteLogRadDeveloProductByLrdpCd(String lrdpCd)
    {
        return logRadDeveloProductMapper.deleteLogRadDeveloProductByLrdpCd(lrdpCd);
    }
}
