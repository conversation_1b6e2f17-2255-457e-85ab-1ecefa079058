package com.lucy.customer.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 部门组织结构 Department groups对象 department_group
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public class DepartmentGroup extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 部门组编号, Group code */
    private String code;

    /** 部门组名称, Group name */
    @Excel(name = "部门组名称, Group name")
    private String name;

    /** 负责人, Manager */
    @Excel(name = "负责人, Manager")
    private String manager;

    /** 成本中心, Cost center */
    @Excel(name = "成本中心, Cost center")
    private String costCenter;

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setManager(String manager) 
    {
        this.manager = manager;
    }

    public String getManager() 
    {
        return manager;
    }

    public void setCostCenter(String costCenter) 
    {
        this.costCenter = costCenter;
    }

    public String getCostCenter() 
    {
        return costCenter;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("code", getCode())
            .append("name", getName())
            .append("manager", getManager())
            .append("costCenter", getCostCenter())
            .toString();
    }
}
