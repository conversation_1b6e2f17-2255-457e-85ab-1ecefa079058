package com.lucy.erp.service.impl;

import java.util.List;

import com.lucy.erp.domain.LogRepackResult;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogRepackPackageUseResultDtlMapper;
import com.lucy.erp.domain.LogRepackPackageUseResultDtl;
import com.lucy.erp.service.ILogRepackPackageUseResultDtlService;

/**
 * 产成品投料明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogRepackPackageUseResultDtlServiceImpl implements ILogRepackPackageUseResultDtlService 
{
    @Autowired
    private LogRepackPackageUseResultDtlMapper logRepackPackageUseResultDtlMapper;

    /**
     * 查询产成品投料明细
     * 
     * @param lrpurdMcd 产成品投料明细主键
     * @return 产成品投料明细
     */
    @Override
    public LogRepackPackageUseResultDtl selectLogRepackPackageUseResultDtlByLrpurdMcd(String lrpurdMcd)
    {
        return logRepackPackageUseResultDtlMapper.selectLogRepackPackageUseResultDtlByLrpurdMcd(lrpurdMcd);
    }

    /**
     * 查询产成品投料明细列表
     * 
     * @param logRepackPackageUseResultDtl 产成品投料明细
     * @return 产成品投料明细
     */
    @Override
    public List<LogRepackPackageUseResultDtl> selectLogRepackPackageUseResultDtlList(LogRepackPackageUseResultDtl logRepackPackageUseResultDtl)
    {
        return logRepackPackageUseResultDtlMapper.selectLogRepackPackageUseResultDtlList(logRepackPackageUseResultDtl);
    }

    /**
     * 查询产成品列表
     *
     * @param logRepackResult 产成品
     * @return 产成品
     */
    @Override
    public List<LogRepackResult> selectLogRepackPackageUseResultDtlAndLrrList(LogRepackResult logRepackResult)
    {
        return logRepackPackageUseResultDtlMapper.selectLogRepackPackageUseResultDtlAndLrrList(logRepackResult);
    }

    /**
     * 新增产成品投料明细
     * 
     * @param logRepackPackageUseResultDtl 产成品投料明细
     * @return 结果
     */
    @Override
    public int insertLogRepackPackageUseResultDtl(LogRepackPackageUseResultDtl logRepackPackageUseResultDtl)
    {
        return logRepackPackageUseResultDtlMapper.insertLogRepackPackageUseResultDtl(logRepackPackageUseResultDtl);
    }

    /**
     * 修改产成品投料明细
     * 
     * @param logRepackPackageUseResultDtl 产成品投料明细
     * @return 结果
     */
    @Override
    public int updateLogRepackPackageUseResultDtl(LogRepackPackageUseResultDtl logRepackPackageUseResultDtl)
    {
        return logRepackPackageUseResultDtlMapper.updateLogRepackPackageUseResultDtl(logRepackPackageUseResultDtl);
    }

    /**
     * 批量删除产成品投料明细
     * 
     * @param lrpurdCds 需要删除的产成品投料明细主键
     * @return 结果
     */
    @Override
    public int deleteLogRepackPackageUseResultDtlByLrpurdCds(String[] lrpurdCds)
    {
        return logRepackPackageUseResultDtlMapper.deleteLogRepackPackageUseResultDtlByLrpurdCds(lrpurdCds);
    }

    /**
     * 删除产成品投料明细信息
     * 
     * @param lrpurdCd 产成品投料明细主键
     * @return 结果
     */
    @Override
    public int deleteLogRepackPackageUseResultDtlByLrpurdCd(String lrpurdCd)
    {
        return logRepackPackageUseResultDtlMapper.deleteLogRepackPackageUseResultDtlByLrpurdCd(lrpurdCd);
    }
}
