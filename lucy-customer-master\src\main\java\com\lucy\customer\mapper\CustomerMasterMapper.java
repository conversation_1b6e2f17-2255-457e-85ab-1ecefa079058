package com.lucy.customer.mapper;

import java.util.List;
import com.lucy.customer.domain.CustomerMaster;

/**
 * 客户主数据 Customer masterMapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface CustomerMasterMapper 
{
    /**
     * 查询客户主数据 Customer master
     * 
     * @param code 客户主数据 Customer master主键
     * @return 客户主数据 Customer master
     */
    public CustomerMaster selectCustomerMasterByCode(String code);

    /**
     * 查询客户主数据 Customer master列表
     * 
     * @param customerMaster 客户主数据 Customer master
     * @return 客户主数据 Customer master集合
     */
    public List<CustomerMaster> selectCustomerMasterList(CustomerMaster customerMaster);

    /**
     * 新增客户主数据 Customer master
     * 
     * @param customerMaster 客户主数据 Customer master
     * @return 结果
     */
    public int insertCustomerMaster(CustomerMaster customerMaster);

    /**
     * 修改客户主数据 Customer master
     * 
     * @param customerMaster 客户主数据 Customer master
     * @return 结果
     */
    public int updateCustomerMaster(CustomerMaster customerMaster);

    /**
     * 删除客户主数据 Customer master
     * 
     * @param code 客户主数据 Customer master主键
     * @return 结果
     */
    public int deleteCustomerMasterByCode(String code);

    /**
     * 批量删除客户主数据 Customer master
     * 
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerMasterByCodes(String[] codes);
}
