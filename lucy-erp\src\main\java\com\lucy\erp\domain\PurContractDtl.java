package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购_合同_明细
对象 pur_contract_dtl
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public class PurContractDtl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 合同编号 */
    @Excel(name = "合同编号")
    private String pcdMcd;

    /** 合同明细编号 */
    private String pcdCd;

    /** 平台单号 */
    @Excel(name = "平台单号")
    private String pcdOpcd;

    /** 类型 */
    @Excel(name = "类型")
    private String pcdTyp;

    /** 品牌 */
    @Excel(name = "品牌")
    private String pcdBr;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String pcdPrecd;

    /** 采购需求编号 */
    @Excel(name = "采购需求编号")
    private String pcdPdcd;

    /** 原始编号 */
    @Excel(name = "原始编号")
    private String pcdPorgcd;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String pcdPcd;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String pcdPnm;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String pcdCas;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String pcdPurity;

    /** 危品分类 */
    @Excel(name = "危品分类")
    private String pcdDcls;

    /** 特殊备注 */
    @Excel(name = "特殊备注")
    private String pcdSprmk;

    /** 易制毒备注 */
    @Excel(name = "易制毒备注")
    private String pcdSprmkPoisonod;

    /** 存储条件 */
    @Excel(name = "存储条件")
    private String pcdStg;

    /** 包装编号 */
    @Excel(name = "包装编号")
    private String pcdPpcd;

    /** 装箱数量 */
    @Excel(name = "装箱数量")
    private Long pcdPkgset;

    /** 包装数量 */
    @Excel(name = "包装数量")
    private BigDecimal pcdPkgqty;

    /** 包装单位 */
    @Excel(name = "包装单位")
    private String pcdPkgunit;

    /** 包装 */
    @Excel(name = "包装")
    private String pcdPack;

    /** 仓库编号 */
    @Excel(name = "仓库编号")
    private String pcdDcd;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String pcdDnm;

    /** 原价 */
    @Excel(name = "原价")
    private BigDecimal pcdOprc;

    /** 折扣 */
    @Excel(name = "折扣")
    private BigDecimal pcdDct;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal pcdUprc;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal pcdQty;

    /** 到货数量 */
    @Excel(name = "到货数量")
    private BigDecimal pcdInqty;

    /** 开票数量 */
    @Excel(name = "开票数量")
    private BigDecimal pcdIqty;

    /** 总价 */
    @Excel(name = "总价")
    private BigDecimal pcdTprc;

    /** 运单号 */
    @Excel(name = "运单号")
    private String pcdTn;

    /** 状态编号 */
    @Excel(name = "状态编号")
    private String pcdStscd;

    /** 状态名称 */
    @Excel(name = "状态名称")
    private String pcdStsnm;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pcdPodt;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pcdIsdt;

    /** 备注 */
    @Excel(name = "备注")
    private String pcdRmk;

    /** 跟单日期 */
    @Excel(name = "跟单日期")
    private String pcdTdt;

    /** 下一次跟单日期 */
    @Excel(name = "下一次跟单日期")
    private String pcdNtdt;

    /** 预计到货日期日期格式 */
    @Excel(name = "预计到货日期日期格式")
    private String pcdDdend;

    /** 汇率 */
    @Excel(name = "汇率")
    private BigDecimal pcdExchange;

    /** 本币单价 */
    @Excel(name = "本币单价")
    private BigDecimal pcdLcUprc;

    /** 本币货值 */
    @Excel(name = "本币货值")
    private BigDecimal pcdLcValue;

    /** 预计到货日期 */
    @Excel(name = "预计到货日期")
    private String pcdEadt;

    /** 未到货原因 */
    @Excel(name = "未到货原因")
    private String pcdNatyp;

    /** 跟单备注 */
    @Excel(name = "跟单备注")
    private String pcdTrmk;

    /** 跟票备注 */
    @Excel(name = "跟票备注")
    private String pcdFprmk;

    /** 采购类目 */
    @Excel(name = "采购类目")
    private String pcdPtyp;

    /** 代工生产(默认是否) */
    @Excel(name = "代工生产(默认是否)")
    private String pcdIsoem;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long rV;

    /** 删除标志 */
    @Excel(name = "删除标志")
    private Integer rDel;

    /** 供应商包装(采购合同打印用) */
    @Excel(name = "供应商包装(采购合同打印用)")
    private String pcdSupPkg;

    /** 其它费用 */
    @Excel(name = "其它费用")
    private BigDecimal pcdLcIncidentals;

    /** 省 */
    @Excel(name = "省")
    private String pcdReturnProvince;

    /** 市 */
    @Excel(name = "市")
    private String pcdReturnCity;

    /** 区 */
    @Excel(name = "区")
    private String pcdReturnDistrict;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String pcdReturnAddr;

    /** 联系人 */
    @Excel(name = "联系人")
    private String pcdReturnContacts;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String pcdReturnPhone;

    /** 采购退货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "采购退货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pcdReturnDt;

    /** 采购周期类型（00：订单采购、01：备库采购、02：柔性分装、03:寄售） */
    @Excel(name = "采购周期类型", readConverterExp = "0=0：订单采购、01：备库采购、02：柔性分装、03:寄售")
    private String pcdPurcycl;

    /** 费用审核（00 没审核 01已审核） */
    @Excel(name = "费用审核", readConverterExp = "0=0,没=审核,0=1已审核")
    private String pcdExpencesSts;

    /** 费用审核通过时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "费用审核通过时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pcdExppassDt;

    /** 供应商品牌 */
    @Excel(name = "供应商品牌")
    private String pcdSupPbcd;

    /** 供应商货号 */
    @Excel(name = "供应商货号")
    private String pcdSupPorgcd;

    /** 供应商包装编号 */
    @Excel(name = "供应商包装编号")
    private String pcdSupPpcd;

    /** 供应数量 */
    @Excel(name = "供应数量")
    private BigDecimal pcdSupQty;

    /** 供应商批次号 */
    @Excel(name = "供应商批次号")
    private String pcdBnEncrypt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPcdMcd(String pcdMcd) 
    {
        this.pcdMcd = pcdMcd;
    }

    public String getPcdMcd() 
    {
        return pcdMcd;
    }

    public void setPcdCd(String pcdCd) 
    {
        this.pcdCd = pcdCd;
    }

    public String getPcdCd() 
    {
        return pcdCd;
    }

    public void setPcdOpcd(String pcdOpcd) 
    {
        this.pcdOpcd = pcdOpcd;
    }

    public String getPcdOpcd() 
    {
        return pcdOpcd;
    }

    public void setPcdTyp(String pcdTyp) 
    {
        this.pcdTyp = pcdTyp;
    }

    public String getPcdTyp() 
    {
        return pcdTyp;
    }

    public void setPcdBr(String pcdBr) 
    {
        this.pcdBr = pcdBr;
    }

    public String getPcdBr() 
    {
        return pcdBr;
    }

    public void setPcdPrecd(String pcdPrecd) 
    {
        this.pcdPrecd = pcdPrecd;
    }

    public String getPcdPrecd() 
    {
        return pcdPrecd;
    }

    public void setPcdPdcd(String pcdPdcd) 
    {
        this.pcdPdcd = pcdPdcd;
    }

    public String getPcdPdcd() 
    {
        return pcdPdcd;
    }

    public void setPcdPorgcd(String pcdPorgcd) 
    {
        this.pcdPorgcd = pcdPorgcd;
    }

    public String getPcdPorgcd() 
    {
        return pcdPorgcd;
    }

    public void setPcdPcd(String pcdPcd) 
    {
        this.pcdPcd = pcdPcd;
    }

    public String getPcdPcd() 
    {
        return pcdPcd;
    }

    public void setPcdPnm(String pcdPnm) 
    {
        this.pcdPnm = pcdPnm;
    }

    public String getPcdPnm() 
    {
        return pcdPnm;
    }

    public void setPcdCas(String pcdCas) 
    {
        this.pcdCas = pcdCas;
    }

    public String getPcdCas() 
    {
        return pcdCas;
    }

    public void setPcdPurity(String pcdPurity) 
    {
        this.pcdPurity = pcdPurity;
    }

    public String getPcdPurity() 
    {
        return pcdPurity;
    }

    public void setPcdDcls(String pcdDcls) 
    {
        this.pcdDcls = pcdDcls;
    }

    public String getPcdDcls() 
    {
        return pcdDcls;
    }

    public void setPcdSprmk(String pcdSprmk) 
    {
        this.pcdSprmk = pcdSprmk;
    }

    public String getPcdSprmk() 
    {
        return pcdSprmk;
    }

    public void setPcdSprmkPoisonod(String pcdSprmkPoisonod) 
    {
        this.pcdSprmkPoisonod = pcdSprmkPoisonod;
    }

    public String getPcdSprmkPoisonod() 
    {
        return pcdSprmkPoisonod;
    }

    public void setPcdStg(String pcdStg) 
    {
        this.pcdStg = pcdStg;
    }

    public String getPcdStg() 
    {
        return pcdStg;
    }

    public void setPcdPpcd(String pcdPpcd) 
    {
        this.pcdPpcd = pcdPpcd;
    }

    public String getPcdPpcd() 
    {
        return pcdPpcd;
    }

    public void setPcdPkgset(Long pcdPkgset) 
    {
        this.pcdPkgset = pcdPkgset;
    }

    public Long getPcdPkgset() 
    {
        return pcdPkgset;
    }

    public void setPcdPkgqty(BigDecimal pcdPkgqty) 
    {
        this.pcdPkgqty = pcdPkgqty;
    }

    public BigDecimal getPcdPkgqty() 
    {
        return pcdPkgqty;
    }

    public void setPcdPkgunit(String pcdPkgunit) 
    {
        this.pcdPkgunit = pcdPkgunit;
    }

    public String getPcdPkgunit() 
    {
        return pcdPkgunit;
    }

    public void setPcdPack(String pcdPack) 
    {
        this.pcdPack = pcdPack;
    }

    public String getPcdPack() 
    {
        return pcdPack;
    }

    public void setPcdDcd(String pcdDcd) 
    {
        this.pcdDcd = pcdDcd;
    }

    public String getPcdDcd() 
    {
        return pcdDcd;
    }

    public void setPcdDnm(String pcdDnm) 
    {
        this.pcdDnm = pcdDnm;
    }

    public String getPcdDnm() 
    {
        return pcdDnm;
    }

    public void setPcdOprc(BigDecimal pcdOprc) 
    {
        this.pcdOprc = pcdOprc;
    }

    public BigDecimal getPcdOprc() 
    {
        return pcdOprc;
    }

    public void setPcdDct(BigDecimal pcdDct) 
    {
        this.pcdDct = pcdDct;
    }

    public BigDecimal getPcdDct() 
    {
        return pcdDct;
    }

    public void setPcdUprc(BigDecimal pcdUprc) 
    {
        this.pcdUprc = pcdUprc;
    }

    public BigDecimal getPcdUprc() 
    {
        return pcdUprc;
    }

    public void setPcdQty(BigDecimal pcdQty) 
    {
        this.pcdQty = pcdQty;
    }

    public BigDecimal getPcdQty() 
    {
        return pcdQty;
    }

    public void setPcdInqty(BigDecimal pcdInqty) 
    {
        this.pcdInqty = pcdInqty;
    }

    public BigDecimal getPcdInqty() 
    {
        return pcdInqty;
    }

    public void setPcdIqty(BigDecimal pcdIqty) 
    {
        this.pcdIqty = pcdIqty;
    }

    public BigDecimal getPcdIqty() 
    {
        return pcdIqty;
    }

    public void setPcdTprc(BigDecimal pcdTprc) 
    {
        this.pcdTprc = pcdTprc;
    }

    public BigDecimal getPcdTprc() 
    {
        return pcdTprc;
    }

    public void setPcdTn(String pcdTn) 
    {
        this.pcdTn = pcdTn;
    }

    public String getPcdTn() 
    {
        return pcdTn;
    }

    public void setPcdStscd(String pcdStscd) 
    {
        this.pcdStscd = pcdStscd;
    }

    public String getPcdStscd() 
    {
        return pcdStscd;
    }

    public void setPcdStsnm(String pcdStsnm) 
    {
        this.pcdStsnm = pcdStsnm;
    }

    public String getPcdStsnm() 
    {
        return pcdStsnm;
    }

    public void setPcdPodt(Date pcdPodt) 
    {
        this.pcdPodt = pcdPodt;
    }

    public Date getPcdPodt() 
    {
        return pcdPodt;
    }

    public void setPcdIsdt(Date pcdIsdt) 
    {
        this.pcdIsdt = pcdIsdt;
    }

    public Date getPcdIsdt() 
    {
        return pcdIsdt;
    }

    public void setPcdRmk(String pcdRmk) 
    {
        this.pcdRmk = pcdRmk;
    }

    public String getPcdRmk() 
    {
        return pcdRmk;
    }

    public void setPcdTdt(String pcdTdt) 
    {
        this.pcdTdt = pcdTdt;
    }

    public String getPcdTdt() 
    {
        return pcdTdt;
    }

    public void setPcdNtdt(String pcdNtdt) 
    {
        this.pcdNtdt = pcdNtdt;
    }

    public String getPcdNtdt() 
    {
        return pcdNtdt;
    }

    public void setPcdDdend(String pcdDdend) 
    {
        this.pcdDdend = pcdDdend;
    }

    public String getPcdDdend() 
    {
        return pcdDdend;
    }

    public void setPcdExchange(BigDecimal pcdExchange) 
    {
        this.pcdExchange = pcdExchange;
    }

    public BigDecimal getPcdExchange() 
    {
        return pcdExchange;
    }

    public void setPcdLcUprc(BigDecimal pcdLcUprc) 
    {
        this.pcdLcUprc = pcdLcUprc;
    }

    public BigDecimal getPcdLcUprc() 
    {
        return pcdLcUprc;
    }

    public void setPcdLcValue(BigDecimal pcdLcValue) 
    {
        this.pcdLcValue = pcdLcValue;
    }

    public BigDecimal getPcdLcValue() 
    {
        return pcdLcValue;
    }

    public void setPcdEadt(String pcdEadt) 
    {
        this.pcdEadt = pcdEadt;
    }

    public String getPcdEadt() 
    {
        return pcdEadt;
    }

    public void setPcdNatyp(String pcdNatyp) 
    {
        this.pcdNatyp = pcdNatyp;
    }

    public String getPcdNatyp() 
    {
        return pcdNatyp;
    }

    public void setPcdTrmk(String pcdTrmk) 
    {
        this.pcdTrmk = pcdTrmk;
    }

    public String getPcdTrmk() 
    {
        return pcdTrmk;
    }

    public void setPcdFprmk(String pcdFprmk) 
    {
        this.pcdFprmk = pcdFprmk;
    }

    public String getPcdFprmk() 
    {
        return pcdFprmk;
    }

    public void setPcdPtyp(String pcdPtyp) 
    {
        this.pcdPtyp = pcdPtyp;
    }

    public String getPcdPtyp() 
    {
        return pcdPtyp;
    }

    public void setPcdIsoem(String pcdIsoem) 
    {
        this.pcdIsoem = pcdIsoem;
    }

    public String getPcdIsoem() 
    {
        return pcdIsoem;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Integer rDel) 
    {
        this.rDel = rDel;
    }

    public Integer getrDel() 
    {
        return rDel;
    }

    public void setPcdSupPkg(String pcdSupPkg) 
    {
        this.pcdSupPkg = pcdSupPkg;
    }

    public String getPcdSupPkg() 
    {
        return pcdSupPkg;
    }

    public void setPcdLcIncidentals(BigDecimal pcdLcIncidentals) 
    {
        this.pcdLcIncidentals = pcdLcIncidentals;
    }

    public BigDecimal getPcdLcIncidentals() 
    {
        return pcdLcIncidentals;
    }

    public void setPcdReturnProvince(String pcdReturnProvince) 
    {
        this.pcdReturnProvince = pcdReturnProvince;
    }

    public String getPcdReturnProvince() 
    {
        return pcdReturnProvince;
    }

    public void setPcdReturnCity(String pcdReturnCity) 
    {
        this.pcdReturnCity = pcdReturnCity;
    }

    public String getPcdReturnCity() 
    {
        return pcdReturnCity;
    }

    public void setPcdReturnDistrict(String pcdReturnDistrict) 
    {
        this.pcdReturnDistrict = pcdReturnDistrict;
    }

    public String getPcdReturnDistrict() 
    {
        return pcdReturnDistrict;
    }

    public void setPcdReturnAddr(String pcdReturnAddr) 
    {
        this.pcdReturnAddr = pcdReturnAddr;
    }

    public String getPcdReturnAddr() 
    {
        return pcdReturnAddr;
    }

    public void setPcdReturnContacts(String pcdReturnContacts) 
    {
        this.pcdReturnContacts = pcdReturnContacts;
    }

    public String getPcdReturnContacts() 
    {
        return pcdReturnContacts;
    }

    public void setPcdReturnPhone(String pcdReturnPhone) 
    {
        this.pcdReturnPhone = pcdReturnPhone;
    }

    public String getPcdReturnPhone() 
    {
        return pcdReturnPhone;
    }

    public void setPcdReturnDt(Date pcdReturnDt) 
    {
        this.pcdReturnDt = pcdReturnDt;
    }

    public Date getPcdReturnDt() 
    {
        return pcdReturnDt;
    }

    public void setPcdPurcycl(String pcdPurcycl) 
    {
        this.pcdPurcycl = pcdPurcycl;
    }

    public String getPcdPurcycl() 
    {
        return pcdPurcycl;
    }

    public void setPcdExpencesSts(String pcdExpencesSts) 
    {
        this.pcdExpencesSts = pcdExpencesSts;
    }

    public String getPcdExpencesSts() 
    {
        return pcdExpencesSts;
    }

    public void setPcdExppassDt(Date pcdExppassDt) 
    {
        this.pcdExppassDt = pcdExppassDt;
    }

    public Date getPcdExppassDt() 
    {
        return pcdExppassDt;
    }

    public void setPcdSupPbcd(String pcdSupPbcd) 
    {
        this.pcdSupPbcd = pcdSupPbcd;
    }

    public String getPcdSupPbcd() 
    {
        return pcdSupPbcd;
    }

    public void setPcdSupPorgcd(String pcdSupPorgcd) 
    {
        this.pcdSupPorgcd = pcdSupPorgcd;
    }

    public String getPcdSupPorgcd() 
    {
        return pcdSupPorgcd;
    }

    public void setPcdSupPpcd(String pcdSupPpcd) 
    {
        this.pcdSupPpcd = pcdSupPpcd;
    }

    public String getPcdSupPpcd() 
    {
        return pcdSupPpcd;
    }

    public void setPcdSupQty(BigDecimal pcdSupQty) 
    {
        this.pcdSupQty = pcdSupQty;
    }

    public BigDecimal getPcdSupQty() 
    {
        return pcdSupQty;
    }

    public void setPcdBnEncrypt(String pcdBnEncrypt) 
    {
        this.pcdBnEncrypt = pcdBnEncrypt;
    }

    public String getPcdBnEncrypt() 
    {
        return pcdBnEncrypt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pcdMcd", getPcdMcd())
            .append("pcdCd", getPcdCd())
            .append("pcdOpcd", getPcdOpcd())
            .append("pcdTyp", getPcdTyp())
            .append("pcdBr", getPcdBr())
            .append("pcdPrecd", getPcdPrecd())
            .append("pcdPdcd", getPcdPdcd())
            .append("pcdPorgcd", getPcdPorgcd())
            .append("pcdPcd", getPcdPcd())
            .append("pcdPnm", getPcdPnm())
            .append("pcdCas", getPcdCas())
            .append("pcdPurity", getPcdPurity())
            .append("pcdDcls", getPcdDcls())
            .append("pcdSprmk", getPcdSprmk())
            .append("pcdSprmkPoisonod", getPcdSprmkPoisonod())
            .append("pcdStg", getPcdStg())
            .append("pcdPpcd", getPcdPpcd())
            .append("pcdPkgset", getPcdPkgset())
            .append("pcdPkgqty", getPcdPkgqty())
            .append("pcdPkgunit", getPcdPkgunit())
            .append("pcdPack", getPcdPack())
            .append("pcdDcd", getPcdDcd())
            .append("pcdDnm", getPcdDnm())
            .append("pcdOprc", getPcdOprc())
            .append("pcdDct", getPcdDct())
            .append("pcdUprc", getPcdUprc())
            .append("pcdQty", getPcdQty())
            .append("pcdInqty", getPcdInqty())
            .append("pcdIqty", getPcdIqty())
            .append("pcdTprc", getPcdTprc())
            .append("pcdTn", getPcdTn())
            .append("pcdStscd", getPcdStscd())
            .append("pcdStsnm", getPcdStsnm())
            .append("pcdPodt", getPcdPodt())
            .append("pcdIsdt", getPcdIsdt())
            .append("pcdRmk", getPcdRmk())
            .append("pcdTdt", getPcdTdt())
            .append("pcdNtdt", getPcdNtdt())
            .append("pcdDdend", getPcdDdend())
            .append("pcdExchange", getPcdExchange())
            .append("pcdLcUprc", getPcdLcUprc())
            .append("pcdLcValue", getPcdLcValue())
            .append("pcdEadt", getPcdEadt())
            .append("pcdNatyp", getPcdNatyp())
            .append("pcdTrmk", getPcdTrmk())
            .append("pcdFprmk", getPcdFprmk())
            .append("pcdPtyp", getPcdPtyp())
            .append("pcdIsoem", getPcdIsoem())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("pcdSupPkg", getPcdSupPkg())
            .append("pcdLcIncidentals", getPcdLcIncidentals())
            .append("pcdReturnProvince", getPcdReturnProvince())
            .append("pcdReturnCity", getPcdReturnCity())
            .append("pcdReturnDistrict", getPcdReturnDistrict())
            .append("pcdReturnAddr", getPcdReturnAddr())
            .append("pcdReturnContacts", getPcdReturnContacts())
            .append("pcdReturnPhone", getPcdReturnPhone())
            .append("pcdReturnDt", getPcdReturnDt())
            .append("pcdPurcycl", getPcdPurcycl())
            .append("pcdExpencesSts", getPcdExpencesSts())
            .append("pcdExppassDt", getPcdExppassDt())
            .append("pcdSupPbcd", getPcdSupPbcd())
            .append("pcdSupPorgcd", getPcdSupPorgcd())
            .append("pcdSupPpcd", getPcdSupPpcd())
            .append("pcdSupQty", getPcdSupQty())
            .append("pcdBnEncrypt", getPcdBnEncrypt())
            .toString();
    }
}
