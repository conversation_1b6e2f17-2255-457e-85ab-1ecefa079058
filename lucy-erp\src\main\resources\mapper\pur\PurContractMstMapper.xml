<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.PurContractMstMapper">
    
    <resultMap type="PurContractMst" id="PurContractMstResult">
        <result property="id"    column="id"    />
        <result property="pcmCd"    column="pcm_cd"    />
        <result property="pcmDt"    column="pcm_dt"    />
        <result property="pcmDtfd"    column="pcm_dtFD"    />
        <result property="pcmOcd"    column="pcm_ocd"    />
        <result property="pcmOnm"    column="pcm_onm"    />
        <result property="pcmUcd"    column="pcm_ucd"    />
        <result property="pcmUnm"    column="pcm_unm"    />
        <result property="pcmScd"    column="pcm_scd"    />
        <result property="pcmSnm"    column="pcm_snm"    />
        <result property="pcmSfp"    column="pcm_sfp"    />
        <result property="pcmSfax"    column="pcm_sfax"    />
        <result property="pcmLmcd"    column="pcm_lmcd"    />
        <result property="pcmLmnm"    column="pcm_lmnm"    />
        <result property="pcmLmmp"    column="pcm_lmmp"    />
        <result property="pcmTotamt"    column="pcm_totamt"    />
        <result property="pcmPreamt"    column="pcm_preamt"    />
        <result property="pcmInvtyp"    column="pcm_invtyp"    />
        <result property="pcmPaytyp"    column="pcm_paytyp"    />
        <result property="pcmPaydetailed"    column="pcm_paydetailed"    />
        <result property="pcmPtyp"    column="pcm_ptyp"    />
        <result property="pcmTransport"    column="pcm_transport"    />
        <result property="pcmEadt"    column="pcm_eadt"    />
        <result property="pcmRmk"    column="pcm_rmk"    />
        <result property="pcmDcd"    column="pcm_dcd"    />
        <result property="pcmStscd"    column="pcm_stscd"    />
        <result property="pcmStsnm"    column="pcm_stsnm"    />
        <result property="pcmCurrency"    column="pcm_currency"    />
        <result property="pcmGeneration"    column="pcm_generation"    />
        <result property="pcmExchange"    column="pcm_exchange"    />
        <result property="pcmHuidan"    column="pcm_huidan"    />
        <result property="pcmExce"    column="pcm_Exce"    />
        <result property="pcmStraight"    column="pcm_straight"    />
        <result property="pcmIcPicurl"    column="pcm_ic_picurl"    />
        <result property="pcmUseVerify"    column="pcm_use_verify"    />
        <result property="pcmLicence"    column="pcm_licence"    />
        <result property="pcmPurCard"    column="pcm_pur_card"    />
        <result property="pcmPurPdf"    column="pcm_pur_pdf"    />
        <result property="pcmInvoicePdf"    column="pcm_invoice_pdf"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="pcmDemander"    column="pcm_demander"    />
        <result property="pcmAqlyn"    column="pcm_aqlYN"    />
        <result property="pcmInvoiceyn"    column="pcm_invoiceYN"    />
        <result property="pcmDdyn"    column="pcm_ddYN"    />
        <result property="pcmExcelNm"    column="pcm_excel_nm"    />
        <result property="pcmIsImportPur"    column="pcm_is_import_pur"    />
        <result property="pcmExportDmcd"    column="pcm_export_dmcd"    />
        <result property="pcmQumethod"    column="pcm_quMethod"    />
    </resultMap>

    <sql id="selectPurContractMstVo">
        select id, pcm_cd, pcm_dt, pcm_dtFD, pcm_ocd, pcm_onm, pcm_ucd, pcm_unm, pcm_scd, pcm_snm, pcm_sfp, pcm_sfax, pcm_lmcd, pcm_lmnm, pcm_lmmp, pcm_totamt, pcm_preamt, pcm_invtyp, pcm_paytyp, pcm_paydetailed, pcm_ptyp, pcm_transport, pcm_eadt, pcm_rmk, pcm_dcd, pcm_stscd, pcm_stsnm, pcm_currency, pcm_generation, pcm_exchange, pcm_huidan, pcm_Exce, pcm_straight, pcm_ic_picurl, pcm_use_verify, pcm_licence, pcm_pur_card, pcm_pur_pdf, pcm_invoice_pdf, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, pcm_demander, pcm_aqlYN, pcm_invoiceYN, pcm_ddYN, pcm_excel_nm, pcm_is_import_pur, pcm_export_dmcd, pcm_quMethod from pur_contract_mst
    </sql>

    <select id="selectPurContractMstList" parameterType="PurContractMst" resultMap="PurContractMstResult">
        <include refid="selectPurContractMstVo"/>
        <where>  
            <if test="pcmDt != null  and pcmDt != ''"> and pcm_dt = #{pcmDt}</if>
            <if test="pcmCd != null  and pcmCd != ''"> and pcm_cd = #{pcmCd}</if>
            <if test="pcmOcd != null  and pcmOcd != ''"> and pcm_ocd = #{pcmOcd}</if>
            <if test="pcmScd != null  and pcmScd != ''"> and pcm_scd = #{pcmScd}</if>
        </where>
    </select>
    
    <select id="selectPurContractMstByPcmCd" parameterType="String" resultMap="PurContractMstResult">
        <include refid="selectPurContractMstVo"/>
        where pcm_cd = #{pcmCd}
    </select>
    <select id="selectPurContractMstListR" resultType="String">
        SELECT pcm_cd
        FROM pur_contract_mst
        where
          pcm_scd = 'SP1001170'
          AND pcm_ocd = 'BM10000050'
          AND pcm_cd LIKE "PO%"
    </select>

    <insert id="insertPurContractMst" parameterType="PurContractMst">
        insert into pur_contract_mst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pcmCd != null">pcm_cd,</if>
            <if test="pcmDt != null">pcm_dt,</if>
            <if test="pcmDtfd != null">pcm_dtFD,</if>
            <if test="pcmOcd != null">pcm_ocd,</if>
            <if test="pcmOnm != null">pcm_onm,</if>
            <if test="pcmUcd != null">pcm_ucd,</if>
            <if test="pcmUnm != null">pcm_unm,</if>
            <if test="pcmScd != null">pcm_scd,</if>
            <if test="pcmSnm != null">pcm_snm,</if>
            <if test="pcmSfp != null">pcm_sfp,</if>
            <if test="pcmSfax != null">pcm_sfax,</if>
            <if test="pcmLmcd != null">pcm_lmcd,</if>
            <if test="pcmLmnm != null">pcm_lmnm,</if>
            <if test="pcmLmmp != null">pcm_lmmp,</if>
            <if test="pcmTotamt != null">pcm_totamt,</if>
            <if test="pcmPreamt != null">pcm_preamt,</if>
            <if test="pcmInvtyp != null">pcm_invtyp,</if>
            <if test="pcmPaytyp != null">pcm_paytyp,</if>
            <if test="pcmPaydetailed != null">pcm_paydetailed,</if>
            <if test="pcmPtyp != null">pcm_ptyp,</if>
            <if test="pcmTransport != null">pcm_transport,</if>
            <if test="pcmEadt != null">pcm_eadt,</if>
            <if test="pcmRmk != null">pcm_rmk,</if>
            <if test="pcmDcd != null">pcm_dcd,</if>
            <if test="pcmStscd != null">pcm_stscd,</if>
            <if test="pcmStsnm != null">pcm_stsnm,</if>
            <if test="pcmCurrency != null">pcm_currency,</if>
            <if test="pcmGeneration != null">pcm_generation,</if>
            <if test="pcmExchange != null">pcm_exchange,</if>
            <if test="pcmHuidan != null">pcm_huidan,</if>
            <if test="pcmExce != null">pcm_Exce,</if>
            <if test="pcmStraight != null">pcm_straight,</if>
            <if test="pcmIcPicurl != null">pcm_ic_picurl,</if>
            <if test="pcmUseVerify != null">pcm_use_verify,</if>
            <if test="pcmLicence != null">pcm_licence,</if>
            <if test="pcmPurCard != null">pcm_pur_card,</if>
            <if test="pcmPurPdf != null">pcm_pur_pdf,</if>
            <if test="pcmInvoicePdf != null">pcm_invoice_pdf,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="pcmDemander != null">pcm_demander,</if>
            <if test="pcmAqlyn != null">pcm_aqlYN,</if>
            <if test="pcmInvoiceyn != null">pcm_invoiceYN,</if>
            <if test="pcmDdyn != null">pcm_ddYN,</if>
            <if test="pcmExcelNm != null">pcm_excel_nm,</if>
            <if test="pcmIsImportPur != null">pcm_is_import_pur,</if>
            <if test="pcmExportDmcd != null">pcm_export_dmcd,</if>
            <if test="pcmQumethod != null">pcm_quMethod,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pcmCd != null">#{pcmCd},</if>
            <if test="pcmDt != null">#{pcmDt},</if>
            <if test="pcmDtfd != null">#{pcmDtfd},</if>
            <if test="pcmOcd != null">#{pcmOcd},</if>
            <if test="pcmOnm != null">#{pcmOnm},</if>
            <if test="pcmUcd != null">#{pcmUcd},</if>
            <if test="pcmUnm != null">#{pcmUnm},</if>
            <if test="pcmScd != null">#{pcmScd},</if>
            <if test="pcmSnm != null">#{pcmSnm},</if>
            <if test="pcmSfp != null">#{pcmSfp},</if>
            <if test="pcmSfax != null">#{pcmSfax},</if>
            <if test="pcmLmcd != null">#{pcmLmcd},</if>
            <if test="pcmLmnm != null">#{pcmLmnm},</if>
            <if test="pcmLmmp != null">#{pcmLmmp},</if>
            <if test="pcmTotamt != null">#{pcmTotamt},</if>
            <if test="pcmPreamt != null">#{pcmPreamt},</if>
            <if test="pcmInvtyp != null">#{pcmInvtyp},</if>
            <if test="pcmPaytyp != null">#{pcmPaytyp},</if>
            <if test="pcmPaydetailed != null">#{pcmPaydetailed},</if>
            <if test="pcmPtyp != null">#{pcmPtyp},</if>
            <if test="pcmTransport != null">#{pcmTransport},</if>
            <if test="pcmEadt != null">#{pcmEadt},</if>
            <if test="pcmRmk != null">#{pcmRmk},</if>
            <if test="pcmDcd != null">#{pcmDcd},</if>
            <if test="pcmStscd != null">#{pcmStscd},</if>
            <if test="pcmStsnm != null">#{pcmStsnm},</if>
            <if test="pcmCurrency != null">#{pcmCurrency},</if>
            <if test="pcmGeneration != null">#{pcmGeneration},</if>
            <if test="pcmExchange != null">#{pcmExchange},</if>
            <if test="pcmHuidan != null">#{pcmHuidan},</if>
            <if test="pcmExce != null">#{pcmExce},</if>
            <if test="pcmStraight != null">#{pcmStraight},</if>
            <if test="pcmIcPicurl != null">#{pcmIcPicurl},</if>
            <if test="pcmUseVerify != null">#{pcmUseVerify},</if>
            <if test="pcmLicence != null">#{pcmLicence},</if>
            <if test="pcmPurCard != null">#{pcmPurCard},</if>
            <if test="pcmPurPdf != null">#{pcmPurPdf},</if>
            <if test="pcmInvoicePdf != null">#{pcmInvoicePdf},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="pcmDemander != null">#{pcmDemander},</if>
            <if test="pcmAqlyn != null">#{pcmAqlyn},</if>
            <if test="pcmInvoiceyn != null">#{pcmInvoiceyn},</if>
            <if test="pcmDdyn != null">#{pcmDdyn},</if>
            <if test="pcmExcelNm != null">#{pcmExcelNm},</if>
            <if test="pcmIsImportPur != null">#{pcmIsImportPur},</if>
            <if test="pcmExportDmcd != null">#{pcmExportDmcd},</if>
            <if test="pcmQumethod != null">#{pcmQumethod},</if>
         </trim>
    </insert>

    <update id="updatePurContractMst" parameterType="PurContractMst">
        update pur_contract_mst
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="pcmDt != null">pcm_dt = #{pcmDt},</if>
            <if test="pcmDtfd != null">pcm_dtFD = #{pcmDtfd},</if>
            <if test="pcmOcd != null">pcm_ocd = #{pcmOcd},</if>
            <if test="pcmOnm != null">pcm_onm = #{pcmOnm},</if>
            <if test="pcmUcd != null">pcm_ucd = #{pcmUcd},</if>
            <if test="pcmUnm != null">pcm_unm = #{pcmUnm},</if>
            <if test="pcmScd != null">pcm_scd = #{pcmScd},</if>
            <if test="pcmSnm != null">pcm_snm = #{pcmSnm},</if>
            <if test="pcmSfp != null">pcm_sfp = #{pcmSfp},</if>
            <if test="pcmSfax != null">pcm_sfax = #{pcmSfax},</if>
            <if test="pcmLmcd != null">pcm_lmcd = #{pcmLmcd},</if>
            <if test="pcmLmnm != null">pcm_lmnm = #{pcmLmnm},</if>
            <if test="pcmLmmp != null">pcm_lmmp = #{pcmLmmp},</if>
            <if test="pcmTotamt != null">pcm_totamt = #{pcmTotamt},</if>
            <if test="pcmPreamt != null">pcm_preamt = #{pcmPreamt},</if>
            <if test="pcmInvtyp != null">pcm_invtyp = #{pcmInvtyp},</if>
            <if test="pcmPaytyp != null">pcm_paytyp = #{pcmPaytyp},</if>
            <if test="pcmPaydetailed != null">pcm_paydetailed = #{pcmPaydetailed},</if>
            <if test="pcmPtyp != null">pcm_ptyp = #{pcmPtyp},</if>
            <if test="pcmTransport != null">pcm_transport = #{pcmTransport},</if>
            <if test="pcmEadt != null">pcm_eadt = #{pcmEadt},</if>
            <if test="pcmRmk != null">pcm_rmk = #{pcmRmk},</if>
            <if test="pcmDcd != null">pcm_dcd = #{pcmDcd},</if>
            <if test="pcmStscd != null">pcm_stscd = #{pcmStscd},</if>
            <if test="pcmStsnm != null">pcm_stsnm = #{pcmStsnm},</if>
            <if test="pcmCurrency != null">pcm_currency = #{pcmCurrency},</if>
            <if test="pcmGeneration != null">pcm_generation = #{pcmGeneration},</if>
            <if test="pcmExchange != null">pcm_exchange = #{pcmExchange},</if>
            <if test="pcmHuidan != null">pcm_huidan = #{pcmHuidan},</if>
            <if test="pcmExce != null">pcm_Exce = #{pcmExce},</if>
            <if test="pcmStraight != null">pcm_straight = #{pcmStraight},</if>
            <if test="pcmIcPicurl != null">pcm_ic_picurl = #{pcmIcPicurl},</if>
            <if test="pcmUseVerify != null">pcm_use_verify = #{pcmUseVerify},</if>
            <if test="pcmLicence != null">pcm_licence = #{pcmLicence},</if>
            <if test="pcmPurCard != null">pcm_pur_card = #{pcmPurCard},</if>
            <if test="pcmPurPdf != null">pcm_pur_pdf = #{pcmPurPdf},</if>
            <if test="pcmInvoicePdf != null">pcm_invoice_pdf = #{pcmInvoicePdf},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="pcmDemander != null">pcm_demander = #{pcmDemander},</if>
            <if test="pcmAqlyn != null">pcm_aqlYN = #{pcmAqlyn},</if>
            <if test="pcmInvoiceyn != null">pcm_invoiceYN = #{pcmInvoiceyn},</if>
            <if test="pcmDdyn != null">pcm_ddYN = #{pcmDdyn},</if>
            <if test="pcmExcelNm != null">pcm_excel_nm = #{pcmExcelNm},</if>
            <if test="pcmIsImportPur != null">pcm_is_import_pur = #{pcmIsImportPur},</if>
            <if test="pcmExportDmcd != null">pcm_export_dmcd = #{pcmExportDmcd},</if>
            <if test="pcmQumethod != null">pcm_quMethod = #{pcmQumethod},</if>
        </trim>
        where pcm_cd = #{pcmCd}
    </update>

    <delete id="deletePurContractMstByPcmCd" parameterType="String">
        delete from pur_contract_mst where pcm_cd = #{pcmCd}
    </delete>

    <delete id="deletePurContractMstByPcmCds" parameterType="String">
        delete from pur_contract_mst where pcm_cd in 
        <foreach item="pcmCd" collection="array" open="(" separator="," close=")">
            #{pcmCd}
        </foreach>
    </delete>
</mapper>