package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.SalesContractMst;
import com.lucy.erp.service.ISalesContractMstService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 销售合同主表Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/sales/sales")
public class SalesContractMstController extends BaseController
{
    @Autowired
    private ISalesContractMstService salesContractMstService;

    /**
     * 查询销售合同主表列表
     */
    @PreAuthorize("@ss.hasPermi('sales:sales:list')")
    @GetMapping("/list")
    public TableDataInfo list(SalesContractMst salesContractMst)
    {
        startPage();
        List<SalesContractMst> list = salesContractMstService.selectSalesContractMstList(salesContractMst);
        return getDataTable(list);
    }

    /**
     * 导出销售合同主表列表
     */
    @PreAuthorize("@ss.hasPermi('sales:sales:export')")
    @Log(title = "销售合同主表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SalesContractMst salesContractMst)
    {
        List<SalesContractMst> list = salesContractMstService.selectSalesContractMstList(salesContractMst);
        ExcelUtil<SalesContractMst> util = new ExcelUtil<SalesContractMst>(SalesContractMst.class);
        util.exportExcel(response, list, "销售合同主表数据");
    }

    /**
     * 获取销售合同主表详细信息
     */
    @PreAuthorize("@ss.hasPermi('sales:sales:query')")
    @GetMapping(value = "/{scmCd}")
    public AjaxResult getInfo(@PathVariable("scmCd") String scmCd)
    {
        return success(salesContractMstService.selectSalesContractMstByScmCd(scmCd));
    }

    /**
     * 新增销售合同主表
     */
    @PreAuthorize("@ss.hasPermi('sales:sales:add')")
    @Log(title = "销售合同主表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SalesContractMst salesContractMst)
    {
        return toAjax(salesContractMstService.insertSalesContractMst(salesContractMst));
    }

    /**
     * 修改销售合同主表
     */
    @PreAuthorize("@ss.hasPermi('sales:sales:edit')")
    @Log(title = "销售合同主表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SalesContractMst salesContractMst)
    {
        return toAjax(salesContractMstService.updateSalesContractMst(salesContractMst));
    }

    /**
     * 删除销售合同主表
     */
    @PreAuthorize("@ss.hasPermi('sales:sales:remove')")
    @Log(title = "销售合同主表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scmCds}")
    public AjaxResult remove(@PathVariable String[] scmCds)
    {
        return toAjax(salesContractMstService.deleteSalesContractMstByScmCds(scmCds));
    }
}
