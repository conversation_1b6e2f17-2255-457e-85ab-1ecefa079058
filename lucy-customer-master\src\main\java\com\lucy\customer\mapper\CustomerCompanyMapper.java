package com.lucy.customer.mapper;

import java.util.List;
import com.lucy.customer.domain.CustomerCompany;

/**
 * 部门组织结构 Department groupsMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface CustomerCompanyMapper 
{
    /**
     * 查询部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 部门组织结构 Department groups
     */
    public CustomerCompany selectCustomerCompanyByCode(String code);

    /**
     * 查询部门组织结构 Department groups列表
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 部门组织结构 Department groups集合
     */
    public List<CustomerCompany> selectCustomerCompanyList(CustomerCompany customerCompany);

    /**
     * 新增部门组织结构 Department groups
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 结果
     */
    public int insertCustomerCompany(CustomerCompany customerCompany);

    /**
     * 修改部门组织结构 Department groups
     * 
     * @param customerCompany 部门组织结构 Department groups
     * @return 结果
     */
    public int updateCustomerCompany(CustomerCompany customerCompany);

    /**
     * 删除部门组织结构 Department groups
     * 
     * @param code 部门组织结构 Department groups主键
     * @return 结果
     */
    public int deleteCustomerCompanyByCode(String code);

    /**
     * 批量删除部门组织结构 Department groups
     * 
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerCompanyByCodes(String[] codes);
}
