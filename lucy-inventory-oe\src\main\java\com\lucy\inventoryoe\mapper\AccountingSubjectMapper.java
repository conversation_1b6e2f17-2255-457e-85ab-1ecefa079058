package com.lucy.inventoryoe.mapper;

import java.util.List;
import com.lucy.inventoryoe.domain.AccountingSubject;

/**
 * 会计科目 Accounting subject masterMapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface AccountingSubjectMapper 
{
    /**
     * 查询会计科目 Accounting subject master
     * 
     * @param code 会计科目 Accounting subject master主键
     * @return 会计科目 Accounting subject master
     */
    public AccountingSubject selectAccountingSubjectByCode(String code);

    /**
     * 查询会计科目 Accounting subject master列表
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 会计科目 Accounting subject master集合
     */
    public List<AccountingSubject> selectAccountingSubjectList(AccountingSubject accountingSubject);

    /**
     * 新增会计科目 Accounting subject master
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 结果
     */
    public int insertAccountingSubject(AccountingSubject accountingSubject);

    /**
     * 修改会计科目 Accounting subject master
     * 
     * @param accountingSubject 会计科目 Accounting subject master
     * @return 结果
     */
    public int updateAccountingSubject(AccountingSubject accountingSubject);

    /**
     * 删除会计科目 Accounting subject master
     * 
     * @param code 会计科目 Accounting subject master主键
     * @return 结果
     */
    public int deleteAccountingSubjectByCode(String code);

    /**
     * 批量删除会计科目 Accounting subject master
     * 
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAccountingSubjectByCodes(String[] codes);
    /**
     * 查询会计科目 Accounting subject master列表
     *
     * @param faiSubject 会计科目 Accounting subject master
     * @return 会计科目 Accounting subject master集合
     */
    public AccountingSubject selectAccountingSubjectByName(String faiSubject);
}
