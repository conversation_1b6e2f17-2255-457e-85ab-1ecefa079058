package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogOsPlanMapper;
import com.lucy.erp.domain.LogOsPlan;
import com.lucy.erp.service.ILogOsPlanService;

/**
 * 物流_出库_计划
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogOsPlanServiceImpl implements ILogOsPlanService 
{
    @Autowired
    private LogOsPlanMapper logOsPlanMapper;

    /**
     * 查询物流_出库_计划

     * 
     * @param id 物流_出库_计划
主键
     * @return 物流_出库_计划

     */
    @Override
    public LogOsPlan selectLogOsPlanById(String id)
    {
        return logOsPlanMapper.selectLogOsPlanById(id);
    }

    /**
     * 查询物流_出库_计划
列表
     * 
     * @param logOsPlan 物流_出库_计划

     * @return 物流_出库_计划

     */
    @Override
    public List<LogOsPlan> selectLogOsPlanList(LogOsPlan logOsPlan)
    {
        return logOsPlanMapper.selectLogOsPlanList(logOsPlan);
    }

    /**
     * 新增物流_出库_计划

     * 
     * @param logOsPlan 物流_出库_计划

     * @return 结果
     */
    @Override
    public int insertLogOsPlan(LogOsPlan logOsPlan)
    {
        return logOsPlanMapper.insertLogOsPlan(logOsPlan);
    }

    /**
     * 修改物流_出库_计划

     * 
     * @param logOsPlan 物流_出库_计划

     * @return 结果
     */
    @Override
    public int updateLogOsPlan(LogOsPlan logOsPlan)
    {
        return logOsPlanMapper.updateLogOsPlan(logOsPlan);
    }

    /**
     * 批量删除物流_出库_计划

     * 
     * @param ids 需要删除的物流_出库_计划
主键
     * @return 结果
     */
    @Override
    public int deleteLogOsPlanByIds(Long[] ids)
    {
        return logOsPlanMapper.deleteLogOsPlanByIds(ids);
    }

    /**
     * 删除物流_出库_计划
信息
     * 
     * @param id 物流_出库_计划
主键
     * @return 结果
     */
    @Override
    public int deleteLogOsPlanById(Long id)
    {
        return logOsPlanMapper.deleteLogOsPlanById(id);
    }
}
