<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasCustorgMstMapper">
    
    <resultMap type="BasCustorgMst" id="BasCustorgMstResult">
        <result property="id"    column="id"    />
        <result property="bcmCd"    column="bcm_cd"    />
        <result property="bcmNm"    column="bcm_nm"    />
        <result property="bcmFnm"    column="bcm_fnm"    />
        <result property="bcmNmabb"    column="bcm_nmabb"    />
        <result property="bcmXyz"    column="bcm_xyz"    />
        <result property="bcmPcd"    column="bcm_pcd"    />
        <result property="bcmTyp"    column="bcm_typ"    />
        <result property="bcmLvl"    column="bcm_lvl"    />
        <result property="bcmFp"    column="bcm_fp"    />
        <result property="bcmMp"    column="bcm_mp"    />
        <result property="bcmFax"    column="bcm_fax"    />
        <result property="bcmPrcd"    column="bcm_prcd"    />
        <result property="bcmPrnm"    column="bcm_prnm"    />
        <result property="bcmCitycd"    column="bcm_citycd"    />
        <result property="bcmCity"    column="bcm_city"    />
        <result property="bcmAreacd"    column="bcm_areacd"    />
        <result property="bcmAreanm"    column="bcm_areanm"    />
        <result property="bcmJdcd"    column="bcm_jdcd"    />
        <result property="bcmJdnm"    column="bcm_jdnm"    />
        <result property="bcmInvcMan"    column="bcm_invc_man"    />
        <result property="bcmAddr"    column="bcm_addr"    />
        <result property="bcmSmcd"    column="bcm_smcd"    />
        <result property="bcmSmcd3a"    column="bcm_smcd_3a"    />
        <result property="bcmDc"    column="bcm_dc"    />
        <result property="bcmFstOrgcd"    column="bcm_fst_orgcd"    />
        <result property="bcmAreaCd"    column="bcm_area_cd"    />
        <result property="bcmPaytyp"    column="bcm_paytyp"    />
        <result property="bcmSendyn"    column="bcm_sendyn"    />
        <result property="bcmIseps"    column="bcm_iseps"    />
        <result property="bcmSmsyn"    column="bcm_smsyn"    />
        <result property="bcmCoayn"    column="bcm_coayn"    />
        <result property="bcmInvoiceyn"    column="bcm_invoiceyn"    />
        <result property="bcmInvoicexe"    column="bcm_invoicexe"    />
        <result property="bcmBarcode"    column="bcm_barcode"    />
        <result property="bcmPrintyn"    column="bcm_printyn"    />
        <result property="bcmLrmk"    column="bcm_lrmk"    />
        <result property="bcmMbgCd"    column="bcm_mbg_cd"    />
        <result property="bcmIsleaf"    column="bcm_isleaf"    />
        <result property="bcmFinanceCode"    column="bcm_finance_code"    />
        <result property="3aBcmCd"    column="3a_bcm_cd"    />
        <result property="bcmPscd"    column="bcm_pscd"    />
        <result property="bcmOrgcd"    column="bcm_orgcd"    />
        <result property="bcmPscdTwo"    column="bcm_pscd_two"    />
        <result property="bcmDcls"    column="bcm_dcls"    />
        <result property="bcmOcd"    column="bcm_ocd"    />
        <result property="bcmRank"    column="bcm_rank"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="bcmAccredit"    column="bcm_accredit"    />
        <result property="bcmCac"    column="bcm_cac"    />
        <result property="bcmInvoiceCac"    column="bcm_invoice_cac"    />
        <result property="bcmBlaList"    column="bcm_bla_list"    />
        <result property="bcmBuyExprs"    column="bcm_buy_exprs"    />
        <result property="bcmInvcrmk"    column="bcm_invcrmk"    />
        <result property="bcmThirdBoard"    column="bcm_third_board"    />
        <result property="bcmQuotaLimit"    column="bcm_quota_limit"    />
        <result property="bcmConAgent"    column="bcm_con_agent"    />
        <result property="bcmCurrency"    column="bcm_currency"    />
        <result property="bcmBccd"    column="bcm_bccd"    />
        <result property="bcmNationcd"    column="bcm_nationcd"    />
        <result property="bcmNationnm"    column="bcm_nationnm"    />
        <result property="bcmClientgrade"    column="bcm_clientgrade"    />
    </resultMap>

    <sql id="selectBasCustorgMstVo">
        select id, bcm_cd, bcm_nm, bcm_fnm, bcm_nmabb, bcm_xyz, bcm_pcd, bcm_typ, bcm_lvl, bcm_fp, bcm_mp, bcm_fax, bcm_prcd, bcm_prnm, bcm_citycd, bcm_city, bcm_areacd, bcm_areanm, bcm_jdcd, bcm_jdnm, bcm_invc_man, bcm_addr, bcm_smcd, bcm_smcd_3a, bcm_dc, bcm_fst_orgcd, bcm_area_cd, bcm_paytyp, bcm_sendyn, bcm_iseps, bcm_smsyn, bcm_coayn, bcm_invoiceyn, bcm_invoicexe, bcm_barcode, bcm_printyn, bcm_lrmk, bcm_mbg_cd, bcm_isleaf, bcm_finance_code, 3a_bcm_cd, bcm_pscd, bcm_orgcd, bcm_pscd_two, bcm_dcls, bcm_ocd, bcm_rank, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, bcm_accredit, bcm_cac, bcm_invoice_cac, bcm_bla_list, bcm_buy_exprs, bcm_invcrmk, bcm_third_board, bcm_quota_limit, bcm_con_agent, bcm_currency, bcm_bccd, bcm_nationcd, bcm_nationnm, bcm_clientgrade from bas_custorg_mst
    </sql>

    <select id="selectBasCustorgMstList" parameterType="BasCustorgMst" resultMap="BasCustorgMstResult">
        <include refid="selectBasCustorgMstVo"/>
        <where>  
            <if test="bcmNm != null  and bcmNm != ''"> and bcm_nm = #{bcmNm}</if>
            <if test="bcmFnm != null  and bcmFnm != ''"> and bcm_fnm = #{bcmFnm}</if>
            <if test="bcmNmabb != null  and bcmNmabb != ''"> and bcm_nmabb = #{bcmNmabb}</if>
            <if test="bcmXyz != null  and bcmXyz != ''"> and bcm_xyz = #{bcmXyz}</if>
            <if test="bcmPcd != null  and bcmPcd != ''"> and bcm_pcd = #{bcmPcd}</if>
            <if test="bcmTyp != null  and bcmTyp != ''"> and bcm_typ = #{bcmTyp}</if>
            <if test="bcmLvl != null  and bcmLvl != ''"> and bcm_lvl = #{bcmLvl}</if>
            <if test="bcmFp != null  and bcmFp != ''"> and bcm_fp = #{bcmFp}</if>
            <if test="bcmMp != null  and bcmMp != ''"> and bcm_mp = #{bcmMp}</if>
            <if test="bcmFax != null  and bcmFax != ''"> and bcm_fax = #{bcmFax}</if>
            <if test="bcmPrcd != null  and bcmPrcd != ''"> and bcm_prcd = #{bcmPrcd}</if>
            <if test="bcmPrnm != null  and bcmPrnm != ''"> and bcm_prnm = #{bcmPrnm}</if>
            <if test="bcmCitycd != null  and bcmCitycd != ''"> and bcm_citycd = #{bcmCitycd}</if>
            <if test="bcmCity != null  and bcmCity != ''"> and bcm_city = #{bcmCity}</if>
            <if test="bcmAreacd != null  and bcmAreacd != ''"> and bcm_areacd = #{bcmAreacd}</if>
            <if test="bcmAreanm != null  and bcmAreanm != ''"> and bcm_areanm = #{bcmAreanm}</if>
            <if test="bcmJdcd != null  and bcmJdcd != ''"> and bcm_jdcd = #{bcmJdcd}</if>
            <if test="bcmJdnm != null  and bcmJdnm != ''"> and bcm_jdnm = #{bcmJdnm}</if>
            <if test="bcmInvcMan != null  and bcmInvcMan != ''"> and bcm_invc_man = #{bcmInvcMan}</if>
            <if test="bcmAddr != null  and bcmAddr != ''"> and bcm_addr = #{bcmAddr}</if>
            <if test="bcmSmcd != null  and bcmSmcd != ''"> and bcm_smcd = #{bcmSmcd}</if>
            <if test="bcmSmcd3a != null  and bcmSmcd3a != ''"> and bcm_smcd_3a = #{bcmSmcd3a}</if>
            <if test="bcmDc != null "> and bcm_dc = #{bcmDc}</if>
            <if test="bcmFstOrgcd != null  and bcmFstOrgcd != ''"> and bcm_fst_orgcd = #{bcmFstOrgcd}</if>
            <if test="bcmAreaCd != null  and bcmAreaCd != ''"> and bcm_area_cd = #{bcmAreaCd}</if>
            <if test="bcmPaytyp != null  and bcmPaytyp != ''"> and bcm_paytyp = #{bcmPaytyp}</if>
            <if test="bcmSendyn != null  and bcmSendyn != ''"> and bcm_sendyn = #{bcmSendyn}</if>
            <if test="bcmIseps != null  and bcmIseps != ''"> and bcm_iseps = #{bcmIseps}</if>
            <if test="bcmSmsyn != null  and bcmSmsyn != ''"> and bcm_smsyn = #{bcmSmsyn}</if>
            <if test="bcmCoayn != null  and bcmCoayn != ''"> and bcm_coayn = #{bcmCoayn}</if>
            <if test="bcmInvoiceyn != null  and bcmInvoiceyn != ''"> and bcm_invoiceyn = #{bcmInvoiceyn}</if>
            <if test="bcmInvoicexe != null "> and bcm_invoicexe = #{bcmInvoicexe}</if>
            <if test="bcmBarcode != null  and bcmBarcode != ''"> and bcm_barcode = #{bcmBarcode}</if>
            <if test="bcmPrintyn != null  and bcmPrintyn != ''"> and bcm_printyn = #{bcmPrintyn}</if>
            <if test="bcmLrmk != null  and bcmLrmk != ''"> and bcm_lrmk = #{bcmLrmk}</if>
            <if test="bcmMbgCd != null  and bcmMbgCd != ''"> and bcm_mbg_cd = #{bcmMbgCd}</if>
            <if test="bcmIsleaf != null "> and bcm_isleaf = #{bcmIsleaf}</if>
            <if test="bcmFinanceCode != null  and bcmFinanceCode != ''"> and bcm_finance_code = #{bcmFinanceCode}</if>
            <if test="3aBcmCd != null  and 3aBcmCd != ''"> and 3a_bcm_cd = #{3aBcmCd}</if>
            <if test="bcmPscd != null  and bcmPscd != ''"> and bcm_pscd = #{bcmPscd}</if>
            <if test="bcmOrgcd != null  and bcmOrgcd != ''"> and bcm_orgcd = #{bcmOrgcd}</if>
            <if test="bcmPscdTwo != null  and bcmPscdTwo != ''"> and bcm_pscd_two = #{bcmPscdTwo}</if>
            <if test="bcmDcls != null  and bcmDcls != ''"> and bcm_dcls = #{bcmDcls}</if>
            <if test="bcmOcd != null  and bcmOcd != ''"> and bcm_ocd = #{bcmOcd}</if>
            <if test="bcmRank != null  and bcmRank != ''"> and bcm_rank = #{bcmRank}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="bcmAccredit != null  and bcmAccredit != ''"> and bcm_accredit = #{bcmAccredit}</if>
            <if test="bcmCac != null "> and bcm_cac = #{bcmCac}</if>
            <if test="bcmInvoiceCac != null "> and bcm_invoice_cac = #{bcmInvoiceCac}</if>
            <if test="bcmBlaList != null  and bcmBlaList != ''"> and bcm_bla_list = #{bcmBlaList}</if>
            <if test="bcmBuyExprs != null  and bcmBuyExprs != ''"> and bcm_buy_exprs = #{bcmBuyExprs}</if>
            <if test="bcmInvcrmk != null  and bcmInvcrmk != ''"> and bcm_invcrmk = #{bcmInvcrmk}</if>
            <if test="bcmThirdBoard != null  and bcmThirdBoard != ''"> and bcm_third_board = #{bcmThirdBoard}</if>
            <if test="bcmQuotaLimit != null  and bcmQuotaLimit != ''"> and bcm_quota_limit = #{bcmQuotaLimit}</if>
            <if test="bcmConAgent != null  and bcmConAgent != ''"> and bcm_con_agent = #{bcmConAgent}</if>
            <if test="bcmCurrency != null  and bcmCurrency != ''"> and bcm_currency = #{bcmCurrency}</if>
            <if test="bcmBccd != null  and bcmBccd != ''"> and bcm_bccd = #{bcmBccd}</if>
            <if test="bcmNationcd != null  and bcmNationcd != ''"> and bcm_nationcd = #{bcmNationcd}</if>
            <if test="bcmNationnm != null  and bcmNationnm != ''"> and bcm_nationnm = #{bcmNationnm}</if>
            <if test="bcmClientgrade != null  and bcmClientgrade != ''"> and bcm_clientgrade = #{bcmClientgrade}</if>
        </where>
    </select>
    
    <select id="selectBasCustorgMstByBcmCd" parameterType="String" resultMap="BasCustorgMstResult">
        <include refid="selectBasCustorgMstVo"/>
        where bcm_cd = #{bcmCd}
    </select>

    <insert id="insertBasCustorgMst" parameterType="BasCustorgMst">
        insert into bas_custorg_mst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bcmCd != null">bcm_cd,</if>
            <if test="bcmNm != null">bcm_nm,</if>
            <if test="bcmFnm != null">bcm_fnm,</if>
            <if test="bcmNmabb != null">bcm_nmabb,</if>
            <if test="bcmXyz != null">bcm_xyz,</if>
            <if test="bcmPcd != null">bcm_pcd,</if>
            <if test="bcmTyp != null">bcm_typ,</if>
            <if test="bcmLvl != null">bcm_lvl,</if>
            <if test="bcmFp != null">bcm_fp,</if>
            <if test="bcmMp != null">bcm_mp,</if>
            <if test="bcmFax != null">bcm_fax,</if>
            <if test="bcmPrcd != null">bcm_prcd,</if>
            <if test="bcmPrnm != null">bcm_prnm,</if>
            <if test="bcmCitycd != null">bcm_citycd,</if>
            <if test="bcmCity != null">bcm_city,</if>
            <if test="bcmAreacd != null">bcm_areacd,</if>
            <if test="bcmAreanm != null">bcm_areanm,</if>
            <if test="bcmJdcd != null">bcm_jdcd,</if>
            <if test="bcmJdnm != null">bcm_jdnm,</if>
            <if test="bcmInvcMan != null">bcm_invc_man,</if>
            <if test="bcmAddr != null">bcm_addr,</if>
            <if test="bcmSmcd != null">bcm_smcd,</if>
            <if test="bcmSmcd3a != null">bcm_smcd_3a,</if>
            <if test="bcmDc != null">bcm_dc,</if>
            <if test="bcmFstOrgcd != null">bcm_fst_orgcd,</if>
            <if test="bcmAreaCd != null">bcm_area_cd,</if>
            <if test="bcmPaytyp != null">bcm_paytyp,</if>
            <if test="bcmSendyn != null">bcm_sendyn,</if>
            <if test="bcmIseps != null">bcm_iseps,</if>
            <if test="bcmSmsyn != null">bcm_smsyn,</if>
            <if test="bcmCoayn != null">bcm_coayn,</if>
            <if test="bcmInvoiceyn != null">bcm_invoiceyn,</if>
            <if test="bcmInvoicexe != null">bcm_invoicexe,</if>
            <if test="bcmBarcode != null">bcm_barcode,</if>
            <if test="bcmPrintyn != null">bcm_printyn,</if>
            <if test="bcmLrmk != null">bcm_lrmk,</if>
            <if test="bcmMbgCd != null">bcm_mbg_cd,</if>
            <if test="bcmIsleaf != null">bcm_isleaf,</if>
            <if test="bcmFinanceCode != null">bcm_finance_code,</if>
            <if test="3aBcmCd != null">3a_bcm_cd,</if>
            <if test="bcmPscd != null">bcm_pscd,</if>
            <if test="bcmOrgcd != null">bcm_orgcd,</if>
            <if test="bcmPscdTwo != null">bcm_pscd_two,</if>
            <if test="bcmDcls != null">bcm_dcls,</if>
            <if test="bcmOcd != null">bcm_ocd,</if>
            <if test="bcmRank != null">bcm_rank,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="bcmAccredit != null">bcm_accredit,</if>
            <if test="bcmCac != null">bcm_cac,</if>
            <if test="bcmInvoiceCac != null">bcm_invoice_cac,</if>
            <if test="bcmBlaList != null">bcm_bla_list,</if>
            <if test="bcmBuyExprs != null">bcm_buy_exprs,</if>
            <if test="bcmInvcrmk != null">bcm_invcrmk,</if>
            <if test="bcmThirdBoard != null">bcm_third_board,</if>
            <if test="bcmQuotaLimit != null">bcm_quota_limit,</if>
            <if test="bcmConAgent != null">bcm_con_agent,</if>
            <if test="bcmCurrency != null">bcm_currency,</if>
            <if test="bcmBccd != null">bcm_bccd,</if>
            <if test="bcmNationcd != null">bcm_nationcd,</if>
            <if test="bcmNationnm != null">bcm_nationnm,</if>
            <if test="bcmClientgrade != null">bcm_clientgrade,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bcmCd != null">#{bcmCd},</if>
            <if test="bcmNm != null">#{bcmNm},</if>
            <if test="bcmFnm != null">#{bcmFnm},</if>
            <if test="bcmNmabb != null">#{bcmNmabb},</if>
            <if test="bcmXyz != null">#{bcmXyz},</if>
            <if test="bcmPcd != null">#{bcmPcd},</if>
            <if test="bcmTyp != null">#{bcmTyp},</if>
            <if test="bcmLvl != null">#{bcmLvl},</if>
            <if test="bcmFp != null">#{bcmFp},</if>
            <if test="bcmMp != null">#{bcmMp},</if>
            <if test="bcmFax != null">#{bcmFax},</if>
            <if test="bcmPrcd != null">#{bcmPrcd},</if>
            <if test="bcmPrnm != null">#{bcmPrnm},</if>
            <if test="bcmCitycd != null">#{bcmCitycd},</if>
            <if test="bcmCity != null">#{bcmCity},</if>
            <if test="bcmAreacd != null">#{bcmAreacd},</if>
            <if test="bcmAreanm != null">#{bcmAreanm},</if>
            <if test="bcmJdcd != null">#{bcmJdcd},</if>
            <if test="bcmJdnm != null">#{bcmJdnm},</if>
            <if test="bcmInvcMan != null">#{bcmInvcMan},</if>
            <if test="bcmAddr != null">#{bcmAddr},</if>
            <if test="bcmSmcd != null">#{bcmSmcd},</if>
            <if test="bcmSmcd3a != null">#{bcmSmcd3a},</if>
            <if test="bcmDc != null">#{bcmDc},</if>
            <if test="bcmFstOrgcd != null">#{bcmFstOrgcd},</if>
            <if test="bcmAreaCd != null">#{bcmAreaCd},</if>
            <if test="bcmPaytyp != null">#{bcmPaytyp},</if>
            <if test="bcmSendyn != null">#{bcmSendyn},</if>
            <if test="bcmIseps != null">#{bcmIseps},</if>
            <if test="bcmSmsyn != null">#{bcmSmsyn},</if>
            <if test="bcmCoayn != null">#{bcmCoayn},</if>
            <if test="bcmInvoiceyn != null">#{bcmInvoiceyn},</if>
            <if test="bcmInvoicexe != null">#{bcmInvoicexe},</if>
            <if test="bcmBarcode != null">#{bcmBarcode},</if>
            <if test="bcmPrintyn != null">#{bcmPrintyn},</if>
            <if test="bcmLrmk != null">#{bcmLrmk},</if>
            <if test="bcmMbgCd != null">#{bcmMbgCd},</if>
            <if test="bcmIsleaf != null">#{bcmIsleaf},</if>
            <if test="bcmFinanceCode != null">#{bcmFinanceCode},</if>
            <if test="3aBcmCd != null">#{3aBcmCd},</if>
            <if test="bcmPscd != null">#{bcmPscd},</if>
            <if test="bcmOrgcd != null">#{bcmOrgcd},</if>
            <if test="bcmPscdTwo != null">#{bcmPscdTwo},</if>
            <if test="bcmDcls != null">#{bcmDcls},</if>
            <if test="bcmOcd != null">#{bcmOcd},</if>
            <if test="bcmRank != null">#{bcmRank},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="bcmAccredit != null">#{bcmAccredit},</if>
            <if test="bcmCac != null">#{bcmCac},</if>
            <if test="bcmInvoiceCac != null">#{bcmInvoiceCac},</if>
            <if test="bcmBlaList != null">#{bcmBlaList},</if>
            <if test="bcmBuyExprs != null">#{bcmBuyExprs},</if>
            <if test="bcmInvcrmk != null">#{bcmInvcrmk},</if>
            <if test="bcmThirdBoard != null">#{bcmThirdBoard},</if>
            <if test="bcmQuotaLimit != null">#{bcmQuotaLimit},</if>
            <if test="bcmConAgent != null">#{bcmConAgent},</if>
            <if test="bcmCurrency != null">#{bcmCurrency},</if>
            <if test="bcmBccd != null">#{bcmBccd},</if>
            <if test="bcmNationcd != null">#{bcmNationcd},</if>
            <if test="bcmNationnm != null">#{bcmNationnm},</if>
            <if test="bcmClientgrade != null">#{bcmClientgrade},</if>
         </trim>
    </insert>

    <update id="updateBasCustorgMst" parameterType="BasCustorgMst">
        update bas_custorg_mst
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="bcmNm != null">bcm_nm = #{bcmNm},</if>
            <if test="bcmFnm != null">bcm_fnm = #{bcmFnm},</if>
            <if test="bcmNmabb != null">bcm_nmabb = #{bcmNmabb},</if>
            <if test="bcmXyz != null">bcm_xyz = #{bcmXyz},</if>
            <if test="bcmPcd != null">bcm_pcd = #{bcmPcd},</if>
            <if test="bcmTyp != null">bcm_typ = #{bcmTyp},</if>
            <if test="bcmLvl != null">bcm_lvl = #{bcmLvl},</if>
            <if test="bcmFp != null">bcm_fp = #{bcmFp},</if>
            <if test="bcmMp != null">bcm_mp = #{bcmMp},</if>
            <if test="bcmFax != null">bcm_fax = #{bcmFax},</if>
            <if test="bcmPrcd != null">bcm_prcd = #{bcmPrcd},</if>
            <if test="bcmPrnm != null">bcm_prnm = #{bcmPrnm},</if>
            <if test="bcmCitycd != null">bcm_citycd = #{bcmCitycd},</if>
            <if test="bcmCity != null">bcm_city = #{bcmCity},</if>
            <if test="bcmAreacd != null">bcm_areacd = #{bcmAreacd},</if>
            <if test="bcmAreanm != null">bcm_areanm = #{bcmAreanm},</if>
            <if test="bcmJdcd != null">bcm_jdcd = #{bcmJdcd},</if>
            <if test="bcmJdnm != null">bcm_jdnm = #{bcmJdnm},</if>
            <if test="bcmInvcMan != null">bcm_invc_man = #{bcmInvcMan},</if>
            <if test="bcmAddr != null">bcm_addr = #{bcmAddr},</if>
            <if test="bcmSmcd != null">bcm_smcd = #{bcmSmcd},</if>
            <if test="bcmSmcd3a != null">bcm_smcd_3a = #{bcmSmcd3a},</if>
            <if test="bcmDc != null">bcm_dc = #{bcmDc},</if>
            <if test="bcmFstOrgcd != null">bcm_fst_orgcd = #{bcmFstOrgcd},</if>
            <if test="bcmAreaCd != null">bcm_area_cd = #{bcmAreaCd},</if>
            <if test="bcmPaytyp != null">bcm_paytyp = #{bcmPaytyp},</if>
            <if test="bcmSendyn != null">bcm_sendyn = #{bcmSendyn},</if>
            <if test="bcmIseps != null">bcm_iseps = #{bcmIseps},</if>
            <if test="bcmSmsyn != null">bcm_smsyn = #{bcmSmsyn},</if>
            <if test="bcmCoayn != null">bcm_coayn = #{bcmCoayn},</if>
            <if test="bcmInvoiceyn != null">bcm_invoiceyn = #{bcmInvoiceyn},</if>
            <if test="bcmInvoicexe != null">bcm_invoicexe = #{bcmInvoicexe},</if>
            <if test="bcmBarcode != null">bcm_barcode = #{bcmBarcode},</if>
            <if test="bcmPrintyn != null">bcm_printyn = #{bcmPrintyn},</if>
            <if test="bcmLrmk != null">bcm_lrmk = #{bcmLrmk},</if>
            <if test="bcmMbgCd != null">bcm_mbg_cd = #{bcmMbgCd},</if>
            <if test="bcmIsleaf != null">bcm_isleaf = #{bcmIsleaf},</if>
            <if test="bcmFinanceCode != null">bcm_finance_code = #{bcmFinanceCode},</if>
            <if test="3aBcmCd != null">3a_bcm_cd = #{3aBcmCd},</if>
            <if test="bcmPscd != null">bcm_pscd = #{bcmPscd},</if>
            <if test="bcmOrgcd != null">bcm_orgcd = #{bcmOrgcd},</if>
            <if test="bcmPscdTwo != null">bcm_pscd_two = #{bcmPscdTwo},</if>
            <if test="bcmDcls != null">bcm_dcls = #{bcmDcls},</if>
            <if test="bcmOcd != null">bcm_ocd = #{bcmOcd},</if>
            <if test="bcmRank != null">bcm_rank = #{bcmRank},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="bcmAccredit != null">bcm_accredit = #{bcmAccredit},</if>
            <if test="bcmCac != null">bcm_cac = #{bcmCac},</if>
            <if test="bcmInvoiceCac != null">bcm_invoice_cac = #{bcmInvoiceCac},</if>
            <if test="bcmBlaList != null">bcm_bla_list = #{bcmBlaList},</if>
            <if test="bcmBuyExprs != null">bcm_buy_exprs = #{bcmBuyExprs},</if>
            <if test="bcmInvcrmk != null">bcm_invcrmk = #{bcmInvcrmk},</if>
            <if test="bcmThirdBoard != null">bcm_third_board = #{bcmThirdBoard},</if>
            <if test="bcmQuotaLimit != null">bcm_quota_limit = #{bcmQuotaLimit},</if>
            <if test="bcmConAgent != null">bcm_con_agent = #{bcmConAgent},</if>
            <if test="bcmCurrency != null">bcm_currency = #{bcmCurrency},</if>
            <if test="bcmBccd != null">bcm_bccd = #{bcmBccd},</if>
            <if test="bcmNationcd != null">bcm_nationcd = #{bcmNationcd},</if>
            <if test="bcmNationnm != null">bcm_nationnm = #{bcmNationnm},</if>
            <if test="bcmClientgrade != null">bcm_clientgrade = #{bcmClientgrade},</if>
        </trim>
        where bcm_cd = #{bcmCd}
    </update>

    <delete id="deleteBasCustorgMstByBcmCd" parameterType="String">
        delete from bas_custorg_mst where bcm_cd = #{bcmCd}
    </delete>

    <delete id="deleteBasCustorgMstByBcmCds" parameterType="String">
        delete from bas_custorg_mst where bcm_cd in 
        <foreach item="bcmCd" collection="array" open="(" separator="," close=")">
            #{bcmCd}
        </foreach>
    </delete>
</mapper>