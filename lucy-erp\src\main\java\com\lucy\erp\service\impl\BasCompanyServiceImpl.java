package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasCompanyMapper;
import com.lucy.erp.domain.BasCompany;
import com.lucy.erp.service.IBasCompanyService;

/**
 * 基础_公司Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasCompanyServiceImpl implements IBasCompanyService 
{
    @Autowired
    private BasCompanyMapper basCompanyMapper;

    /**
     * 查询基础_公司
     * 
     * @param bcCd 基础_公司主键
     * @return 基础_公司
     */
    @Override
    public BasCompany selectBasCompanyByBcCd(String bcCd)
    {
        return basCompanyMapper.selectBasCompanyByBcCd(bcCd);
    }

    /**
     * 查询基础_公司列表
     * 
     * @param basCompany 基础_公司
     * @return 基础_公司
     */
    @Override
    public List<BasCompany> selectBasCompanyList(BasCompany basCompany)
    {
        return basCompanyMapper.selectBasCompanyList(basCompany);
    }

    /**
     * 新增基础_公司
     * 
     * @param basCompany 基础_公司
     * @return 结果
     */
    @Override
    public int insertBasCompany(BasCompany basCompany)
    {
        return basCompanyMapper.insertBasCompany(basCompany);
    }

    /**
     * 修改基础_公司
     * 
     * @param basCompany 基础_公司
     * @return 结果
     */
    @Override
    public int updateBasCompany(BasCompany basCompany)
    {
        return basCompanyMapper.updateBasCompany(basCompany);
    }

    /**
     * 批量删除基础_公司
     * 
     * @param bcCds 需要删除的基础_公司主键
     * @return 结果
     */
    @Override
    public int deleteBasCompanyByBcCds(String[] bcCds)
    {
        return basCompanyMapper.deleteBasCompanyByBcCds(bcCds);
    }

    /**
     * 删除基础_公司信息
     * 
     * @param bcCd 基础_公司主键
     * @return 结果
     */
    @Override
    public int deleteBasCompanyByBcCd(String bcCd)
    {
        return basCompanyMapper.deleteBasCompanyByBcCd(bcCd);
    }
}
