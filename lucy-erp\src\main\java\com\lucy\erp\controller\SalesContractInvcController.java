package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.SalesContractInvc;
import com.lucy.erp.service.ISalesContractInvcService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 销售_合同_发票Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/sales/invc")
public class SalesContractInvcController extends BaseController
{
    @Autowired
    private ISalesContractInvcService salesContractInvcService;

    /**
     * 查询销售_合同_发票列表
     */
    @PreAuthorize("@ss.hasPermi('sales:invc:list')")
    @GetMapping("/list")
    public TableDataInfo list(SalesContractInvc salesContractInvc)
    {
        startPage();
        List<SalesContractInvc> list = salesContractInvcService.selectSalesContractInvcList(salesContractInvc);
        return getDataTable(list);
    }

    /**
     * 导出销售_合同_发票列表
     */
    @PreAuthorize("@ss.hasPermi('sales:invc:export')")
    @Log(title = "销售_合同_发票", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SalesContractInvc salesContractInvc)
    {
        List<SalesContractInvc> list = salesContractInvcService.selectSalesContractInvcList(salesContractInvc);
        ExcelUtil<SalesContractInvc> util = new ExcelUtil<SalesContractInvc>(SalesContractInvc.class);
        util.exportExcel(response, list, "销售_合同_发票数据");
    }

    /**
     * 获取销售_合同_发票详细信息
     */
    @PreAuthorize("@ss.hasPermi('sales:invc:query')")
    @GetMapping(value = "/{sciMcd}")
    public AjaxResult getInfo(@PathVariable("sciMcd") String sciMcd)
    {
        return success(salesContractInvcService.selectSalesContractInvcBySciMcd(sciMcd));
    }

    /**
     * 新增销售_合同_发票
     */
    @PreAuthorize("@ss.hasPermi('sales:invc:add')")
    @Log(title = "销售_合同_发票", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SalesContractInvc salesContractInvc)
    {
        return toAjax(salesContractInvcService.insertSalesContractInvc(salesContractInvc));
    }

    /**
     * 修改销售_合同_发票
     */
    @PreAuthorize("@ss.hasPermi('sales:invc:edit')")
    @Log(title = "销售_合同_发票", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SalesContractInvc salesContractInvc)
    {
        return toAjax(salesContractInvcService.updateSalesContractInvc(salesContractInvc));
    }

    /**
     * 删除销售_合同_发票
     */
    @PreAuthorize("@ss.hasPermi('sales:invc:remove')")
    @Log(title = "销售_合同_发票", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sciMcds}")
    public AjaxResult remove(@PathVariable String[] sciMcds)
    {
        return toAjax(salesContractInvcService.deleteSalesContractInvcBySciMcds(sciMcds));
    }
}
