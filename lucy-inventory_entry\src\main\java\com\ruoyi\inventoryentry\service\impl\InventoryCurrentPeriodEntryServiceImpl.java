package com.ruoyi.inventoryentry.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.lucy.erp.domain.*;
import com.lucy.erp.mapper.LogInventoryIoTransactionMapper;
import com.lucy.erp.service.*;
import com.lucy.erp.service.impl.PurContractMstServiceImpl;
import com.lucy.inventoryoe.domain.CostComposition;
import com.lucy.inventoryoe.service.ICostCompositionService;
import com.lucy.skumaster.service.ISkuMasterService;
import com.lucy.supplier.service.ISupplierMasterService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.WXUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.inventoryentry.mapper.InventoryCurrentPeriodEntryMapper;
import com.ruoyi.inventoryentry.domain.InventoryCurrentPeriodEntry;
import com.ruoyi.inventoryentry.service.IInventoryCurrentPeriodEntryService;

import javax.annotation.PreDestroy;

/**
 * 本期入库明细 Current period inventory entriesService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class InventoryCurrentPeriodEntryServiceImpl implements IInventoryCurrentPeriodEntryService 
{
    private static final Logger log = LoggerFactory.getLogger(InventoryCurrentPeriodEntryServiceImpl.class);

    private static final String TABLE_NAME = "inventory_current_period_entry";

    // 批量插入的批次大小，避免内存溢出

    private static final int BATCH_SIZE = 1000;

    // 线程池用于并行处理load方法
    private final ExecutorService executorService = Executors.newFixedThreadPool(8);

    @Autowired
    private InventoryCurrentPeriodEntryMapper inventoryCurrentPeriodEntryMapper;
    @Autowired
    private ILogInventoryIoTransactionService logInventoryIoTransactionService;
    @Autowired
    private IBasProdPkgService basProdPkgService;
    @Autowired
    private IBasProdMstService basProdMstService;
    @Autowired
    private IPurContractMstService purContractMstService;
    @Autowired
    private IPurContractDtlService purContractDtlService;
    @Autowired
    private IBasSupMstService basSupMstService;
    @Autowired
    private ISystemUserService systemUserService;
    @Autowired
    private ILogAgListService logAgListService;
    @Autowired
    private IExpencesAmortizationDetailsService expencesAmortizationDetailsService;
    @Autowired
    private ILogOsPlanService logOsPlanService;
    @Autowired
    private IFinAccAvgCostService finAccAvgCostService;
    @Autowired
    private ICostCompositionService costCompositionService;
    @Autowired
    private ISalesContractMstService salesContractMstService;
    @Autowired
    private ISalesContractDtlService salesContractDtlService;
    @Autowired
    private IFinProdCodeService finProdCodeService;
    @Autowired
    private IBasCustorgMstService basCustorgMstService;
    @Autowired
    private ILogRadDeveloProductService logRadDeveloProductService;
    @Autowired
    private ILabProjectService labProjectService;
    @Autowired
    private ILogRepackResultService logRepackResultService;
    @Autowired
    private ILogRepackPackageUsePlanService logRepackPackageUsePlanService;
    @Autowired
    private ILogRepackPackageUseResultDtlService logRepackPackageUseResultDtlService;
    @Autowired
    private IFinAccMasterRecordsUniqService finAccMasterRecordsUniqService;
    @Autowired
    private IFinAccInventoryRepackService finAccInventoryRepackService;
    @Autowired
    private ISkuMasterService skuMasterService;
    @Autowired
    private ISupplierMasterService supplierMasterService;

    private Map<String, BasProdMst> basProdMstMap = new HashMap<>();

    private Map<String, BasProdPkg> basProdPkgMap = new HashMap<>();

    // 数据处理专用线程池，用于并行处理
    private final ExecutorService dataProcessorExecutor = Executors.newFixedThreadPool(
            Math.max(4, Runtime.getRuntime().availableProcessors())
    );

    /**
     * 查询本期入库明细 Current period inventory entries
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 本期入库明细 Current period inventory entries
     */
    @Override
    public InventoryCurrentPeriodEntry selectInventoryCurrentPeriodEntryById(Long id)
    {
        return inventoryCurrentPeriodEntryMapper.selectInventoryCurrentPeriodEntryById(id);
    }

    /**
     * 查询本期入库明细 Current period inventory entries列表
     * 
     * @param inventoryCurrentPeriodEntry 本期入库明细 Current period inventory entries
     * @return 本期入库明细 Current period inventory entries
     */
    @Override
    public List<InventoryCurrentPeriodEntry> selectInventoryCurrentPeriodEntryList(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry)
    {
        return inventoryCurrentPeriodEntryMapper.selectInventoryCurrentPeriodEntryList(inventoryCurrentPeriodEntry);
    }

    /**
     *
     * @param InventoryCurrentPeriodEntrys
     * @return
     */
    @Override
    public int batchInsertInventoryCurrentPeriodIssue(List<InventoryCurrentPeriodEntry> InventoryCurrentPeriodEntrys) {
        if (InventoryCurrentPeriodEntrys == null || InventoryCurrentPeriodEntrys.isEmpty()) {
            return 0;
        }

        int totalInserted = 0;
        int size = InventoryCurrentPeriodEntrys.size();

        // 分批处理，避免内存溢出和SQL语句过长
        for (int i = 0; i < size; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, size);
            List<InventoryCurrentPeriodEntry> batch = InventoryCurrentPeriodEntrys.subList(i, endIndex);

            try {
                int inserted = inventoryCurrentPeriodEntryMapper.batchInsertInventoryCurrentPeriodIssue(batch);
                totalInserted += inserted;
            } catch (Exception e) {
                throw new RuntimeException("批量插入数据时发生异常" + e.getMessage(), e);
            }
        }

        return totalInserted;
    }

    /**
     * 新增本期入库明细 Current period inventory entries
     * 
     * @param inventoryCurrentPeriodEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    @Override
    public int insertInventoryCurrentPeriodEntry(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry)
    {
        return inventoryCurrentPeriodEntryMapper.insertInventoryCurrentPeriodEntry(inventoryCurrentPeriodEntry);
    }

    /**
     * 修改本期入库明细 Current period inventory entries
     * 
     * @param inventoryCurrentPeriodEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    @Override
    public int updateInventoryCurrentPeriodEntry(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry)
    {
        return inventoryCurrentPeriodEntryMapper.updateInventoryCurrentPeriodEntry(inventoryCurrentPeriodEntry);
    }

    /**
     * 批量删除本期入库明细 Current period inventory entries
     * 
     * @param ids 需要删除的本期入库明细 Current period inventory entries主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodEntryByIds(Long[] ids)
    {
        return inventoryCurrentPeriodEntryMapper.deleteInventoryCurrentPeriodEntryByIds(ids);
    }

    /**
     * 删除本期入库明细 Current period inventory entries信息
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodEntryById(Long id)
    {
        return inventoryCurrentPeriodEntryMapper.deleteInventoryCurrentPeriodEntryById(id);
    }

    public void loadBQRK(String dateStart, String dateEnd, AssetOwner assetOwner) {
        // 解析日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(dateStart, formatter);
        LocalDate endDate = LocalDate.parse(dateEnd, formatter);

        // 按月分组处理
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // 计算当前月的开始和结束日期
            LocalDate monthStart = currentDate.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate monthEnd = currentDate.with(TemporalAdjusters.lastDayOfMonth());

            // 确保不超出原始日期范围
            LocalDate rangeStart = monthStart.isBefore(startDate) ? startDate : monthStart;
            LocalDate rangeEnd = monthEnd.isAfter(endDate) ? endDate : monthEnd;

            // 转换为字符串格式
            String monthStartStr = rangeStart.format(formatter);
            String monthEndStr = rangeEnd.format(formatter);
            String currentMonth = rangeStart.format(DateTimeFormatter.ofPattern("yyyy-MM"));

            // 处理当前月的数据
            processMonthlyData(monthStartStr, monthEndStr, assetOwner, currentMonth);

            // 移动到下一个月
            currentDate = currentDate.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        }
    }


    /**
     * 处理单月数据
     * @param dateStart 开始日期
     * @param dateEnd 结束日期
     * @param month 月份 yyyy-MM
     */
    public void processMonthlyData(String dateStart, String dateEnd, AssetOwner assetOwner, String month) {
        // 使用并行处理提高性能
        List<CompletableFuture<List<InventoryCurrentPeriodEntry>>> futures = new ArrayList<>();
        // 记录整体处理开始时间
        long overallStartTime = System.currentTimeMillis();

        // 创建异步任务
        futures.add(CompletableFuture.supplyAsync(() -> loadCGRK(dateStart, dateEnd, assetOwner), executorService));
//        futures.add(CompletableFuture.supplyAsync(() -> loadXSTH(dateStart, dateEnd, assetOwner,month), executorService));
//        futures.add(CompletableFuture.supplyAsync(() -> loadCPSCRK(dateStart, dateEnd, assetOwner), executorService));
//        futures.add(CompletableFuture.supplyAsync(() -> loadKCCSH(dateStart, dateEnd, assetOwner), executorService));
//        futures.add(CompletableFuture.supplyAsync(() -> loadXYSC(dateStart, dateEnd, assetOwner), executorService));
//        futures.add(CompletableFuture.supplyAsync(() -> loadBCRK(dateStart, dateEnd, assetOwner), executorService));
//        futures.add(CompletableFuture.supplyAsync(() -> loadCPSCRK(dateStart, dateEnd, assetOwner), executorService));
//        futures.add(CompletableFuture.supplyAsync(() -> loadHHHSCR(dateStart, dateEnd, assetOwner,month), executorService));

        List<InventoryCurrentPeriodEntry> total = new ArrayList<>();
        for (CompletableFuture<List<InventoryCurrentPeriodEntry>> future : futures) {
            try {
                total.addAll(future.get());
            } catch (Exception e) {
                throw new RuntimeException("并行处理load方法时发生异常" + e.getMessage());
            }
        }

        // 批量插入数据，提高性能
        if (!total.isEmpty()) {
            total.clear();
        }

        // 计算整体耗时
        long overallEndTime = System.currentTimeMillis();
        long overallDuration = overallEndTime - overallStartTime;
        String overallDurationStr = formatDuration(overallDuration);

        // 构建处理结果摘要
        String resultSummary = String.format("入库数据同步完成，总耗时：%s", overallDurationStr);

        WXUtils.invokeWorkNotification("***********", String.format("[%s] %s %s，日期：%s至%s",
                new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                assetOwner.entity().getName(), resultSummary, dateStart, dateEnd));
    }


    /**
     * 处理插入本期入库凭证
     * @param inventoryCurrentPeriodEntry
     * @param resultList
     */
    private void handleinventoryCurrentPeriodEntry(InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry, List<InventoryCurrentPeriodEntry> resultList) {
        InventoryCurrentPeriodEntry query = new InventoryCurrentPeriodEntry();
        query.setSourceTable(inventoryCurrentPeriodEntry.getSourceTable());
        query.setSourceRecordId(inventoryCurrentPeriodEntry.getSourceRecordId());
        List<InventoryCurrentPeriodEntry> inventoryCurrentPeriodEntrys = inventoryCurrentPeriodEntryMapper
                .selectInventoryCurrentPeriodEntryList(query);
        if (!inventoryCurrentPeriodEntrys.isEmpty()) {
            CostComposition costComposition = new CostComposition();
            costComposition.setStructureName(TABLE_NAME);
            costComposition.setParentStructureId(inventoryCurrentPeriodEntrys.get(0).getId());
            costComposition.setStatus("X");
            costComposition.setTotalAmount(inventoryCurrentPeriodEntry.getAmount());
            costComposition.setGenerationContext("");
            costComposition.setCreatedBy("");
            costComposition.setCreatedTime(new Date());
            costComposition.setUpdatedBy("");
            costComposition.setUpdatedTime(new Date());
            costComposition.setVersion(0);
            costComposition.setIsDeleted(0);
            costCompositionService.insertCostComposition(costComposition);
        } else {
            resultList.add(inventoryCurrentPeriodEntry);
        }
    }

    /**
     * 格式化耗时显示
     * @param duration 耗时（毫秒）
     * @return 格式化的耗时字符串
     */
    private String formatDuration(long duration) {
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.2fs", duration / 1000.0);
        } else {
            long minutes = duration / 60000;
            long seconds = (duration % 60000) / 1000;
            return String.format("%dm%ds", minutes, seconds);
        }
    }

    /**
     * 计算最优分片数量
     */
    private int calculateOptimalChunkCount(int dataSize) {
        if (dataSize <= 500) return 1;
        if (dataSize <= 2000) return 2;
        if (dataSize <= 10000) return 4;
        if (dataSize <= 50000) return 8;
        return Math.min(16, Runtime.getRuntime().availableProcessors() * 2);
    }

    /**
     * 将列表分片
     */
    private <T> List<List<T>> partitionList(List<T> list, int chunkSize) {
        List<List<T>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            chunks.add(list.subList(i, Math.min(i + chunkSize, list.size())));
        }
        return chunks;
    }

    /**
     * 处理单个数据分片
     */
    private List<InventoryCurrentPeriodEntry> processChunk(
            List<LogInventoryIoTransaction> chunk,
            String businessType,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodEntry> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodEntry result = processSingleItem(item, businessType, assetOwner,exceptCusts);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条{}数据时发生异常，数据ID：{}", businessType, item.getId(), e);
            }
        }

        return chunkResult;
    }


    /**
     * 处理销售退货单个数据分片
     */
    private List<InventoryCurrentPeriodEntry> processXSTHChunk(
            List<LogInventoryIoTransaction> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String startDate, String month) {

        List<InventoryCurrentPeriodEntry> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodEntry result = processXSTHSingleItem(item, assetOwner,exceptCusts,startDate,month);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条销售退货数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    /**
     * 处理产品生产入库单个数据分片
     */
    private List<InventoryCurrentPeriodEntry> processCPSCRKChunk(
            List<LogRadDeveloProduct> chunk,
            AssetOwner assetOwner) {

        List<InventoryCurrentPeriodEntry> chunkResult = new ArrayList<>();

        for (LogRadDeveloProduct item : chunk) {
            try {
                InventoryCurrentPeriodEntry result = processCPSCRKSingleItem(item, assetOwner);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条产品生产入库数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    /**
     * 加载采购入库数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param assetOwner 资产主体
     * @return
     */
    public List<InventoryCurrentPeriodEntry> loadCGRK(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "采购入库(CGRK)";

        List<InventoryCurrentPeriodEntry> cgrk = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("采购入库", startDate, endDate, assetOwner);

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("300102");

            // 研发
            List<String> pcm_cdsr = purContractMstService.selectPurContractMstListR();
            List<String> exceptCusts = new ArrayList<>(pcm_cdsr);

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            logInventoryIoTransaction.setLiotFinFlag("1");
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("采购入库数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                cgrk = processDataInParallel(logInventoryIoTransactions, "CGRK", assetOwner,exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("采购入库数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                cgrk = processCGRKSequentially(logInventoryIoTransactions, assetOwner,exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (cgrk != null && !cgrk.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, cgrk.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return cgrk;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理销售出库数据时发生异常", e);
            throw e;
        }
    }

    /**
     * 通用的并行数据处理方法
     * @param allData 所有数据
     * @param businessType 业务类型（等）
     * @param assetOwner 资产主体
     * @return 处理结果
     */
    private List<InventoryCurrentPeriodEntry> processDataInParallel(
            List<LogInventoryIoTransaction> allData,
            String businessType,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理{}数据，总数据量：{}，分片数：{}，每片大小：{}",
                businessType, allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodEntry>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processChunk(chunk, businessType, assetOwner, exceptCusts), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodEntry> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get(300, TimeUnit.SECONDS); // 设置2分钟超时
                    } catch (Exception e) {
                        log.error("处理数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodEntry>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("{}数据并行处理完成，结果数量：{}", businessType, result.size());
        return result;
    }
    /**
     * 串行处理采购入库数据
     */
    private List<InventoryCurrentPeriodEntry> processCGRKSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts
            ) {

        List<InventoryCurrentPeriodEntry> xsck = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodEntry result = processCGRKSingleItem(item, assetOwner, exceptCusts);
                if (result != null && StringUtils.isNotBlank(result.getDocumentType())) {
                    handleinventoryCurrentPeriodEntry(result, xsck);
                }
            } catch (Exception e) {
                log.error("处理销售出库数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return xsck;
    }
    /**
     * 处理单条数据的通用方法
     */
    private InventoryCurrentPeriodEntry processSingleItem(
            LogInventoryIoTransaction item,
            String businessType,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        // 根据业务类型调用不同的处理方法
        switch (businessType) {
            case "CGRK":
                return processCGRKSingleItem(item, assetOwner, exceptCusts);
            case "KCCSH":
                return processKCCSHSingleItem(item, assetOwner, exceptCusts);
            default:
                log.warn("未知的业务类型：{}", businessType);
                return null;
        }
    }

    /**
     * 处理单条采购入库数据
     * @param item
     * @param assetOwner
     * @return
     */
    private InventoryCurrentPeriodEntry processCGRKSingleItem(LogInventoryIoTransaction item,AssetOwner assetOwner,List<String> exceptCusts){
        try {
            // 验证必要字段
            if (item.getLiotPpcd() == null || item.getLiotRmk1() == null) {
                return null;
            }

            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            PurContractDtl purContractDtl = purContractDtlService.selectPurContractDtlByPcdCd(item.getLiotRmk2());
            if (purContractDtl==null){
                return null;
            }
            PurContractMst purContractMst = purContractMstService.selectPurContractMstByPcmCd(purContractDtl.getPcdMcd());
            if (purContractMst==null){
                return null;
            }

            // 排除研发领用
            if (exceptCusts.contains(purContractMst.getPcmCd())) {
                return null;
            }
            BasSupMst basSupMst = basSupMstService.selectBasSupMstByBsmCd(purContractMst.getPcmScd());
            LogAgList logAgList = logAgListService.selectLogAgListByLalCd(item.getLiotRmk1());
            ExpencesAmortizationDetails expencesAmortizationDetails = expencesAmortizationDetailsService.selectExpencesAmortizationDetailsByEcdPcd(logAgList.getLalPcd());

            //更新sku信息
            skuMasterService.insertSkuMasterIfNotExist(basProdPkg.getBppCd());
            //更新供应商信息
            supplierMasterService.insertSupplierMasterIfNotExist(purContractMst.getPcmScd());

            //财务科目
            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            finProdCode = finProdCodes.get(0);
            //封装数据
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();
            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodEntry.setQuantity(item.getLiotSlcd().contains("poor-999")?BigDecimal.ZERO:item.getLiotQty());
            //外采
            inventoryCurrentPeriodEntry.setSourceType("PURCHASED");
            //判断
            BigDecimal amount;
            BigDecimal qty;
            if (!"02".equals(purContractMst.getPcmIsImportPur())){
                // 国内采购
                inventoryCurrentPeriodEntry.setPurchaseScope("DOMESTIC");
                //金额
                if (!Arrays.asList("BM10000009","BM10000014","BM10000023","BM10000030","BM1000004").contains(basSupMst.getBsmOrgcd())){
                    //是否含税
                    if (item.getLiotQty()==null || item.getLiotQty().compareTo(BigDecimal.ZERO) == 0){
                        amount = BigDecimal.ZERO;
                    }else {
                        BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
                        amount = item.getLiotIspart().equals(1L)
                                ? price.divide(item.getLiotBit().add(new BigDecimal(1)), 2, RoundingMode.HALF_UP)
                                : price;
                    }
                }else {
                    amount = BigDecimal.ZERO;
                }
                qty = item.getLiotSlcd().contains("poor-999")?BigDecimal.ZERO:item.getLiotQty();
            }else {
                // 国外采购
                inventoryCurrentPeriodEntry.setPurchaseScope("INTERNATIONAL");
                //国外采购合同其他费用
                if (expencesAmortizationDetails!=null && expencesAmortizationDetails.getEadIsAsset()==1){
                    amount = item.getLiotQty()
                            .divide(expencesAmortizationDetails.getEadPskuQty().multiply(expencesAmortizationDetails.getEadAmt()),2,RoundingMode.HALF_UP);
                    qty = BigDecimal.ZERO;
                }else {
                    //金额
                    if (purContractDtl.getPcdQty().compareTo(BigDecimal.ZERO)==0||purContractDtl.getPcdLcValue().compareTo(BigDecimal.ZERO)==0){
                        amount = BigDecimal.ZERO;
                    }else {
                        amount = item.getLiotQty().divide(purContractDtl.getPcdQty(),2,RoundingMode.HALF_UP).multiply(purContractDtl.getPcdLcValue());
                    }
                    qty = item.getLiotSlcd().contains("poor-999")?BigDecimal.ZERO:item.getLiotQty();
                }
            }
            inventoryCurrentPeriodEntry.setAmount(amount);
            inventoryCurrentPeriodEntry.setQuantity(qty);
            inventoryCurrentPeriodEntry.setSupplierCode(purContractMst.getPcmScd());
            inventoryCurrentPeriodEntry.setSupplierName(purContractMst.getPcmSnm());
            inventoryCurrentPeriodEntry.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodEntry.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodEntry.setSourceRecordId(item.getId());
            inventoryCurrentPeriodEntry.setEntryTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setOperator(item.getrCreKid());
            inventoryCurrentPeriodEntry.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodEntry.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodEntry.setDocumentType("采购入库");
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLiotOrgmcd());
            String rCreKid = purContractMst.getPcmUcd()==null?"":purContractMst.getPcmUcd();
            inventoryCurrentPeriodEntry.setProcessor(rCreKid);
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setDepartment("PUR");
            // 部门审核人
            SystemUser manager = null;
            if (rCreKid != null) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                try {
                    inventoryCurrentPeriodEntry.setDepartmentAuditTime(DateUtils.parseDate( purContractMst.getPcmDt(),"yyyy-MM-dd"));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            inventoryCurrentPeriodEntry.setCreatedBy(item.getrCreKid());
            inventoryCurrentPeriodEntry.setUpdatedBy(item.getrCreKid());
            return inventoryCurrentPeriodEntry;
        } catch (Exception e) {
            log.error("处理采购入库数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }


    /**
     * 销售退货
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodEntry> loadXSTH(String startDate, String endDate, AssetOwner assetOwner,String month) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "销售退货(XSTH)";

        List<InventoryCurrentPeriodEntry> xsth = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("销售退货", startDate, endDate, assetOwner);

            // 研发
            List<String> exceptCusts = new ArrayList<>();
            exceptCusts.add("KH10116772");
            exceptCusts.add("KH10129596");

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("110101");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("销售退货数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                xsth = processXSTHInParallel(logInventoryIoTransactions, assetOwner, exceptCusts, startDate, month);
            } else {
                // 数据量小，使用串行处理
                log.info("销售退货数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                xsth = processXSTHSequentially(logInventoryIoTransactions, assetOwner, exceptCusts, startDate, month);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!xsth.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, xsth.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return xsth;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理销售退货数据时发生异常", e);
            return xsth;
        }
    }
    /**
     * 销售退货的并行数据处理方法
     * @param allData 所有数据
     * @param assetOwner 资产主体
     * @return 处理结果
     */
    private List<InventoryCurrentPeriodEntry> processXSTHInParallel(
            List<LogInventoryIoTransaction> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String startDate, String month) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理销售退货数据，总数据量：{}，分片数：{}，每片大小：{}"
                , allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodEntry>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processXSTHChunk(chunk, assetOwner, exceptCusts, startDate, month), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodEntry> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get(120, TimeUnit.SECONDS); // 设置2分钟超时
                    } catch (Exception e) {
                        log.error("处理数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodEntry>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("销售退货数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 串行处理销售退货数据
     */
    private List<InventoryCurrentPeriodEntry> processXSTHSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String startDate,String month) {

        List<InventoryCurrentPeriodEntry> xsth = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodEntry result = processXSTHSingleItem(item, assetOwner, exceptCusts,startDate, month);
                if (result != null) {
                    handleinventoryCurrentPeriodEntry(result, xsth);
                }
            } catch (Exception e) {
                log.error("处理销售退货数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return xsth;
    }

    /**
     * 销售退货单条数据处理
     * @param item
     * @param assetOwner
     * @param exceptCusts
     * @param startDate
     * @param month
     * @return
     */
    private InventoryCurrentPeriodEntry processXSTHSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts,String startDate,String month) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            // 销售合同明细
            SalesContractDtl salesContractDtl = salesContractDtlService.selectSalesContractDtlByScdcd(item.getLiotRmk1());
            if (salesContractDtl == null) {
                return null;
            }

            //销售合同主表
            SalesContractMst salesContractMst = salesContractMstService.selectSalesContractMstByScmCd(salesContractDtl.getScdMcd());
            if (salesContractMst == null) {
                return null;
            }

            // 排除研发领用
            if (exceptCusts.contains(salesContractMst.getScmCustcd())) {
                return null;
            }

            LogAgList logAgList = logAgListService.selectLogAgListByLalCd(item.getLiotRmk1());
            LogOsPlan logOsPlan= new LogOsPlan();
            if (logAgList!=null&&logAgList.getLopId()==null){
                //出库计划表
                logOsPlan = logOsPlanService.selectLogOsPlanById(logAgList.getLopId());
            }

            BasCustorgMst basCustorgMst = basCustorgMstService.selectBasCustorgMstByBcmCd(salesContractMst.getScmPrcocd());



            FinAccAvgCost finAccAvgCost = new FinAccAvgCost();
            finAccAvgCost.setFaacId(finAccMasterRecordsUniqService.getFaacId(assetOwner.code(),month));
            finAccAvgCost.setSku(basProdPkg.getBppCd());
            finAccAvgCost = finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost);

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            //数据封装
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();

            //金额
            if (logOsPlan==null || logOsPlan.getId()==null || logOsPlan.getLopOutdt().after(DateUtils.parseDate(startDate,"yyyy-MM-dd"))){
                //金额-含税
                BigDecimal amount = item.getLiotQty().multiply(item.getLiotUnitp());
                //金额-不含税金额
                BigDecimal amt = item.getLiotQty().multiply(item.getLiotUnitp())
                        .divide(item.getLiotBit().add(new BigDecimal(1)), 2, RoundingMode.HALF_UP);
                //是否含税
                if ("1".equals(item.getLiotIspart())){
                    inventoryCurrentPeriodEntry.setAmount(amt);
                }else {
                    inventoryCurrentPeriodEntry.setAmount(amount);
                }
            }else {
                BigDecimal amount = item.getLiotQty().multiply(finAccAvgCost.getFaacCost());
                inventoryCurrentPeriodEntry.setAmount(amount);
            }

            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodEntry.setQuantity(item.getLiotQty());
            inventoryCurrentPeriodEntry.setSourceType("-");
            inventoryCurrentPeriodEntry.setPurchaseScope("-");
            inventoryCurrentPeriodEntry.setSupplierCode(basCustorgMst.getBcmCd());
            inventoryCurrentPeriodEntry.setSupplierName(basCustorgMst.getBcmNm());
            inventoryCurrentPeriodEntry.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodEntry.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodEntry.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodEntry.setSourceRecordId(item.getId());
            inventoryCurrentPeriodEntry.setEntryTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setOperator(item.getrCreKid());
            inventoryCurrentPeriodEntry.setEncryptedBatch(item.getLiotBnEncrypt());
            inventoryCurrentPeriodEntry.setDocumentType("销售退货");
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodEntry.setProcessor(salesContractMst.getScmSmcd());
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setDepartment("SAL");

            // 部门审核人
            SystemUser manager = systemUserService.selectSystemUserManager(salesContractMst.getScmSmcd());
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodEntry.setDepartmentAuditTime(salesContractMst.getScmShdt());
            }
            inventoryCurrentPeriodEntry.setCreatedBy(item.getrCreKid());
            inventoryCurrentPeriodEntry.setUpdatedBy(item.getrCreKid());
            return inventoryCurrentPeriodEntry;

        } catch (Exception e) {
            log.error("处理采购退货数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 产品生产入库
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodEntry> loadCPSCRK(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "产品生产入库(CPSXRK)";

        List<InventoryCurrentPeriodEntry> cpxsrk = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("产品生产入库", startDate, endDate, assetOwner);

            //获取生产表信息
            LogRadDeveloProduct logRadDeveloProduct = new LogRadDeveloProduct();
            logRadDeveloProduct.setStartDate(startDate);
            logRadDeveloProduct.setEndDate(endDate);
            logRadDeveloProduct.setLrdpOrgmcd(assetOwner.code());
            List<LogRadDeveloProduct> logRadDeveloProducts = logRadDeveloProductService.selectLogRadDeveloProductList(logRadDeveloProduct);

            // 根据数据量选择处理方式
            if (logRadDeveloProducts.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("产品生产入库数据量较大({}条)，启用并行处理", logRadDeveloProducts.size());
                cpxsrk = processCPSCRKInParallel(logRadDeveloProducts, assetOwner);
            } else {
                // 数据量小，使用串行处理
                log.info("产品生产入库数据量较小({}条)，使用串行处理", logRadDeveloProducts.size());
                cpxsrk = processCPSCRKSequentially(logRadDeveloProducts, assetOwner);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!cpxsrk.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, cpxsrk.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return cpxsrk;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理产品生产入库数据时发生异常", e);
            return cpxsrk;
        }
    }


    /**
     * 库存初始化
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodEntry> loadKCCSH(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "库存初始化(KCCSH)";

        List<InventoryCurrentPeriodEntry> kccsh = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("库存初始化", startDate, endDate, assetOwner);

            // 研发
            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500401");
            liot_typs.add("500606");
            liot_typs.add("500101");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("库存初始化数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                kccsh = processDataInParallel(logInventoryIoTransactions, "KCCSH", assetOwner, exceptCusts);
            } else {
                // 数据量小，使用串行处理
                log.info("库存初始化数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                kccsh = processKCCSHSequentially(logInventoryIoTransactions, assetOwner, exceptCusts);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!kccsh.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, kccsh.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return kccsh;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理库存初始化数据时发生异常", e);
            return kccsh;
        }
    }

    /**
     * 产品生产入库并行数据处理方法
     * @param allData 所有数据
     * @param assetOwner 资产主体
     * @return 处理结果
     */
    private List<InventoryCurrentPeriodEntry> processCPSCRKInParallel(
            List<LogRadDeveloProduct> allData,
            AssetOwner assetOwner) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理产品生产入库数据，总数据量：{}，分片数：{}，每片大小：{}",
                 allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogRadDeveloProduct>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodEntry>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processCPSCRKChunk(chunk, assetOwner), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodEntry> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get(120, TimeUnit.SECONDS); // 设置2分钟超时
                    } catch (Exception e) {
                        log.error("处理数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodEntry>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("产品生产入库数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 串行处理产品生产入库数据
     */
    private List<InventoryCurrentPeriodEntry> processCPSCRKSequentially(
            List<LogRadDeveloProduct> LogRadDeveloProducts,
            AssetOwner assetOwner) {

        List<InventoryCurrentPeriodEntry> cpscrk = new ArrayList<>();

        for (LogRadDeveloProduct item : LogRadDeveloProducts) {
            try {
                InventoryCurrentPeriodEntry result = processCPSCRKSingleItem(item, assetOwner);
                if (result != null) {
                    handleinventoryCurrentPeriodEntry(result, cpscrk);
                }
            } catch (Exception e) {
                log.error("处理产品生产入库数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return cpscrk;
    }

    /**
     * 产品生产入库单条数据处理
     * @param item
     * @param assetOwner
     * @return
     */
    private InventoryCurrentPeriodEntry processCPSCRKSingleItem(LogRadDeveloProduct item, AssetOwner assetOwner){
        try {

            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLrdpPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            // 排除没有生产领料项目的
            LabProject labProject = labProjectService.selectLabProjectByLpCd(item.getLrdpMcd());
            if (labProject == null || labProject.getLpCd().isEmpty()){
                return null;
            }

            //更新sku信息
            skuMasterService.insertSkuMasterIfNotExist(basProdPkg.getBppCd());

            //封装数据
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();
            //财务科目
            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            finProdCode = finProdCodes.get(0);
            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLrdpPpcd());
            inventoryCurrentPeriodEntry.setSourceType("SELF_PRODUCED");
            inventoryCurrentPeriodEntry.setPurchaseScope("-");
            inventoryCurrentPeriodEntry.setSupplierCode("");
            inventoryCurrentPeriodEntry.setSupplierName("");
            inventoryCurrentPeriodEntry.setQuantity(item.getLrdpActualqty());
            inventoryCurrentPeriodEntry.setAmount(item.getLrdpTotprc());
            inventoryCurrentPeriodEntry.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodEntry.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setBatchNumber(item.getLrdpBn());
            inventoryCurrentPeriodEntry.setWarehouse(item.getLrdpDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(item.getLrdpSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(item.getLrdpTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_rad_develo_product");
            inventoryCurrentPeriodEntry.setSourceRecordId(item.getId());
            inventoryCurrentPeriodEntry.setEntryTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setEncryptedBatch(item.getLrdpBnEncrypt());
            inventoryCurrentPeriodEntry.setDocumentType("产品生产入库");
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLrdpOrgmcd());
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            inventoryCurrentPeriodEntry.setOperator(rCreKid);
            inventoryCurrentPeriodEntry.setProcessor(rCreKid);
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setDepartment("PRO");
//            inventoryCurrentPeriodEntry.setProjectCode(labProject.getLpPcd());
//            inventoryCurrentPeriodEntry.setProjectName(labProject.getLpNm());
            inventoryCurrentPeriodEntry.setRdSystemId(item.getLrdpRdId().toString());


            // 部门审核人
            SystemUser manager = null;
            if (rCreKid != null && rCreKid != "") {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodEntry.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodEntry.setCreatedBy(rCreKid);
            inventoryCurrentPeriodEntry.setUpdatedBy(rCreKid);

        }catch (Exception e) {
            log.error("处理产品分装入库数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
        return null;
    }


    /**
     * 串行处理库存初始化数据
     */
    private List<InventoryCurrentPeriodEntry> processKCCSHSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts) {

        List<InventoryCurrentPeriodEntry> cb = new ArrayList<>();

        for (LogInventoryIoTransaction item : logInventoryIoTransactions) {
            try {
                InventoryCurrentPeriodEntry result = processKCCSHSingleItem(item, assetOwner, exceptCusts);
                if (result != null) {
                    handleinventoryCurrentPeriodEntry(result, cb);
                }
            } catch (Exception e) {
                log.error("处理库存初始化数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return cb;
    }

    /**
     * 单条处理库存初始化数据
     * @param item
     * @param assetOwner
     * @param exceptCusts
     * @return
     */
    private InventoryCurrentPeriodEntry processKCCSHSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            //数据封装
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();
            //是否含税
            BigDecimal amount;
            if (item.getLiotQty()==null || item.getLiotQty().compareTo(BigDecimal.ZERO)==0){
                amount = BigDecimal.ZERO;
            }else {
                BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
                amount = item.getLiotIspart().equals(1L)
                        ? price.divide(item.getLiotBit().add(new BigDecimal(1)), 2, RoundingMode.HALF_UP)
                        : price;
            }
            inventoryCurrentPeriodEntry.setAmount(amount);
            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodEntry.setSupplierCode("");
            inventoryCurrentPeriodEntry.setSupplierName("");
            inventoryCurrentPeriodEntry.setSourceType("-");
            inventoryCurrentPeriodEntry.setPurchaseScope("-");
            inventoryCurrentPeriodEntry.setQuantity(item.getLiotQty());
            inventoryCurrentPeriodEntry.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodEntry.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setBatchNumber(item.getLiotBn());
            inventoryCurrentPeriodEntry.setWarehouse(item.getLiotDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(item.getLiotSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(item.getLiotTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodEntry.setSourceRecordId(item.getId());
            inventoryCurrentPeriodEntry.setEntryTime(item.getrCreDt());
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            inventoryCurrentPeriodEntry.setOperator(rCreKid);
            inventoryCurrentPeriodEntry.setEncryptedBatch(item.getLiotBnEncrypt());
            switch (item.getLiotTyp()){
                case 500401:
                    inventoryCurrentPeriodEntry.setDocumentType("库存初始化"); //库存初始化
                    break;
                case 500606:
                    inventoryCurrentPeriodEntry.setDocumentType("拆包入"); //拆包入
                    break;
                case 500101:
                    inventoryCurrentPeriodEntry.setDocumentType("盘盈"); //盘盈
                    break;
            }
            inventoryCurrentPeriodEntry.setProcessor(rCreKid);
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodEntry.setDepartment("LOG");

            // 部门审核人
            SystemUser manager = null;
            if (rCreKid != null) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodEntry.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodEntry.setCreatedBy(rCreKid);
            inventoryCurrentPeriodEntry.setUpdatedBy(rCreKid);
            return inventoryCurrentPeriodEntry;

        } catch (Exception e) {
            log.error("处理库存初始化数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }



    /**
     * 小样生产入库
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodEntry> loadXYSC(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "小样生产(XYSC)";

        List<InventoryCurrentPeriodEntry> xysc = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("小样生产", startDate, endDate, assetOwner);


            LogRepackResult logRepackResult = new LogRepackResult();
            logRepackResult.setStartDate(startDate);
            logRepackResult.setEndDate(endDate);
            logRepackResult.setLrrOrgmcd(assetOwner.code());
            logRepackResult.setLrrTyp("09");//小样生产
            logRepackResult.setFlag(8);
            List<LogRepackResult> logRepackResults = logRepackResultService.selectLogRepackResultList(logRepackResult);


            // 根据数据量选择处理方式
            if (logRepackResults.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("小样生产数据量较大({}条)，启用并行处理", logRepackResults.size());
                xysc = processLrrDataInParallel(logRepackResults, "XYSC", assetOwner);
            } else {
                // 数据量小，使用串行处理
                log.info("小样生产数据量较小({}条)，使用串行处理", logRepackResults.size());
                xysc = processLrrChunk(logRepackResults,"XYSC", assetOwner);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!xysc.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, xysc.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return xysc;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理小样生产入库数据时发生异常", e);
            return xysc;
        }
    }

    /**
     * 包材入库
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodEntry> loadBCRK(String startDate, String endDate, AssetOwner assetOwner) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "包材入坑(BCRK)";

        List<InventoryCurrentPeriodEntry> bcrk = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("包材入库", startDate, endDate, assetOwner);


            LogRepackResult logRepackResult = new LogRepackResult();
            logRepackResult.setStartDate(startDate);
            logRepackResult.setEndDate(endDate);
            logRepackResult.setLrrOrgmcd(assetOwner.code());
            List<LogRepackResult> logRepackResults = logRepackResultService.selectLogRepackResultList(logRepackResult);


            // 根据数据量选择处理方式
            if (logRepackResults.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("包材入库数据量较大({}条)，启用并行处理", logRepackResults.size());
                bcrk = processLrrDataInParallel(logRepackResults, "XYSC", assetOwner);
            } else {
                // 数据量小，使用串行处理
                log.info("包材入库调整数据量较小({}条)，使用串行处理", logRepackResults.size());
                bcrk = processLrrChunk(logRepackResults,"XYSC", assetOwner);
            }

            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!bcrk.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, bcrk.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return bcrk;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理包材入库数据时发生异常", e);
            return bcrk;
        }
    }



    /**
     * 通用的并行数据处理方法
     * @param allData      所有数据
     * @param businessType 业务类型（等）
     * @param assetOwner   资产主体
     * @return 处理结果
     */
    private List<InventoryCurrentPeriodEntry> processLrrDataInParallel(
            List<LogRepackResult> allData,
            String businessType,
            AssetOwner assetOwner) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(allData.size());
        int chunkSize = Math.max(1, allData.size() / chunkCount);

        log.info("开始并行处理{}数据，总数据量：{}，分片数：{}，每片大小：{}",
                businessType, allData.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogRepackResult>> chunks = partitionList(allData, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodEntry>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processLrrChunk(chunk, businessType, assetOwner), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodEntry> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodEntry>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("{}数据并行处理完成，结果数量：{}", businessType, result.size());
        return result;
    }

    /**
     * 处理单个数据分片
     */
    private List<InventoryCurrentPeriodEntry> processLrrChunk(
            List<LogRepackResult> chunk,
            String businessType,
            AssetOwner assetOwner) {

        List<InventoryCurrentPeriodEntry> chunkResult = new ArrayList<>();

        for (LogRepackResult item : chunk) {
            try {
                InventoryCurrentPeriodEntry result = processLrrSingleItem(item, businessType, assetOwner);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条{}数据时发生异常，数据ID：{}", businessType, item.getId(), e);
            }
        }

        return chunkResult;
    }

    /**
     * 处理单条数据的通用方法
     */
    private InventoryCurrentPeriodEntry processLrrSingleItem(
            LogRepackResult item,
            String businessType,
            AssetOwner assetOwner) {

        // 根据业务类型调用不同的处理方法
        switch (businessType) {
            case "XYSC":
                return processXYSCRKSingleItem(item, assetOwner);
            case "BCRK":
                return processBCRKSingleItem(item, assetOwner);
            default:
                log.warn("未知的业务类型：{}", businessType);
                return null;
        }
    }


    /***
     * 处理小样生产入库单条数据
     * @param item
     * @param assetOwner
     * @return
     */
    private InventoryCurrentPeriodEntry processXYSCRKSingleItem(LogRepackResult item, AssetOwner assetOwner) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLrrMainSkuCd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);


            if (item.getLrrPsn()==null||item.getLrrPsn().isEmpty()){
                return null;
            }
            LogRepackPackageUsePlan logRepackPackageUsePlan = new LogRepackPackageUsePlan();
            logRepackPackageUsePlan.setLrpupPsn(item.getLrrPsn());
            List<LogRepackPackageUsePlan> logRepackPackageUsePlans = logRepackPackageUsePlanService.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);
            //排除异常情况 没有领料 但有产成
            if (logRepackPackageUsePlans.size()==0){
                return null;
            }

            //更新sku信息
            skuMasterService.insertSkuMasterIfNotExist(basProdPkg.getBppCd());

            //数据封装
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();
            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLrrMainSkuCd());
            inventoryCurrentPeriodEntry.setSupplierCode("");
            inventoryCurrentPeriodEntry.setSupplierName("");
            inventoryCurrentPeriodEntry.setSourceType("SELF_PRODUCED");
            inventoryCurrentPeriodEntry.setPurchaseScope("-");
            inventoryCurrentPeriodEntry.setQuantity(item.getLrrMainSkuQty());
            inventoryCurrentPeriodEntry.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodEntry.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setAmount(item.getLrrMainTotalPrc());
            inventoryCurrentPeriodEntry.setBatchNumber(item.getLrrBn());
            inventoryCurrentPeriodEntry.setWarehouse(item.getLrrDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(item.getLrrSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(item.getLrrTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_repack_result");
            inventoryCurrentPeriodEntry.setSourceRecordId(item.getId());
            inventoryCurrentPeriodEntry.setEntryTime(item.getrCreDt());
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            inventoryCurrentPeriodEntry.setOperator(rCreKid);
            inventoryCurrentPeriodEntry.setEncryptedBatch(item.getLrrBnEncrypt());
            inventoryCurrentPeriodEntry.setDocumentType("产品分装入库-小样生产");
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLrrOrgmcd());
            inventoryCurrentPeriodEntry.setProcessor(rCreKid);
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setDepartment("PRO");

            // 部门审核人
            SystemUser manager = null;
            if (rCreKid != null) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodEntry.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodEntry.setCreatedBy(rCreKid);
            inventoryCurrentPeriodEntry.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodEntry;
        }catch (Exception e) {
            log.error("处理小样生产入库数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }


    /***
     * 处理包材入库单条数据
     * @param item
     * @param assetOwner
     * @return
     */
    private InventoryCurrentPeriodEntry processBCRKSingleItem(LogRepackResult item, AssetOwner assetOwner) {
        try {

            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg1 = getCachedBasProdPkg(item.getLrrMainSkuCd());
            BasProdMst basProdMst1 = getCachedBasProdMst(basProdPkg1.getBppPcd());

            BasProdPkg basProdPkg2 = getCachedBasProdPkg(item.getLrpurdSkuCd());
            BasProdMst basProdMst2 = getCachedBasProdMst(basProdPkg2.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst1.getBpmSort())) {
                return null;
            }
            //排除产品种类为非生产包材的
            if (!"13".equals(basProdMst2.getBpmSort())) {
                return null;
            }
            if (item.getLrrPsn()==null||item.getLrrPsn().isEmpty()){
                return null;
            }

            LogRepackPackageUsePlan logRepackPackageUsePlan = new LogRepackPackageUsePlan();
            logRepackPackageUsePlan.setLrpupPsn(item.getLrrPsn());
            List<LogRepackPackageUsePlan> logRepackPackageUsePlans = logRepackPackageUsePlanService.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);
            //排除异常情况 没有领料 但有产成
            if (logRepackPackageUsePlans.size() == 0) {
                return null;
            }

            //排除领料计划flag非6的
            boolean containsPlan6 = logRepackPackageUsePlans.stream()
                    .anyMatch(plan -> plan.getFlag() == 6);
            if (containsPlan6) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst1.getBpmCcd());
            finProdCode.setBpmSort(basProdMst1.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            //更新sku信息
            skuMasterService.insertSkuMasterIfNotExist(basProdPkg1.getBppCd());
            //数据封装
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();

            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLrrMainSkuCd());
            inventoryCurrentPeriodEntry.setSupplierCode("");
            inventoryCurrentPeriodEntry.setSupplierName("");
            inventoryCurrentPeriodEntry.setSourceType("SELF_PRODUCED");
            inventoryCurrentPeriodEntry.setPurchaseScope("-");
            inventoryCurrentPeriodEntry.setQuantity(item.getLrrMainSkuQty());
            inventoryCurrentPeriodEntry.setAuditor(item.getrCreKid());
            inventoryCurrentPeriodEntry.setAuditTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setAmount(item.getLrpurdTotalPrc());
            inventoryCurrentPeriodEntry.setBatchNumber(item.getLrrBn());
            inventoryCurrentPeriodEntry.setWarehouse(item.getLrrDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(item.getLrrSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(item.getLrrTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_repack_result");
            inventoryCurrentPeriodEntry.setSourceRecordId(item.getId());
            inventoryCurrentPeriodEntry.setEntryTime(item.getrCreDt());
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            inventoryCurrentPeriodEntry.setOperator(rCreKid);
            inventoryCurrentPeriodEntry.setEncryptedBatch(item.getLrrBnEncrypt());
            inventoryCurrentPeriodEntry.setProcessor(rCreKid);
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setDocumentType("包材入库");
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLrrOrgmcd());
            inventoryCurrentPeriodEntry.setDepartment("PRO");

            // 部门审核人
            SystemUser manager = null;
            if (rCreKid != null) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodEntry.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodEntry.setCreatedBy(rCreKid);
            inventoryCurrentPeriodEntry.setUpdatedBy(rCreKid);
            return inventoryCurrentPeriodEntry;
        }catch(Exception e){
            log.error("处理包材入库数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }



    /***
     * 处理产品分装入库单条数据
     * @param item
     * @param assetOwner
     * @return
     */
    private InventoryCurrentPeriodEntry processCPFZRKSingleItem(LogRepackResult item, AssetOwner assetOwner) {
        try {

            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg1 = getCachedBasProdPkg(item.getLrrMainSkuCd());
            BasProdMst basProdMst1 = getCachedBasProdMst(basProdPkg1.getBppPcd());

            BasProdPkg basProdPkg2 = getCachedBasProdPkg(item.getLrpurdSkuCd());
            BasProdMst basProdMst2 = getCachedBasProdMst(basProdPkg2.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst1.getBpmSort())) {
                return null;
            }
            //更新sku信息
            skuMasterService.insertSkuMasterIfNotExist(basProdPkg1.getBppCd());

            LogRepackPackageUsePlan logRepackPackageUsePlan = new LogRepackPackageUsePlan();
            List<LogRepackPackageUsePlan> logRepackPackageUsePlans = new ArrayList<>();
            if (item.getLrrPsn()!=null){
                logRepackPackageUsePlan.setLrpupPsn(item.getLrrPsn());
                logRepackPackageUsePlans = logRepackPackageUsePlanService.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst1.getBpmCcd());
            finProdCode.setBpmSort(basProdMst1.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            FinAccAvgCost finAccAvgCost = new FinAccAvgCost();
            finAccAvgCost.setFaacId(assetOwner.code());
            finAccAvgCost.setSku(item.getLrrMainSkuCd());
            finAccAvgCost = finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost);

            //数据封装
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();
            //排除异常情况 没有领料 但有产成  非小样生产 属于原材料
            if (logRepackPackageUsePlans.size()>0  && !"09".equals(item.getLrrTyp()) && "03".equals(basProdMst2.getBpmSort())){
                BigDecimal qty = "13".equals(basProdMst2.getBpmSort())?BigDecimal.ZERO:item.getLrrMainSkuQty();
                inventoryCurrentPeriodEntry.setQuantity(qty);
                inventoryCurrentPeriodEntry.setAmount(qty.compareTo(BigDecimal.ZERO)==-1?
                        finAccAvgCost.getFaacCost().multiply(qty):item.getLrpurdTotalPrc());
                inventoryCurrentPeriodEntry.setDocumentType("产品分装入库-现货");
            }else if (Arrays.asList("01","02").contains(basProdMst2.getBpmSort()) && item.getFlag()==6){
                BigDecimal qty = item.getLrrMainSkuQty();
                inventoryCurrentPeriodEntry.setQuantity(qty);
                inventoryCurrentPeriodEntry.setAmount(qty.compareTo(BigDecimal.ZERO)==-1?
                        finAccAvgCost.getFaacCost().multiply(qty):item.getLrpurdTotalPrc());
                inventoryCurrentPeriodEntry.setDocumentType("产品分装入库-非现货");
            }else {
                return null;
            }

            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLrrMainSkuCd());
            inventoryCurrentPeriodEntry.setSupplierCode("");
            inventoryCurrentPeriodEntry.setSupplierName("");
            inventoryCurrentPeriodEntry.setSourceType("SELF_PRODUCED");
            inventoryCurrentPeriodEntry.setPurchaseScope("-");
            inventoryCurrentPeriodEntry.setBatchNumber(item.getLrrBn());
            inventoryCurrentPeriodEntry.setWarehouse(item.getLrrDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(item.getLrrSlcd()==null?"-":item.getLrrSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(item.getLrrTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_repack_result");
            inventoryCurrentPeriodEntry.setSourceRecordId(item.getId());
            inventoryCurrentPeriodEntry.setEntryTime(item.getrCreDt());
            String rCreKid = item.getrCreKid() != null ? item.getrCreKid() : "";
            inventoryCurrentPeriodEntry.setOperator(rCreKid);
            inventoryCurrentPeriodEntry.setEncryptedBatch(item.getLrrBnEncrypt());
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLrrOrgmcd());
            inventoryCurrentPeriodEntry.setDepartment("PRO");
            inventoryCurrentPeriodEntry.setProcessor(rCreKid);
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());

            // 部门审核人
            SystemUser manager = null;
            if (rCreKid != null) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodEntry.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodEntry.setCreatedBy(rCreKid);
            inventoryCurrentPeriodEntry.setUpdatedBy(rCreKid);
            return inventoryCurrentPeriodEntry;

        }catch(Exception e){
            log.error("处理产品分装入库数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }

    /**
     * 换货号生产入
     *
     * @param startDate
     * @param endDate
     * @param assetOwner
     * @return
     */
    public List<InventoryCurrentPeriodEntry> loadHHHSCR(String startDate, String endDate, AssetOwner assetOwner, String month) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String businessTypeName = "换货号生产入库(HHHSCR)";

        List<InventoryCurrentPeriodEntry> hhhscr = new ArrayList<>();

        try {
            // 使用公共方法发送通知
            sendStartNotification("换货号生产入库", startDate, endDate, assetOwner);

            List<String> exceptCusts = new ArrayList<>();

            // 查询库存事务数据
            List<String> liot_typs = new ArrayList<>();
            liot_typs.add("500601");
            liot_typs.add("500602");
            liot_typs.add("500603");
            liot_typs.add("500604");

            // 查询库存事务数据
            LogInventoryIoTransaction logInventoryIoTransaction = createInventoryQuery(liot_typs, startDate, endDate, assetOwner);
            List<LogInventoryIoTransaction> logInventoryIoTransactions =
                    logInventoryIoTransactionService.selectLogInventoryIoTransactionList(logInventoryIoTransaction);

            // 根据数据量选择处理方式
            if (logInventoryIoTransactions.size() > 1000) {
                // 数据量大，使用并行处理
                log.info("换货号生产入库数据量较大({}条)，启用并行处理", logInventoryIoTransactions.size());
                hhhscr = processHHHDataInParallel(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            } else {
                // 数据量小，使用串行处理
                log.info("换货号生产入库数据量较小({}条)，使用串行处理", logInventoryIoTransactions.size());
                hhhscr = processHHHSequentially(logInventoryIoTransactions, assetOwner, exceptCusts, month);
            }


            // 计算耗时并发送成功通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            if (!hhhscr.isEmpty()) {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，数据条数：%d，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, hhhscr.size(), durationStr));
            } else {
                WXUtils.invokeWorkNotification("***********", String.format("[%s] %s 处理成功，无数据，耗时：%s",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        businessTypeName, durationStr));
            }

            return hhhscr;

        } catch (Exception e) {
            // 计算耗时并发送失败通知
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String durationStr = formatDuration(duration);

            String errorMsg = String.format("[%s] %s 处理失败 - 日期：%s至%s，资产主体：%s，耗时：%s，异常信息：%s",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                    businessTypeName, startDate, endDate, assetOwner.code(), durationStr, e.getMessage());

            WXUtils.invokeWorkNotification("***********", errorMsg);
            log.error("处理换货号生产入库数据时发生异常", e);
            return hhhscr;
        }
    }

    /**
     * 并行处理换货号生产入数据
     */
    private List<InventoryCurrentPeriodEntry> processHHHDataInParallel(
            List<LogInventoryIoTransaction> allData,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        if (allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 分类换货号出 和 换货号入
        List<LogInventoryIoTransaction> outList = allData.stream()
                .filter(t -> t.getLiotTyp() == 500601 || t.getLiotTyp() == 500603)
                .collect(Collectors.toList());
        List<LogInventoryIoTransaction> inList = allData.stream()
                .filter(t -> t.getLiotTyp() == 500602 || t.getLiotTyp() == 500604)
                .collect(Collectors.toList());

        // 找出要排除的
        Set<String> excludeRmk1 = outList.stream()
                .filter(out -> inList.stream().anyMatch(in ->
                        Objects.equals(out.getLiotRmk1(), in.getLiotRmk1()) &&
                                Objects.equals(out.getLiotPpcd(), in.getLiotPpcd()) &&
                                (out.getLiotQty().compareTo(in.getLiotQty()) == 0
                                        || in.getLiotQty().compareTo(out.getLiotQty().multiply(BigDecimal.valueOf(2))) == 0)))
                .map(LogInventoryIoTransaction::getLiotRmk1)
                .collect(Collectors.toSet());

        Map<String, BigDecimal> outMap = outList.stream()
                .filter(out -> !excludeRmk1.contains(out.getLiotRmk1()))
                .collect(Collectors.groupingBy(LogInventoryIoTransaction::getLiotPpcd,
                        Collectors.mapping(LogInventoryIoTransaction::getLiotQty,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        // 根据数据量决定分片数量
        int chunkCount = calculateOptimalChunkCount(outList.size());
        int chunkSize = Math.max(1, outList.size() / chunkCount);

        log.info("开始并行处理换货号生产入数据，总数据量：{}，分片数：{}，每片大小：{}",
                outList.size(), chunkCount, chunkSize);

        // 数据分片
        List<List<LogInventoryIoTransaction>> chunks = partitionList(outList, chunkSize);

        // 创建并行任务
        List<CompletableFuture<List<InventoryCurrentPeriodEntry>>> chunkFutures =
                chunks.stream()
                        .map(chunk -> CompletableFuture.supplyAsync(() ->
                                processHHHChunk(chunk, assetOwner, exceptCusts, month, outMap), dataProcessorExecutor))
                        .collect(Collectors.toList());

        // 等待所有分片处理完成并合并结果
        List<InventoryCurrentPeriodEntry> result = chunkFutures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理换货号生产入数据分片时发生异常", e);
                        return new ArrayList<InventoryCurrentPeriodEntry>();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        log.info("换货号生产入数据并行处理完成，结果数量：{}", result.size());
        return result;
    }

    /**
     * 处理换货号生产入数据分片
     */
    private List<InventoryCurrentPeriodEntry> processHHHChunk(
            List<LogInventoryIoTransaction> chunk,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month,
            Map<String, BigDecimal> outMap) {

        List<InventoryCurrentPeriodEntry> chunkResult = new ArrayList<>();

        for (LogInventoryIoTransaction item : chunk) {
            try {
                InventoryCurrentPeriodEntry result = processHHHSingleItem(item, assetOwner, exceptCusts, month, outMap);
                if (result != null) {
                    chunkResult.add(result);
                }
            } catch (Exception e) {
                log.error("处理单条换货号生产入数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }

        return chunkResult;
    }

    /**
     * 串行处理换货号数据
     */
    private List<InventoryCurrentPeriodEntry> processHHHSequentially(
            List<LogInventoryIoTransaction> logInventoryIoTransactions,
            AssetOwner assetOwner,
            List<String> exceptCusts,
            String month) {

        List<InventoryCurrentPeriodEntry> hhh = new ArrayList<>();

        // 分类换货号出 和 换货号入
        List<LogInventoryIoTransaction> outList = logInventoryIoTransactions.stream()
                .filter(t -> t.getLiotTyp() == 500601 || t.getLiotTyp() == 500603)
                .collect(Collectors.toList());
        List<LogInventoryIoTransaction> inList = logInventoryIoTransactions.stream()
                .filter(t -> t.getLiotTyp() == 500602 || t.getLiotTyp() == 500604)
                .collect(Collectors.toList());

        // 找出要排除的
        Set<String> excludeRmk1 = outList.stream()
                .filter(out -> inList.stream().anyMatch(in ->
                        Objects.equals(out.getLiotRmk1(), in.getLiotRmk1()) &&
                                Objects.equals(out.getLiotPpcd(), in.getLiotPpcd()) &&
                                (out.getLiotQty().compareTo(in.getLiotQty()) == 0
                                        || in.getLiotQty().compareTo(out.getLiotQty().multiply(BigDecimal.valueOf(2))) == 0)))
                .map(LogInventoryIoTransaction::getLiotRmk1)
                .collect(Collectors.toSet());

        Map<String, BigDecimal> outMap = outList.stream()
                .filter(out -> !excludeRmk1.contains(out.getLiotRmk1()))
                .collect(Collectors.groupingBy(LogInventoryIoTransaction::getLiotPpcd,
                        Collectors.mapping(LogInventoryIoTransaction::getLiotQty,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        for (LogInventoryIoTransaction item : outList) {
            try {
                InventoryCurrentPeriodEntry result = processHHHSingleItem(item, assetOwner, exceptCusts, month, outMap);
                if (result != null) {
                    handleinventoryCurrentPeriodEntry(result, hhh);
                }
            } catch (Exception e) {
                log.error("处理换货号数据时发生异常，数据ID：{}", item.getId(), e);
            }
        }
        return hhh;
    }

    private InventoryCurrentPeriodEntry processHHHSingleItem(LogInventoryIoTransaction item, AssetOwner assetOwner, List<String> exceptCusts, String month, Map<String, BigDecimal> outMap) {
        try {
            // 使用缓存工具方法获取产品信息
            BasProdPkg basProdPkg = getCachedBasProdPkg(item.getLiotPpcd());
            BasProdMst basProdMst = getCachedBasProdMst(basProdPkg.getBppPcd());
            // 排除产品种类为服务费的
            if ("06".equals(basProdMst.getBpmSort())) {
                return null;
            }

            FinAccAvgCost finAccAvgCost = new FinAccAvgCost();
            finAccAvgCost.setFaacId(finAccMasterRecordsUniqService.getFaacId(assetOwner.code(), month));
            finAccAvgCost.setSku(basProdPkg.getBppCd());
            finAccAvgCost = finAccAvgCostService.selectFinAccAvgCostByFaacId(finAccAvgCost);

            if (outMap.containsKey(item.getLiotPpcd())
                    && finAccAvgCost.getFaacQty().compareTo(outMap.get(item.getLiotPpcd())) < 0) {
                return null;
            }

            FinProdCode finProdCode = new FinProdCode();
            finProdCode.setBpmCcd(basProdMst.getBpmCcd());
            finProdCode.setBpmSort(basProdMst.getBpmSort());
            List<FinProdCode> finProdCodes = finProdCodeService.selectFinProdCodeList(finProdCode);
            if (finProdCodes == null || finProdCodes.isEmpty()) {
                return null;
            }
            finProdCode = finProdCodes.get(0);

            skuMasterService.insertSkuMasterIfNotExist(item.getLiotPpcd());

            LogInventoryIoTransaction loit1Param = new LogInventoryIoTransaction();
            List<String> liot_typ = new ArrayList<>();
            liot_typ.add("500602");
            liot_typ.add("500604");
            loit1Param.setLiotRmk1(item.getLiotRmk1());
            loit1Param.setLiotTyps(liot_typ);
            loit1Param.setLiotTyps(liot_typ);
            List<LogInventoryIoTransaction> loits1 = logInventoryIoTransactionService.selectLogInventoryIoTransactionList(loit1Param);
            LogInventoryIoTransaction loit1 = loits1.get(0);
            //更新sku信息
            skuMasterService.insertSkuMasterIfNotExist(basProdPkg.getBppCd());
            // 封装数据
            InventoryCurrentPeriodEntry inventoryCurrentPeriodEntry = new InventoryCurrentPeriodEntry();
            inventoryCurrentPeriodEntry.setAccountingPeriod(DateUtils.parseDateToStr("YYYY-MM", item.getrCreDt()));
            inventoryCurrentPeriodEntry.setFinancialAccount(finProdCode.getLgcode());
            inventoryCurrentPeriodEntry.setSku(item.getLiotPpcd());
            inventoryCurrentPeriodEntry.setSupplierCode("");
            inventoryCurrentPeriodEntry.setSupplierName("");
            inventoryCurrentPeriodEntry.setSourceType("SELF_PRODUCED");
            inventoryCurrentPeriodEntry.setPurchaseScope("-");
            inventoryCurrentPeriodEntry.setQuantity(item.getLiotQty());
            BigDecimal amount;
            BigDecimal price = item.getLiotQty().multiply(item.getLiotUnitp());
            BigDecimal amt = item.getLiotIspart().equals(1L)
                    ? price.divide(item.getLiotBit().add(new BigDecimal(1)), 2, RoundingMode.HALF_UP)
                    : price;
            if (!"03".equals(basProdMst.getBpmSort())) {
                amount = item.getLiotQty().multiply(finAccAvgCost.getFaacCost());
                if (amount.compareTo(new BigDecimal(0)) == 0) {
                    amount = amt;
                }
            } else {
                amount = amt;
            }
            if (item.getLiotTyp() == 500603) {
                inventoryCurrentPeriodEntry.setAmount(finAccAvgCost.getFaacCost().multiply(item.getLiotQty()));
            } else {
                inventoryCurrentPeriodEntry.setAmount(amount);
            }
            inventoryCurrentPeriodEntry.setBatchNumber(loit1.getLiotDmcd());
            inventoryCurrentPeriodEntry.setWarehouse(loit1.getLiotDmcd());
            inventoryCurrentPeriodEntry.setStorageLocation(loit1.getLiotSlcd());
            inventoryCurrentPeriodEntry.setStorageZone(loit1.getLiotTcd());
            inventoryCurrentPeriodEntry.setSourceTable("log_inventory_io_transaction");
            inventoryCurrentPeriodEntry.setSourceRecordId(loit1.getId());
            inventoryCurrentPeriodEntry.setEntryTime(loit1.getrCreDt());
            String rCreKid = item.getrCreKid() != null ? loit1.getrCreKid() : "";
            inventoryCurrentPeriodEntry.setOperator(rCreKid);
            inventoryCurrentPeriodEntry.setAuditor(rCreKid);
            inventoryCurrentPeriodEntry.setAuditTime(loit1.getrCreDt());
            inventoryCurrentPeriodEntry.setEncryptedBatch(loit1.getLiotBnEncrypt());
            String documentType = item.getLiotTyp() == 500601 ? "换货号生产入" : "换货号入";
            inventoryCurrentPeriodEntry.setDocumentType(documentType);
            inventoryCurrentPeriodEntry.setAssetOwner(item.getLiotOrgmcd());
            inventoryCurrentPeriodEntry.setProcessor(rCreKid);
            inventoryCurrentPeriodEntry.setProcessTime(item.getrCreDt());
            inventoryCurrentPeriodEntry.setDepartment("LOG");
            // 部门审核人
            SystemUser manager = null;
            if (rCreKid != null) {
                manager = systemUserService.selectSystemUserManager(rCreKid);
            }
            if (manager != null) {
                inventoryCurrentPeriodEntry.setDepartmentAuditor(manager.getSuLogno());
                inventoryCurrentPeriodEntry.setDepartmentAuditTime(item.getrCreDt());
            }
            inventoryCurrentPeriodEntry.setCreatedBy(rCreKid);
            inventoryCurrentPeriodEntry.setUpdatedBy(rCreKid);

            return inventoryCurrentPeriodEntry;
        } catch (Exception e) {
            log.error("处理换货号数据时发生异常，数据ID：{}", item.getId(), e);
            return null;
        }
    }


    /**
     * 发送数据同步开始通知
     * @param operationType 操作类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param assetOwner 资产主体
     */
    private void sendStartNotification(String operationType, String startDate, String endDate, AssetOwner assetOwner) {
        WXUtils.invokeWorkNotification("***********",
                operationType + "数据同步开始执行，日期：" + startDate + "至" + endDate + "，资产主体：" + assetOwner.code());
    }

    /**
     * 创建库存事务查询对象
     * @param liotTyps 事务类型列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param assetOwner 资产主体
     * @return 查询对象
     */
    private LogInventoryIoTransaction createInventoryQuery(List<String> liotTyps, String startDate, String endDate, AssetOwner assetOwner) {
        LogInventoryIoTransaction logInventoryIoTransaction = new LogInventoryIoTransaction();
        logInventoryIoTransaction.setLiotTyps(liotTyps);
        logInventoryIoTransaction.setStartDate(startDate);
        logInventoryIoTransaction.setEndDate(endDate);
        logInventoryIoTransaction.setLiotOrgmcd(assetOwner.code());
        return logInventoryIoTransaction;
    }

    /**
     * 获取产品包装信息（带缓存）
     * @param packageCode 包装编号
     * @return 产品包装信息
     */
    private BasProdPkg getCachedBasProdPkg(String packageCode) {
        if (packageCode == null || packageCode.trim().isEmpty()) {
            throw new IllegalArgumentException("包装编号不能为空");
        }

        if (basProdPkgMap.containsKey(packageCode)) {
            BasProdPkg cached = basProdPkgMap.get(packageCode);
            if (cached == null) {
                throw new RuntimeException("产品包装信息不存在，包装编号：" + packageCode);
            }
            return cached;
        } else {
            BasProdPkg basProdPkg = basProdPkgService.selectBasProdPkgByBppCd(packageCode);
            if (basProdPkg == null) {
                throw new RuntimeException("产品包装信息不存在，包装编号：" + packageCode);
            }
            basProdPkgMap.put(packageCode, basProdPkg);
            return basProdPkg;
        }
    }

    /**
     * 获取产品主表信息（带缓存）
     * @param productCode 产品编号
     * @return 产品主表信息
     */
    private BasProdMst getCachedBasProdMst(String productCode) {
        if (productCode == null || productCode.trim().isEmpty()) {
            throw new IllegalArgumentException("产品编号不能为空");
        }

        if (basProdMstMap.containsKey(productCode)) {
            BasProdMst cached = basProdMstMap.get(productCode);
            if (cached == null) {
                throw new RuntimeException("产品主表信息不存在，产品编号：" + productCode);
            }
            return cached;
        } else {
            BasProdMst basProdMst = basProdMstService.selectBasProdMstByBpmCd(productCode);
            if (basProdMst == null) {
                throw new RuntimeException("产品主表信息不存在，产品编号：" + productCode);
            }
            basProdMstMap.put(productCode, basProdMst);
            return basProdMst;
        }
    }

    /**
     * 验证LogInventoryIoTransaction对象的必要字段
     * @param item 库存事务对象
     * @return 是否通过验证
     */
    private boolean validateInventoryTransaction(LogInventoryIoTransaction item) {
        if (item == null) {
            return false;
        }

        // 检查关键字段
        if (item.getLiotPpcd() == null || item.getLiotPpcd().trim().isEmpty()) {
            return false;
        }

        if (item.getLiotQty() == null) {
            return false;
        }

        if (item.getrCreDt() == null) {
            return false;
        }

        return true;
    }

    /**
     * 安全获取BigDecimal值，如果为null则返回ZERO
     * @param value 原始值
     * @return 安全的BigDecimal值
     */
    private BigDecimal safeGetBigDecimal(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    /**
     * 安全获取Long值，如果为null则返回0L
     * @param value 原始值
     * @return 安全的Long值
     */
    private Long safeGetLong(Long value) {
        return value != null ? value : 0L;
    }

    /**
     * 安全获取String值，如果为null则返回空字符串
     * @param value 原始值
     * @return 安全的String值
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }

    /**
     * 销毁方法，关闭线程池
     */
    @PreDestroy
    public void destroy() {
        // 关闭业务处理线程池
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }

        // 关闭数据处理线程池
        if (dataProcessorExecutor != null && !dataProcessorExecutor.isShutdown()) {
            dataProcessorExecutor.shutdown();
            try {
                if (!dataProcessorExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    dataProcessorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                dataProcessorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

}
