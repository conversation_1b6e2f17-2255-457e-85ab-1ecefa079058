package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.PurContractMst;

/**
 * 采购_合同_主
Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface PurContractMstMapper 
{
    /**
     * 查询采购_合同_主

     * 
     * @param pcmCd 采购_合同_主
主键
     * @return 采购_合同_主

     */
    public PurContractMst selectPurContractMstByPcmCd(String pcmCd);

    /**
     * 查询采购_合同_主
列表
     * 
     * @param purContractMst 采购_合同_主

     * @return 采购_合同_主
集合
     */
    public List<PurContractMst> selectPurContractMstList(PurContractMst purContractMst);

    /**
     * 新增采购_合同_主

     * 
     * @param purContractMst 采购_合同_主

     * @return 结果
     */
    public int insertPurContractMst(PurContractMst purContractMst);

    /**
     * 修改采购_合同_主

     * 
     * @param purContractMst 采购_合同_主

     * @return 结果
     */
    public int updatePurContractMst(PurContractMst purContractMst);

    /**
     * 删除采购_合同_主

     * 
     * @param pcmCd 采购_合同_主
主键
     * @return 结果
     */
    public int deletePurContractMstByPcmCd(String pcmCd);

    /**
     * 批量删除采购_合同_主

     * 
     * @param pcmCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePurContractMstByPcmCds(String[] pcmCds);

    List<String> selectPurContractMstListR();
}
