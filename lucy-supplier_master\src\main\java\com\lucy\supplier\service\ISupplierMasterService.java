package com.lucy.supplier.service;

import java.util.List;
import com.lucy.supplier.domain.SupplierMaster;

/**
 * 供应商主数据 Supplier masterService接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface ISupplierMasterService 
{
    /**
     * 查询供应商主数据 Supplier master
     * 
     * @param code 供应商主数据 Supplier master主键
     * @return 供应商主数据 Supplier master
     */
    public SupplierMaster selectSupplierMasterByCode(String code);

    /**
     * 查询供应商主数据 Supplier master列表
     * 
     * @param supplierMaster 供应商主数据 Supplier master
     * @return 供应商主数据 Supplier master集合
     */
    public List<SupplierMaster> selectSupplierMasterList(SupplierMaster supplierMaster);

    /**
     * 新增供应商主数据 Supplier master
     * 
     * @param supplierMaster 供应商主数据 Supplier master
     * @return 结果
     */
    public int insertSupplierMaster(SupplierMaster supplierMaster);

    /**
     * 修改供应商主数据 Supplier master
     * 
     * @param supplierMaster 供应商主数据 Supplier master
     * @return 结果
     */
    public int updateSupplierMaster(SupplierMaster supplierMaster);

    /**
     * 批量删除供应商主数据 Supplier master
     * 
     * @param codes 需要删除的供应商主数据 Supplier master主键集合
     * @return 结果
     */
    public int deleteSupplierMasterByCodes(String[] codes);

    /**
     * 删除供应商主数据 Supplier master信息
     * 
     * @param code 供应商主数据 Supplier master主键
     * @return 结果
     */
    public int deleteSupplierMasterByCode(String code);
    
    public void insertSupplierMasterIfNotExist(String code);
}
