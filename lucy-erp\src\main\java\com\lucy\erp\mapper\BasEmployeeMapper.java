package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.BasEmployee;

/**
 * 基础_员工
Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface BasEmployeeMapper 
{
    /**
     * 查询基础_员工

     * 
     * @param beNo 基础_员工
主键
     * @return 基础_员工

     */
    public BasEmployee selectBasEmployeeByBeNo(String beNo);

    /**
     * 查询基础_员工
列表
     * 
     * @param basEmployee 基础_员工

     * @return 基础_员工
集合
     */
    public List<BasEmployee> selectBasEmployeeList(BasEmployee basEmployee);

    /**
     * 新增基础_员工

     * 
     * @param basEmployee 基础_员工

     * @return 结果
     */
    public int insertBasEmployee(BasEmployee basEmployee);

    /**
     * 修改基础_员工

     * 
     * @param basEmployee 基础_员工

     * @return 结果
     */
    public int updateBasEmployee(BasEmployee basEmployee);

    /**
     * 删除基础_员工

     * 
     * @param beNo 基础_员工
主键
     * @return 结果
     */
    public int deleteBasEmployeeByBeNo(String beNo);

    /**
     * 批量删除基础_员工

     * 
     * @param beNos 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasEmployeeByBeNos(String[] beNos);
}
