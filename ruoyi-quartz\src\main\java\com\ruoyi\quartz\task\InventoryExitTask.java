package com.ruoyi.quartz.task;

import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.inventoryexit.service.IInventoryCurrentPeriodIssueService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.WXUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 库存出库定时任务
 *
 * <AUTHOR>
 */
@Component("inventoryExitTask")
public class InventoryExitTask {

    private static final Logger log = LoggerFactory.getLogger(InventoryExitTask.class);

    @Autowired
    private IInventoryCurrentPeriodIssueService inventoryCurrentPeriodIssueService;

    /**
     * 无参数库存出库数据同步任务
     */
    public void syncCurrentMonthInventoryExit() {
        System.out.println("无参数库存出库数据同步任务");
    }

    /**
     * 多参数库存出库数据同步任务
     *
     * @param dateStart 开始日期（格式：yyyy-MM-dd）
     * @param dateEnd 结束日期（格式：yyyy-MM-dd）
     * @param zti 资产主体代码
     */
    public void syncInventoryExitWithMultiParams(String dateStart, String dateEnd, String zti) {
        log.info("开始执行库存出库数据同步任务（多参数）：开始日期={}, 结束日期={}, 资产主体={}",
                dateStart, dateEnd, zti);
        try {
            // 参数校验
            if (StringUtils.isEmpty(dateStart) || StringUtils.isEmpty(dateEnd)) {
                log.error("日期参数不能为空");
                throw new IllegalArgumentException("日期参数不能为空");
            }

            if (StringUtils.isEmpty(zti)) {
                log.warn("资产主体参数为空，使用默认值SESW");
            }

            log.info("执行库存出库数据同步，参数：开始日期={}, 结束日期={}, 资产主体={}", dateStart, dateEnd, zti);

            // 执行库存出库数据加载
//            inventoryCurrentPeriodIssueService.loadBQCK(dateStart, dateEnd, zti);

            log.info("库存出库数据同步任务执行完成");
        } catch (Exception e) {
            log.error("库存出库数据同步任务执行失败，参数：dateStart={}, dateEnd={}, zti={}",
                    dateStart, dateEnd, zti, e);
            throw e;
        }
    }
    /**
     * 多参数库存出库数据同步任务 全部资产主体
     *
     * @param dateStart 开始日期（格式：yyyy-MM-dd）
     * @param dateEnd 结束日期（格式：yyyy-MM-dd）
     */
    public void syncInventoryExitWithMultiParams(String dateStart, String dateEnd) {
        // 参数校验
        if (StringUtils.isEmpty(dateStart) || StringUtils.isEmpty(dateEnd)) {
            throw new IllegalArgumentException("日期参数不能为空");
        }

        String userid_zc = "18986648956";

        try {
            for (AssetOwner assetOwner : AssetOwner.values()) {
                WXUtils.invokeWorkNotification(userid_zc, "出库数据同步" + assetOwner.entity().getName()
                    + "开始执行，日期：" + dateStart + "至" + dateEnd);
                inventoryCurrentPeriodIssueService.loadBQCK(dateStart, dateEnd, assetOwner);
            }
        } catch (Exception e) {
            log.error("出库数据同步失败", e);
            WXUtils.invokeWorkNotification(userid_zc, "出库数据同步失败，日期：" + dateStart + "至" + dateEnd + "，错误信息：" + e.getMessage());
        }
    }

}
