package com.lucy.supplier.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.supplier.domain.SupplierMaster;
import com.lucy.supplier.service.ISupplierMasterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 供应商主数据 Supplier masterController
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/supplier/master")
public class SupplierMasterController extends BaseController
{
    @Autowired
    private ISupplierMasterService supplierMasterService;

    /**
     * 查询供应商主数据 Supplier master列表
     */
    @PreAuthorize("@ss.hasPermi('supplier:master:list')")
    @GetMapping("/list")
    public TableDataInfo list(SupplierMaster supplierMaster)
    {
        startPage();
        List<SupplierMaster> list = supplierMasterService.selectSupplierMasterList(supplierMaster);
        return getDataTable(list);
    }

    /**
     * 导出供应商主数据 Supplier master列表
     */
    @PreAuthorize("@ss.hasPermi('supplier:master:export')")
    @Log(title = "供应商主数据 Supplier master", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SupplierMaster supplierMaster)
    {
        List<SupplierMaster> list = supplierMasterService.selectSupplierMasterList(supplierMaster);
        ExcelUtil<SupplierMaster> util = new ExcelUtil<SupplierMaster>(SupplierMaster.class);
        util.exportExcel(response, list, "供应商主数据 Supplier master数据");
    }

    /**
     * 获取供应商主数据 Supplier master详细信息
     */
    @PreAuthorize("@ss.hasPermi('supplier:master:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(supplierMasterService.selectSupplierMasterByCode(code));
    }

    /**
     * 新增供应商主数据 Supplier master
     */
    @PreAuthorize("@ss.hasPermi('supplier:master:add')")
    @Log(title = "供应商主数据 Supplier master", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SupplierMaster supplierMaster)
    {
        return toAjax(supplierMasterService.insertSupplierMaster(supplierMaster));
    }

    /**
     * 修改供应商主数据 Supplier master
     */
    @PreAuthorize("@ss.hasPermi('supplier:master:edit')")
    @Log(title = "供应商主数据 Supplier master", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SupplierMaster supplierMaster)
    {
        return toAjax(supplierMasterService.updateSupplierMaster(supplierMaster));
    }

    /**
     * 删除供应商主数据 Supplier master
     */
    @PreAuthorize("@ss.hasPermi('supplier:master:remove')")
    @Log(title = "供应商主数据 Supplier master", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(supplierMasterService.deleteSupplierMasterByCodes(codes));
    }
}
