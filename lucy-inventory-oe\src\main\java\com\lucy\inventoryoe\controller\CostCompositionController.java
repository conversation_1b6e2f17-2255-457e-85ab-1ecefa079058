package com.lucy.inventoryoe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.inventoryoe.domain.CostComposition;
import com.lucy.inventoryoe.service.ICostCompositionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * Cost composition structureController
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@RestController
@RequestMapping("/lucy.inventoryoe/composition2")
public class CostCompositionController extends BaseController
{
    @Autowired
    private ICostCompositionService costCompositionService;

    /**
     * 查询Cost composition structure列表
     */
    @PreAuthorize("@ss.hasPermi('lucy.inventoryoe:composition2:list')")
    @GetMapping("/list")
    public TableDataInfo list(CostComposition costComposition)
    {
        startPage();
        List<CostComposition> list = costCompositionService.selectCostCompositionList(costComposition);
        return getDataTable(list);
    }

    /**
     * 导出Cost composition structure列表
     */
    @PreAuthorize("@ss.hasPermi('lucy.inventoryoe:composition2:export')")
    @Log(title = "Cost composition structure", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CostComposition costComposition)
    {
        List<CostComposition> list = costCompositionService.selectCostCompositionList(costComposition);
        ExcelUtil<CostComposition> util = new ExcelUtil<CostComposition>(CostComposition.class);
        util.exportExcel(response, list, "Cost composition structure数据");
    }

    /**
     * 获取Cost composition structure详细信息
     */
    @PreAuthorize("@ss.hasPermi('lucy.inventoryoe:composition2:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(costCompositionService.selectCostCompositionById(id));
    }

    /**
     * 新增Cost composition structure
     */
    @PreAuthorize("@ss.hasPermi('lucy.inventoryoe:composition2:add')")
    @Log(title = "Cost composition structure", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CostComposition costComposition)
    {
        return toAjax(costCompositionService.insertCostComposition(costComposition));
    }

    /**
     * 修改Cost composition structure
     */
    @PreAuthorize("@ss.hasPermi('lucy.inventoryoe:composition2:edit')")
    @Log(title = "Cost composition structure", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CostComposition costComposition)
    {
        return toAjax(costCompositionService.updateCostComposition(costComposition));
    }

    /**
     * 删除Cost composition structure
     */
    @PreAuthorize("@ss.hasPermi('lucy.inventoryoe:composition2:remove')")
    @Log(title = "Cost composition structure", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(costCompositionService.deleteCostCompositionByIds(ids));
    }
}
