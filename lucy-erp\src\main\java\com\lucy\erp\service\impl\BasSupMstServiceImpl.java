package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasSupMstMapper;
import com.lucy.erp.domain.BasSupMst;
import com.lucy.erp.service.IBasSupMstService;

/**
 * 基础_供应商Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasSupMstServiceImpl implements IBasSupMstService
{
    @Autowired
    private BasSupMstMapper basSupMstMapper;

    /**
     * 查询基础_供应商
     * 
     * @param bsmCcd 基础_供应商主键
     * @return 基础_供应商
     */
    @Override
    public BasSupMst selectBasSupMstByBsmCcd(String bsmCcd)
    {
        return basSupMstMapper.selectBasSupMstByBsmCcd(bsmCcd);
    }

    /**
     * 查询基础_供应商
     *
     * @param bsmCd 基础_供应商CD
     * @return 基础_供应商
     */
    @Override
    public BasSupMst selectBasSupMstByBsmCd(String bsmCd)
    {
        return basSupMstMapper.selectBasSupMstByBsmCd(bsmCd);
    }

    /**
     * 查询基础_供应商列表
     * 
     * @param basSupMst 基础_供应商
     * @return 基础_供应商
     */
    @Override
    public List<BasSupMst> selectBasSupMstList(BasSupMst basSupMst)
    {
        return basSupMstMapper.selectBasSupMstList(basSupMst);
    }

    /**
     * 新增基础_供应商
     * 
     * @param basSupMst 基础_供应商
     * @return 结果
     */
    @Override
    public int insertBasSupMst(BasSupMst basSupMst)
    {
        return basSupMstMapper.insertBasSupMst(basSupMst);
    }

    /**
     * 修改基础_供应商
     * 
     * @param basSupMst 基础_供应商
     * @return 结果
     */
    @Override
    public int updateBasSupMst(BasSupMst basSupMst)
    {
        return basSupMstMapper.updateBasSupMst(basSupMst);
    }

    /**
     * 批量删除基础_供应商
     * 
     * @param bsmCcds 需要删除的基础_供应商主键
     * @return 结果
     */
    @Override
    public int deleteBasSupMstByBsmCcds(String[] bsmCcds)
    {
        return basSupMstMapper.deleteBasSupMstByBsmCcds(bsmCcds);
    }

    /**
     * 删除基础_供应商信息
     * 
     * @param bsmCcd 基础_供应商主键
     * @return 结果
     */
    @Override
    public int deleteBasSupMstByBsmCcd(String bsmCcd)
    {
        return basSupMstMapper.deleteBasSupMstByBsmCcd(bsmCcd);
    }
}
