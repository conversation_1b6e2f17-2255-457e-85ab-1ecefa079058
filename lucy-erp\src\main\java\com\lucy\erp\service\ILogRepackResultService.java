package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogRepackResult;

/**
 * 产成品Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ILogRepackResultService 
{
    /**
     * 查询产成品
     * 
     * @param lrrCd 产成品主键
     * @return 产成品
     */
    public LogRepackResult selectLogRepackResultByLrrCd(String lrrCd);

    /**
     * 查询产成品列表
     * 
     * @param logRepackResult 产成品
     * @return 产成品集合
     */
    public List<LogRepackResult> selectLogRepackResultList(LogRepackResult logRepackResult);

    /**
     * 新增产成品
     * 
     * @param logRepackResult 产成品
     * @return 结果
     */
    public int insertLogRepackResult(LogRepackResult logRepackResult);

    /**
     * 修改产成品
     * 
     * @param logRepackResult 产成品
     * @return 结果
     */
    public int updateLogRepackResult(LogRepackResult logRepackResult);

    /**
     * 批量删除产成品
     * 
     * @param lrrCds 需要删除的产成品主键集合
     * @return 结果
     */
    public int deleteLogRepackResultByLrrCds(String[] lrrCds);

    /**
     * 删除产成品信息
     * 
     * @param lrrCd 产成品主键
     * @return 结果
     */
    public int deleteLogRepackResultByLrrCd(String lrrCd);
}
