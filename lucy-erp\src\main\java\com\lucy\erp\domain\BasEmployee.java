package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_员工
对象 bas_employee
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class BasEmployee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 员工编号 */
    private String beNo;

    /** 员工姓名 */
    @Excel(name = "员工姓名")
    private String beNm;

    /** 员工英文名 */
    @Excel(name = "员工英文名")
    private String beEnm;

    /** 所属组织编号 */
    @Excel(name = "所属组织编号")
    private String beMcd;

    /** 部门 */
    @Excel(name = "部门")
    private String beDept;

    /** 办公所在地 */
    @Excel(name = "办公所在地")
    private String beProv;

    /** 员工头衔 */
    @Excel(name = "员工头衔")
    private String beTitl;

    /** 性别 */
    @Excel(name = "性别")
    private String beSex;

    /** 生日 */
    @Excel(name = "生日")
    private String beBd;

    /** 入职日期 */
    @Excel(name = "入职日期")
    private String beRd;

    /** 座机 */
    @Excel(name = "座机")
    private String beFp;

    /** 手机 */
    @Excel(name = "手机")
    private String beMp;

    /** 员工电子邮箱 */
    @Excel(name = "员工电子邮箱")
    private String beEmail;

    /** 经理 */
    @Excel(name = "经理")
    private String beMng;

    /** 助理 */
    @Excel(name = "助理")
    private String beAss;

    /** 个人信用额度 */
    @Excel(name = "个人信用额度")
    private BigDecimal beCrd;

    /** 个人已使用信用额度 */
    @Excel(name = "个人已使用信用额度")
    private Long beCrdBud;

    /** QQ */
    @Excel(name = "QQ")
    private String beQq;

    /** 所属组织 */
    @Excel(name = "所属组织")
    private String beOrgcd;

    /** 是否离职 Y:是  N:否 */
    @Excel(name = "是否离职 Y:是  N:否")
    private String beIsquit;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBeNo(String beNo) 
    {
        this.beNo = beNo;
    }

    public String getBeNo() 
    {
        return beNo;
    }

    public void setBeNm(String beNm) 
    {
        this.beNm = beNm;
    }

    public String getBeNm() 
    {
        return beNm;
    }

    public void setBeEnm(String beEnm) 
    {
        this.beEnm = beEnm;
    }

    public String getBeEnm() 
    {
        return beEnm;
    }

    public void setBeMcd(String beMcd) 
    {
        this.beMcd = beMcd;
    }

    public String getBeMcd() 
    {
        return beMcd;
    }

    public void setBeDept(String beDept) 
    {
        this.beDept = beDept;
    }

    public String getBeDept() 
    {
        return beDept;
    }

    public void setBeProv(String beProv) 
    {
        this.beProv = beProv;
    }

    public String getBeProv() 
    {
        return beProv;
    }

    public void setBeTitl(String beTitl) 
    {
        this.beTitl = beTitl;
    }

    public String getBeTitl() 
    {
        return beTitl;
    }

    public void setBeSex(String beSex) 
    {
        this.beSex = beSex;
    }

    public String getBeSex() 
    {
        return beSex;
    }

    public void setBeBd(String beBd) 
    {
        this.beBd = beBd;
    }

    public String getBeBd() 
    {
        return beBd;
    }

    public void setBeRd(String beRd) 
    {
        this.beRd = beRd;
    }

    public String getBeRd() 
    {
        return beRd;
    }

    public void setBeFp(String beFp) 
    {
        this.beFp = beFp;
    }

    public String getBeFp() 
    {
        return beFp;
    }

    public void setBeMp(String beMp) 
    {
        this.beMp = beMp;
    }

    public String getBeMp() 
    {
        return beMp;
    }

    public void setBeEmail(String beEmail) 
    {
        this.beEmail = beEmail;
    }

    public String getBeEmail() 
    {
        return beEmail;
    }

    public void setBeMng(String beMng) 
    {
        this.beMng = beMng;
    }

    public String getBeMng() 
    {
        return beMng;
    }

    public void setBeAss(String beAss) 
    {
        this.beAss = beAss;
    }

    public String getBeAss() 
    {
        return beAss;
    }

    public void setBeCrd(BigDecimal beCrd) 
    {
        this.beCrd = beCrd;
    }

    public BigDecimal getBeCrd() 
    {
        return beCrd;
    }

    public void setBeCrdBud(Long beCrdBud) 
    {
        this.beCrdBud = beCrdBud;
    }

    public Long getBeCrdBud() 
    {
        return beCrdBud;
    }

    public void setBeQq(String beQq) 
    {
        this.beQq = beQq;
    }

    public String getBeQq() 
    {
        return beQq;
    }

    public void setBeOrgcd(String beOrgcd) 
    {
        this.beOrgcd = beOrgcd;
    }

    public String getBeOrgcd() 
    {
        return beOrgcd;
    }

    public void setBeIsquit(String beIsquit) 
    {
        this.beIsquit = beIsquit;
    }

    public String getBeIsquit() 
    {
        return beIsquit;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("beNo", getBeNo())
            .append("beNm", getBeNm())
            .append("beEnm", getBeEnm())
            .append("beMcd", getBeMcd())
            .append("beDept", getBeDept())
            .append("beProv", getBeProv())
            .append("beTitl", getBeTitl())
            .append("beSex", getBeSex())
            .append("beBd", getBeBd())
            .append("beRd", getBeRd())
            .append("beFp", getBeFp())
            .append("beMp", getBeMp())
            .append("beEmail", getBeEmail())
            .append("beMng", getBeMng())
            .append("beAss", getBeAss())
            .append("beCrd", getBeCrd())
            .append("beCrdBud", getBeCrdBud())
            .append("beQq", getBeQq())
            .append("beOrgcd", getBeOrgcd())
            .append("beIsquit", getBeIsquit())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .toString();
    }
}
