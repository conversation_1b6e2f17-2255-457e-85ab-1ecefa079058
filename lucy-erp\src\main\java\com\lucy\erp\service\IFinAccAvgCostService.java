package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.FinAccAvgCost;

/**
 * 财务_平均_费Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IFinAccAvgCostService 
{
    /**
     * 查询财务_平均_费
     * 
     * @param finAccAvgCost 财务_平均_费主键
     * @return 财务_平均_费
     */
    public FinAccAvgCost selectFinAccAvgCostByFaacId(FinAccAvgCost finAccAvgCost);

    /**
     * 查询财务_平均_费列表
     * 
     * @param finAccAvgCost 财务_平均_费
     * @return 财务_平均_费集合
     */
    public List<FinAccAvgCost> selectFinAccAvgCostList(FinAccAvgCost finAccAvgCost);

    /**
     * 新增财务_平均_费
     * 
     * @param finAccAvgCost 财务_平均_费
     * @return 结果
     */
    public int insertFinAccAvgCost(FinAccAvgCost finAccAvgCost);

    /**
     * 修改财务_平均_费
     * 
     * @param finAccAvgCost 财务_平均_费
     * @return 结果
     */
    public int updateFinAccAvgCost(FinAccAvgCost finAccAvgCost);

    /**
     * 批量删除财务_平均_费
     * 
     * @param faacIds 需要删除的财务_平均_费主键集合
     * @return 结果
     */
    public int deleteFinAccAvgCostByFaacIds(String[] faacIds);

    /**
     * 删除财务_平均_费信息
     * 
     * @param faacId 财务_平均_费主键
     * @return 结果
     */
    public int deleteFinAccAvgCostByFaacId(String faacId);
}
