package com.lucy.inventoryoe.mapper;

import java.util.List;
import com.lucy.inventoryoe.domain.Zw20241231FinAccInventoryOe2;

/**
 * 24期初库存初始化Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface Zw20241231FinAccInventoryOe2Mapper 
{
    /**
     * 查询24期初库存初始化
     * 
     * @param orgcd 24期初库存初始化主键
     * @return 24期初库存初始化
     */
    public Zw20241231FinAccInventoryOe2 selectZw20241231FinAccInventoryOe2ByOrgcd(String orgcd);

    /**
     * 查询24期初库存初始化列表
     * 
     * @param zw20241231FinAccInventoryOe2 24期初库存初始化
     * @return 24期初库存初始化集合
     */
    public List<Zw20241231FinAccInventoryOe2> selectZw20241231FinAccInventoryOe2List(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2);

    /**
     * 新增24期初库存初始化
     * 
     * @param zw20241231FinAccInventoryOe2 24期初库存初始化
     * @return 结果
     */
    public int insertZw20241231FinAccInventoryOe2(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2);

    /**
     * 修改24期初库存初始化
     * 
     * @param zw20241231FinAccInventoryOe2 24期初库存初始化
     * @return 结果
     */
    public int updateZw20241231FinAccInventoryOe2(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2);

    /**
     * 删除24期初库存初始化
     * 
     * @param orgcd 24期初库存初始化主键
     * @return 结果
     */
    public int deleteZw20241231FinAccInventoryOe2ByOrgcd(String orgcd);

    /**
     * 批量删除24期初库存初始化
     * 
     * @param orgcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZw20241231FinAccInventoryOe2ByOrgcds(String[] orgcds);

    public List<Zw20241231FinAccInventoryOe2> selectZw20241231FinAccInventoryOe2ByType(String type);

    List<Zw20241231FinAccInventoryOe2> selectZw20241231FinAccInventoryOe2ByTypeAndOrgcd(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2);
}
