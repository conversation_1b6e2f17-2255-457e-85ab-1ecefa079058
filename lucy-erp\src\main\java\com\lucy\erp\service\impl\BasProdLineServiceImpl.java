package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasProdLineMapper;
import com.lucy.erp.domain.BasProdLine;
import com.lucy.erp.service.IBasProdLineService;

/**
 * 产线数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasProdLineServiceImpl implements IBasProdLineService
{
    @Autowired
    private BasProdLineMapper basProdLineMapper;

    /**
     * 查询产线数据
     * 
     * @param id 产线数据主键
     * @return 产线数据
     */
    @Override
    public BasProdLine selectBasProdLineById(Long id)
    {
        return basProdLineMapper.selectBasProdLineById(id);
    }

    /**
     * 查询产线数据列表
     * 
     * @param basProdLine 产线数据
     * @return 产线数据
     */
    @Override
    public List<BasProdLine> selectBasProdLineList(BasProdLine basProdLine)
    {
        return basProdLineMapper.selectBasProdLineList(basProdLine);
    }

    /**
     * 新增产线数据
     * 
     * @param basProdLine 产线数据
     * @return 结果
     */
    @Override
    public int insertBasProdLine(BasProdLine basProdLine)
    {
        return basProdLineMapper.insertBasProdLine(basProdLine);
    }

    /**
     * 修改产线数据
     * 
     * @param basProdLine 产线数据
     * @return 结果
     */
    @Override
    public int updateBasProdLine(BasProdLine basProdLine)
    {
        return basProdLineMapper.updateBasProdLine(basProdLine);
    }

    /**
     * 批量删除产线数据
     * 
     * @param ids 需要删除的产线数据主键
     * @return 结果
     */
    @Override
    public int deleteBasProdLineByIds(Long[] ids)
    {
        return basProdLineMapper.deleteBasProdLineByIds(ids);
    }

    /**
     * 删除产线数据信息
     * 
     * @param id 产线数据主键
     * @return 结果
     */
    @Override
    public int deleteBasProdLineById(Long id)
    {
        return basProdLineMapper.deleteBasProdLineById(id);
    }
}
