package com.ruoyi.repackage.service;

import java.util.List;
import com.ruoyi.repackage.domain.ConversionMaterialDependencyClosure;

/**
 * 物料依赖图谱Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IConversionMaterialDependencyClosureService 
{
    /**
     * 查询物料依赖图谱
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 物料依赖图谱
     */
    public ConversionMaterialDependencyClosure selectConversionMaterialDependencyClosureByAncestorSku(String ancestorSku);

    /**
     * 查询物料依赖图谱列表
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 物料依赖图谱集合
     */
    public List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureList(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure);

    /**
     * 新增物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    public int insertConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure);

    /**
     * 修改物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    public int updateConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure);

    /**
     * 批量删除物料依赖图谱
     * 
     * @param ancestorSkus 需要删除的物料依赖图谱主键集合
     * @return 结果
     */
    public int deleteConversionMaterialDependencyClosureByAncestorSkus(String[] ancestorSkus);

    /**
     * 删除物料依赖图谱信息
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 结果
     */
    public int deleteConversionMaterialDependencyClosureByAncestorSku(String ancestorSku);
}
