package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.LogRadDeveloProduct;

/**
 * 小样或者产成品结果Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface ILogRadDeveloProductService 
{
    /**
     * 查询小样或者产成品结果
     * 
     * @param lrdpCd 小样或者产成品结果主键
     * @return 小样或者产成品结果
     */
    public LogRadDeveloProduct selectLogRadDeveloProductByLrdpCd(String lrdpCd);

    /**
     * 查询小样或者产成品结果列表
     * 
     * @param logRadDeveloProduct 小样或者产成品结果
     * @return 小样或者产成品结果集合
     */
    public List<LogRadDeveloProduct> selectLogRadDeveloProductList(LogRadDeveloProduct logRadDeveloProduct);

    /**
     * 新增小样或者产成品结果
     * 
     * @param logRadDeveloProduct 小样或者产成品结果
     * @return 结果
     */
    public int insertLogRadDeveloProduct(LogRadDeveloProduct logRadDeveloProduct);

    /**
     * 修改小样或者产成品结果
     * 
     * @param logRadDeveloProduct 小样或者产成品结果
     * @return 结果
     */
    public int updateLogRadDeveloProduct(LogRadDeveloProduct logRadDeveloProduct);

    /**
     * 批量删除小样或者产成品结果
     * 
     * @param lrdpCds 需要删除的小样或者产成品结果主键集合
     * @return 结果
     */
    public int deleteLogRadDeveloProductByLrdpCds(String[] lrdpCds);

    /**
     * 删除小样或者产成品结果信息
     * 
     * @param lrdpCd 小样或者产成品结果主键
     * @return 结果
     */
    public int deleteLogRadDeveloProductByLrdpCd(String lrdpCd);
}
