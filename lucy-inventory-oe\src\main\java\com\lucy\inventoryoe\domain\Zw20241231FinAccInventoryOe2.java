package com.lucy.inventoryoe.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 24期初库存初始化对象 zw_20241231_fin_acc_inventory_oe
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public class Zw20241231FinAccInventoryOe2 extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主体编码, Owner code */
    @Excel(name = "主体编码, Owner code")
    private String orgcd;

    /** sku */
    @Excel(name = "sku")
    private String faiSku;

    /** 主体编码, Owner code */
    @Excel(name = "主体编码, Owner code")
    private BigDecimal amt;

    /** 主体编码, Owner code */
    @Excel(name = "主体编码, Owner code")
    private BigDecimal qty;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String faiSubject;

    /** 期末账套ID */
    @Excel(name = "期末账套ID")
    private String faiEid;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    public void setOrgcd(String orgcd) 
    {
        this.orgcd = orgcd;
    }

    public String getOrgcd() 
    {
        return orgcd;
    }

    public void setFaiSku(String faiSku) 
    {
        this.faiSku = faiSku;
    }

    public String getFaiSku() 
    {
        return faiSku;
    }

    public void setAmt(BigDecimal amt) 
    {
        this.amt = amt;
    }

    public BigDecimal getAmt() 
    {
        return amt;
    }

    public void setQty(BigDecimal qty) 
    {
        this.qty = qty;
    }

    public BigDecimal getQty() 
    {
        return qty;
    }

    public void setFaiSubject(String faiSubject) 
    {
        this.faiSubject = faiSubject;
    }

    public String getFaiSubject() 
    {
        return faiSubject;
    }

    public void setFaiEid(String faiEid) 
    {
        this.faiEid = faiEid;
    }

    public String getFaiEid() 
    {
        return faiEid;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orgcd", getOrgcd())
            .append("faiSku", getFaiSku())
            .append("amt", getAmt())
            .append("qty", getQty())
            .append("faiSubject", getFaiSubject())
            .append("faiEid", getFaiEid())
            .append("type", getType())
            .toString();
    }
}
