package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogRadDeveloComposeMapper;
import com.lucy.erp.domain.LogRadDeveloCompose;
import com.lucy.erp.service.ILogRadDeveloComposeService;

/**
 * 小样工单-主料明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogRadDeveloComposeServiceImpl implements ILogRadDeveloComposeService
{
    @Autowired
    private LogRadDeveloComposeMapper logRadDeveloComposeMapper;

    /**
     * 查询小样工单-主料明细
     * 
     * @param lrdcCd 小样工单-主料明细主键
     * @return 小样工单-主料明细
     */
    @Override
    public LogRadDeveloCompose selectLogRadDeveloComposeByLrdcCd(String lrdcCd)
    {
        return logRadDeveloComposeMapper.selectLogRadDeveloComposeByLrdcCd(lrdcCd);
    }

    /**
     * 查询小样工单-主料明细列表
     * 
     * @param logRadDeveloCompose 小样工单-主料明细
     * @return 小样工单-主料明细
     */
    @Override
    public List<LogRadDeveloCompose> selectLogRadDeveloComposeList(LogRadDeveloCompose logRadDeveloCompose)
    {
        return logRadDeveloComposeMapper.selectLogRadDeveloComposeList(logRadDeveloCompose);
    }

    /**
     * 新增小样工单-主料明细
     * 
     * @param logRadDeveloCompose 小样工单-主料明细
     * @return 结果
     */
    @Override
    public int insertLogRadDeveloCompose(LogRadDeveloCompose logRadDeveloCompose)
    {
        return logRadDeveloComposeMapper.insertLogRadDeveloCompose(logRadDeveloCompose);
    }

    /**
     * 修改小样工单-主料明细
     * 
     * @param logRadDeveloCompose 小样工单-主料明细
     * @return 结果
     */
    @Override
    public int updateLogRadDeveloCompose(LogRadDeveloCompose logRadDeveloCompose)
    {
        return logRadDeveloComposeMapper.updateLogRadDeveloCompose(logRadDeveloCompose);
    }

    /**
     * 批量删除小样工单-主料明细
     * 
     * @param lrdcCds 需要删除的小样工单-主料明细主键
     * @return 结果
     */
    @Override
    public int deleteLogRadDeveloComposeByLrdcCds(String[] lrdcCds)
    {
        return logRadDeveloComposeMapper.deleteLogRadDeveloComposeByLrdcCds(lrdcCds);
    }

    /**
     * 删除小样工单-主料明细信息
     * 
     * @param lrdcCd 小样工单-主料明细主键
     * @return 结果
     */
    @Override
    public int deleteLogRadDeveloComposeByLrdcCd(String lrdcCd)
    {
        return logRadDeveloComposeMapper.deleteLogRadDeveloComposeByLrdcCd(lrdcCd);
    }
}
