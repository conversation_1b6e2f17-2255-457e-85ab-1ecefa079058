package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasInvoiceTyp;
import com.lucy.erp.service.IBasInvoiceTypService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_发票_类型
Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/sales/invtyp")
public class BasInvoiceTypController extends BaseController
{
    @Autowired
    private IBasInvoiceTypService basInvoiceTypService;

    /**
     * 查询基础_发票_类型
列表
     */
    @PreAuthorize("@ss.hasPermi('sales:invtyp:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasInvoiceTyp basInvoiceTyp)
    {
        startPage();
        List<BasInvoiceTyp> list = basInvoiceTypService.selectBasInvoiceTypList(basInvoiceTyp);
        return getDataTable(list);
    }

    /**
     * 导出基础_发票_类型
列表
     */
    @PreAuthorize("@ss.hasPermi('sales:invtyp:export')")
    @Log(title = "基础_发票_类型 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasInvoiceTyp basInvoiceTyp)
    {
        List<BasInvoiceTyp> list = basInvoiceTypService.selectBasInvoiceTypList(basInvoiceTyp);
        ExcelUtil<BasInvoiceTyp> util = new ExcelUtil<BasInvoiceTyp>(BasInvoiceTyp.class);
        util.exportExcel(response, list, "基础_发票_类型 数据");
    }

    /**
     * 获取基础_发票_类型
详细信息
     */
    @PreAuthorize("@ss.hasPermi('sales:invtyp:query')")
    @GetMapping(value = "/{bitCd}")
    public AjaxResult getInfo(@PathVariable("bitCd") String bitCd)
    {
        return success(basInvoiceTypService.selectBasInvoiceTypByBitCd(bitCd));
    }

    /**
     * 新增基础_发票_类型

     */
    @PreAuthorize("@ss.hasPermi('sales:invtyp:add')")
    @Log(title = "基础_发票_类型 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasInvoiceTyp basInvoiceTyp)
    {
        return toAjax(basInvoiceTypService.insertBasInvoiceTyp(basInvoiceTyp));
    }

    /**
     * 修改基础_发票_类型

     */
    @PreAuthorize("@ss.hasPermi('sales:invtyp:edit')")
    @Log(title = "基础_发票_类型 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasInvoiceTyp basInvoiceTyp)
    {
        return toAjax(basInvoiceTypService.updateBasInvoiceTyp(basInvoiceTyp));
    }

    /**
     * 删除基础_发票_类型

     */
    @PreAuthorize("@ss.hasPermi('sales:invtyp:remove')")
    @Log(title = "基础_发票_类型 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bitCds}")
    public AjaxResult remove(@PathVariable String[] bitCds)
    {
        return toAjax(basInvoiceTypService.deleteBasInvoiceTypByBitCds(bitCds));
    }
}
