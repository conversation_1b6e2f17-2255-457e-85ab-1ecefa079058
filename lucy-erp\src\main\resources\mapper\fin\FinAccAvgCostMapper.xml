<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.FinAccAvgCostMapper">
    
    <resultMap type="FinAccAvgCost" id="FinAccAvgCostResult">
        <result property="faacId"    column="faac_id"    />
        <result property="faacAccurTime"    column="faac_accur_time"    />
        <result property="faacCreater"    column="faac_creater"    />
        <result property="faacApprover"    column="faac_approver"    />
        <result property="faacApproTime"    column="faac_appro_time"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="sku"    column="sku"    />
        <result property="faacQty"    column="faac_qty"    />
        <result property="faacCost"    column="faac_cost"    />
    </resultMap>

    <sql id="selectFinAccAvgCostVo">
        select faac_id, faac_accur_time, faac_creater, faac_approver, faac_appro_time, r_cre_kid, r_cre_dt, sku, faac_qty, faac_cost from financial_account_db.fin_acc_avg_cost
    </sql>

    <select id="selectFinAccAvgCostList" parameterType="FinAccAvgCost" resultMap="FinAccAvgCostResult">
        <include refid="selectFinAccAvgCostVo"/>
        <where>  
            <if test="faacId != null  and faacId != ''"> and faac_id = #{faacId}</if>
            <if test="faacAccurTime != null "> and faac_accur_time = #{faacAccurTime}</if>
            <if test="faacCreater != null  and faacCreater != ''"> and faac_creater = #{faacCreater}</if>
            <if test="faacApprover != null  and faacApprover != ''"> and faac_approver = #{faacApprover}</if>
            <if test="faacApproTime != null "> and faac_appro_time = #{faacApproTime}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null  and rCreDt != ''"> and r_cre_dt = #{rCreDt}</if>
            <if test="sku != null  and sku != ''"> and sku = #{sku}</if>
            <if test="faacQty != null "> and faac_qty = #{faacQty}</if>
            <if test="faacCost != null "> and faac_cost = #{faacCost}</if>
        </where>
    </select>
    
    <select id="selectFinAccAvgCostByFaacId" parameterType="FinAccAvgCost" resultMap="FinAccAvgCostResult">
        <include refid="selectFinAccAvgCostVo"/>
        where faac_id = #{faacId}
        <if test="sku != null  and sku != ''"> and sku = #{sku}</if>
    </select>

    <insert id="insertFinAccAvgCost" parameterType="FinAccAvgCost">
        insert into fin_acc_avg_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="faacId != null">faac_id,</if>
            <if test="faacAccurTime != null">faac_accur_time,</if>
            <if test="faacCreater != null">faac_creater,</if>
            <if test="faacApprover != null">faac_approver,</if>
            <if test="faacApproTime != null">faac_appro_time,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="sku != null">sku,</if>
            <if test="faacQty != null">faac_qty,</if>
            <if test="faacCost != null">faac_cost,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="faacId != null">#{faacId},</if>
            <if test="faacAccurTime != null">#{faacAccurTime},</if>
            <if test="faacCreater != null">#{faacCreater},</if>
            <if test="faacApprover != null">#{faacApprover},</if>
            <if test="faacApproTime != null">#{faacApproTime},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="sku != null">#{sku},</if>
            <if test="faacQty != null">#{faacQty},</if>
            <if test="faacCost != null">#{faacCost},</if>
         </trim>
    </insert>

    <update id="updateFinAccAvgCost" parameterType="FinAccAvgCost">
        update fin_acc_avg_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="faacAccurTime != null">faac_accur_time = #{faacAccurTime},</if>
            <if test="faacCreater != null">faac_creater = #{faacCreater},</if>
            <if test="faacApprover != null">faac_approver = #{faacApprover},</if>
            <if test="faacApproTime != null">faac_appro_time = #{faacApproTime},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="sku != null">sku = #{sku},</if>
            <if test="faacQty != null">faac_qty = #{faacQty},</if>
            <if test="faacCost != null">faac_cost = #{faacCost},</if>
        </trim>
        where faac_id = #{faacId}
    </update>

    <delete id="deleteFinAccAvgCostByFaacId" parameterType="String">
        delete from fin_acc_avg_cost where faac_id = #{faacId}
    </delete>

    <delete id="deleteFinAccAvgCostByFaacIds" parameterType="String">
        delete from fin_acc_avg_cost where faac_id in 
        <foreach item="faacId" collection="array" open="(" separator="," close=")">
            #{faacId}
        </foreach>
    </delete>
</mapper>