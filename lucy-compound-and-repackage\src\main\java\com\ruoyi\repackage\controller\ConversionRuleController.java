package com.ruoyi.repackage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.repackage.domain.ConversionRule;
import com.ruoyi.repackage.service.IConversionRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分装规则主Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/repackage/rule")
public class ConversionRuleController extends BaseController
{
    @Autowired
    private IConversionRuleService conversionRuleService;

    /**
     * 查询分装规则主列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConversionRule conversionRule)
    {
        startPage();
        List<ConversionRule> list = conversionRuleService.selectConversionRuleList(conversionRule);
        return getDataTable(list);
    }

    /**
     * 导出分装规则主列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:rule:export')")
    @Log(title = "分装规则主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConversionRule conversionRule)
    {
        List<ConversionRule> list = conversionRuleService.selectConversionRuleList(conversionRule);
        ExcelUtil<ConversionRule> util = new ExcelUtil<ConversionRule>(ConversionRule.class);
        util.exportExcel(response, list, "分装规则主数据");
    }

    /**
     * 获取分装规则主详细信息
     */
    @PreAuthorize("@ss.hasPermi('repackage:rule:query')")
    @GetMapping(value = "/{ruleId}")
    public AjaxResult getInfo(@PathVariable("ruleId") Long ruleId)
    {
        return success(conversionRuleService.selectConversionRuleByRuleId(ruleId));
    }

    /**
     * 新增分装规则主
     */
    @PreAuthorize("@ss.hasPermi('repackage:rule:add')")
    @Log(title = "分装规则主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConversionRule conversionRule)
    {
//        return toAjax(conversionRuleService.insertConversionRule(conversionRule));
        return toAjax(0);
    }

    /**
     * 修改分装规则主
     */
    @PreAuthorize("@ss.hasPermi('repackage:rule:edit')")
    @Log(title = "分装规则主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConversionRule conversionRule)
    {
        return toAjax(conversionRuleService.updateConversionRule(conversionRule));
    }

    /**
     * 删除分装规则主
     */
    @PreAuthorize("@ss.hasPermi('repackage:rule:remove')")
    @Log(title = "分装规则主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ruleIds}")
    public AjaxResult remove(@PathVariable Long[] ruleIds)
    {
        return toAjax(conversionRuleService.deleteConversionRuleByRuleIds(ruleIds));
    }
}
