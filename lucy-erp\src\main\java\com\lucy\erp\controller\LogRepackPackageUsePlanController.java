package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.LogRepackPackageUsePlan;
import com.lucy.erp.service.ILogRepackPackageUsePlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产成品包材领料主Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/log/plan")
public class LogRepackPackageUsePlanController extends BaseController
{
    @Autowired
    private ILogRepackPackageUsePlanService logRepackPackageUsePlanService;

    /**
     * 查询产成品包材领料主列表
     */
    @PreAuthorize("@ss.hasPermi('log:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(LogRepackPackageUsePlan logRepackPackageUsePlan)
    {
        startPage();
        List<LogRepackPackageUsePlan> list = logRepackPackageUsePlanService.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);
        return getDataTable(list);
    }

    /**
     * 导出产成品包材领料主列表
     */
    @PreAuthorize("@ss.hasPermi('log:plan:export')")
    @Log(title = "产成品包材领料主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogRepackPackageUsePlan logRepackPackageUsePlan)
    {
        List<LogRepackPackageUsePlan> list = logRepackPackageUsePlanService.selectLogRepackPackageUsePlanList(logRepackPackageUsePlan);
        ExcelUtil<LogRepackPackageUsePlan> util = new ExcelUtil<LogRepackPackageUsePlan>(LogRepackPackageUsePlan.class);
        util.exportExcel(response, list, "产成品包材领料主数据");
    }

    /**
     * 获取产成品包材领料主详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:plan:query')")
    @GetMapping(value = "/{lrpupCd}")
    public AjaxResult getInfo(@PathVariable("lrpupCd") String lrpupCd)
    {
        return success(logRepackPackageUsePlanService.selectLogRepackPackageUsePlanByLrpupCd(lrpupCd));
    }

    /**
     * 新增产成品包材领料主
     */
    @PreAuthorize("@ss.hasPermi('log:plan:add')")
    @Log(title = "产成品包材领料主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogRepackPackageUsePlan logRepackPackageUsePlan)
    {
        return toAjax(logRepackPackageUsePlanService.insertLogRepackPackageUsePlan(logRepackPackageUsePlan));
    }

    /**
     * 修改产成品包材领料主
     */
    @PreAuthorize("@ss.hasPermi('log:plan:edit')")
    @Log(title = "产成品包材领料主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogRepackPackageUsePlan logRepackPackageUsePlan)
    {
        return toAjax(logRepackPackageUsePlanService.updateLogRepackPackageUsePlan(logRepackPackageUsePlan));
    }

    /**
     * 删除产成品包材领料主
     */
    @PreAuthorize("@ss.hasPermi('log:plan:remove')")
    @Log(title = "产成品包材领料主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lrpupCds}")
    public AjaxResult remove(@PathVariable String[] lrpupCds)
    {
        return toAjax(logRepackPackageUsePlanService.deleteLogRepackPackageUsePlanByLrpupCds(lrpupCds));
    }
}
