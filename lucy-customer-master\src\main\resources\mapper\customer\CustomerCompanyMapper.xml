<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.customer.mapper.CustomerCompanyMapper">
    
    <resultMap type="CustomerCompany" id="CustomerCompanyResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="taxNo"    column="tax_no"    />
        <result property="category"    column="category"    />
        <result property="classificationSubclass"    column="classification_subclass"    />
        <result property="classificationMajor"    column="classification_major"    />
        <result property="profile"    column="profile"    />
        <result property="mergerSituation"    column="merger_situation"    />
    </resultMap>

    <sql id="selectCustomerCompanyVo">
        select code, name, tax_no, category, classification_subclass, classification_major, profile, merger_situation from customer_company
    </sql>

    <select id="selectCustomerCompanyList" parameterType="CustomerCompany" resultMap="CustomerCompanyResult">
        <include refid="selectCustomerCompanyVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="taxNo != null  and taxNo != ''"> and tax_no = #{taxNo}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="classificationSubclass != null  and classificationSubclass != ''"> and classification_subclass = #{classificationSubclass}</if>
            <if test="classificationMajor != null  and classificationMajor != ''"> and classification_major = #{classificationMajor}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
            <if test="mergerSituation != null  and mergerSituation != ''"> and merger_situation = #{mergerSituation}</if>
        </where>
    </select>
    
    <select id="selectCustomerCompanyByCode" parameterType="String" resultMap="CustomerCompanyResult">
        <include refid="selectCustomerCompanyVo"/>
        where code = #{code}
    </select>

    <insert id="insertCustomerCompany" parameterType="CustomerCompany">
        insert into customer_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="taxNo != null and taxNo != ''">tax_no,</if>
            <if test="category != null">category,</if>
            <if test="classificationSubclass != null">classification_subclass,</if>
            <if test="classificationMajor != null">classification_major,</if>
            <if test="profile != null">profile,</if>
            <if test="mergerSituation != null">merger_situation,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="taxNo != null and taxNo != ''">#{taxNo},</if>
            <if test="category != null">#{category},</if>
            <if test="classificationSubclass != null">#{classificationSubclass},</if>
            <if test="classificationMajor != null">#{classificationMajor},</if>
            <if test="profile != null">#{profile},</if>
            <if test="mergerSituation != null">#{mergerSituation},</if>
         </trim>
    </insert>

    <update id="updateCustomerCompany" parameterType="CustomerCompany">
        update customer_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="taxNo != null and taxNo != ''">tax_no = #{taxNo},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="classificationSubclass != null and classificationSubclass != ''">classification_subclass = #{classificationSubclass},</if>
            <if test="classificationMajor != null and classificationMajor != ''">classification_major = #{classificationMajor},</if>
            <if test="profile != null and profile != ''">profile = #{profile},</if>
            <if test="mergerSituation != null and mergerSituation != ''">merger_situation = #{mergerSituation},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteCustomerCompanyByCode" parameterType="String">
        delete from customer_company where code = #{code}
    </delete>

    <delete id="deleteCustomerCompanyByCodes" parameterType="String">
        delete from customer_company where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>