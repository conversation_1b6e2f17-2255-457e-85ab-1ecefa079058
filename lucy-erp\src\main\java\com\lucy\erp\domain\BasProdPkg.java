package com.lucy.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础_产品_包装
对象 bas_prod_pkg
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class BasProdPkg extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 产品id */
    @Excel(name = "产品id")
    private Long bppPid;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String bppPcd;

    /** 包装编号 */
    private String bppCd;

    /** 装箱数量 */
    @Excel(name = "装箱数量")
    private Long bppSet;

    /** 包装数量 */
    @Excel(name = "包装数量")
    private BigDecimal bppQty;

    /** 包装单位 */
    @Excel(name = "包装单位")
    private String bppUnit;

    /** 包装信息 */
    @Excel(name = "包装信息")
    private String bppPack;

    /** 单位 开发票用 */
    @Excel(name = "单位 开发票用")
    private String bppUninvoice;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal bppPrc;

    /** 活动价(没有就为(null)) */
    @Excel(name = "活动价(没有就为(null))")
    private BigDecimal bppPprc;

    /** 标准计量 */
    @Excel(name = "标准计量")
    private BigDecimal bppStdQty;

    /** 标准计量单位 */
    @Excel(name = "标准计量单位")
    private String bppStdUnit;

    /** 转换率 */
    @Excel(name = "转换率")
    private BigDecimal bppRatio;

    /** 标准重量（单位默认G/ML） */
    @Excel(name = "标准重量", readConverterExp = "单=位默认G/ML")
    private BigDecimal bppMtw;

    /** 库位 */
    @Excel(name = "库位")
    private String bppPpd;

    /** 供应商目录价格 */
    @Excel(name = "供应商目录价格")
    private BigDecimal bppSupprc;

    /** 供应商折扣 */
    @Excel(name = "供应商折扣")
    private BigDecimal bppSupreb;

    /** Y:是 N:否(网站是否同步) */
    @Excel(name = "Y:是 N:否(网站是否同步)")
    private String bppSyn;

    /** 包装状态 */
    @Excel(name = "包装状态")
    private String bppSts;

    /** 新包装状态 */
    @Excel(name = "新包装状态")
    private String bppNsts;

    /** 包装状态备注 */
    @Excel(name = "包装状态备注")
    private String bppStsrmk;

    /** 发货包装-套筒 */
    @Excel(name = "发货包装-套筒")
    private String bppSleeve;

    /** 港版价格 */
    @Excel(name = "港版价格")
    private BigDecimal bppHkprc;

    /** 港版活动价 */
    @Excel(name = "港版活动价")
    private BigDecimal bppHkpprc;

    /** 美元价 */
    @Excel(name = "美元价")
    private BigDecimal bppUsdprc;

    /** 是否带危险品标志 是 否 */
    @Excel(name = "是否带危险品标志 是 否")
    private String bppDm;

    /** 售完即止状态 */
    @Excel(name = "售完即止状态")
    private String bppSsts;

    /** 售完即止备注 */
    @Excel(name = "售完即止备注")
    private String bppSstsrmk;

    /** 标签号 */
    @Excel(name = "标签号")
    private String bppLs;

    /** 包装工艺 */
    @Excel(name = "包装工艺")
    private String bppPkgStd;

    /** 包装级别 */
    @Excel(name = "包装级别")
    private String bppWrap;

    /** 创建者 */
    @Excel(name = "创建者")
    private String rCreKid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rCreDt;

    /** 更新者 */
    @Excel(name = "更新者")
    private String rUpdKid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rUpdDt;

    /** 行版本 */
    @Excel(name = "行版本")
    private Long rV;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long rDel;

    /** 供应商包装编号 */
    @Excel(name = "供应商包装编号")
    private String bppSupSkuCd;

    /** 包装后缀 */
    @Excel(name = "包装后缀")
    private String bppSuffix;

    /** 调价备注 */
    @Excel(name = "调价备注")
    private String bppRemark;

    /** 自动停产时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "自动停产时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bppAutoShutdownTime;

    /** 虚拟包装对应真实小包装的包装编号 */
    @Excel(name = "虚拟包装对应真实小包装的包装编号")
    private String bppCdReal;

    /** 虚拟包装对应真实小包装的包装倍数 */
    @Excel(name = "虚拟包装对应真实小包装的包装倍数")
    private Integer vpTime;

    /** 虚拟包装价格系数 */
    @Excel(name = "虚拟包装价格系数")
    private BigDecimal vpPriceScale;

    /** 是否是真实包装，1:是，0:否 */
    @Excel(name = "是否是真实包装，1:是，0:否")
    private Integer isReal;

    /** 规格参数信息 */
    @Excel(name = "规格参数信息")
    private String skuSpec;

    /** 附加属性信息 */
    @Excel(name = "附加属性信息")
    private String skuAttr;

    /** 是否特惠,1:是,0:否 */
    @Excel(name = "是否特惠,1:是,0:否")
    private Integer bppIsPreferential;

    /** 是否出口 Y:是  N：否 */
    @Excel(name = "是否出口 Y:是  N：否")
    private String bppIsexport;

    /** 包装信息(英文) */
    @Excel(name = "包装信息(英文)")
    private String bppPackEn;

    /** 包装属性 */
    @Excel(name = "包装属性")
    private String bppSpecAttr;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBppPid(Long bppPid) 
    {
        this.bppPid = bppPid;
    }

    public Long getBppPid() 
    {
        return bppPid;
    }

    public void setBppPcd(String bppPcd) 
    {
        this.bppPcd = bppPcd;
    }

    public String getBppPcd() 
    {
        return bppPcd;
    }

    public void setBppCd(String bppCd) 
    {
        this.bppCd = bppCd;
    }

    public String getBppCd() 
    {
        return bppCd;
    }

    public void setBppSet(Long bppSet) 
    {
        this.bppSet = bppSet;
    }

    public Long getBppSet() 
    {
        return bppSet;
    }

    public void setBppQty(BigDecimal bppQty) 
    {
        this.bppQty = bppQty;
    }

    public BigDecimal getBppQty() 
    {
        return bppQty;
    }

    public void setBppUnit(String bppUnit) 
    {
        this.bppUnit = bppUnit;
    }

    public String getBppUnit() 
    {
        return bppUnit;
    }

    public void setBppPack(String bppPack) 
    {
        this.bppPack = bppPack;
    }

    public String getBppPack() 
    {
        return bppPack;
    }

    public void setBppUninvoice(String bppUninvoice) 
    {
        this.bppUninvoice = bppUninvoice;
    }

    public String getBppUninvoice() 
    {
        return bppUninvoice;
    }

    public void setBppPrc(BigDecimal bppPrc) 
    {
        this.bppPrc = bppPrc;
    }

    public BigDecimal getBppPrc() 
    {
        return bppPrc;
    }

    public void setBppPprc(BigDecimal bppPprc) 
    {
        this.bppPprc = bppPprc;
    }

    public BigDecimal getBppPprc() 
    {
        return bppPprc;
    }

    public void setBppStdQty(BigDecimal bppStdQty) 
    {
        this.bppStdQty = bppStdQty;
    }

    public BigDecimal getBppStdQty() 
    {
        return bppStdQty;
    }

    public void setBppStdUnit(String bppStdUnit) 
    {
        this.bppStdUnit = bppStdUnit;
    }

    public String getBppStdUnit() 
    {
        return bppStdUnit;
    }

    public void setBppRatio(BigDecimal bppRatio) 
    {
        this.bppRatio = bppRatio;
    }

    public BigDecimal getBppRatio() 
    {
        return bppRatio;
    }

    public void setBppMtw(BigDecimal bppMtw) 
    {
        this.bppMtw = bppMtw;
    }

    public BigDecimal getBppMtw() 
    {
        return bppMtw;
    }

    public void setBppPpd(String bppPpd) 
    {
        this.bppPpd = bppPpd;
    }

    public String getBppPpd() 
    {
        return bppPpd;
    }

    public void setBppSupprc(BigDecimal bppSupprc) 
    {
        this.bppSupprc = bppSupprc;
    }

    public BigDecimal getBppSupprc() 
    {
        return bppSupprc;
    }

    public void setBppSupreb(BigDecimal bppSupreb) 
    {
        this.bppSupreb = bppSupreb;
    }

    public BigDecimal getBppSupreb() 
    {
        return bppSupreb;
    }

    public void setBppSyn(String bppSyn) 
    {
        this.bppSyn = bppSyn;
    }

    public String getBppSyn() 
    {
        return bppSyn;
    }

    public void setBppSts(String bppSts) 
    {
        this.bppSts = bppSts;
    }

    public String getBppSts() 
    {
        return bppSts;
    }

    public void setBppNsts(String bppNsts) 
    {
        this.bppNsts = bppNsts;
    }

    public String getBppNsts() 
    {
        return bppNsts;
    }

    public void setBppStsrmk(String bppStsrmk) 
    {
        this.bppStsrmk = bppStsrmk;
    }

    public String getBppStsrmk() 
    {
        return bppStsrmk;
    }

    public void setBppSleeve(String bppSleeve) 
    {
        this.bppSleeve = bppSleeve;
    }

    public String getBppSleeve() 
    {
        return bppSleeve;
    }

    public void setBppHkprc(BigDecimal bppHkprc) 
    {
        this.bppHkprc = bppHkprc;
    }

    public BigDecimal getBppHkprc() 
    {
        return bppHkprc;
    }

    public void setBppHkpprc(BigDecimal bppHkpprc) 
    {
        this.bppHkpprc = bppHkpprc;
    }

    public BigDecimal getBppHkpprc() 
    {
        return bppHkpprc;
    }

    public void setBppUsdprc(BigDecimal bppUsdprc) 
    {
        this.bppUsdprc = bppUsdprc;
    }

    public BigDecimal getBppUsdprc() 
    {
        return bppUsdprc;
    }

    public void setBppDm(String bppDm) 
    {
        this.bppDm = bppDm;
    }

    public String getBppDm() 
    {
        return bppDm;
    }

    public void setBppSsts(String bppSsts) 
    {
        this.bppSsts = bppSsts;
    }

    public String getBppSsts() 
    {
        return bppSsts;
    }

    public void setBppSstsrmk(String bppSstsrmk) 
    {
        this.bppSstsrmk = bppSstsrmk;
    }

    public String getBppSstsrmk() 
    {
        return bppSstsrmk;
    }

    public void setBppLs(String bppLs) 
    {
        this.bppLs = bppLs;
    }

    public String getBppLs() 
    {
        return bppLs;
    }

    public void setBppPkgStd(String bppPkgStd) 
    {
        this.bppPkgStd = bppPkgStd;
    }

    public String getBppPkgStd() 
    {
        return bppPkgStd;
    }

    public void setBppWrap(String bppWrap) 
    {
        this.bppWrap = bppWrap;
    }

    public String getBppWrap() 
    {
        return bppWrap;
    }

    public void setrCreKid(String rCreKid) 
    {
        this.rCreKid = rCreKid;
    }

    public String getrCreKid() 
    {
        return rCreKid;
    }

    public void setrCreDt(Date rCreDt) 
    {
        this.rCreDt = rCreDt;
    }

    public Date getrCreDt() 
    {
        return rCreDt;
    }

    public void setrUpdKid(String rUpdKid) 
    {
        this.rUpdKid = rUpdKid;
    }

    public String getrUpdKid() 
    {
        return rUpdKid;
    }

    public void setrUpdDt(Date rUpdDt) 
    {
        this.rUpdDt = rUpdDt;
    }

    public Date getrUpdDt() 
    {
        return rUpdDt;
    }

    public void setRV(Long rV) 
    {
        this.rV = rV;
    }

    public Long getRV() 
    {
        return rV;
    }

    public void setrDel(Long rDel) 
    {
        this.rDel = rDel;
    }

    public Long getrDel() 
    {
        return rDel;
    }

    public void setBppSupSkuCd(String bppSupSkuCd) 
    {
        this.bppSupSkuCd = bppSupSkuCd;
    }

    public String getBppSupSkuCd() 
    {
        return bppSupSkuCd;
    }

    public void setBppSuffix(String bppSuffix) 
    {
        this.bppSuffix = bppSuffix;
    }

    public String getBppSuffix() 
    {
        return bppSuffix;
    }

    public void setBppRemark(String bppRemark) 
    {
        this.bppRemark = bppRemark;
    }

    public String getBppRemark() 
    {
        return bppRemark;
    }

    public void setBppAutoShutdownTime(Date bppAutoShutdownTime) 
    {
        this.bppAutoShutdownTime = bppAutoShutdownTime;
    }

    public Date getBppAutoShutdownTime() 
    {
        return bppAutoShutdownTime;
    }

    public void setBppCdReal(String bppCdReal) 
    {
        this.bppCdReal = bppCdReal;
    }

    public String getBppCdReal() 
    {
        return bppCdReal;
    }

    public void setVpTime(Integer vpTime) 
    {
        this.vpTime = vpTime;
    }

    public Integer getVpTime() 
    {
        return vpTime;
    }

    public void setVpPriceScale(BigDecimal vpPriceScale) 
    {
        this.vpPriceScale = vpPriceScale;
    }

    public BigDecimal getVpPriceScale() 
    {
        return vpPriceScale;
    }

    public void setIsReal(Integer isReal) 
    {
        this.isReal = isReal;
    }

    public Integer getIsReal() 
    {
        return isReal;
    }

    public void setSkuSpec(String skuSpec) 
    {
        this.skuSpec = skuSpec;
    }

    public String getSkuSpec() 
    {
        return skuSpec;
    }

    public void setSkuAttr(String skuAttr) 
    {
        this.skuAttr = skuAttr;
    }

    public String getSkuAttr() 
    {
        return skuAttr;
    }

    public void setBppIsPreferential(Integer bppIsPreferential) 
    {
        this.bppIsPreferential = bppIsPreferential;
    }

    public Integer getBppIsPreferential() 
    {
        return bppIsPreferential;
    }

    public void setBppIsexport(String bppIsexport) 
    {
        this.bppIsexport = bppIsexport;
    }

    public String getBppIsexport() 
    {
        return bppIsexport;
    }

    public void setBppPackEn(String bppPackEn) 
    {
        this.bppPackEn = bppPackEn;
    }

    public String getBppPackEn() 
    {
        return bppPackEn;
    }

    public void setBppSpecAttr(String bppSpecAttr) 
    {
        this.bppSpecAttr = bppSpecAttr;
    }

    public String getBppSpecAttr() 
    {
        return bppSpecAttr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bppPid", getBppPid())
            .append("bppPcd", getBppPcd())
            .append("bppCd", getBppCd())
            .append("bppSet", getBppSet())
            .append("bppQty", getBppQty())
            .append("bppUnit", getBppUnit())
            .append("bppPack", getBppPack())
            .append("bppUninvoice", getBppUninvoice())
            .append("bppPrc", getBppPrc())
            .append("bppPprc", getBppPprc())
            .append("bppStdQty", getBppStdQty())
            .append("bppStdUnit", getBppStdUnit())
            .append("bppRatio", getBppRatio())
            .append("bppMtw", getBppMtw())
            .append("bppPpd", getBppPpd())
            .append("bppSupprc", getBppSupprc())
            .append("bppSupreb", getBppSupreb())
            .append("bppSyn", getBppSyn())
            .append("bppSts", getBppSts())
            .append("bppNsts", getBppNsts())
            .append("bppStsrmk", getBppStsrmk())
            .append("bppSleeve", getBppSleeve())
            .append("bppHkprc", getBppHkprc())
            .append("bppHkpprc", getBppHkpprc())
            .append("bppUsdprc", getBppUsdprc())
            .append("bppDm", getBppDm())
            .append("bppSsts", getBppSsts())
            .append("bppSstsrmk", getBppSstsrmk())
            .append("bppLs", getBppLs())
            .append("bppPkgStd", getBppPkgStd())
            .append("bppWrap", getBppWrap())
            .append("rCreKid", getrCreKid())
            .append("rCreDt", getrCreDt())
            .append("rUpdKid", getrUpdKid())
            .append("rUpdDt", getrUpdDt())
            .append("rV", getRV())
            .append("rDel", getrDel())
            .append("bppSupSkuCd", getBppSupSkuCd())
            .append("bppSuffix", getBppSuffix())
            .append("bppRemark", getBppRemark())
            .append("bppAutoShutdownTime", getBppAutoShutdownTime())
            .append("bppCdReal", getBppCdReal())
            .append("vpTime", getVpTime())
            .append("vpPriceScale", getVpPriceScale())
            .append("isReal", getIsReal())
            .append("skuSpec", getSkuSpec())
            .append("skuAttr", getSkuAttr())
            .append("bppIsPreferential", getBppIsPreferential())
            .append("bppIsexport", getBppIsexport())
            .append("bppPackEn", getBppPackEn())
            .append("bppSpecAttr", getBppSpecAttr())
            .toString();
    }
}
