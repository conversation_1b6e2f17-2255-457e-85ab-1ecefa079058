<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.supplier.mapper.SupplierMasterMapper">
    
    <resultMap type="SupplierMaster" id="SupplierMasterResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="certification"    column="certification"    />
        <result property="taxNo"    column="tax_no"    />
        <result property="contact"    column="contact"    />
    </resultMap>

    <sql id="selectSupplierMasterVo">
        select code, name, type, certification, tax_no, contact from supplier_master
    </sql>

    <select id="selectSupplierMasterList" parameterType="SupplierMaster" resultMap="SupplierMasterResult">
        <include refid="selectSupplierMasterVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="certification != null  and certification != ''"> and certification = #{certification}</if>
            <if test="taxNo != null  and taxNo != ''"> and tax_no = #{taxNo}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
        </where>
    </select>
    
    <select id="selectSupplierMasterByCode" parameterType="String" resultMap="SupplierMasterResult">
        <include refid="selectSupplierMasterVo"/>
        where code = #{code}
    </select>

    <insert id="insertSupplierMaster" parameterType="SupplierMaster">
        insert into supplier_master
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="certification != null">certification,</if>
            <if test="taxNo != null and taxNo != ''">tax_no,</if>
            <if test="contact != null and contact != ''">contact,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="certification != null">#{certification},</if>
            <if test="taxNo != null and taxNo != ''">#{taxNo},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
         </trim>
    </insert>

    <update id="updateSupplierMaster" parameterType="SupplierMaster">
        update supplier_master
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="certification != null">certification = #{certification},</if>
            <if test="taxNo != null and taxNo != ''">tax_no = #{taxNo},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
        </trim>
        where code = #{code}
    </update>

    <delete id="deleteSupplierMasterByCode" parameterType="String">
        delete from supplier_master where code = #{code}
    </delete>

    <delete id="deleteSupplierMasterByCodes" parameterType="String">
        delete from supplier_master where code in 
        <foreach item="code" collection="array" open="(" separator="," close=")">
            #{code}
        </foreach>
    </delete>
</mapper>