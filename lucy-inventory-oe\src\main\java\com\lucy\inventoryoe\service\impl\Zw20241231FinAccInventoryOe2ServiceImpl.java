package com.lucy.inventoryoe.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.inventoryoe.mapper.Zw20241231FinAccInventoryOe2Mapper;
import com.lucy.inventoryoe.domain.Zw20241231FinAccInventoryOe2;
import com.lucy.inventoryoe.service.IZw20241231FinAccInventoryOe2Service;

/**
 * 24期初库存初始化Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class Zw20241231FinAccInventoryOe2ServiceImpl implements IZw20241231FinAccInventoryOe2Service 
{
    @Autowired
    private Zw20241231FinAccInventoryOe2Mapper zw20241231FinAccInventoryOe2Mapper;

    /**
     * 查询24期初库存初始化
     * 
     * @param orgcd 24期初库存初始化主键
     * @return 24期初库存初始化
     */
    @Override
    public Zw20241231FinAccInventoryOe2 selectZw20241231FinAccInventoryOe2ByOrgcd(String orgcd)
    {
        return zw20241231FinAccInventoryOe2Mapper.selectZw20241231FinAccInventoryOe2ByOrgcd(orgcd);
    }

    /**
     * 查询24期初库存初始化列表
     * 
     * @param zw20241231FinAccInventoryOe2 24期初库存初始化
     * @return 24期初库存初始化
     */
    @Override
    public List<Zw20241231FinAccInventoryOe2> selectZw20241231FinAccInventoryOe2List(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2)
    {
        return zw20241231FinAccInventoryOe2Mapper.selectZw20241231FinAccInventoryOe2List(zw20241231FinAccInventoryOe2);
    }

    /**
     * 新增24期初库存初始化
     * 
     * @param zw20241231FinAccInventoryOe2 24期初库存初始化
     * @return 结果
     */
    @Override
    public int insertZw20241231FinAccInventoryOe2(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2)
    {
        return zw20241231FinAccInventoryOe2Mapper.insertZw20241231FinAccInventoryOe2(zw20241231FinAccInventoryOe2);
    }

    /**
     * 修改24期初库存初始化
     * 
     * @param zw20241231FinAccInventoryOe2 24期初库存初始化
     * @return 结果
     */
    @Override
    public int updateZw20241231FinAccInventoryOe2(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2)
    {
        return zw20241231FinAccInventoryOe2Mapper.updateZw20241231FinAccInventoryOe2(zw20241231FinAccInventoryOe2);
    }

    /**
     * 批量删除24期初库存初始化
     * 
     * @param orgcds 需要删除的24期初库存初始化主键
     * @return 结果
     */
    @Override
    public int deleteZw20241231FinAccInventoryOe2ByOrgcds(String[] orgcds)
    {
        return zw20241231FinAccInventoryOe2Mapper.deleteZw20241231FinAccInventoryOe2ByOrgcds(orgcds);
    }

    /**
     * 删除24期初库存初始化信息
     * 
     * @param orgcd 24期初库存初始化主键
     * @return 结果
     */
    @Override
    public int deleteZw20241231FinAccInventoryOe2ByOrgcd(String orgcd)
    {
        return zw20241231FinAccInventoryOe2Mapper.deleteZw20241231FinAccInventoryOe2ByOrgcd(orgcd);
    }

    @Override
    public List<Zw20241231FinAccInventoryOe2> selectZw20241231FinAccInventoryOe2ByType(String type){
        return zw20241231FinAccInventoryOe2Mapper.selectZw20241231FinAccInventoryOe2ByType(type);
    }

    @Override
    public List<Zw20241231FinAccInventoryOe2> selectZw20241231FinAccInventoryOe2ByTypeAndOrgcd(Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2) {
        return zw20241231FinAccInventoryOe2Mapper.selectZw20241231FinAccInventoryOe2ByTypeAndOrgcd(zw20241231FinAccInventoryOe2);
    }
}
