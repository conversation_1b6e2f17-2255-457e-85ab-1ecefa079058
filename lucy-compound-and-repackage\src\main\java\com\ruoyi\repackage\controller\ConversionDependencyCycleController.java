package com.ruoyi.repackage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.repackage.domain.ConversionDependencyCycle;
import com.ruoyi.repackage.service.IConversionDependencyCycleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 环状依赖记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/repackage/cycle")
public class ConversionDependencyCycleController extends BaseController
{
    @Autowired
    private IConversionDependencyCycleService conversionDependencyCycleService;

    /**
     * 查询环状依赖记录列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:cycle:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConversionDependencyCycle conversionDependencyCycle)
    {
        startPage();
        List<ConversionDependencyCycle> list = conversionDependencyCycleService.selectConversionDependencyCycleList(conversionDependencyCycle);
        return getDataTable(list);
    }

    /**
     * 导出环状依赖记录列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:cycle:export')")
    @Log(title = "环状依赖记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConversionDependencyCycle conversionDependencyCycle)
    {
        List<ConversionDependencyCycle> list = conversionDependencyCycleService.selectConversionDependencyCycleList(conversionDependencyCycle);
        ExcelUtil<ConversionDependencyCycle> util = new ExcelUtil<ConversionDependencyCycle>(ConversionDependencyCycle.class);
        util.exportExcel(response, list, "环状依赖记录数据");
    }

    /**
     * 获取环状依赖记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('repackage:cycle:query')")
    @GetMapping(value = "/{cycleId}")
    public AjaxResult getInfo(@PathVariable("cycleId") Long cycleId)
    {
        return success(conversionDependencyCycleService.selectConversionDependencyCycleByCycleId(cycleId));
    }

    /**
     * 新增环状依赖记录
     */
    @PreAuthorize("@ss.hasPermi('repackage:cycle:add')")
    @Log(title = "环状依赖记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConversionDependencyCycle conversionDependencyCycle)
    {
        return toAjax(conversionDependencyCycleService.insertConversionDependencyCycle(conversionDependencyCycle));
    }

    /**
     * 修改环状依赖记录
     */
    @PreAuthorize("@ss.hasPermi('repackage:cycle:edit')")
    @Log(title = "环状依赖记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConversionDependencyCycle conversionDependencyCycle)
    {
        return toAjax(conversionDependencyCycleService.updateConversionDependencyCycle(conversionDependencyCycle));
    }

    /**
     * 删除环状依赖记录
     */
    @PreAuthorize("@ss.hasPermi('repackage:cycle:remove')")
    @Log(title = "环状依赖记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cycleIds}")
    public AjaxResult remove(@PathVariable Long[] cycleIds)
    {
        return toAjax(conversionDependencyCycleService.deleteConversionDependencyCycleByCycleIds(cycleIds));
    }
}
