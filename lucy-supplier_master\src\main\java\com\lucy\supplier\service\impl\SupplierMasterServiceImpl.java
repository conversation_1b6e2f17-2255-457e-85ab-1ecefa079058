package com.lucy.supplier.service.impl;

import java.util.List;

import com.lucy.erp.domain.BasSupMst;
import com.lucy.erp.service.IBasSupMstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.supplier.mapper.SupplierMasterMapper;
import com.lucy.supplier.domain.SupplierMaster;
import com.lucy.supplier.service.ISupplierMasterService;

/**
 * 供应商主数据 Supplier masterService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class SupplierMasterServiceImpl implements ISupplierMasterService 
{
    @Autowired
    private SupplierMasterMapper supplierMasterMapper;

    @Autowired
    private IBasSupMstService basSupMstService;

    /**
     * 查询供应商主数据 Supplier master
     * 
     * @param code 供应商主数据 Supplier master主键
     * @return 供应商主数据 Supplier master
     */
    @Override
    public SupplierMaster selectSupplierMasterByCode(String code)
    {
        return supplierMasterMapper.selectSupplierMasterByCode(code);
    }

    /**
     * 查询供应商主数据 Supplier master列表
     * 
     * @param supplierMaster 供应商主数据 Supplier master
     * @return 供应商主数据 Supplier master
     */
    @Override
    public List<SupplierMaster> selectSupplierMasterList(SupplierMaster supplierMaster)
    {
        return supplierMasterMapper.selectSupplierMasterList(supplierMaster);
    }

    /**
     * 新增供应商主数据 Supplier master
     * 
     * @param supplierMaster 供应商主数据 Supplier master
     * @return 结果
     */
    @Override
    public int insertSupplierMaster(SupplierMaster supplierMaster)
    {
        return supplierMasterMapper.insertSupplierMaster(supplierMaster);
    }

    /**
     * 修改供应商主数据 Supplier master
     * 
     * @param supplierMaster 供应商主数据 Supplier master
     * @return 结果
     */
    @Override
    public int updateSupplierMaster(SupplierMaster supplierMaster)
    {
        return supplierMasterMapper.updateSupplierMaster(supplierMaster);
    }

    /**
     * 批量删除供应商主数据 Supplier master
     * 
     * @param codes 需要删除的供应商主数据 Supplier master主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMasterByCodes(String[] codes)
    {
        return supplierMasterMapper.deleteSupplierMasterByCodes(codes);
    }

    /**
     * 删除供应商主数据 Supplier master信息
     * 
     * @param code 供应商主数据 Supplier master主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMasterByCode(String code)
    {
        return supplierMasterMapper.deleteSupplierMasterByCode(code);
    }

    @Override
    public void insertSupplierMasterIfNotExist(String code) {
        SupplierMaster supplierMaster = supplierMasterMapper.selectSupplierMasterByCode(code);
        if (supplierMaster == null) {
            BasSupMst basSupMst = basSupMstService.selectBasSupMstByBsmCd(code);
            supplierMaster = new SupplierMaster();
            supplierMaster.setCode(code);
            supplierMaster.setName(basSupMst.getBsmNm());
            // TODO
            // 供应商类型
            supplierMaster.setType("");
            // 资质证书编号
            supplierMaster.setCertification("");
            supplierMaster.setTaxNo("");
            // 联系人
            supplierMaster.setContact("");
            supplierMasterMapper.insertSupplierMaster(supplierMaster);
        }
    }
}
