package com.lucy.inventoryexit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.inventoryexit.domain.InventoryCurrentPeriodIssue;
import com.lucy.inventoryexit.service.IInventoryCurrentPeriodIssueService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 本期出库明细 Current period inventory issuesController
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/inventoryexit/issue")
public class InventoryCurrentPeriodIssueController extends BaseController
{
    @Autowired
    private IInventoryCurrentPeriodIssueService inventoryCurrentPeriodIssueService;

    /**
     * 查询本期出库明细 Current period inventory issues列表
     */
    @PreAuthorize("@ss.hasPermi('inventoryexit:issue:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue)
    {
        startPage();
        List<InventoryCurrentPeriodIssue> list = inventoryCurrentPeriodIssueService.selectInventoryCurrentPeriodIssueList(inventoryCurrentPeriodIssue);
        return getDataTable(list);
    }

    /**
     * 导出本期出库明细 Current period inventory issues列表
     */
    @PreAuthorize("@ss.hasPermi('inventoryexit:issue:export')")
    @Log(title = "本期出库明细 Current period inventory issues", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue)
    {
        List<InventoryCurrentPeriodIssue> list = inventoryCurrentPeriodIssueService.selectInventoryCurrentPeriodIssueList(inventoryCurrentPeriodIssue);
        ExcelUtil<InventoryCurrentPeriodIssue> util = new ExcelUtil<InventoryCurrentPeriodIssue>(InventoryCurrentPeriodIssue.class);
        util.exportExcel(response, list, "本期出库明细 Current period inventory issues数据");
    }

    /**
     * 获取本期出库明细 Current period inventory issues详细信息
     */
    @PreAuthorize("@ss.hasPermi('inventoryexit:issue:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryCurrentPeriodIssueService.selectInventoryCurrentPeriodIssueById(id));
    }

    /**
     * 新增本期出库明细 Current period inventory issues
     */
    @PreAuthorize("@ss.hasPermi('inventoryexit:issue:add')")
    @Log(title = "本期出库明细 Current period inventory issues", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue)
    {
        return toAjax(inventoryCurrentPeriodIssueService.insertInventoryCurrentPeriodIssue(inventoryCurrentPeriodIssue));
    }

    /**
     * 修改本期出库明细 Current period inventory issues
     */
    @PreAuthorize("@ss.hasPermi('inventoryexit:issue:edit')")
    @Log(title = "本期出库明细 Current period inventory issues", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue)
    {
        return toAjax(inventoryCurrentPeriodIssueService.updateInventoryCurrentPeriodIssue(inventoryCurrentPeriodIssue));
    }

    /**
     * 删除本期出库明细 Current period inventory issues
     */
    @PreAuthorize("@ss.hasPermi('inventoryexit:issue:remove')")
    @Log(title = "本期出库明细 Current period inventory issues", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inventoryCurrentPeriodIssueService.deleteInventoryCurrentPeriodIssueByIds(ids));
    }

    @GetMapping("/test")
    @Anonymous
    public AjaxResult test() {
        return success();
    }
}
