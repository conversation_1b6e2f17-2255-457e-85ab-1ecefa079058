<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasInvoiceTypMapper">
    
    <resultMap type="BasInvoiceTyp" id="BasInvoiceTypResult">
        <result property="id"    column="id"    />
        <result property="bitTyp"    column="bit_typ"    />
        <result property="bitCd"    column="bit_cd"    />
        <result property="bitNm"    column="bit_nm"    />
        <result property="bitIsded"    column="bit_isded"    />
        <result property="bitIsts"    column="bit_ists"    />
        <result property="bitTr"    column="bit_tr"    />
        <result property="bitOs"    column="bit_os"    />
        <result property="bitRedFlush"    column="bit_red_flush"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
    </resultMap>

    <sql id="selectBasInvoiceTypVo">
        select id, bit_typ, bit_cd, bit_nm, bit_isded, bit_ists, bit_tr, bit_os, bit_red_flush, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del from bas_invoice_typ
    </sql>

    <select id="selectBasInvoiceTypList" parameterType="BasInvoiceTyp" resultMap="BasInvoiceTypResult">
        <include refid="selectBasInvoiceTypVo"/>
        <where>  
            <if test="bitTyp != null  and bitTyp != ''"> and bit_typ = #{bitTyp}</if>
            <if test="bitNm != null  and bitNm != ''"> and bit_nm = #{bitNm}</if>
            <if test="bitIsded != null  and bitIsded != ''"> and bit_isded = #{bitIsded}</if>
            <if test="bitIsts != null  and bitIsts != ''"> and bit_ists = #{bitIsts}</if>
            <if test="bitTr != null "> and bit_tr = #{bitTr}</if>
            <if test="bitOs != null "> and bit_os = #{bitOs}</if>
            <if test="bitRedFlush != null  and bitRedFlush != ''"> and bit_red_flush = #{bitRedFlush}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
        </where>
    </select>
    
    <select id="selectBasInvoiceTypByBitCd" parameterType="String" resultMap="BasInvoiceTypResult">
        <include refid="selectBasInvoiceTypVo"/>
        where bit_cd = #{bitCd}
    </select>

    <insert id="insertBasInvoiceTyp" parameterType="BasInvoiceTyp">
        insert into bas_invoice_typ
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bitTyp != null">bit_typ,</if>
            <if test="bitCd != null">bit_cd,</if>
            <if test="bitNm != null">bit_nm,</if>
            <if test="bitIsded != null">bit_isded,</if>
            <if test="bitIsts != null">bit_ists,</if>
            <if test="bitTr != null">bit_tr,</if>
            <if test="bitOs != null">bit_os,</if>
            <if test="bitRedFlush != null">bit_red_flush,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bitTyp != null">#{bitTyp},</if>
            <if test="bitCd != null">#{bitCd},</if>
            <if test="bitNm != null">#{bitNm},</if>
            <if test="bitIsded != null">#{bitIsded},</if>
            <if test="bitIsts != null">#{bitIsts},</if>
            <if test="bitTr != null">#{bitTr},</if>
            <if test="bitOs != null">#{bitOs},</if>
            <if test="bitRedFlush != null">#{bitRedFlush},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
         </trim>
    </insert>

    <update id="updateBasInvoiceTyp" parameterType="BasInvoiceTyp">
        update bas_invoice_typ
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="bitTyp != null">bit_typ = #{bitTyp},</if>
            <if test="bitNm != null">bit_nm = #{bitNm},</if>
            <if test="bitIsded != null">bit_isded = #{bitIsded},</if>
            <if test="bitIsts != null">bit_ists = #{bitIsts},</if>
            <if test="bitTr != null">bit_tr = #{bitTr},</if>
            <if test="bitOs != null">bit_os = #{bitOs},</if>
            <if test="bitRedFlush != null">bit_red_flush = #{bitRedFlush},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
        </trim>
        where bit_cd = #{bitCd}
    </update>

    <delete id="deleteBasInvoiceTypByBitCd" parameterType="String">
        delete from bas_invoice_typ where bit_cd = #{bitCd}
    </delete>

    <delete id="deleteBasInvoiceTypByBitCds" parameterType="String">
        delete from bas_invoice_typ where bit_cd in 
        <foreach item="bitCd" collection="array" open="(" separator="," close=")">
            #{bitCd}
        </foreach>
    </delete>
</mapper>