package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasProdPkgMapper;
import com.lucy.erp.domain.BasProdPkg;
import com.lucy.erp.service.IBasProdPkgService;

/**
 * 基础_产品_包装
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasProdPkgServiceImpl implements IBasProdPkgService
{
    @Autowired
    private BasProdPkgMapper basProdPkgMapper;

    /**
     * 查询基础_产品_包装

     * 
     * @param bppCd 基础_产品_包装
主键
     * @return 基础_产品_包装

     */
    @Override
    public BasProdPkg selectBasProdPkgByBppCd(String bppCd)
    {
        return basProdPkgMapper.selectBasProdPkgByBppCd(bppCd);
    }

    /**
     * 查询基础_产品_包装
列表
     * 
     * @param basProdPkg 基础_产品_包装

     * @return 基础_产品_包装

     */
    @Override
    public List<BasProdPkg> selectBasProdPkgList(BasProdPkg basProdPkg)
    {
        return basProdPkgMapper.selectBasProdPkgList(basProdPkg);
    }

    /**
     * 新增基础_产品_包装

     * 
     * @param basProdPkg 基础_产品_包装

     * @return 结果
     */
    @Override
    public int insertBasProdPkg(BasProdPkg basProdPkg)
    {
        return basProdPkgMapper.insertBasProdPkg(basProdPkg);
    }

    /**
     * 修改基础_产品_包装

     * 
     * @param basProdPkg 基础_产品_包装

     * @return 结果
     */
    @Override
    public int updateBasProdPkg(BasProdPkg basProdPkg)
    {
        return basProdPkgMapper.updateBasProdPkg(basProdPkg);
    }

    /**
     * 批量删除基础_产品_包装

     * 
     * @param bppCds 需要删除的基础_产品_包装
主键
     * @return 结果
     */
    @Override
    public int deleteBasProdPkgByBppCds(String[] bppCds)
    {
        return basProdPkgMapper.deleteBasProdPkgByBppCds(bppCds);
    }

    /**
     * 删除基础_产品_包装
信息
     * 
     * @param bppCd 基础_产品_包装
主键
     * @return 结果
     */
    @Override
    public int deleteBasProdPkgByBppCd(String bppCd)
    {
        return basProdPkgMapper.deleteBasProdPkgByBppCd(bppCd);
    }
}
