package com.lucy.inventoryoe.mapper;

import java.util.List;
import com.lucy.inventoryoe.domain.InventoryOpeningBalance;

/**
 * 库存期初汇总 Inventory opening balance summaryMapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface InventoryOpeningBalanceMapper 
{
    /**
     * 查询库存期初汇总 Inventory opening balance summary
     * 
     * @param id 库存期初汇总 Inventory opening balance summary主键
     * @return 库存期初汇总 Inventory opening balance summary
     */
    public InventoryOpeningBalance selectInventoryOpeningBalanceById(Long id);

    /**
     * 查询库存期初汇总 Inventory opening balance summary列表
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 库存期初汇总 Inventory opening balance summary集合
     */
    public List<InventoryOpeningBalance> selectInventoryOpeningBalanceList(InventoryOpeningBalance inventoryOpeningBalance);

    /**
     * 新增库存期初汇总 Inventory opening balance summary
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 结果
     */
    public int insertInventoryOpeningBalance(InventoryOpeningBalance inventoryOpeningBalance);

    /**
     * 修改库存期初汇总 Inventory opening balance summary
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 结果
     */
    public int updateInventoryOpeningBalance(InventoryOpeningBalance inventoryOpeningBalance);

    /**
     * 删除库存期初汇总 Inventory opening balance summary
     * 
     * @param id 库存期初汇总 Inventory opening balance summary主键
     * @return 结果
     */
    public int deleteInventoryOpeningBalanceById(Long id);

    /**
     * 批量删除库存期初汇总 Inventory opening balance summary
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryOpeningBalanceByIds(Long[] ids);
}
