package com.ruoyi.quartz.task;

import com.lucy.assetowner.controller.AssetOwnerMasterRegistry;
import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.assetowner.service.IAssetOwnerMasterService;
import com.lucy.inventoryoe.domain.*;
import com.lucy.inventoryoe.service.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.math3.dfp.DfpField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Component("inventoryOInit24")
public class InventoryOInit24
{
    @Autowired
    private IZw20241231FinAccInventoryOe2Service  iZw20241231FinAccInventoryOe2Service;

    @Autowired
    private IInventoryOpeningBalanceService iInventoryOpeningBalanceService;
    @Autowired
    private IInventoryOpeningDetailService iInventoryOpeningDetailService;
    @Autowired
    private ICostCompositionService  iCostCompositionService;
    @Autowired
    private ILogInventoryList2025010101Service iLogInventoryList2025010101Service;
    @Autowired
    private IAccountingSubjectService iAccountingSubjectService;
    @Autowired
    private IAssetOwnerMasterService iAssetOwnerMasterService;
    public void ryMultipleParams(String s, Boolean b, Long l, Double d, Integer i)
    {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params)
    {
        System.out.println("执行有参方法：" + params);
    }

    public void initParams()
    {


        System.out.println(" -- 1  126 行   账上有 快照无   已经手工处理 \n" +
                "  \n" +
                "  -- 2  489 行   帐上多 \n" +
                "  -- 3 112586 行   相等 \n" +
                "  -- 4 2768 行    账上少 \n" +
                "  -- 5 5142 行\n   账上数量为0 金额有的 异常数据" +
                "  select 126+489+112586+2768+5142;\n" +
                "  ");

//     this.initInventoryBalance("3");
     this.initExecpitonInventory("5");
    }

    private void initExecpitonInventory(String type) {


        Zw20241231FinAccInventoryOe2 zw20241231FinAccInventoryOe2   = new Zw20241231FinAccInventoryOe2();
        List<Zw20241231FinAccInventoryOe2>  type1List = iZw20241231FinAccInventoryOe2Service.selectZw20241231FinAccInventoryOe2ByTypeAndOrgcd(zw20241231FinAccInventoryOe2);


    }

    private void  test(){

      iAssetOwnerMasterService.selectAssetOwnerMasterById((long)1);

      System.out.println("成功");
}
    private void initInventoryBalance(String type ) {
         /**
         * 从类型2 开始处理
         */
        List<Zw20241231FinAccInventoryOe2>  type1List = iZw20241231FinAccInventoryOe2Service.selectZw20241231FinAccInventoryOe2ByType(type );
         int flag =0;
        for(Zw20241231FinAccInventoryOe2 oe :type1List ){
            LogInventoryList2025010101 param = new LogInventoryList2025010101();
            param.setLilPpcd(oe.getFaiSku());
            param.setLilOrgmcd(oe.getOrgcd());

            AccountingSubject as =  iAccountingSubjectService.selectAccountingSubjectByName(oe.getFaiSubject());
            Date dt = DateUtils.parseDate("2025-05-19 12:12:06");
            /*
             不论什么时候 期初库存汇总表直接插入
             */
            this.inventoryOpenSummarySave(oe,dt,as,type);

            List<LogInventoryList2025010101> lils = iLogInventoryList2025010101Service.selectLogInventoryList2025010101List(param);
            BigDecimal lilQty = BigDecimal.ZERO;
            BigDecimal totalAmt = BigDecimal.ZERO;
            //求出每一个的账面上单价 ，精确两位数
            BigDecimal avgCost = oe.getAmt().divide(oe.getQty(),2, java.math.RoundingMode.HALF_UP);
            if(oe.getQty().compareTo(BigDecimal.ZERO) <1){
                BigDecimal execptionQty = oe.getQty().subtract(lilQty);
                BigDecimal execptionAmt =oe.getAmt().subtract(totalAmt);
                // 将财务账目上多出来的数量 匹配最后一条库存的批次库位信息
                //当账面多于实际的时候，会触发这种情况
                lils.get(0).setLilSlcd(lils.get(0).getLilSlcd()+"_2");
                this.inventoryOpenDetailSave(oe,lils.get(0),dt,as,type,execptionQty,execptionAmt,"异常，账面数量小于等于0");
                Zw20241231FinAccInventoryOe2 param2 = new Zw20241231FinAccInventoryOe2();
                param2.setOrgcd(oe.getOrgcd());
                param2.setFaiSku(oe.getFaiSku());
                param2.setType(oe.getType()+"-1");
                iZw20241231FinAccInventoryOe2Service.updateZw20241231FinAccInventoryOe2(param2);

                continue;
            }

            for(int i=0 ;i<lils.size();i++){
                /**
                 * 期初库存批次明细表要从库存清单表里去取批次
                 *
                 * 需要处理特例库存清单表的数量为负数  直接先看底层数据 处理掉 代码不用考虑  --已处理 库存清单表里有几条数量是负数 处理掉了
                 *
                 * 如果是类型2 账上多于库存清单的  之间的差值 直接按照最后一条的批次填写
                 */
                LogInventoryList2025010101 inv = lils.get(i);
                BigDecimal invQty = new BigDecimal(inv.getLilQty());
                //累计库存+本次将要累计的库存大于了账面数量
                if(lilQty.add(invQty).compareTo(oe.getQty())==1){
                    BigDecimal execptionQty = oe.getQty().subtract(lilQty);
                    BigDecimal execptionAmt =oe.getAmt().subtract(totalAmt);
                    //当账面少于实际的时候，会触发这种情况
                    inv.setLilSlcd(inv.getLilSlcd()+"_2");
                    this.inventoryOpenDetailSave(oe,inv,dt,as,type,execptionQty,execptionAmt,"异常，账面少");
                    lilQty = lilQty.add(execptionQty);
                    totalAmt = totalAmt.add(execptionAmt);
                    break;
                }else{
                    if(i==lils.size()-1){
                        BigDecimal execptionQty = oe.getQty().subtract(lilQty);
                        BigDecimal execptionAmt =oe.getAmt().subtract(totalAmt);
                        lilQty= lilQty.add(invQty);
                         totalAmt = totalAmt.add(execptionAmt);
                        this.inventoryOpenDetailSave(oe,inv,dt,as,type,execptionQty,execptionAmt,"正常");
                    }else{
                        lilQty= lilQty.add(invQty);
                        BigDecimal lilAmt = avgCost.multiply(invQty);
                        totalAmt = totalAmt.add(lilAmt);
                        this.inventoryOpenDetailSave(oe,inv,dt,as,type,invQty,lilAmt,"正常");
                    }

                }

            }

//            if(oe.getQty().compareTo(lilQty)==1){
//                BigDecimal execptionQty = oe.getQty().subtract(lilQty);
//                BigDecimal execptionAmt =oe.getAmt().subtract(totalAmt);
//                // 将财务账目上多出来的数量 匹配最后一条库存的批次库位信息
//                //当账面多于实际的时候，会触发这种情况
//                lils.get(0).setLilSlcd(lils.get(0).getLilSlcd()+"_2");
//                this.inventoryOpenDetailSave(oe,lils.get(0),dt,as,type,execptionQty,execptionAmt,"异常，账面多");
//
//            }


            System.out.println("一条明细处理完成，进行下一条 "+flag++);
            /**
             * 将这条库存标记成已经初始化完成
             */
            Zw20241231FinAccInventoryOe2 param2 = new Zw20241231FinAccInventoryOe2();
            param2.setOrgcd(oe.getOrgcd());
            param2.setFaiSku(oe.getFaiSku());
            param2.setType(oe.getType()+"-1");
            iZw20241231FinAccInventoryOe2Service.updateZw20241231FinAccInventoryOe2(param2);

        }
    }

    private void inventoryOpenSummarySave(Zw20241231FinAccInventoryOe2 oe, Date dt, AccountingSubject as,String type) {
        InventoryOpeningBalance openingBalance = new InventoryOpeningBalance();
        openingBalance.setFinancialAccount(as.getCode());
        openingBalance.setAccountingPeriod("2024-01");
        openingBalance.setSku(oe.getFaiSku());
        openingBalance.setQuantity(oe.getQty());
        openingBalance.setAmount(oe.getAmt());
        openingBalance.setSourceType("PURCHASED");
        openingBalance.setAssetOwner(oe.getOrgcd());


        openingBalance.setValuationTime(dt);
        String valuator = this.determineValuator(oe.getOrgcd());
        openingBalance.setValuator(valuator);
        openingBalance.setAuditTime(dt);
        openingBalance.setAuditor("J03");
        openingBalance.setCreatedBy("IT01");
        openingBalance.setCreatedTime(new Date());
        openingBalance.setUpdatedBy("IT01");
        openingBalance.setUpdatedTime(new Date());
        openingBalance.setVersion(1);
        openingBalance.setIsDeleted(0);

        int count = iInventoryOpeningBalanceService.insertInventoryOpeningBalance(openingBalance);

        CostComposition cost = new CostComposition();
        cost.setStructureName("inventory_opening_balance");
        cost.setParentStructureId(openingBalance.getId());
        cost.setStatus("S");
        cost.setGenerationContext(type);
        cost.setCreatedBy("IT01");
        cost.setCreatedTime(new Date());
        cost.setUpdatedBy("IT01");
        cost.setUpdatedTime(new Date());
        cost.setVersion(1);
        cost.setIsDeleted(0);
        iCostCompositionService.insertCostComposition(cost);
    }

    private void inventoryOpenDetailSave(Zw20241231FinAccInventoryOe2 oe, LogInventoryList2025010101 inv,Date dt,AccountingSubject as,String type,BigDecimal qty,BigDecimal amt,String context) {


        InventoryOpeningDetail openDetail = new InventoryOpeningDetail();
        openDetail.setAccountingPeriod("2024-01");
        openDetail.setFinancialAccount(as.getCode());

        openDetail.setSku(oe.getFaiSku());
        openDetail.setQuantity(qty);
        openDetail.setAmount(amt);
        openDetail.setSourceType("PURCHASED");
        openDetail.setAssetOwner(oe.getOrgcd());

        if("UU".equals(inv.getLilTcd())){
            inv.setLilBnEncrypt(inv.getLilBn());
        }
        openDetail.setBatchNumber(inv.getLilBn());
        openDetail.setEncryptedBatch(inv.getLilBnEncrypt());
        openDetail.setWarehouse(inv.getLilDmcd());
        openDetail.setStorageLocation(inv.getLilSlcd());
        openDetail.setStorageZone(inv.getLilTcd());

        Date dt2 = DateUtils.parseDate("2025-05-19 12:12:06");
        openDetail.setValuationTime(dt2);
        String valuator2 = this.determineValuator(oe.getOrgcd());
        openDetail.setValuator(valuator2);
        openDetail.setAuditTime(dt);
        openDetail.setAuditor("J03");
        openDetail.setCreatedBy("IT01");
        openDetail.setCreatedTime(new Date());
        openDetail.setUpdatedBy("IT01");
        openDetail.setUpdatedTime(new Date());
        openDetail.setVersion(1);
        openDetail.setIsDeleted(0);

        openDetail.setStructureName("log_inventory_list_2025010101");
        openDetail.setParentStructureId(inv.getId());

        int detailcount = iInventoryOpeningDetailService.insertInventoryOpeningDetail(openDetail);

        CostComposition costDtl = new CostComposition();
        costDtl.setStructureName("inventory_opening_detail");
        costDtl.setParentStructureId(openDetail.getId());
        costDtl.setStatus("S");
        costDtl.setGenerationContext(type+context);
        costDtl.setCreatedBy("IT01");
        costDtl.setCreatedTime(new Date());
        costDtl.setUpdatedBy("IT01");
        costDtl.setUpdatedTime(new Date());
        costDtl.setVersion(1);
        costDtl.setIsDeleted(0);
        iCostCompositionService.insertCostComposition(costDtl);
    }

    /**
     * 根据不同情况判断核算人是谁
     * @param orgcd
     * @return
     *
     *  CASE   WHEN 资产所属 ='BM10000060' THEN 'Z38' -- 周杰
     *   WHEN 资产所属 ='BM10000050' THEN 'X15'  -- 徐鑫
     *   WHEN 资产所属 ='BM10000062' THEN 'W22' -- 王美玉
     *   WHEN 资产所属 ='BM10000048' THEN 'L11'  --  刘苏雨
     *   WHEN 资产所属 ='BM10000057' THEN 'X15' -- 徐鑫
     *   WHEN 资产所属 ='BM10000056' THEN 'W22' -- 王美玉
     *    END AS 核算人,
     */

    private String determineValuator(String orgcd) {
        if("BM10000060".equals(orgcd)){
          return "Z38";
        }else if("BM10000050".equals(orgcd)){
            return "X15";
        }else if("BM10000062".equals(orgcd)){
            return "W22";
        }else if("BM10000048".equals(orgcd)){
            return "L11";
        }else if("BM10000057".equals(orgcd)){
            return "X15";
        }else if("BM10000056".equals(orgcd)){
            return "W22";
        }
        return "X15";
    }
}
