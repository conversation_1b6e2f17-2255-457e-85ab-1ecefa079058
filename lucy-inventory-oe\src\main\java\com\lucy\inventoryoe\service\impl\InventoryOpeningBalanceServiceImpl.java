package com.lucy.inventoryoe.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.inventoryoe.mapper.InventoryOpeningBalanceMapper;
import com.lucy.inventoryoe.domain.InventoryOpeningBalance;
import com.lucy.inventoryoe.service.IInventoryOpeningBalanceService;

/**
 * 库存期初汇总 Inventory opening balance summaryService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class InventoryOpeningBalanceServiceImpl implements IInventoryOpeningBalanceService 
{
    @Autowired
    private InventoryOpeningBalanceMapper inventoryOpeningBalanceMapper;

    /**
     * 查询库存期初汇总 Inventory opening balance summary
     * 
     * @param id 库存期初汇总 Inventory opening balance summary主键
     * @return 库存期初汇总 Inventory opening balance summary
     */
    @Override
    public InventoryOpeningBalance selectInventoryOpeningBalanceById(Long id)
    {
        return inventoryOpeningBalanceMapper.selectInventoryOpeningBalanceById(id);
    }

    /**
     * 查询库存期初汇总 Inventory opening balance summary列表
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 库存期初汇总 Inventory opening balance summary
     */
    @Override
    public List<InventoryOpeningBalance> selectInventoryOpeningBalanceList(InventoryOpeningBalance inventoryOpeningBalance)
    {
        return inventoryOpeningBalanceMapper.selectInventoryOpeningBalanceList(inventoryOpeningBalance);
    }

    /**
     * 新增库存期初汇总 Inventory opening balance summary
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 结果
     */
    @Override
    public int insertInventoryOpeningBalance(InventoryOpeningBalance inventoryOpeningBalance)
    {
        return inventoryOpeningBalanceMapper.insertInventoryOpeningBalance(inventoryOpeningBalance);
    }

    /**
     * 修改库存期初汇总 Inventory opening balance summary
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 结果
     */
    @Override
    public int updateInventoryOpeningBalance(InventoryOpeningBalance inventoryOpeningBalance)
    {
        return inventoryOpeningBalanceMapper.updateInventoryOpeningBalance(inventoryOpeningBalance);
    }

    /**
     * 批量删除库存期初汇总 Inventory opening balance summary
     * 
     * @param ids 需要删除的库存期初汇总 Inventory opening balance summary主键
     * @return 结果
     */
    @Override
    public int deleteInventoryOpeningBalanceByIds(Long[] ids)
    {
        return inventoryOpeningBalanceMapper.deleteInventoryOpeningBalanceByIds(ids);
    }

    /**
     * 删除库存期初汇总 Inventory opening balance summary信息
     * 
     * @param id 库存期初汇总 Inventory opening balance summary主键
     * @return 结果
     */
    @Override
    public int deleteInventoryOpeningBalanceById(Long id)
    {
        return inventoryOpeningBalanceMapper.deleteInventoryOpeningBalanceById(id);
    }
}
