package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.SalesContractMstMapper;
import com.lucy.erp.domain.SalesContractMst;
import com.lucy.erp.service.ISalesContractMstService;

/**
 * 销售合同主表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class SalesContractMstServiceImpl implements ISalesContractMstService
{
    @Autowired
    private SalesContractMstMapper salesContractMstMapper;

    /**
     * 查询销售合同主表
     * 
     * @param scmCd 销售合同主表主键
     * @return 销售合同主表
     */
    @Override
    public SalesContractMst selectSalesContractMstByScmCd(String scmCd)
    {
        return salesContractMstMapper.selectSalesContractMstByScmCd(scmCd);
    }

    /**
     * 查询销售合同主表列表
     * 
     * @param salesContractMst 销售合同主表
     * @return 销售合同主表
     */
    @Override
    public List<SalesContractMst> selectSalesContractMstList(SalesContractMst salesContractMst)
    {
        return salesContractMstMapper.selectSalesContractMstList(salesContractMst);
    }

    /**
     * 新增销售合同主表
     * 
     * @param salesContractMst 销售合同主表
     * @return 结果
     */
    @Override
    public int insertSalesContractMst(SalesContractMst salesContractMst)
    {
        return salesContractMstMapper.insertSalesContractMst(salesContractMst);
    }

    /**
     * 修改销售合同主表
     * 
     * @param salesContractMst 销售合同主表
     * @return 结果
     */
    @Override
    public int updateSalesContractMst(SalesContractMst salesContractMst)
    {
        return salesContractMstMapper.updateSalesContractMst(salesContractMst);
    }

    /**
     * 批量删除销售合同主表
     * 
     * @param scmCds 需要删除的销售合同主表主键
     * @return 结果
     */
    @Override
    public int deleteSalesContractMstByScmCds(String[] scmCds)
    {
        return salesContractMstMapper.deleteSalesContractMstByScmCds(scmCds);
    }

    /**
     * 删除销售合同主表信息
     * 
     * @param scmCd 销售合同主表主键
     * @return 结果
     */
    @Override
    public int deleteSalesContractMstByScmCd(String scmCd)
    {
        return salesContractMstMapper.deleteSalesContractMstByScmCd(scmCd);
    }
}
