<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogRadDeveloProductMapper">
    
    <resultMap type="LogRadDeveloProduct" id="LogRadDeveloProductResult">
        <result property="id"    column="id"    />
        <result property="lrdpCd"    column="lrdp_cd"    />
        <result property="lrdpMcd"    column="lrdp_mcd"    />
        <result property="lrdpSource"    column="lrdp_source"    />
        <result property="lrdpBn"    column="lrdp_bn"    />
        <result property="lrdpBnEncrypt"    column="lrdp_bn_encrypt"    />
        <result property="lrdpPcd"    column="lrdp_pcd"    />
        <result property="lrdpPpcd"    column="lrdp_ppcd"    />
        <result property="lrdpSlcd"    column="lrdp_slcd"    />
        <result property="lrdpTcd"    column="lrdp_tcd"    />
        <result property="lrdpDmcd"    column="lrdp_dmcd"    />
        <result property="lrdpOrgmcd"    column="lrdp_orgmcd"    />
        <result property="lrdpQty"    column="lrdp_qty"    />
        <result property="lrdpPlanqty"    column="lrdp_planqty"    />
        <result property="lrdpActualqty"    column="lrdp_actualqty"    />
        <result property="lrdpUnitp"    column="lrdp_unitp"    />
        <result property="lrdpTotprc"    column="lrdp_totprc"    />
        <result property="lrdpFinFlag"    column="lrdp_fin_flag"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lrdpRdId"    column="lrdp_rd_id"    />
    </resultMap>

    <sql id="selectLogRadDeveloProductVo">
        select id, lrdp_cd, lrdp_mcd, lrdp_source, lrdp_bn, lrdp_bn_encrypt, lrdp_pcd, lrdp_ppcd, lrdp_slcd, lrdp_tcd, lrdp_dmcd, lrdp_orgmcd, lrdp_qty, lrdp_planqty, lrdp_actualqty, lrdp_unitp, lrdp_totprc, lrdp_fin_flag, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, lrdp_rd_id from log_rad_develo_product
    </sql>

    <select id="selectLogRadDeveloProductList" parameterType="LogRadDeveloProduct" resultMap="LogRadDeveloProductResult">
        <include refid="selectLogRadDeveloProductVo"/>
        <where>  
            <if test="lrdpMcd != null  and lrdpMcd != ''"> and lrdp_mcd = #{lrdpMcd}</if>
            <if test="lrdpSource != null  and lrdpSource != ''"> and lrdp_source = #{lrdpSource}</if>
            <if test="lrdpBn != null  and lrdpBn != ''"> and lrdp_bn = #{lrdpBn}</if>
            <if test="lrdpBnEncrypt != null  and lrdpBnEncrypt != ''"> and lrdp_bn_encrypt = #{lrdpBnEncrypt}</if>
            <if test="lrdpPcd != null  and lrdpPcd != ''"> and lrdp_pcd = #{lrdpPcd}</if>
            <if test="lrdpPpcd != null  and lrdpPpcd != ''"> and lrdp_ppcd = #{lrdpPpcd}</if>
            <if test="lrdpSlcd != null  and lrdpSlcd != ''"> and lrdp_slcd = #{lrdpSlcd}</if>
            <if test="lrdpTcd != null  and lrdpTcd != ''"> and lrdp_tcd = #{lrdpTcd}</if>
            <if test="lrdpDmcd != null  and lrdpDmcd != ''"> and lrdp_dmcd = #{lrdpDmcd}</if>
            <if test="lrdpOrgmcd != null  and lrdpOrgmcd != ''"> and lrdp_orgmcd = #{lrdpOrgmcd}</if>
            <if test="lrdpQty != null "> and lrdp_qty = #{lrdpQty}</if>
            <if test="lrdpPlanqty != null "> and lrdp_planqty = #{lrdpPlanqty}</if>
            <if test="lrdpActualqty != null "> and lrdp_actualqty = #{lrdpActualqty}</if>
            <if test="lrdpUnitp != null "> and lrdp_unitp = #{lrdpUnitp}</if>
            <if test="lrdpTotprc != null "> and lrdp_totprc = #{lrdpTotprc}</if>
            <if test="lrdpFinFlag != null "> and lrdp_fin_flag = #{lrdpFinFlag}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="lrdpRdId != null "> and lrdp_rd_id = #{lrdpRdId}</if>
            <if test="startDate != null">
                AND r_cre_dt >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                AND r_cre_dt &lt;= DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
            </if>
        </where>
    </select>
    
    <select id="selectLogRadDeveloProductByLrdpCd" parameterType="String" resultMap="LogRadDeveloProductResult">
        <include refid="selectLogRadDeveloProductVo"/>
        where lrdp_cd = #{lrdpCd}
    </select>

    <insert id="insertLogRadDeveloProduct" parameterType="LogRadDeveloProduct">
        insert into log_rad_develo_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lrdpCd != null">lrdp_cd,</if>
            <if test="lrdpMcd != null">lrdp_mcd,</if>
            <if test="lrdpSource != null">lrdp_source,</if>
            <if test="lrdpBn != null">lrdp_bn,</if>
            <if test="lrdpBnEncrypt != null">lrdp_bn_encrypt,</if>
            <if test="lrdpPcd != null">lrdp_pcd,</if>
            <if test="lrdpPpcd != null">lrdp_ppcd,</if>
            <if test="lrdpSlcd != null">lrdp_slcd,</if>
            <if test="lrdpTcd != null">lrdp_tcd,</if>
            <if test="lrdpDmcd != null">lrdp_dmcd,</if>
            <if test="lrdpOrgmcd != null">lrdp_orgmcd,</if>
            <if test="lrdpQty != null">lrdp_qty,</if>
            <if test="lrdpPlanqty != null">lrdp_planqty,</if>
            <if test="lrdpActualqty != null">lrdp_actualqty,</if>
            <if test="lrdpUnitp != null">lrdp_unitp,</if>
            <if test="lrdpTotprc != null">lrdp_totprc,</if>
            <if test="lrdpFinFlag != null">lrdp_fin_flag,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="lrdpRdId != null">lrdp_rd_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lrdpCd != null">#{lrdpCd},</if>
            <if test="lrdpMcd != null">#{lrdpMcd},</if>
            <if test="lrdpSource != null">#{lrdpSource},</if>
            <if test="lrdpBn != null">#{lrdpBn},</if>
            <if test="lrdpBnEncrypt != null">#{lrdpBnEncrypt},</if>
            <if test="lrdpPcd != null">#{lrdpPcd},</if>
            <if test="lrdpPpcd != null">#{lrdpPpcd},</if>
            <if test="lrdpSlcd != null">#{lrdpSlcd},</if>
            <if test="lrdpTcd != null">#{lrdpTcd},</if>
            <if test="lrdpDmcd != null">#{lrdpDmcd},</if>
            <if test="lrdpOrgmcd != null">#{lrdpOrgmcd},</if>
            <if test="lrdpQty != null">#{lrdpQty},</if>
            <if test="lrdpPlanqty != null">#{lrdpPlanqty},</if>
            <if test="lrdpActualqty != null">#{lrdpActualqty},</if>
            <if test="lrdpUnitp != null">#{lrdpUnitp},</if>
            <if test="lrdpTotprc != null">#{lrdpTotprc},</if>
            <if test="lrdpFinFlag != null">#{lrdpFinFlag},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="lrdpRdId != null">#{lrdpRdId},</if>
         </trim>
    </insert>

    <update id="updateLogRadDeveloProduct" parameterType="LogRadDeveloProduct">
        update log_rad_develo_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lrdpMcd != null">lrdp_mcd = #{lrdpMcd},</if>
            <if test="lrdpSource != null">lrdp_source = #{lrdpSource},</if>
            <if test="lrdpBn != null">lrdp_bn = #{lrdpBn},</if>
            <if test="lrdpBnEncrypt != null">lrdp_bn_encrypt = #{lrdpBnEncrypt},</if>
            <if test="lrdpPcd != null">lrdp_pcd = #{lrdpPcd},</if>
            <if test="lrdpPpcd != null">lrdp_ppcd = #{lrdpPpcd},</if>
            <if test="lrdpSlcd != null">lrdp_slcd = #{lrdpSlcd},</if>
            <if test="lrdpTcd != null">lrdp_tcd = #{lrdpTcd},</if>
            <if test="lrdpDmcd != null">lrdp_dmcd = #{lrdpDmcd},</if>
            <if test="lrdpOrgmcd != null">lrdp_orgmcd = #{lrdpOrgmcd},</if>
            <if test="lrdpQty != null">lrdp_qty = #{lrdpQty},</if>
            <if test="lrdpPlanqty != null">lrdp_planqty = #{lrdpPlanqty},</if>
            <if test="lrdpActualqty != null">lrdp_actualqty = #{lrdpActualqty},</if>
            <if test="lrdpUnitp != null">lrdp_unitp = #{lrdpUnitp},</if>
            <if test="lrdpTotprc != null">lrdp_totprc = #{lrdpTotprc},</if>
            <if test="lrdpFinFlag != null">lrdp_fin_flag = #{lrdpFinFlag},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="lrdpRdId != null">lrdp_rd_id = #{lrdpRdId},</if>
        </trim>
        where lrdp_cd = #{lrdpCd}
    </update>

    <delete id="deleteLogRadDeveloProductByLrdpCd" parameterType="String">
        delete from log_rad_develo_product where lrdp_cd = #{lrdpCd}
    </delete>

    <delete id="deleteLogRadDeveloProductByLrdpCds" parameterType="String">
        delete from log_rad_develo_product where lrdp_cd in 
        <foreach item="lrdpCd" collection="array" open="(" separator="," close=")">
            #{lrdpCd}
        </foreach>
    </delete>
</mapper>