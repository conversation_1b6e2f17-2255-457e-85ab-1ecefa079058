package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasInvoiceTypMapper;
import com.lucy.erp.domain.BasInvoiceTyp;
import com.lucy.erp.service.IBasInvoiceTypService;

/**
 * 基础_发票_类型
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasInvoiceTypServiceImpl implements IBasInvoiceTypService
{
    @Autowired
    private BasInvoiceTypMapper basInvoiceTypMapper;

    /**
     * 查询基础_发票_类型

     * 
     * @param bitCd 基础_发票_类型
主键
     * @return 基础_发票_类型

     */
    @Override
    public BasInvoiceTyp selectBasInvoiceTypByBitCd(String bitCd)
    {
        return basInvoiceTypMapper.selectBasInvoiceTypByBitCd(bitCd);
    }

    /**
     * 查询基础_发票_类型
列表
     * 
     * @param basInvoiceTyp 基础_发票_类型

     * @return 基础_发票_类型

     */
    @Override
    public List<BasInvoiceTyp> selectBasInvoiceTypList(BasInvoiceTyp basInvoiceTyp)
    {
        return basInvoiceTypMapper.selectBasInvoiceTypList(basInvoiceTyp);
    }

    /**
     * 新增基础_发票_类型

     * 
     * @param basInvoiceTyp 基础_发票_类型

     * @return 结果
     */
    @Override
    public int insertBasInvoiceTyp(BasInvoiceTyp basInvoiceTyp)
    {
        return basInvoiceTypMapper.insertBasInvoiceTyp(basInvoiceTyp);
    }

    /**
     * 修改基础_发票_类型

     * 
     * @param basInvoiceTyp 基础_发票_类型

     * @return 结果
     */
    @Override
    public int updateBasInvoiceTyp(BasInvoiceTyp basInvoiceTyp)
    {
        return basInvoiceTypMapper.updateBasInvoiceTyp(basInvoiceTyp);
    }

    /**
     * 批量删除基础_发票_类型

     * 
     * @param bitCds 需要删除的基础_发票_类型
主键
     * @return 结果
     */
    @Override
    public int deleteBasInvoiceTypByBitCds(String[] bitCds)
    {
        return basInvoiceTypMapper.deleteBasInvoiceTypByBitCds(bitCds);
    }

    /**
     * 删除基础_发票_类型
信息
     * 
     * @param bitCd 基础_发票_类型
主键
     * @return 结果
     */
    @Override
    public int deleteBasInvoiceTypByBitCd(String bitCd)
    {
        return basInvoiceTypMapper.deleteBasInvoiceTypByBitCd(bitCd);
    }
}
