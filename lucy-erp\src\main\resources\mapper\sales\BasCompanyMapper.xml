<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.BasCompanyMapper">
    
    <resultMap type="BasCompany" id="BasCompanyResult">
        <result property="id"    column="id"    />
        <result property="bcCd"    column="bc_cd"    />
        <result property="bcNm"    column="bc_nm"    />
        <result property="bcTyp"    column="bc_typ"    />
        <result property="bcSpecicalVerify"    column="bc_specical_verify"    />
        <result property="bcVerifyDt"    column="bc_verify_dt"    />
        <result property="bcVerifyupDt"    column="bc_verifyup_dt"    />
        <result property="bcVerifyNm"    column="bc_verify_nm"    />
        <result property="bcVerify"    column="bc_verify"    />
        <result property="bcLicence"    column="bc_licence"    />
        <result property="bcLicenceDt"    column="bc_licence_dt"    />
        <result property="bcInvcPri"    column="bc_invc_pri"    />
        <result property="bcUscc"    column="bc_uscc"    />
        <result property="bcOc"    column="bc_oc"    />
        <result property="bcTrcno"    column="bc_trcno"    />
        <result property="bcBrno"    column="bc_brno"    />
        <result property="bcBcode"    column="bc_bcode"    />
        <result property="bcLp"    column="bc_lp"    />
        <result property="bcEmail"    column="bc_email"    />
        <result property="bcWeb"    column="bc_web"    />
        <result property="bcRdt"    column="bc_rdt"    />
        <result property="bcRaddr"    column="bc_raddr"    />
        <result property="bcTel"    column="bc_tel"    />
        <result property="bcBank"    column="bc_bank"    />
        <result property="bcAno"    column="bc_ano"    />
        <result property="bcRc"    column="bc_rc"    />
        <result property="bcFbt"    column="bc_fbt"    />
        <result property="bcTbt"    column="bc_tbt"    />
        <result property="bcBs"    column="bc_bs"    />
        <result property="bcSts"    column="bc_sts"    />
        <result property="bcRmk"    column="bc_rmk"    />
        <result property="3aBcCd"    column="3a_bc_cd"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="bcInitialcd"    column="bc_initialcd"    />
        <result property="bcBloc"    column="bc_bloc"    />
        <result property="bcLongterm"    column="bc_longterm"    />
        <result property="bcLicenceupDt"    column="bc_licenceup_dt"    />
        <result property="bcLegalDt"    column="bc_legal_dt"    />
        <result property="bcLegalupDt"    column="bc_legalup_dt"    />
        <result property="bcScopeupDt"    column="bc_scopeup_dt"    />
    </resultMap>

    <sql id="selectBasCompanyVo">
        select id, bc_cd, bc_nm, bc_typ, bc_specical_verify, bc_verify_dt, bc_verifyup_dt, bc_verify_nm, bc_verify, bc_licence, bc_licence_dt, bc_invc_pri, bc_uscc, bc_oc, bc_trcno, bc_brno, bc_bcode, bc_lp, bc_email, bc_web, bc_rdt, bc_raddr, bc_tel, bc_bank, bc_ano, bc_rc, bc_fbt, bc_tbt, bc_bs, bc_sts, bc_rmk, 3a_bc_cd, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, bc_initialcd, bc_bloc, bc_longterm, bc_licenceup_dt, bc_legal_dt, bc_legalup_dt, bc_scopeup_dt from bas_company
    </sql>

    <select id="selectBasCompanyList" parameterType="BasCompany" resultMap="BasCompanyResult">
        <include refid="selectBasCompanyVo"/>
        <where>  
            <if test="bcNm != null  and bcNm != ''"> and bc_nm = #{bcNm}</if>
            <if test="bcTyp != null  and bcTyp != ''"> and bc_typ = #{bcTyp}</if>
            <if test="bcSpecicalVerify != null  and bcSpecicalVerify != ''"> and bc_specical_verify = #{bcSpecicalVerify}</if>
            <if test="bcVerifyDt != null  and bcVerifyDt != ''"> and bc_verify_dt = #{bcVerifyDt}</if>
            <if test="bcVerifyupDt != null  and bcVerifyupDt != ''"> and bc_verifyup_dt = #{bcVerifyupDt}</if>
            <if test="bcVerifyNm != null  and bcVerifyNm != ''"> and bc_verify_nm = #{bcVerifyNm}</if>
            <if test="bcVerify != null  and bcVerify != ''"> and bc_verify = #{bcVerify}</if>
            <if test="bcLicence != null  and bcLicence != ''"> and bc_licence = #{bcLicence}</if>
            <if test="bcLicenceDt != null  and bcLicenceDt != ''"> and bc_licence_dt = #{bcLicenceDt}</if>
            <if test="bcInvcPri != null  and bcInvcPri != ''"> and bc_invc_pri = #{bcInvcPri}</if>
            <if test="bcUscc != null  and bcUscc != ''"> and bc_uscc = #{bcUscc}</if>
            <if test="bcOc != null  and bcOc != ''"> and bc_oc = #{bcOc}</if>
            <if test="bcTrcno != null  and bcTrcno != ''"> and bc_trcno = #{bcTrcno}</if>
            <if test="bcBrno != null  and bcBrno != ''"> and bc_brno = #{bcBrno}</if>
            <if test="bcBcode != null  and bcBcode != ''"> and bc_bcode = #{bcBcode}</if>
            <if test="bcLp != null  and bcLp != ''"> and bc_lp = #{bcLp}</if>
            <if test="bcEmail != null  and bcEmail != ''"> and bc_email = #{bcEmail}</if>
            <if test="bcWeb != null  and bcWeb != ''"> and bc_web = #{bcWeb}</if>
            <if test="bcRdt != null  and bcRdt != ''"> and bc_rdt = #{bcRdt}</if>
            <if test="bcRaddr != null  and bcRaddr != ''"> and bc_raddr = #{bcRaddr}</if>
            <if test="bcTel != null  and bcTel != ''"> and bc_tel = #{bcTel}</if>
            <if test="bcBank != null  and bcBank != ''"> and bc_bank = #{bcBank}</if>
            <if test="bcAno != null  and bcAno != ''"> and bc_ano = #{bcAno}</if>
            <if test="bcRc != null "> and bc_rc = #{bcRc}</if>
            <if test="bcFbt != null  and bcFbt != ''"> and bc_fbt = #{bcFbt}</if>
            <if test="bcTbt != null  and bcTbt != ''"> and bc_tbt = #{bcTbt}</if>
            <if test="bcBs != null  and bcBs != ''"> and bc_bs = #{bcBs}</if>
            <if test="bcSts != null  and bcSts != ''"> and bc_sts = #{bcSts}</if>
            <if test="bcRmk != null  and bcRmk != ''"> and bc_rmk = #{bcRmk}</if>
            <if test="3aBcCd != null  and 3aBcCd != ''"> and 3a_bc_cd = #{3aBcCd}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="bcInitialcd != null  and bcInitialcd != ''"> and bc_initialcd = #{bcInitialcd}</if>
            <if test="bcBloc != null  and bcBloc != ''"> and bc_bloc = #{bcBloc}</if>
            <if test="bcLongterm != null  and bcLongterm != ''"> and bc_longterm = #{bcLongterm}</if>
            <if test="bcLicenceupDt != null  and bcLicenceupDt != ''"> and bc_licenceup_dt = #{bcLicenceupDt}</if>
            <if test="bcLegalDt != null  and bcLegalDt != ''"> and bc_legal_dt = #{bcLegalDt}</if>
            <if test="bcLegalupDt != null  and bcLegalupDt != ''"> and bc_legalup_dt = #{bcLegalupDt}</if>
            <if test="bcScopeupDt != null  and bcScopeupDt != ''"> and bc_scopeup_dt = #{bcScopeupDt}</if>
        </where>
    </select>
    
    <select id="selectBasCompanyByBcCd" parameterType="String" resultMap="BasCompanyResult">
        <include refid="selectBasCompanyVo"/>
        where bc_cd = #{bcCd}
    </select>

    <insert id="insertBasCompany" parameterType="BasCompany">
        insert into bas_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bcCd != null">bc_cd,</if>
            <if test="bcNm != null and bcNm != ''">bc_nm,</if>
            <if test="bcTyp != null">bc_typ,</if>
            <if test="bcSpecicalVerify != null">bc_specical_verify,</if>
            <if test="bcVerifyDt != null">bc_verify_dt,</if>
            <if test="bcVerifyupDt != null">bc_verifyup_dt,</if>
            <if test="bcVerifyNm != null">bc_verify_nm,</if>
            <if test="bcVerify != null">bc_verify,</if>
            <if test="bcLicence != null">bc_licence,</if>
            <if test="bcLicenceDt != null">bc_licence_dt,</if>
            <if test="bcInvcPri != null">bc_invc_pri,</if>
            <if test="bcUscc != null">bc_uscc,</if>
            <if test="bcOc != null">bc_oc,</if>
            <if test="bcTrcno != null">bc_trcno,</if>
            <if test="bcBrno != null">bc_brno,</if>
            <if test="bcBcode != null">bc_bcode,</if>
            <if test="bcLp != null">bc_lp,</if>
            <if test="bcEmail != null">bc_email,</if>
            <if test="bcWeb != null">bc_web,</if>
            <if test="bcRdt != null">bc_rdt,</if>
            <if test="bcRaddr != null">bc_raddr,</if>
            <if test="bcTel != null">bc_tel,</if>
            <if test="bcBank != null">bc_bank,</if>
            <if test="bcAno != null">bc_ano,</if>
            <if test="bcRc != null">bc_rc,</if>
            <if test="bcFbt != null">bc_fbt,</if>
            <if test="bcTbt != null">bc_tbt,</if>
            <if test="bcBs != null">bc_bs,</if>
            <if test="bcSts != null">bc_sts,</if>
            <if test="bcRmk != null">bc_rmk,</if>
            <if test="3aBcCd != null">3a_bc_cd,</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="bcInitialcd != null">bc_initialcd,</if>
            <if test="bcBloc != null">bc_bloc,</if>
            <if test="bcLongterm != null">bc_longterm,</if>
            <if test="bcLicenceupDt != null">bc_licenceup_dt,</if>
            <if test="bcLegalDt != null">bc_legal_dt,</if>
            <if test="bcLegalupDt != null">bc_legalup_dt,</if>
            <if test="bcScopeupDt != null">bc_scopeup_dt,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bcCd != null">#{bcCd},</if>
            <if test="bcNm != null and bcNm != ''">#{bcNm},</if>
            <if test="bcTyp != null">#{bcTyp},</if>
            <if test="bcSpecicalVerify != null">#{bcSpecicalVerify},</if>
            <if test="bcVerifyDt != null">#{bcVerifyDt},</if>
            <if test="bcVerifyupDt != null">#{bcVerifyupDt},</if>
            <if test="bcVerifyNm != null">#{bcVerifyNm},</if>
            <if test="bcVerify != null">#{bcVerify},</if>
            <if test="bcLicence != null">#{bcLicence},</if>
            <if test="bcLicenceDt != null">#{bcLicenceDt},</if>
            <if test="bcInvcPri != null">#{bcInvcPri},</if>
            <if test="bcUscc != null">#{bcUscc},</if>
            <if test="bcOc != null">#{bcOc},</if>
            <if test="bcTrcno != null">#{bcTrcno},</if>
            <if test="bcBrno != null">#{bcBrno},</if>
            <if test="bcBcode != null">#{bcBcode},</if>
            <if test="bcLp != null">#{bcLp},</if>
            <if test="bcEmail != null">#{bcEmail},</if>
            <if test="bcWeb != null">#{bcWeb},</if>
            <if test="bcRdt != null">#{bcRdt},</if>
            <if test="bcRaddr != null">#{bcRaddr},</if>
            <if test="bcTel != null">#{bcTel},</if>
            <if test="bcBank != null">#{bcBank},</if>
            <if test="bcAno != null">#{bcAno},</if>
            <if test="bcRc != null">#{bcRc},</if>
            <if test="bcFbt != null">#{bcFbt},</if>
            <if test="bcTbt != null">#{bcTbt},</if>
            <if test="bcBs != null">#{bcBs},</if>
            <if test="bcSts != null">#{bcSts},</if>
            <if test="bcRmk != null">#{bcRmk},</if>
            <if test="3aBcCd != null">#{3aBcCd},</if>
            <if test="rCreKid != null and rCreKid != ''">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="bcInitialcd != null">#{bcInitialcd},</if>
            <if test="bcBloc != null">#{bcBloc},</if>
            <if test="bcLongterm != null">#{bcLongterm},</if>
            <if test="bcLicenceupDt != null">#{bcLicenceupDt},</if>
            <if test="bcLegalDt != null">#{bcLegalDt},</if>
            <if test="bcLegalupDt != null">#{bcLegalupDt},</if>
            <if test="bcScopeupDt != null">#{bcScopeupDt},</if>
         </trim>
    </insert>

    <update id="updateBasCompany" parameterType="BasCompany">
        update bas_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="bcNm != null and bcNm != ''">bc_nm = #{bcNm},</if>
            <if test="bcTyp != null">bc_typ = #{bcTyp},</if>
            <if test="bcSpecicalVerify != null">bc_specical_verify = #{bcSpecicalVerify},</if>
            <if test="bcVerifyDt != null">bc_verify_dt = #{bcVerifyDt},</if>
            <if test="bcVerifyupDt != null">bc_verifyup_dt = #{bcVerifyupDt},</if>
            <if test="bcVerifyNm != null">bc_verify_nm = #{bcVerifyNm},</if>
            <if test="bcVerify != null">bc_verify = #{bcVerify},</if>
            <if test="bcLicence != null">bc_licence = #{bcLicence},</if>
            <if test="bcLicenceDt != null">bc_licence_dt = #{bcLicenceDt},</if>
            <if test="bcInvcPri != null">bc_invc_pri = #{bcInvcPri},</if>
            <if test="bcUscc != null">bc_uscc = #{bcUscc},</if>
            <if test="bcOc != null">bc_oc = #{bcOc},</if>
            <if test="bcTrcno != null">bc_trcno = #{bcTrcno},</if>
            <if test="bcBrno != null">bc_brno = #{bcBrno},</if>
            <if test="bcBcode != null">bc_bcode = #{bcBcode},</if>
            <if test="bcLp != null">bc_lp = #{bcLp},</if>
            <if test="bcEmail != null">bc_email = #{bcEmail},</if>
            <if test="bcWeb != null">bc_web = #{bcWeb},</if>
            <if test="bcRdt != null">bc_rdt = #{bcRdt},</if>
            <if test="bcRaddr != null">bc_raddr = #{bcRaddr},</if>
            <if test="bcTel != null">bc_tel = #{bcTel},</if>
            <if test="bcBank != null">bc_bank = #{bcBank},</if>
            <if test="bcAno != null">bc_ano = #{bcAno},</if>
            <if test="bcRc != null">bc_rc = #{bcRc},</if>
            <if test="bcFbt != null">bc_fbt = #{bcFbt},</if>
            <if test="bcTbt != null">bc_tbt = #{bcTbt},</if>
            <if test="bcBs != null">bc_bs = #{bcBs},</if>
            <if test="bcSts != null">bc_sts = #{bcSts},</if>
            <if test="bcRmk != null">bc_rmk = #{bcRmk},</if>
            <if test="3aBcCd != null">3a_bc_cd = #{3aBcCd},</if>
            <if test="rCreKid != null and rCreKid != ''">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null and rUpdKid != ''">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="bcInitialcd != null">bc_initialcd = #{bcInitialcd},</if>
            <if test="bcBloc != null">bc_bloc = #{bcBloc},</if>
            <if test="bcLongterm != null">bc_longterm = #{bcLongterm},</if>
            <if test="bcLicenceupDt != null">bc_licenceup_dt = #{bcLicenceupDt},</if>
            <if test="bcLegalDt != null">bc_legal_dt = #{bcLegalDt},</if>
            <if test="bcLegalupDt != null">bc_legalup_dt = #{bcLegalupDt},</if>
            <if test="bcScopeupDt != null">bc_scopeup_dt = #{bcScopeupDt},</if>
        </trim>
        where bc_cd = #{bcCd}
    </update>

    <delete id="deleteBasCompanyByBcCd" parameterType="String">
        delete from bas_company where bc_cd = #{bcCd}
    </delete>

    <delete id="deleteBasCompanyByBcCds" parameterType="String">
        delete from bas_company where bc_cd in 
        <foreach item="bcCd" collection="array" open="(" separator="," close=")">
            #{bcCd}
        </foreach>
    </delete>
</mapper>