package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.SalesContractMst;

/**
 * 销售合同主表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface SalesContractMstMapper 
{
    /**
     * 查询销售合同主表
     * 
     * @param scmCd 销售合同主表主键
     * @return 销售合同主表
     */
    public SalesContractMst selectSalesContractMstByScmCd(String scmCd);

    /**
     * 查询销售合同主表列表
     * 
     * @param salesContractMst 销售合同主表
     * @return 销售合同主表集合
     */
    public List<SalesContractMst> selectSalesContractMstList(SalesContractMst salesContractMst);

    /**
     * 新增销售合同主表
     * 
     * @param salesContractMst 销售合同主表
     * @return 结果
     */
    public int insertSalesContractMst(SalesContractMst salesContractMst);

    /**
     * 修改销售合同主表
     * 
     * @param salesContractMst 销售合同主表
     * @return 结果
     */
    public int updateSalesContractMst(SalesContractMst salesContractMst);

    /**
     * 删除销售合同主表
     * 
     * @param scmCd 销售合同主表主键
     * @return 结果
     */
    public int deleteSalesContractMstByScmCd(String scmCd);

    /**
     * 批量删除销售合同主表
     * 
     * @param scmCds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSalesContractMstByScmCds(String[] scmCds);
}
