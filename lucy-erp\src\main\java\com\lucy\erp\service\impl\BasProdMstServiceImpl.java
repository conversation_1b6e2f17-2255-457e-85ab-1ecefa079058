package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.BasProdMstMapper;
import com.lucy.erp.domain.BasProdMst;
import com.lucy.erp.service.IBasProdMstService;

/**
 * 基础_产品_主
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
@DataSource(DataSourceType.ERP)
public class BasProdMstServiceImpl implements IBasProdMstService
{
    @Autowired
    private BasProdMstMapper basProdMstMapper;

    /**
     * 查询基础_产品_主

     * 
     * @param bpmCd 基础_产品_主
主键
     * @return 基础_产品_主

     */
    @Override
    public BasProdMst selectBasProdMstByBpmCd(String bpmCd)
    {
        return basProdMstMapper.selectBasProdMstByBpmCd(bpmCd);
    }

    /**
     * 查询基础_产品_主
列表
     * 
     * @param basProdMst 基础_产品_主

     * @return 基础_产品_主

     */
    @Override
    public List<BasProdMst> selectBasProdMstList(BasProdMst basProdMst)
    {
        return basProdMstMapper.selectBasProdMstList(basProdMst);
    }

    /**
     * 新增基础_产品_主

     * 
     * @param basProdMst 基础_产品_主

     * @return 结果
     */
    @Override
    public int insertBasProdMst(BasProdMst basProdMst)
    {
        return basProdMstMapper.insertBasProdMst(basProdMst);
    }

    /**
     * 修改基础_产品_主

     * 
     * @param basProdMst 基础_产品_主

     * @return 结果
     */
    @Override
    public int updateBasProdMst(BasProdMst basProdMst)
    {
        return basProdMstMapper.updateBasProdMst(basProdMst);
    }

    /**
     * 批量删除基础_产品_主

     * 
     * @param bpmCds 需要删除的基础_产品_主
主键
     * @return 结果
     */
    @Override
    public int deleteBasProdMstByBpmCds(String[] bpmCds)
    {
        return basProdMstMapper.deleteBasProdMstByBpmCds(bpmCds);
    }

    /**
     * 删除基础_产品_主
信息
     * 
     * @param bpmCd 基础_产品_主
主键
     * @return 结果
     */
    @Override
    public int deleteBasProdMstByBpmCd(String bpmCd)
    {
        return basProdMstMapper.deleteBasProdMstByBpmCd(bpmCd);
    }
}
