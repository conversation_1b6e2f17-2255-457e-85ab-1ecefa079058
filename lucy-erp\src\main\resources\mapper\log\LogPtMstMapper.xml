<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.LogPtMstMapper">
    
    <resultMap type="LogPtMst" id="LogPtMstResult">
        <result property="id"    column="id"    />
        <result property="lpmCd"    column="lpm_cd"    />
        <result property="lpmPri"    column="lpm_pri"    />
        <result property="lpmBn"    column="lpm_bn"    />
        <result property="lpmPcd"    column="lpm_pcd"    />
        <result property="lpmPpcd"    column="lpm_ppcd"    />
        <result property="lpmSlcd"    column="lpm_slcd"    />
        <result property="lpmIsrp"    column="lpm_isrp"    />
        <result property="lpmRpwt"    column="lpm_rpwt"    />
        <result property="lpmZxwtslcd"    column="lpm_ZXwtslcd"    />
        <result property="lpmIsqc"    column="lpm_isqc"    />
        <result property="lpmQcwt"    column="lpm_qcwt"    />
        <result property="lpmQcwtdt"    column="lpm_qcwtdt"    />
        <result property="lpmSts"    column="lpm_sts"    />
        <result property="lpmTyp"    column="lpm_typ"    />
        <result property="lpmRmk"    column="lpm_rmk"    />
        <result property="lpmLocPbn"    column="lpm_loc_pbn"    />
        <result property="lpmLocSbn"    column="lpm_loc_sbn"    />
        <result property="lpmLocOrgmcd"    column="lpm_loc_orgmcd"    />
        <result property="lpmLocDmcd"    column="lpm_loc_dmcd"    />
        <result property="lpmLocTcd"    column="lpm_loc_tcd"    />
        <result property="lpmLocSlcd"    column="lpm_loc_slcd"    />
        <result property="lpmLocBnEncrypt"    column="lpm_loc_bn_encrypt"    />
        <result property="lpmLocQty"    column="lpm_loc_qty"    />
        <result property="lpmLocUnitp"    column="lpm_loc_unitp"    />
        <result property="lpmLocIspart"    column="lpm_loc_ispart"    />
        <result property="lpmLocBit"    column="lpm_loc_bit"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="lpmDmcdfz"    column="lpm_dmcdFZ"    />
        <result property="lpmDmcdfzpc"    column="lpm_dmcdFZpc"    />
        <result property="lpmSourcetype"    column="lpm_sourceType"    />
        <result property="lpmOriginally"    column="lpm_originally"    />
        <result property="lpmSupplierDt"    column="lpm_supplier_dt"    />
        <result property="lpmReqty"    column="lpm_reqty"    />
        <result property="lpmFlag"    column="lpm_flag"    />
        <result property="lpmOutTcd"    column="lpm_out_tcd"    />
    </resultMap>

    <sql id="selectLogPtMstVo">
        select id, lpm_cd, lpm_pri, lpm_bn, lpm_pcd, lpm_ppcd, lpm_slcd, lpm_isrp, lpm_rpwt, lpm_ZXwtslcd, lpm_isqc, lpm_qcwt, lpm_qcwtdt, lpm_sts, lpm_typ, lpm_rmk, lpm_loc_pbn, lpm_loc_sbn, lpm_loc_orgmcd, lpm_loc_dmcd, lpm_loc_tcd, lpm_loc_slcd, lpm_loc_bn_encrypt, lpm_loc_qty, lpm_loc_unitp, lpm_loc_ispart, lpm_loc_bit, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, lpm_dmcdFZ, lpm_dmcdFZpc, lpm_sourceType, lpm_originally, lpm_supplier_dt, lpm_reqty, lpm_flag, lpm_out_tcd from log_pt_mst
    </sql>

    <select id="selectLogPtMstList" parameterType="LogPtMst" resultMap="LogPtMstResult">
        <include refid="selectLogPtMstVo"/>
        <where>  
            <if test="lpmPri != null "> and lpm_pri = #{lpmPri}</if>
            <if test="lpmBn != null  and lpmBn != ''"> and lpm_bn = #{lpmBn}</if>
            <if test="lpmPcd != null  and lpmPcd != ''"> and lpm_pcd = #{lpmPcd}</if>
            <if test="lpmPpcd != null  and lpmPpcd != ''"> and lpm_ppcd = #{lpmPpcd}</if>
            <if test="lpmSlcd != null  and lpmSlcd != ''"> and lpm_slcd = #{lpmSlcd}</if>
            <if test="lpmIsrp != null  and lpmIsrp != ''"> and lpm_isrp = #{lpmIsrp}</if>
            <if test="lpmRpwt != null  and lpmRpwt != ''"> and lpm_rpwt = #{lpmRpwt}</if>
            <if test="lpmZxwtslcd != null  and lpmZxwtslcd != ''"> and lpm_ZXwtslcd = #{lpmZxwtslcd}</if>
            <if test="lpmIsqc != null  and lpmIsqc != ''"> and lpm_isqc = #{lpmIsqc}</if>
            <if test="lpmQcwt != null  and lpmQcwt != ''"> and lpm_qcwt = #{lpmQcwt}</if>
            <if test="lpmQcwtdt != null  and lpmQcwtdt != ''"> and lpm_qcwtdt = #{lpmQcwtdt}</if>
            <if test="lpmSts != null  and lpmSts != ''"> and lpm_sts = #{lpmSts}</if>
            <if test="lpmTyp != null  and lpmTyp != ''"> and lpm_typ = #{lpmTyp}</if>
            <if test="lpmRmk != null  and lpmRmk != ''"> and lpm_rmk = #{lpmRmk}</if>
            <if test="lpmLocPbn != null  and lpmLocPbn != ''"> and lpm_loc_pbn = #{lpmLocPbn}</if>
            <if test="lpmLocSbn != null  and lpmLocSbn != ''"> and lpm_loc_sbn = #{lpmLocSbn}</if>
            <if test="lpmLocOrgmcd != null  and lpmLocOrgmcd != ''"> and lpm_loc_orgmcd = #{lpmLocOrgmcd}</if>
            <if test="lpmLocDmcd != null  and lpmLocDmcd != ''"> and lpm_loc_dmcd = #{lpmLocDmcd}</if>
            <if test="lpmLocTcd != null  and lpmLocTcd != ''"> and lpm_loc_tcd = #{lpmLocTcd}</if>
            <if test="lpmLocSlcd != null  and lpmLocSlcd != ''"> and lpm_loc_slcd = #{lpmLocSlcd}</if>
            <if test="lpmLocBnEncrypt != null  and lpmLocBnEncrypt != ''"> and lpm_loc_bn_encrypt = #{lpmLocBnEncrypt}</if>
            <if test="lpmLocQty != null "> and lpm_loc_qty = #{lpmLocQty}</if>
            <if test="lpmLocUnitp != null "> and lpm_loc_unitp = #{lpmLocUnitp}</if>
            <if test="lpmLocIspart != null "> and lpm_loc_ispart = #{lpmLocIspart}</if>
            <if test="lpmLocBit != null "> and lpm_loc_bit = #{lpmLocBit}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="lpmDmcdfz != null  and lpmDmcdfz != ''"> and lpm_dmcdFZ = #{lpmDmcdfz}</if>
            <if test="lpmDmcdfzpc != null  and lpmDmcdfzpc != ''"> and lpm_dmcdFZpc = #{lpmDmcdfzpc}</if>
            <if test="lpmSourcetype != null  and lpmSourcetype != ''"> and lpm_sourceType = #{lpmSourcetype}</if>
            <if test="lpmOriginally != null  and lpmOriginally != ''"> and lpm_originally = #{lpmOriginally}</if>
            <if test="lpmSupplierDt != null  and lpmSupplierDt != ''"> and lpm_supplier_dt = #{lpmSupplierDt}</if>
            <if test="lpmReqty != null "> and lpm_reqty = #{lpmReqty}</if>
            <if test="lpmFlag != null  and lpmFlag != ''"> and lpm_flag = #{lpmFlag}</if>
            <if test="lpmOutTcd != null  and lpmOutTcd != ''"> and lpm_out_tcd = #{lpmOutTcd}</if>
        </where>
    </select>
    
    <select id="selectLogPtMstByLpmCd" parameterType="String" resultMap="LogPtMstResult">
        <include refid="selectLogPtMstVo"/>
        where lpm_cd = #{lpmCd}
    </select>

    <insert id="insertLogPtMst" parameterType="LogPtMst">
        insert into log_pt_mst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lpmCd != null">lpm_cd,</if>
            <if test="lpmPri != null">lpm_pri,</if>
            <if test="lpmBn != null">lpm_bn,</if>
            <if test="lpmPcd != null">lpm_pcd,</if>
            <if test="lpmPpcd != null">lpm_ppcd,</if>
            <if test="lpmSlcd != null">lpm_slcd,</if>
            <if test="lpmIsrp != null">lpm_isrp,</if>
            <if test="lpmRpwt != null">lpm_rpwt,</if>
            <if test="lpmZxwtslcd != null">lpm_ZXwtslcd,</if>
            <if test="lpmIsqc != null">lpm_isqc,</if>
            <if test="lpmQcwt != null">lpm_qcwt,</if>
            <if test="lpmQcwtdt != null">lpm_qcwtdt,</if>
            <if test="lpmSts != null">lpm_sts,</if>
            <if test="lpmTyp != null">lpm_typ,</if>
            <if test="lpmRmk != null">lpm_rmk,</if>
            <if test="lpmLocPbn != null">lpm_loc_pbn,</if>
            <if test="lpmLocSbn != null">lpm_loc_sbn,</if>
            <if test="lpmLocOrgmcd != null">lpm_loc_orgmcd,</if>
            <if test="lpmLocDmcd != null">lpm_loc_dmcd,</if>
            <if test="lpmLocTcd != null">lpm_loc_tcd,</if>
            <if test="lpmLocSlcd != null">lpm_loc_slcd,</if>
            <if test="lpmLocBnEncrypt != null">lpm_loc_bn_encrypt,</if>
            <if test="lpmLocQty != null">lpm_loc_qty,</if>
            <if test="lpmLocUnitp != null">lpm_loc_unitp,</if>
            <if test="lpmLocIspart != null">lpm_loc_ispart,</if>
            <if test="lpmLocBit != null">lpm_loc_bit,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="lpmDmcdfz != null">lpm_dmcdFZ,</if>
            <if test="lpmDmcdfzpc != null">lpm_dmcdFZpc,</if>
            <if test="lpmSourcetype != null">lpm_sourceType,</if>
            <if test="lpmOriginally != null">lpm_originally,</if>
            <if test="lpmSupplierDt != null">lpm_supplier_dt,</if>
            <if test="lpmReqty != null">lpm_reqty,</if>
            <if test="lpmFlag != null">lpm_flag,</if>
            <if test="lpmOutTcd != null">lpm_out_tcd,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lpmCd != null">#{lpmCd},</if>
            <if test="lpmPri != null">#{lpmPri},</if>
            <if test="lpmBn != null">#{lpmBn},</if>
            <if test="lpmPcd != null">#{lpmPcd},</if>
            <if test="lpmPpcd != null">#{lpmPpcd},</if>
            <if test="lpmSlcd != null">#{lpmSlcd},</if>
            <if test="lpmIsrp != null">#{lpmIsrp},</if>
            <if test="lpmRpwt != null">#{lpmRpwt},</if>
            <if test="lpmZxwtslcd != null">#{lpmZxwtslcd},</if>
            <if test="lpmIsqc != null">#{lpmIsqc},</if>
            <if test="lpmQcwt != null">#{lpmQcwt},</if>
            <if test="lpmQcwtdt != null">#{lpmQcwtdt},</if>
            <if test="lpmSts != null">#{lpmSts},</if>
            <if test="lpmTyp != null">#{lpmTyp},</if>
            <if test="lpmRmk != null">#{lpmRmk},</if>
            <if test="lpmLocPbn != null">#{lpmLocPbn},</if>
            <if test="lpmLocSbn != null">#{lpmLocSbn},</if>
            <if test="lpmLocOrgmcd != null">#{lpmLocOrgmcd},</if>
            <if test="lpmLocDmcd != null">#{lpmLocDmcd},</if>
            <if test="lpmLocTcd != null">#{lpmLocTcd},</if>
            <if test="lpmLocSlcd != null">#{lpmLocSlcd},</if>
            <if test="lpmLocBnEncrypt != null">#{lpmLocBnEncrypt},</if>
            <if test="lpmLocQty != null">#{lpmLocQty},</if>
            <if test="lpmLocUnitp != null">#{lpmLocUnitp},</if>
            <if test="lpmLocIspart != null">#{lpmLocIspart},</if>
            <if test="lpmLocBit != null">#{lpmLocBit},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="lpmDmcdfz != null">#{lpmDmcdfz},</if>
            <if test="lpmDmcdfzpc != null">#{lpmDmcdfzpc},</if>
            <if test="lpmSourcetype != null">#{lpmSourcetype},</if>
            <if test="lpmOriginally != null">#{lpmOriginally},</if>
            <if test="lpmSupplierDt != null">#{lpmSupplierDt},</if>
            <if test="lpmReqty != null">#{lpmReqty},</if>
            <if test="lpmFlag != null">#{lpmFlag},</if>
            <if test="lpmOutTcd != null">#{lpmOutTcd},</if>
         </trim>
    </insert>

    <update id="updateLogPtMst" parameterType="LogPtMst">
        update log_pt_mst
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="lpmPri != null">lpm_pri = #{lpmPri},</if>
            <if test="lpmBn != null">lpm_bn = #{lpmBn},</if>
            <if test="lpmPcd != null">lpm_pcd = #{lpmPcd},</if>
            <if test="lpmPpcd != null">lpm_ppcd = #{lpmPpcd},</if>
            <if test="lpmSlcd != null">lpm_slcd = #{lpmSlcd},</if>
            <if test="lpmIsrp != null">lpm_isrp = #{lpmIsrp},</if>
            <if test="lpmRpwt != null">lpm_rpwt = #{lpmRpwt},</if>
            <if test="lpmZxwtslcd != null">lpm_ZXwtslcd = #{lpmZxwtslcd},</if>
            <if test="lpmIsqc != null">lpm_isqc = #{lpmIsqc},</if>
            <if test="lpmQcwt != null">lpm_qcwt = #{lpmQcwt},</if>
            <if test="lpmQcwtdt != null">lpm_qcwtdt = #{lpmQcwtdt},</if>
            <if test="lpmSts != null">lpm_sts = #{lpmSts},</if>
            <if test="lpmTyp != null">lpm_typ = #{lpmTyp},</if>
            <if test="lpmRmk != null">lpm_rmk = #{lpmRmk},</if>
            <if test="lpmLocPbn != null">lpm_loc_pbn = #{lpmLocPbn},</if>
            <if test="lpmLocSbn != null">lpm_loc_sbn = #{lpmLocSbn},</if>
            <if test="lpmLocOrgmcd != null">lpm_loc_orgmcd = #{lpmLocOrgmcd},</if>
            <if test="lpmLocDmcd != null">lpm_loc_dmcd = #{lpmLocDmcd},</if>
            <if test="lpmLocTcd != null">lpm_loc_tcd = #{lpmLocTcd},</if>
            <if test="lpmLocSlcd != null">lpm_loc_slcd = #{lpmLocSlcd},</if>
            <if test="lpmLocBnEncrypt != null">lpm_loc_bn_encrypt = #{lpmLocBnEncrypt},</if>
            <if test="lpmLocQty != null">lpm_loc_qty = #{lpmLocQty},</if>
            <if test="lpmLocUnitp != null">lpm_loc_unitp = #{lpmLocUnitp},</if>
            <if test="lpmLocIspart != null">lpm_loc_ispart = #{lpmLocIspart},</if>
            <if test="lpmLocBit != null">lpm_loc_bit = #{lpmLocBit},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="lpmDmcdfz != null">lpm_dmcdFZ = #{lpmDmcdfz},</if>
            <if test="lpmDmcdfzpc != null">lpm_dmcdFZpc = #{lpmDmcdfzpc},</if>
            <if test="lpmSourcetype != null">lpm_sourceType = #{lpmSourcetype},</if>
            <if test="lpmOriginally != null">lpm_originally = #{lpmOriginally},</if>
            <if test="lpmSupplierDt != null">lpm_supplier_dt = #{lpmSupplierDt},</if>
            <if test="lpmReqty != null">lpm_reqty = #{lpmReqty},</if>
            <if test="lpmFlag != null">lpm_flag = #{lpmFlag},</if>
            <if test="lpmOutTcd != null">lpm_out_tcd = #{lpmOutTcd},</if>
        </trim>
        where lpm_cd = #{lpmCd}
    </update>

    <delete id="deleteLogPtMstByLpmCd" parameterType="String">
        delete from log_pt_mst where lpm_cd = #{lpmCd}
    </delete>

    <delete id="deleteLogPtMstByLpmCds" parameterType="String">
        delete from log_pt_mst where lpm_cd in 
        <foreach item="lpmCd" collection="array" open="(" separator="," close=")">
            #{lpmCd}
        </foreach>
    </delete>
</mapper>