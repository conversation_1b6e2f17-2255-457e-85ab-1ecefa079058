package com.lucy.erp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.lucy.erp.domain.BasCustorgMst;
import com.lucy.erp.service.IBasCustorgMstService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基础_客户组织_主Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/sales/custorg")
public class BasCustorgMstController extends BaseController
{
    @Autowired
    private IBasCustorgMstService basCustorgMstService;

    /**
     * 查询基础_客户组织_主列表
     */
    @PreAuthorize("@ss.hasPermi('sales:custorg:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCustorgMst basCustorgMst)
    {
        startPage();
        List<BasCustorgMst> list = basCustorgMstService.selectBasCustorgMstList(basCustorgMst);
        return getDataTable(list);
    }

    /**
     * 导出基础_客户组织_主列表
     */
    @PreAuthorize("@ss.hasPermi('sales:custorg:export')")
    @Log(title = "基础_客户组织_主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCustorgMst basCustorgMst)
    {
        List<BasCustorgMst> list = basCustorgMstService.selectBasCustorgMstList(basCustorgMst);
        ExcelUtil<BasCustorgMst> util = new ExcelUtil<BasCustorgMst>(BasCustorgMst.class);
        util.exportExcel(response, list, "基础_客户组织_主数据");
    }

    /**
     * 获取基础_客户组织_主详细信息
     */
    @PreAuthorize("@ss.hasPermi('sales:custorg:query')")
    @GetMapping(value = "/{bcmCd}")
    public AjaxResult getInfo(@PathVariable("bcmCd") String bcmCd)
    {
        return success(basCustorgMstService.selectBasCustorgMstByBcmCd(bcmCd));
    }

    /**
     * 新增基础_客户组织_主
     */
    @PreAuthorize("@ss.hasPermi('sales:custorg:add')")
    @Log(title = "基础_客户组织_主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCustorgMst basCustorgMst)
    {
        return toAjax(basCustorgMstService.insertBasCustorgMst(basCustorgMst));
    }

    /**
     * 修改基础_客户组织_主
     */
    @PreAuthorize("@ss.hasPermi('sales:custorg:edit')")
    @Log(title = "基础_客户组织_主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCustorgMst basCustorgMst)
    {
        return toAjax(basCustorgMstService.updateBasCustorgMst(basCustorgMst));
    }

    /**
     * 删除基础_客户组织_主
     */
    @PreAuthorize("@ss.hasPermi('sales:custorg:remove')")
    @Log(title = "基础_客户组织_主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bcmCds}")
    public AjaxResult remove(@PathVariable String[] bcmCds)
    {
        return toAjax(basCustorgMstService.deleteBasCustorgMstByBcmCds(bcmCds));
    }
}
