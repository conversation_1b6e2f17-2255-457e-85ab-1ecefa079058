<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.inventoryoe.mapper.LogInventoryList2025010101Mapper">
    
    <resultMap type="LogInventoryList2025010101" id="LogInventoryList2025010101Result">
        <result property="id"    column="id"    />
        <result property="lilDmcd"    column="lil_dmcd"    />
        <result property="lilTcd"    column="lil_tcd"    />
        <result property="lilSlcd"    column="lil_slcd"    />
        <result property="lilPpcd"    column="lil_ppcd"    />
        <result property="lilBn"    column="lil_bn"    />
        <result property="lilQty"    column="lil_qty"    />
        <result property="lilOrgmcd"    column="lil_orgmcd"    />
        <result property="lilBnEncrypt"    column="lil_bn_encrypt"    />
    </resultMap>

    <sql id="selectLogInventoryList2025010101Vo">
        select id, lil_dmcd, lil_tcd, lil_slcd, lil_ppcd, lil_bn, lil_qty, lil_orgmcd, lil_bn_encrypt from log_inventory_list_2025010101
    </sql>

    <select id="selectLogInventoryList2025010101List" parameterType="LogInventoryList2025010101" resultMap="LogInventoryList2025010101Result">
        <include refid="selectLogInventoryList2025010101Vo"/>
        <where>
          1=1
          and lil_qty>0
            AND (lil_tcd  NOT IN ('UU','PP','LL','ZKK','BB'  ) OR (lil_tcd ='UU' AND lil_bn LIKE '%,ZS' AND lil_orgmcd ='BM10000050'))
            AND lil_orgmcd IN('BM10000048','BM10000050','BM10000056','BM10000060','BM10000062','BM10000057')
            <if test="lilDmcd != null  and lilDmcd != ''"> and lil_dmcd = #{lilDmcd}</if>
            <if test="lilTcd != null  and lilTcd != ''"> and lil_tcd = #{lilTcd}</if>
            <if test="lilSlcd != null  and lilSlcd != ''"> and lil_slcd = #{lilSlcd}</if>
            <if test="lilPpcd != null  and lilPpcd != ''"> and lil_ppcd = #{lilPpcd}</if>
            <if test="lilBn != null  and lilBn != ''"> and lil_bn = #{lilBn}</if>
            <if test="lilQty != null  and lilQty != ''"> and lil_qty = #{lilQty}</if>

            <if test="lilOrgmcd != null  and lilOrgmcd != ''"> and lil_orgmcd = #{lilOrgmcd}</if>
            <if test="lilBnEncrypt != null  and lilBnEncrypt != ''"> and lil_bn_encrypt = #{lilBnEncrypt}</if>
        </where>
    </select>
    
    <select id="selectLogInventoryList2025010101ById" parameterType="Long" resultMap="LogInventoryList2025010101Result">
        <include refid="selectLogInventoryList2025010101Vo"/>
        where id = #{id}
    </select>

    <insert id="insertLogInventoryList2025010101" parameterType="LogInventoryList2025010101">
        insert into log_inventory_list_2025010101
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="lilDmcd != null and lilDmcd != ''">lil_dmcd,</if>
            <if test="lilTcd != null and lilTcd != ''">lil_tcd,</if>
            <if test="lilSlcd != null and lilSlcd != ''">lil_slcd,</if>
            <if test="lilPpcd != null and lilPpcd != ''">lil_ppcd,</if>
            <if test="lilBn != null and lilBn != ''">lil_bn,</if>
            <if test="lilQty != null and lilQty != ''">lil_qty,</if>
            <if test="lilOrgmcd != null and lilOrgmcd != ''">lil_orgmcd,</if>
            <if test="lilBnEncrypt != null and lilBnEncrypt != ''">lil_bn_encrypt,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="lilDmcd != null and lilDmcd != ''">#{lilDmcd},</if>
            <if test="lilTcd != null and lilTcd != ''">#{lilTcd},</if>
            <if test="lilSlcd != null and lilSlcd != ''">#{lilSlcd},</if>
            <if test="lilPpcd != null and lilPpcd != ''">#{lilPpcd},</if>
            <if test="lilBn != null and lilBn != ''">#{lilBn},</if>
            <if test="lilQty != null and lilQty != ''">#{lilQty},</if>
            <if test="lilOrgmcd != null and lilOrgmcd != ''">#{lilOrgmcd},</if>
            <if test="lilBnEncrypt != null and lilBnEncrypt != ''">#{lilBnEncrypt},</if>
         </trim>
    </insert>

    <update id="updateLogInventoryList2025010101" parameterType="LogInventoryList2025010101">
        update log_inventory_list_2025010101
        <trim prefix="SET" suffixOverrides=",">
            <if test="lilDmcd != null and lilDmcd != ''">lil_dmcd = #{lilDmcd},</if>
            <if test="lilTcd != null and lilTcd != ''">lil_tcd = #{lilTcd},</if>
            <if test="lilSlcd != null and lilSlcd != ''">lil_slcd = #{lilSlcd},</if>
            <if test="lilPpcd != null and lilPpcd != ''">lil_ppcd = #{lilPpcd},</if>
            <if test="lilBn != null and lilBn != ''">lil_bn = #{lilBn},</if>
            <if test="lilQty != null and lilQty != ''">lil_qty = #{lilQty},</if>
            <if test="lilOrgmcd != null and lilOrgmcd != ''">lil_orgmcd = #{lilOrgmcd},</if>
            <if test="lilBnEncrypt != null and lilBnEncrypt != ''">lil_bn_encrypt = #{lilBnEncrypt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLogInventoryList2025010101ById" parameterType="Long">
        delete from log_inventory_list_2025010101 where id = #{id}
    </delete>

    <delete id="deleteLogInventoryList2025010101ByIds" parameterType="String">
        delete from log_inventory_list_2025010101 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>