package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.LogPtMstMapper;
import com.lucy.erp.domain.LogPtMst;
import com.lucy.erp.service.ILogPtMstService;

/**
 * 物流_生产跟踪_主
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
@DataSource(DataSourceType.ERP)
public class LogPtMstServiceImpl implements ILogPtMstService
{
    @Autowired
    private LogPtMstMapper logPtMstMapper;

    /**
     * 查询物流_生产跟踪_主

     * 
     * @param lpmCd 物流_生产跟踪_主
主键
     * @return 物流_生产跟踪_主

     */
    @Override
    public LogPtMst selectLogPtMstByLpmCd(String lpmCd)
    {
        return logPtMstMapper.selectLogPtMstByLpmCd(lpmCd);
    }

    /**
     * 查询物流_生产跟踪_主
列表
     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 物流_生产跟踪_主

     */
    @Override
    public List<LogPtMst> selectLogPtMstList(LogPtMst logPtMst)
    {
        return logPtMstMapper.selectLogPtMstList(logPtMst);
    }

    /**
     * 新增物流_生产跟踪_主

     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 结果
     */
    @Override
    public int insertLogPtMst(LogPtMst logPtMst)
    {
        return logPtMstMapper.insertLogPtMst(logPtMst);
    }

    /**
     * 修改物流_生产跟踪_主

     * 
     * @param logPtMst 物流_生产跟踪_主

     * @return 结果
     */
    @Override
    public int updateLogPtMst(LogPtMst logPtMst)
    {
        return logPtMstMapper.updateLogPtMst(logPtMst);
    }

    /**
     * 批量删除物流_生产跟踪_主

     * 
     * @param lpmCds 需要删除的物流_生产跟踪_主
主键
     * @return 结果
     */
    @Override
    public int deleteLogPtMstByLpmCds(String[] lpmCds)
    {
        return logPtMstMapper.deleteLogPtMstByLpmCds(lpmCds);
    }

    /**
     * 删除物流_生产跟踪_主
信息
     * 
     * @param lpmCd 物流_生产跟踪_主
主键
     * @return 结果
     */
    @Override
    public int deleteLogPtMstByLpmCd(String lpmCd)
    {
        return logPtMstMapper.deleteLogPtMstByLpmCd(lpmCd);
    }
}
