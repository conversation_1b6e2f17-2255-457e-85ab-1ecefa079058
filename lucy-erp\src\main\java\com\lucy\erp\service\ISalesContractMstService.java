package com.lucy.erp.service;

import java.util.List;
import com.lucy.erp.domain.SalesContractMst;

/**
 * 销售合同主表Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ISalesContractMstService 
{
    /**
     * 查询销售合同主表
     * 
     * @param scmCd 销售合同主表主键
     * @return 销售合同主表
     */
    public SalesContractMst selectSalesContractMstByScmCd(String scmCd);

    /**
     * 查询销售合同主表列表
     * 
     * @param salesContractMst 销售合同主表
     * @return 销售合同主表集合
     */
    public List<SalesContractMst> selectSalesContractMstList(SalesContractMst salesContractMst);

    /**
     * 新增销售合同主表
     * 
     * @param salesContractMst 销售合同主表
     * @return 结果
     */
    public int insertSalesContractMst(SalesContractMst salesContractMst);

    /**
     * 修改销售合同主表
     * 
     * @param salesContractMst 销售合同主表
     * @return 结果
     */
    public int updateSalesContractMst(SalesContractMst salesContractMst);

    /**
     * 批量删除销售合同主表
     * 
     * @param scmCds 需要删除的销售合同主表主键集合
     * @return 结果
     */
    public int deleteSalesContractMstByScmCds(String[] scmCds);

    /**
     * 删除销售合同主表信息
     * 
     * @param scmCd 销售合同主表主键
     * @return 结果
     */
    public int deleteSalesContractMstByScmCd(String scmCd);
}
