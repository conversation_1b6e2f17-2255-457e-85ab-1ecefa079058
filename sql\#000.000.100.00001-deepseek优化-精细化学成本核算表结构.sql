 
--  第一版数据库 `lucyerp_fin_db` 字段命名是中文首字母
-- 第二版本数据库 `lucy_bridge_erp_u8`    ERP生产环境  PolarDB里都有对应的表 只是个别字段没有。
-- 如：`ending_quantity` DECIMAL(20,6) GENERATED ALWAYS AS 
--     (beginning_quantity + inbound_quantity - outbound_quantity) STORED COMMENT '期末数量, Ending quantity',
 
 CREATE TABLE `currency` 
 
 (
  `code` CHAR(3) COLLATE utf8mb4_unicode_ci NOT NULL PRIMARY KEY,  -- ISO货币代码
  `name` VARCHAR(50) NOT NULL,          -- 货币名称
  `symbol` VARCHAR(5) NOT NULL,         -- 符号（￥/$/€）
  `decimal_places` TINYINT NOT NULL     -- 小数位数（如CNY=2，JPY=0）
) COMMENT='ISO currency master';

CREATE TABLE `asset_owner_master` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  
  -- 主体标识信息
  `code` VARCHAR(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主体编码, Owner code',
  `name` VARCHAR(100) NOT NULL COMMENT '主体名称, Owner name',
  `short_name` VARCHAR(50) COMMENT '主体简称, Short name',
  `type` ENUM('COMPANY','DEPARTMENT','PROJECT','OTHER') NOT NULL 
    COMMENT '主体类型(公司/部门/项目/其他), Owner type',
  
  -- 组织关系
  `parent_code` VARCHAR(20) COMMENT '上级主体编码, Parent owner code',
  `level` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '组织层级, Organization level',
  `path` VARCHAR(255) COMMENT '组织路径(存储ID路径), Organization path',
  
  -- 财务信息
  `cost_center` VARCHAR(20) COMMENT '成本中心代码, Cost center code',
  `profit_center` VARCHAR(20) COMMENT '利润中心代码, Profit center code',
  `tax_number` VARCHAR(30) COMMENT '税号, Tax identification number',
  
  -- 状态控制
  `status` ENUM('ACTIVE','INACTIVE','SUSPENDED') NOT NULL DEFAULT 'ACTIVE' 
    COMMENT '状态(活跃/停用/暂停), Status',
  `start_date` DATE NOT NULL COMMENT '生效日期, Effective date',
  `end_date` DATE COMMENT '失效日期, Expiration date',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP 
    COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_owner_code` (`code`),
  KEY `idx_parent` (`parent_code`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='资产所属主体主表 Asset owner master data';



CREATE TABLE `lucy_bridge_erp_u8`.`cost_composition` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `structure_name` VARCHAR(50) NOT NULL COMMENT '表结构名称',
  `parent_structure_id` BIGINT UNSIGNED NOT NULL COMMENT '所在表结构ID',
  `status` ENUM('S','X') NOT NULL COMMENT '明细状态 S：生效  X：废弃',
  `generation_context` VARCHAR(20) NOT NULL COMMENT '明细生成背景',
  
  -- 货币相关
  `currency_code` CHAR(3) NOT NULL COMMENT '交易货币代码',
  `base_currency_code` CHAR(3) NOT NULL COMMENT '基准货币代码',
  `exchange_rate` DECIMAL(12,6) NOT NULL COMMENT '汇率',
  
  -- 金额构成
  `total_amount` DECIMAL(20,6) NOT NULL COMMENT '总金额',
  `material_cost` DECIMAL(20,6) NOT NULL COMMENT '物料金额',
  `labor_cost` DECIMAL(20,6) NOT NULL COMMENT '人工金额',
  `manufacturing_cost` DECIMAL(20,6) NOT NULL COMMENT '制造费用金额',
  `logistics_cost` DECIMAL(20,6) NOT NULL COMMENT '物流费用金额',
  `packaging_cost` DECIMAL(20,6) NOT NULL COMMENT '物流包材金额',
  
  -- 标准成本
  `standard_cost` DECIMAL(20,6) NOT NULL COMMENT '标准成本',
  `standard_material` DECIMAL(20,6) NOT NULL COMMENT '标准料',
  `standard_labor` DECIMAL(20,6) NOT NULL COMMENT '标准工',
  `standard_overhead` DECIMAL(20,6) NOT NULL COMMENT '标准费',
  `standard_logistics` DECIMAL(20,6) NOT NULL COMMENT '标准物流费',
  `standard_packaging` DECIMAL(20,6) NOT NULL COMMENT '标准物流包材费',
  
  -- 审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记 0:正常 1:删除',
  
  PRIMARY KEY (`id`),
  KEY `idx_parent_structure` (`parent_structure_id`),
  KEY `idx_status` (`status`) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Cost composition structure';
-- 
  ALTER TABLE `lucy_bridge_erp_u8`.`cost_composition`  
    ADD CONSTRAINT `fk_currency_code` FOREIGN KEY (`currency_code`) REFERENCES `lucy_bridge_erp_u8`.`currency`(`code`);

ALTER TABLE `lucy_bridge_erp_u8`.`cost_composition`   
  CHANGE `currency_code` `currency_code` CHAR(3) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NULL  COMMENT '交易货币代码',
  CHANGE `base_currency_code` `base_currency_code` CHAR(3) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NULL  COMMENT '基准货币代码',
  CHANGE `exchange_rate` `exchange_rate` DECIMAL(12,6) NULL  COMMENT '汇率',
  CHANGE `total_amount` `total_amount` DECIMAL(20,6) NULL  COMMENT '总金额',
  CHANGE `material_cost` `material_cost` DECIMAL(20,6) NULL  COMMENT '物料金额',
  CHANGE `labor_cost` `labor_cost` DECIMAL(20,6) NULL  COMMENT '人工金额',
  CHANGE `manufacturing_cost` `manufacturing_cost` DECIMAL(20,6) NULL  COMMENT '制造费用金额',
  CHANGE `logistics_cost` `logistics_cost` DECIMAL(20,6) NULL  COMMENT '物流费用金额',
  CHANGE `packaging_cost` `packaging_cost` DECIMAL(20,6) NULL  COMMENT '物流包材金额',
  CHANGE `standard_cost` `standard_cost` DECIMAL(20,6) NULL  COMMENT '标准成本',
  CHANGE `standard_material` `standard_material` DECIMAL(20,6) NULL  COMMENT '标准料',
  CHANGE `standard_labor` `standard_labor` DECIMAL(20,6) NULL  COMMENT '标准工',
  CHANGE `standard_overhead` `standard_overhead` DECIMAL(20,6) NULL  COMMENT '标准费',
  CHANGE `standard_logistics` `standard_logistics` DECIMAL(20,6) NULL  COMMENT '标准物流费',
  CHANGE `standard_packaging` `standard_packaging` DECIMAL(20,6) NULL  COMMENT '标准物流包材费';


-- 自动计算原币金额
 ALTER TABLE `cost_composition`
 ADD `total_amount_original` DECIMAL(20,6) 
    GENERATED ALWAYS AS (total_amount / exchange_rate) VIRTUAL;
    
 -- ---------------------------
   
 CREATE TABLE `accounting_subject` (
  `code` VARCHAR(20) COLLATE utf8mb4_unicode_ci  NOT NULL PRIMARY KEY COMMENT '科目代码, Account code',
  `name` VARCHAR(100) NOT NULL COMMENT '科目名称, Account name',
  `category` ENUM('ASSET','LIABILITY','EQUITY','REVENUE','EXPENSE') 
    COMMENT '科目类别, Account category',
  `level` TINYINT NOT NULL COMMENT '科目层级(1-5), Account level',
  `parent_code` VARCHAR(20) COLLATE utf8mb4_unicode_ci  COMMENT '上级科目代码, Parent account code',
  FOREIGN KEY (`parent_code`) REFERENCES `accounting_subject` (`code`)
) COMMENT='会计科目表 Accounting subject master';

CREATE TABLE `sku_master` (
  `code` VARCHAR(50) COLLATE utf8mb4_unicode_ci  NOT NULL PRIMARY KEY COMMENT 'SKU编码, SKU code',
  `name` VARCHAR(200) NOT NULL COMMENT '商品名称, Product name',
  `uom` VARCHAR(10) NOT NULL COMMENT '计量单位, Unit of measure',
  `inventory_type` ENUM('RAW','WIP','FINISHED') 
    COMMENT '库存类型(原料/在制品/成品), Inventory type',
  `cost_method` ENUM('FIFO','LIFO','AVERAGE') 
    COMMENT '成本计价方式, Costing method'
) COMMENT='SKU主数据表 SKU master data';

CREATE TABLE `lucy_bridge_erp_u8`.`inventory_opening_balance` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `accounting_period` CHAR(7) NOT NULL COMMENT '期初库存年月(YYYY-MM), Accounting period',
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目代码, Financial account code',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `quantity` DECIMAL(20,6) NOT NULL COMMENT '期初数量, Opening quantity',
  `amount` DECIMAL(18,2) NOT NULL COMMENT '期初金额, Opening amount',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Inventory source: Purchased/Self-produced',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset ownership entity',
  
  -- 核算审计字段
  `valuation_time` DATETIME NOT NULL COMMENT '核算时间, Valuation timestamp',
  `valuator` VARCHAR(10) NOT NULL COMMENT '核算人, Valuation operator',
  `audit_time` DATETIME COMMENT '审核时间, Audit timestamp',
  `auditor` VARCHAR(10) COMMENT '审核人, Audit operator',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by user',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Last updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Soft delete flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_account_sku` (`accounting_period`,`financial_account`,`sku`),
  KEY `idx_valuation_time` (`valuation_time`),
  KEY `idx_asset_owner` (`asset_owner`)
    ,
  CONSTRAINT `fk_financial_account` 
    FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `fk_sku_master` 
    FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='库存期初汇总表 Inventory opening balance summary';



CREATE VIEW vw_opening_balance_summary AS
SELECT 
  iob.accounting_period AS '会计期间',
  acc.name AS '科目名称',
  sku.name AS '商品名称',
  iob.quantity AS '数量',
  iob.amount AS '金额'
FROM inventory_opening_balance iob
JOIN accounting_subject acc ON iob.financial_account = acc.code
JOIN sku_master sku ON iob.sku = sku.code
WHERE iob.is_deleted = 0;



CREATE TABLE `warehouse_master` (
  `code`  VARCHAR(20)   COLLATE utf8mb4_unicode_ci   NOT NULL PRIMARY KEY COMMENT '仓库代码, Warehouse code',
  `name` VARCHAR(100) NOT NULL COMMENT '仓库名称, Warehouse name',
  `type` ENUM('RAW_MATERIAL','WIP','FINISHED_GOODS','COLD_STORAGE') 
    COMMENT '仓库类型, Warehouse type',
  `location` VARCHAR(200) COMMENT '地理坐标, GPS coordinates',
  `manager` VARCHAR(50) COMMENT '负责人, Warehouse manager',
  `capacity` DECIMAL(18,2) COMMENT '存储容量(m³), Storage capacity'
) COMMENT='仓库主数据表 Warehouse master data';

CREATE TABLE `storage_location` (
  `warehouse` VARCHAR(20)  COLLATE utf8mb4_unicode_ci   NOT NULL COMMENT '所属仓库, Warehouse code',
  `zone` VARCHAR(20) NOT NULL COMMENT '库区代码, Zone code',
  `location` VARCHAR(20) NOT NULL COMMENT '库位代码, Location code',
  `status` ENUM('AVAILABLE','OCCUPIED','LOCKED') NOT NULL DEFAULT 'AVAILABLE',
  `max_weight` DECIMAL(18,2) COMMENT '最大承重(kg), Maximum load',
  `temperature_zone` VARCHAR(10) COMMENT '温控区域, Temperature zone',
  PRIMARY KEY (`warehouse`, `zone`, `location`),
  FOREIGN KEY (`warehouse`) REFERENCES `warehouse_master` (`code`)
) COMMENT='库位结构配置表 Storage location structure';




CREATE TABLE `lucy_bridge_erp_u8`.`inventory_opening_detail` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `accounting_period` CHAR(7) NOT NULL COMMENT '期初库存年月(YYYY-MM), Accounting period',
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目代码, Financial account code',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `quantity` DECIMAL(20,6) NOT NULL COMMENT '期初数量, Opening quantity',
  `amount` DECIMAL(18,2) NOT NULL COMMENT '期初金额, Opening amount',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Inventory source',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset ownership entity',
  
  -- 仓储维度
  `batch_number` VARCHAR(20) NOT NULL COMMENT '库存批次, Batch/Lot number',
  `encrypted_batch` VARCHAR(20) NOT NULL COMMENT '加密批次, Encrypted batch code',
  `warehouse` VARCHAR(20) NOT NULL COMMENT '仓库代码, Warehouse code',
  `storage_location` VARCHAR(20) NOT NULL COMMENT '库位代码, Storage bin location',
  `storage_zone` VARCHAR(20) NOT NULL COMMENT '库区代码, Storage zone',
  
  -- 核算审计字段
  `valuation_time` DATETIME NOT NULL COMMENT '核算时间, Valuation timestamp',
  `valuator` VARCHAR(10) NOT NULL COMMENT '核算人, Valuation operator',
  `audit_time` DATETIME COMMENT '审核时间, Audit timestamp',
  `auditor` VARCHAR(10) COMMENT '审核人, Audit operator',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by user',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Last updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Soft delete flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stock_unique` (
    `accounting_period`,
    `financial_account`,
    `sku`,
    `batch_number`,
    `warehouse`,
    `storage_location`
  ),
  KEY `idx_warehouse` (`warehouse`),
  KEY `idx_batch` (`batch_number`),
  KEY `idx_valuation` (`valuation_time`)
  ,CONSTRAINT `fk_detail_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `fk_detail_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`),
  CONSTRAINT `fk_warehouse` FOREIGN KEY (`warehouse`) 
    REFERENCES `warehouse_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='库存期初明细表 Inventory opening detail records';

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_opening_detail`   
  DROP INDEX `uk_stock_unique`,
  ADD  UNIQUE INDEX `uk_stock_unique` (`accounting_period`, `financial_account`, `sku`, `batch_number`, `warehouse`, `storage_location`, `asset_owner`, `storage_zone`);
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_opening_detail`   
  CHANGE `storage_location` `storage_location` VARCHAR(25) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '库位代码, Storage bin location';


CREATE TABLE `supplier_master` (
  `code` VARCHAR(30)  COLLATE utf8mb4_unicode_ci  NOT NULL PRIMARY KEY COMMENT '供应商代码, Supplier code',
  `name` VARCHAR(100) NOT NULL COMMENT '供应商名称, Supplier name',
  `type` ENUM('DOMESTIC','INTERNATIONAL') NOT NULL COMMENT '供应商类型',
  `certification` VARCHAR(20) COMMENT '资质证书编号, Certification number',
  `contact` VARCHAR(50) NOT NULL COMMENT '联系人, Contact person'
) COMMENT='供应商主数据 Supplier master';

CREATE TABLE `project_master` (
  `code` VARCHAR(10) COLLATE utf8mb4_unicode_ci   NOT NULL PRIMARY KEY COMMENT '项目代码, Project code',
  `name` VARCHAR(100) NOT NULL COMMENT '项目名称, Project name',
  `manager` VARCHAR(20) NOT NULL COMMENT '项目经理, Project manager',
  `start_date` DATE NOT NULL COMMENT '开始日期, Start date',
  `end_date` DATE COMMENT '计划结束日期, Planned end date'
) COMMENT='项目管理表 Project master data';


CREATE TABLE `lucy_bridge_erp_u8`.`inventory_current_period_entry` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  
  -- 核心业务字段
  `accounting_period` CHAR(7) NOT NULL COMMENT '本期入库年月(YYYY-MM), Accounting period',
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目代码, Financial account code',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `quantity` DECIMAL(20,6) NOT NULL COMMENT '入库数量, Entry quantity',
  `amount` DECIMAL(20,2) NOT NULL COMMENT '入库金额, Entry amount',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Source type',
  `purchase_scope` ENUM('DOMESTIC','INTERNATIONAL') NOT NULL COMMENT '国内/国外采购, Purchase scope',
  
  -- 供应商信息
  `supplier_code` VARCHAR(30) NOT NULL COMMENT '供应商编号, Supplier code',
  `supplier_name` VARCHAR(100) NOT NULL COMMENT '供应商名称, Supplier name',
  
  -- 仓储维度
  `batch_number` VARCHAR(20) NOT NULL COMMENT '批次号, Batch number',
  `warehouse` VARCHAR(20) NOT NULL COMMENT '仓库代码, Warehouse code',
  `storage_location` VARCHAR(20) NOT NULL COMMENT '库位代码, Storage location',
  `storage_zone` VARCHAR(20) NOT NULL COMMENT '库区代码, Storage zone',
  
  -- 业务追溯
  `source_table` VARCHAR(50) NOT NULL COMMENT '来源数据表名, Source table name',
  `source_record_id` BIGINT UNSIGNED NOT NULL COMMENT '来源记录ID, Source record ID',
  
  -- 流程控制
  `entry_time` DATETIME NOT NULL COMMENT '入库时间, Entry timestamp',
  `operator` VARCHAR(10) NOT NULL COMMENT '入库人, Operator',
  `auditor` VARCHAR(10) COMMENT '审核人, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit timestamp',
  `encrypted_batch` VARCHAR(20) NOT NULL COMMENT '加密批次, Encrypted batch',
  `document_type` ENUM('PURCHASE_ORDER','PRODUCTION_ENTRY','TRANSFER','ADJUSTMENT') 
    COMMENT '单据类型, Document type',
  
  -- 资产管理
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  
  -- 审批流程
  `processor` VARCHAR(10) NOT NULL COMMENT '经办人, Processor',
  `process_time` DATETIME NOT NULL COMMENT '经办时间, Process time',
  `department` VARCHAR(10) NOT NULL COMMENT '经办部门, Department',
  `department_auditor` VARCHAR(10) COMMENT '部门审核人, Department auditor',
  `department_audit_time` DATETIME COMMENT '部门审核时间, Department audit time',
  
  -- 项目关联
  `site_code` VARCHAR(10) COMMENT 'Site点代码, Site code',
  `project_code` VARCHAR(10) COMMENT '项目编号, Project code',
  `project_name` VARCHAR(100) COMMENT '项目名称, Project name',
  `rd_system_id` VARCHAR(20) COMMENT 'RD系统ID, RD system ID',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP 
    COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_entry_unique` (
    `accounting_period`,
    `financial_account`,
    `sku`,
    `batch_number`,
    `warehouse`,
    `storage_location`
  ),
  KEY `idx_supplier` (`supplier_code`),
  KEY `idx_entry_time` (`entry_time`),
  KEY `idx_project` (`project_code`)
  ,
  CONSTRAINT `fk_entry_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `fk_entry_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`),
  CONSTRAINT `fk_entry_warehouse` FOREIGN KEY (`warehouse`) 
    REFERENCES `warehouse_master` (`code`),
  CONSTRAINT `fk_entry_supplier` FOREIGN KEY (`supplier_code`) 
    REFERENCES `supplier_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='本期入库明细 Current period inventory entries';


CREATE TABLE `customer_master` (
  `code` VARCHAR(30) COLLATE utf8mb4_unicode_ci  NOT NULL PRIMARY KEY COMMENT '客户编号, Customer code',
  `name` VARCHAR(100) NOT NULL COMMENT '客户名称, Customer name',
  `credit_rating` ENUM('A','B','C','D') COMMENT '信用等级, Credit rating',
  `payment_terms` VARCHAR(50) COMMENT '付款条件, Payment terms'
) COMMENT='客户主数据 Customer master';
CREATE TABLE `department_group` (
  `code` VARCHAR(30) COLLATE utf8mb4_unicode_ci  NOT NULL PRIMARY KEY COMMENT '部门组编号, Group code',
  `name` VARCHAR(100) NOT NULL COMMENT '部门组名称, Group name',
  `manager` VARCHAR(20) NOT NULL COMMENT '负责人, Manager',
  `cost_center` VARCHAR(20) COMMENT '成本中心, Cost center'
) COMMENT='部门组织结构 Department groups';


CREATE TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  
  -- 核心业务字段
  `accounting_period` CHAR(7) NOT NULL COMMENT '本期出库年月(YYYY-MM), Accounting period',
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目代码, Financial account code',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `quantity` DECIMAL(20,6) NOT NULL COMMENT '出库数量, Issue quantity',
  `amount` DECIMAL(20,2) NOT NULL COMMENT '出库金额, Issue amount',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Source type',
  `purchase_scope` ENUM('DOMESTIC','INTERNATIONAL') NOT NULL COMMENT '国内/国外采购, Purchase scope',
  
  -- 客户信息
  `customer_code` VARCHAR(30) NOT NULL COMMENT '客户编号, Customer code',
  `customer_name` VARCHAR(100) NOT NULL COMMENT '客户名称, Customer name',
  
  -- 部门信息
  `department_group_code` VARCHAR(30) COMMENT '部门组编号, Department group code',
  `department_group_name` VARCHAR(100) COMMENT '部门组名称, Department group name',

  -- 仓储维度
  `batch_number` VARCHAR(20) NOT NULL COMMENT '批次号, Batch number',
  `warehouse` VARCHAR(20) NOT NULL COMMENT '仓库代码, Warehouse code',
  `storage_location` VARCHAR(20) NOT NULL COMMENT '库位代码, Storage location',
  `storage_zone` VARCHAR(20) NOT NULL COMMENT '库区代码, Storage zone',
  
  -- 业务追溯
  `source_table` VARCHAR(50) NOT NULL COMMENT '来源数据表名, Source table name',
  `source_record_id` BIGINT UNSIGNED NOT NULL COMMENT '来源记录ID, Source record ID',
  
  -- 流程控制
  `issue_time` DATETIME NOT NULL COMMENT '出库时间, Issue timestamp',
  `operator` VARCHAR(10) NOT NULL COMMENT '出库人, Operator',
  `auditor` VARCHAR(10) COMMENT '审核人, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit timestamp',
  `encrypted_batch` VARCHAR(20) NOT NULL COMMENT '加密批次, Encrypted batch',
  `document_type` ENUM('SALES_ORDER','PRODUCTION_ISSUE','TRANSFER','ADJUSTMENT') 
    COMMENT '单据类型, Document type',
  
  -- 资产管理
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  
  -- 审批流程
  `processor` VARCHAR(10) NOT NULL COMMENT '经办人, Processor',
  `process_time` DATETIME NOT NULL COMMENT '经办时间, Process time',
  `department` VARCHAR(10) NOT NULL COMMENT '经办部门, Department',
  `department_auditor` VARCHAR(10) COMMENT '部门审核人, Department auditor',
  `department_audit_time` DATETIME COMMENT '部门审核时间, Department audit time',
  `expense_transfer_flag` ENUM('Y','N') COMMENT '结转成当月费用标志, Expense transfer flag',
  
  -- 项目关联
  `site_code` VARCHAR(10) COMMENT 'Site点代码, Site code',
  `project_code` VARCHAR(10) COMMENT '项目编号, Project code',
  `project_name` VARCHAR(100) COMMENT '项目名称, Project name',
  `rd_system_id` VARCHAR(20) COMMENT 'RD系统ID, RD system ID',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP 
    COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_issue_unique` (
    `accounting_period`,
    `financial_account`,
    `sku`,
    `batch_number`,
    `warehouse`,
    `storage_location`
  ),
  KEY `idx_customer` (`customer_code`),
  KEY `idx_issue_time` (`issue_time`),
  KEY `idx_project` (`project_code`),
  KEY `idx_department_group` (`department_group_code`)
 ,CONSTRAINT `fk_issue_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `fk_issue_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`),
  CONSTRAINT `fk_issue_warehouse` FOREIGN KEY (`warehouse`) 
    REFERENCES `warehouse_master` (`code`),
  CONSTRAINT `fk_issue_customer` FOREIGN KEY (`customer_code`) 
    REFERENCES `customer_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='本期出库明细 Current period inventory issues';



CREATE TABLE `lucy_bridge_erp_u8`.`inventory_ending_summary` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `accounting_period` CHAR(7) NOT NULL COMMENT '期末库存年月(YYYY-MM), Accounting period',
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目代码, Financial account code',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `quantity` DECIMAL(20,6) NOT NULL COMMENT '期末数量, Ending quantity',
  `amount` DECIMAL(20,2) NOT NULL COMMENT '期末金额, Ending amount',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Source type',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset ownership entity',
  
  -- 核算审计字段
  `valuation_time` DATETIME NOT NULL COMMENT '核算时间, Valuation timestamp',
  `valuator` VARCHAR(10) NOT NULL COMMENT '核算人, Valuation operator',
  `audit_time` DATETIME COMMENT '审核时间, Audit timestamp',
  `auditor` VARCHAR(10) COMMENT '审核人, Audit operator',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by user',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Last updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Soft delete flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_account_sku` (`accounting_period`,`financial_account`,`sku`),
  KEY `idx_valuation_time` (`valuation_time`),
  KEY `idx_asset_owner` (`asset_owner`)
  ,
  CONSTRAINT `fk_es_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `fk_es_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='库存期末汇总表 Inventory ending balance summary';

CREATE TABLE `lucy_bridge_erp_u8`.`inventory_ending_detail` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `accounting_period` CHAR(7) NOT NULL COMMENT '期末库存年月(YYYY-MM), Accounting period',
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目代码, Financial account code',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `quantity` DECIMAL(20,6) NOT NULL COMMENT '期末数量, Ending quantity',
  `amount` DECIMAL(20,2) NOT NULL COMMENT '期末金额, Ending amount',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Source type',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset ownership entity',
  
  -- 仓储维度
  `batch_number` VARCHAR(20) NOT NULL COMMENT '库存批次, Batch/Lot number',
  `encrypted_batch` VARCHAR(20) NOT NULL COMMENT '加密批次, Encrypted batch code',
  `warehouse` VARCHAR(20) NOT NULL COMMENT '仓库代码, Warehouse code',
  `storage_location` VARCHAR(20) NOT NULL COMMENT '库位代码, Storage bin location',
  `storage_zone` VARCHAR(20) NOT NULL COMMENT '库区代码, Storage zone',
  
  -- 核算审计字段
  `valuation_time` DATETIME NOT NULL COMMENT '核算时间, Valuation timestamp',
  `valuator` VARCHAR(10) NOT NULL COMMENT '核算人, Valuation operator',
  `audit_time` DATETIME COMMENT '审核时间, Audit timestamp',
  `auditor` VARCHAR(10) COMMENT '审核人, Audit operator',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by user',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Last updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Soft delete flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_detail_unique` (
    `accounting_period`,
    `financial_account`,
    `sku`,
    `batch_number`,
    `warehouse`,
    `storage_location`
  ),
  KEY `idx_warehouse` (`warehouse`),
  KEY `idx_batch` (`batch_number`),
  KEY `idx_valuation` (`valuation_time`)
  ,
  CONSTRAINT `fk_ed_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `fk_ed_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`),
  CONSTRAINT `fk_ed_warehouse` FOREIGN KEY (`warehouse`) 
    REFERENCES `warehouse_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='库存期末明细表 Inventory ending detail records';

CREATE TABLE `lucy_bridge_erp_u8`.`expense_monthly_summary` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `expense_type` VARCHAR(20) NOT NULL COMMENT '费用类型, Expense type',
  `accounting_period` CHAR(7) NOT NULL COMMENT '费用年月(YYYY-MM), Accounting period',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  `site_code` VARCHAR(10) NOT NULL COMMENT 'Site点代码, Site code',
  `amount` DECIMAL(20,2) NOT NULL COMMENT '费用金额, Expense amount',
  
  -- 流程控制
  `provider` VARCHAR(10) NOT NULL COMMENT '提供人员, Provider',
  `provide_time` DATETIME NOT NULL COMMENT '提供时间, Provide time',
  `auditor` VARCHAR(10) COMMENT '审核人员, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit time',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_expense_summary` (`expense_type`, `accounting_period`, `asset_owner`, `site_code`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_asset_owner` (`asset_owner`)
  ,
  CONSTRAINT `fk_ems_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='月度费用归集总表 Monthly expense summary';


CREATE TABLE `lucy_bridge_erp_u8`.`expense_monthly_detail` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `expense_subtype` VARCHAR(20) NOT NULL COMMENT '费用明细类型, Expense subtype',
  `accounting_period` CHAR(7) NOT NULL COMMENT '费用年月(YYYY-MM), Accounting period',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  `site_code` VARCHAR(10) NOT NULL COMMENT 'Site点代码, Site code',
  `amount` DECIMAL(20,2) NOT NULL COMMENT '费用金额, Expense amount',
  
  -- 流程控制
  `provider` VARCHAR(10) NOT NULL COMMENT '提供人员, Provider',
  `provide_time` DATETIME NOT NULL COMMENT '提供时间, Provide time',
  `auditor` VARCHAR(10) COMMENT '审核人员, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit time',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  KEY `idx_expense_type` (`expense_subtype`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_asset_owner` (`asset_owner`)
  ,
  CONSTRAINT `fk_emd_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='月度费用归集明细表 Monthly expense detail';


CREATE TABLE `lucy_bridge_erp_u8`.`expense_allocation_basis` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `expense_subtype` VARCHAR(20) NOT NULL COMMENT '费用明细类型, Expense subtype',
  `accounting_period` CHAR(7) NOT NULL COMMENT '费用年月(YYYY-MM), Accounting period',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  `project_code` VARCHAR(30) NOT NULL COMMENT '所属项目号, Project code',
  `site_code` VARCHAR(10) NOT NULL COMMENT 'Site点代码, Site code',
  
  -- 分摊依据
  `labor_hours` DECIMAL(18,2) COMMENT '人工工时, Labor hours',
  `bottle_count` DECIMAL(18,2) COMMENT '当月总瓶数, Bottle count',
  
  -- 流程控制
  `provider` VARCHAR(10) NOT NULL COMMENT '提供人员, Provider',
  `provide_time` DATETIME NOT NULL COMMENT '提供时间, Provide time',
  `auditor` VARCHAR(10) COMMENT '审核人员, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit time',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_allocation_basis` (
    `expense_subtype`, 
    `accounting_period`, 
    `asset_owner`, 
    `project_code`
  ),
  KEY `idx_project` (`project_code`)
  ,
  CONSTRAINT `fk_eab_project` FOREIGN KEY (`project_code`) 
    REFERENCES `project_master` (`code`),
  CONSTRAINT `fk_eab_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='月度费用分摊依据表 Expense allocation basis';

CREATE TABLE `lucy_bridge_erp_u8`.`expense_allocation_result` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `expense_subtype` VARCHAR(20) NOT NULL COMMENT '费用明细类型, Expense subtype',
  `accounting_period` CHAR(7) NOT NULL COMMENT '费用年月(YYYY-MM), Accounting period',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  `project_code` VARCHAR(30) NOT NULL COMMENT '所属项目号, Project code',
  `site_code` VARCHAR(10) NOT NULL COMMENT 'Site点代码, Site code',
  
  -- 分摊依据
  `labor_hours` DECIMAL(18,2) COMMENT '人工工时, Labor hours',
  `bottle_count` DECIMAL(18,2) COMMENT '当月总瓶数, Bottle count',
  `finished_bottle_count` DECIMAL(18,2) COMMENT '成品入库瓶数, Finished bottle count',
  
  -- 分摊结果
  `allocated_amount` DECIMAL(20,6) NOT NULL COMMENT '分摊金额, Allocated amount',
  
  -- 流程控制
  `accountant` VARCHAR(10) NOT NULL COMMENT '核算人员, Accountant',
  `accounting_time` DATETIME NOT NULL COMMENT '核算时间, Accounting time',
  `auditor` VARCHAR(10) COMMENT '审核人员, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit time',
  
  -- 业务追溯
  `source_table` VARCHAR(50) COMMENT '来源数据表名, Source table',
  `source_record_id` BIGINT UNSIGNED COMMENT '来源记录ID, Source record ID',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  KEY `idx_project` (`project_code`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_source` (`source_table`, `source_record_id`)
  ,
  CONSTRAINT `fk_ear_project` FOREIGN KEY (`project_code`) 
    REFERENCES `project_master` (`code`),
  CONSTRAINT `fk_ear_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='月度费用分摊结果单据表 Expense allocation result';

CREATE TABLE `lucy_bridge_erp_u8`.`finished_goods_composition` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `product_type` VARCHAR(20) NOT NULL COMMENT '产成品类型, Product type',
  `accounting_period` CHAR(7) NOT NULL COMMENT '产成品年月(YYYY-MM), Accounting period',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  `project_code` VARCHAR(30) NOT NULL COMMENT '所属项目号, Project code',
  `site_code` VARCHAR(10) NOT NULL COMMENT 'Site点代码, Site code',
  
  -- 财务信息
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目, Financial account',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Source type',
  `finished_amount` DECIMAL(20,2) NOT NULL COMMENT '成品入库金额, Finished goods amount',
  
  -- 流程控制
  `accountant` VARCHAR(10) NOT NULL COMMENT '核算人员, Accountant',
  `accounting_time` DATETIME NOT NULL COMMENT '核算时间, Accounting time',
  `auditor` VARCHAR(10) COMMENT '审核人员, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit time',
  
  -- 业务追溯
  `source_table` VARCHAR(50) COMMENT '来源数据表名, Source table',
  `source_record_id` BIGINT UNSIGNED COMMENT '来源记录ID, Source record ID',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_project` (`project_code`),
  KEY `idx_accounting_period` (`accounting_period`)
  ,
  CONSTRAINT `fgc_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `fgc_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`),
  CONSTRAINT `fgc_project` FOREIGN KEY (`project_code`) 
    REFERENCES `project_master` (`code`),
  CONSTRAINT `fgc_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='产成品构成明细表 Finished goods composition';

CREATE TABLE `lucy_bridge_erp_u8`.`wip_composition` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `wip_type` VARCHAR(20) NOT NULL COMMENT '在产品类型, WIP type',
  `accounting_period` CHAR(7) NOT NULL COMMENT '在产品年月(YYYY-MM), Accounting period',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属主体, Asset owner',
  `project_code` VARCHAR(30) NOT NULL COMMENT '所属项目号, Project code',
  `site_code` VARCHAR(10) NOT NULL COMMENT 'Site点代码, Site code',
  
  -- 财务信息
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目, Financial account',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `source_type` ENUM('PURCHASED','SELF_PRODUCED') NOT NULL COMMENT '外采/自产, Source type',
  `finished_amount` DECIMAL(20,2) NOT NULL COMMENT '成品入库金额, Finished goods amount',
  
  -- 流程控制
  `accountant` VARCHAR(10) NOT NULL COMMENT '核算人员, Accountant',
  `accounting_time` DATETIME NOT NULL COMMENT '核算时间, Accounting time',
  `auditor` VARCHAR(10) COMMENT '审核人员, Auditor',
  `audit_time` DATETIME COMMENT '审核时间, Audit time',
  
  -- 业务追溯
  `source_table` VARCHAR(50) COMMENT '来源数据表名, Source table',
  `source_record_id` BIGINT UNSIGNED COMMENT '来源记录ID, Source record ID',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_project` (`project_code`),
  KEY `idx_accounting_period` (`accounting_period`)
  ,
  CONSTRAINT `wipc_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`),
  CONSTRAINT `wipc_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`),
  CONSTRAINT `wipc_project` FOREIGN KEY (`project_code`) 
    REFERENCES `project_master` (`code`),
  CONSTRAINT `wipc_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='在产品构成明细表 Work in process composition';

CREATE TABLE `lucy_bridge_erp_u8`.`customer_organization_mapping` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属组织, Asset owner',
  `erp_org_code` VARCHAR(30) NOT NULL COMMENT 'ERP组织编号, ERP organization code',
  `erp_org_name` VARCHAR(500) NOT NULL COMMENT 'ERP组织名称, ERP organization name',
  `u8_org_code` VARCHAR(20) COMMENT 'U8组织编号, U8 organization code',
  `u8_org_name` VARCHAR(500) COMMENT 'U8组织名称, U8 organization name',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_erp_org` (`erp_org_code`),
  KEY `idx_u8_org` (`u8_org_code`)
  ,
  CONSTRAINT `fk_com_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='客户组织映射关系表 Customer organization mapping';

CREATE TABLE `lucy_bridge_erp_u8`.`u8_customer_master` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `asset_owner` VARCHAR(20) NOT NULL COMMENT '资产所属组织, Asset owner',
  `u8_org_code` VARCHAR(20) NOT NULL COMMENT 'U8组织编号, U8 organization code',
  `u8_org_name` VARCHAR(500) NOT NULL COMMENT 'U8组织名称, U8 organization name',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_u8_org` (`u8_org_code`)
  ,
  CONSTRAINT `fk_u8cm_asset_owner` FOREIGN KEY (`asset_owner`) 
    REFERENCES `asset_owner_master` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='U8客户档案表 U8 customer master';

CREATE TABLE `lucy_bridge_erp_u8`.`sku_history` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID, Primary key',
  `sku` VARCHAR(50) NOT NULL COMMENT 'SKU编码, Stock Keeping Unit',
  `accounting_period` CHAR(7) NOT NULL COMMENT '记账年月(YYYY-MM), Accounting period',
  `financial_account` VARCHAR(20) NOT NULL COMMENT '财务科目, Financial account',
  
  -- 存在标志
  `beginning_exists` ENUM('Y','N') NOT NULL COMMENT '期初存在标志, Beginning existence flag',
  `inbound_exists` ENUM('Y','N') NOT NULL COMMENT '本期入库存在标志, Inbound existence flag',
  `outbound_exists` ENUM('Y','N') NOT NULL COMMENT '本期出库存在标志, Outbound existence flag',
  
  -- 数量信息
  `beginning_quantity` DECIMAL(20,6) NOT NULL COMMENT '期初数量, Beginning quantity',
  `inbound_quantity` DECIMAL(20,6) NOT NULL COMMENT '本期入库数量, Inbound quantity',
  `outbound_quantity` DECIMAL(20,6) NOT NULL COMMENT '本期出库数量, Outbound quantity',
  `ending_quantity` DECIMAL(20,6) GENERATED ALWAYS AS 
    (beginning_quantity + inbound_quantity - outbound_quantity) STORED COMMENT '期末数量, Ending quantity',
  
  -- 系统审计字段
  `created_by` VARCHAR(10) NOT NULL COMMENT '创建人, Created by',
  `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间, Creation time',
  `updated_by` VARCHAR(10) NOT NULL COMMENT '更新人, Updated by',
  `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间, Update time',
  `version` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '行版本, Row version',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0:正常 1:删除), Deletion flag',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_period` (`sku`, `accounting_period`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_fin_account` (`financial_account`)
  ,
  CONSTRAINT `fk_sh_sku` FOREIGN KEY (`sku`) 
    REFERENCES `sku_master` (`code`),
  CONSTRAINT `fk_sh_fin_account` FOREIGN KEY (`financial_account`) 
    REFERENCES `accounting_subject` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='SKU履历表 SKU history';


--  2025-05-28 数据初始化过程中的表结构优化
 -- 初始化   
   ALTER TABLE inventory_opening_detail
ADD COLUMN `structure_name` VARCHAR(50) NOT NULL COMMENT '表结构名称',
ADD COLUMN `parent_structure_id` BIGINT UNSIGNED NOT NULL COMMENT '所在表结构ID';
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_opening_balance`   
  DROP INDEX `uk_period_account_sku`,
  ADD  UNIQUE INDEX `uk_period_account_sku` (`accounting_period`, `financial_account`, `sku`,asset_owner);



ALTER TABLE `lucy_bridge_erp_u8`.`inventory_opening_detail`   
  CHANGE `batch_number` `batch_number` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '库存批次, Batch/Lot number',
  CHANGE `encrypted_batch` `encrypted_batch` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '加密批次, Encrypted batch code';
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_ending_detail`   
  CHANGE `batch_number` `batch_number` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '库存批次, Batch/Lot number',
  CHANGE `encrypted_batch` `encrypted_batch` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '加密批次, Encrypted batch code';
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue`   
  CHANGE `batch_number` `batch_number` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '库存批次, Batch/Lot number',
  CHANGE `encrypted_batch` `encrypted_batch` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '加密批次, Encrypted batch code';
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_entry`   
  CHANGE `batch_number` `batch_number` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '库存批次, Batch/Lot number',
  CHANGE `encrypted_batch` `encrypted_batch` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '加密批次, Encrypted batch code';



ALTER TABLE `lucy_bridge_erp_u8`.`sku_master`   
  ADD COLUMN `bpm_ccd` VARCHAR(10) NULL  COMMENT '产品分类编号' AFTER `cost_method`,
  ADD COLUMN `bpm_cnm` VARCHAR(20) NULL  COMMENT '产品分类' AFTER `bpm_ccd`,
  ADD COLUMN `bpm_sort` VARCHAR(10) NULL  COMMENT '产品种类编号' AFTER `bpm_cnm`,
  ADD COLUMN `bpm_sortnm` VARCHAR(20) NULL  COMMENT '产品种类' AFTER `bpm_sort`,
  ADD COLUMN `bpm_pcd` VARCHAR(10) NULL  COMMENT '品牌编号' AFTER `bpm_sortnm`,
  ADD COLUMN `bpm_pnm` VARCHAR(20) NULL  COMMENT '品牌名称' AFTER `bpm_pcd`,
  ADD COLUMN `bpm_cd` VARCHAR(40) NULL  COMMENT '产品编号' AFTER `bpm_pnm`,
  ADD COLUMN `bpm_orgcd` VARCHAR(30) NULL  COMMENT '货号' AFTER `bpm_cd`,
  ADD COLUMN `bpm_nm` VARCHAR(1000) NULL  COMMENT '品名' AFTER `bpm_orgcd`,
  ADD COLUMN `bpp_pack` VARCHAR(40) NULL  COMMENT '包装' AFTER `bpm_nm`,
  ADD COLUMN `bpp_std_qty` DECIMAL(18,9) NULL  COMMENT '标准重量kg/L' AFTER `bpp_pack`,
  ADD COLUMN `bpac_cas` VARCHAR(50) NULL  COMMENT 'CAS' AFTER `bpp_std_qty`,
  ADD COLUMN `bpac_dgcls` VARCHAR(50) NULL  COMMENT '国危2022版' AFTER `bpac_cas`,
  ADD COLUMN `bpt_nm` VARCHAR(50) NULL  COMMENT '产线分类' AFTER `bpac_dgcls`,
  ADD COLUMN `fpc_code` VARCHAR(50) NULL  COMMENT '物料名称' AFTER `bpt_nm`
  ;




-- 
ALTER TABLE `lucy_bridge_erp_u8`.`zw_20241231_fin_acc_inventory_oe`   
  ADD COLUMN `type` CHAR(5) NULL AFTER `fai_eid`;

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue`
  CHANGE `source_type` `source_type` ENUM('PURCHASED','SELF_PRODUCED','-') CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '外采/自产, Source type';
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_entry`
  CHANGE `source_type` `source_type` ENUM('PURCHASED','SELF_PRODUCED','-') CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL  COMMENT '外采/自产, Source type';



-- 增加索引

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_opening_detail`
  ADD INDEX (`asset_owner`),
  ADD INDEX (`encrypted_batch`);

-- 出库单据类型
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue`
  CHANGE `document_type` `document_type` ENUM('销售出库','采购退货','换货号出','换货号生产出','分装领料出库','分装领料退库','差额盘亏','耗材领用','生产领用','研发领用', '差错单调整', '报废', '盘亏', '拆包出', '小样生产领料') CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NULL  COMMENT '单据类型, Document type';

-- 暂时去掉外键限制
ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue`
  DROP FOREIGN KEY `fk_issue_customer`,
  DROP FOREIGN KEY `fk_issue_sku`;

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_entry`
  DROP FOREIGN KEY `fk_entry_sku`,
  DROP FOREIGN KEY `fk_entry_supplier`;

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_entry`
DROP INDEX `uk_entry_unique`;

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue`
DROP INDEX `uk_issue_unique`;

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue`
DROP FOREIGN KEY `fk_issue_customer`;

ALTER TABLE `lucy_bridge_erp_u8`.`sku_master`
    CHANGE `name` `name` VARCHAR(1000) CHARSET utf8 COLLATE utf8_general_ci NOT NULL  COMMENT '商品名称, Product name';

ALTER TABLE `lucy_bridge_erp_u8`.`supplier_master`
  ADD COLUMN `tax_no` VARCHAR(20) NOT NULL  COMMENT '税号, tax no' AFTER `certification`;

ALTER TABLE `lucy_bridge_erp_u8`.`customer_master`
  ADD COLUMN `company_code` VARCHAR(30) NOT NULL  COMMENT '公司编号, Group code' AFTER `payment_terms`,
  ADD COLUMN `company_name` VARCHAR(100) NOT NULL  COMMENT '公司名称, Group name' AFTER `company_code`,
  ADD COLUMN `tax_no` VARCHAR(20) NOT NULL  COMMENT '税号, tax no' AFTER `company_name`,
  ADD COLUMN `category` VARCHAR(20) NOT NULL  COMMENT '客户类别（直销-终端客户、贸易商、经销商）' AFTER `company_code`,
  ADD COLUMN `classification_subclass` VARCHAR(20) NOT NULL  COMMENT '客户分类-小类' AFTER `company_code`,
  ADD COLUMN `classification_major` VARCHAR(20) NOT NULL  COMMENT '客户分类-大类' AFTER `company_code`,
  ADD COLUMN `profile` VARCHAR(100) NOT NULL  COMMENT '匹配客户档案信息【应用领域、省份等信息】' AFTER `company_code`,
  ADD COLUMN `merger_situation` VARCHAR(50) NOT NULL  COMMENT '客户同控合并情况' AFTER `company_code`;

ALTER TABLE `lucy_bridge_erp_u8`.`inventory_current_period_issue`
    CHANGE `project_code` `project_code` VARCHAR(20) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NULL  COMMENT '项目编号, Project code';
ALTER TABLE `lucy_bridge_erp_u8`.`customer_master`
    CHANGE `tax_no` `tax_no` VARCHAR(40) CHARSET utf8 COLLATE utf8_general_ci NOT NULL  COMMENT '税号, tax no';
