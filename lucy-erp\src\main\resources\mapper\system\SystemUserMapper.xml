<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.erp.mapper.SystemUserMapper">
    
    <resultMap type="SystemUser" id="SystemUserResult">
        <result property="id"    column="id"    />
        <result property="suTyp"    column="su_typ"    />
        <result property="suEno"    column="su_eno"    />
        <result property="suEnm"    column="su_enm"    />
        <result property="suLogno"    column="su_logno"    />
        <result property="suRnum"    column="su_rnum"    />
        <result property="suPwd"    column="su_pwd"    />
        <result property="suLogip"    column="su_logip"    />
        <result property="suLogdt"    column="su_logdt"    />
        <result property="suLogflag"    column="su_logflag"    />
        <result property="suQywxEno"    column="su_qywx_eno"    />
        <result property="rCreKid"    column="r_cre_kid"    />
        <result property="rCreDt"    column="r_cre_dt"    />
        <result property="rUpdKid"    column="r_upd_kid"    />
        <result property="rUpdDt"    column="r_upd_dt"    />
        <result property="rV"    column="r_v"    />
        <result property="rDel"    column="r_del"    />
        <result property="suSort"    column="su_sort"    />
        <result property="suQywxCheckdt"    column="su_qywx_checkdt"    />
    </resultMap>

    <sql id="selectSystemUserVo">
        select id, su_typ, su_eno, su_enm, su_logno, su_rnum, su_pwd, su_logip, su_logdt, su_logflag, su_qywx_eno, r_cre_kid, r_cre_dt, r_upd_kid, r_upd_dt, r_v, r_del, su_sort, su_qywx_checkdt from system_user
    </sql>

    <select id="selectSystemUserList" parameterType="SystemUser" resultMap="SystemUserResult">
        <include refid="selectSystemUserVo"/>
        <where>  
            <if test="suTyp != null  and suTyp != ''"> and su_typ = #{suTyp}</if>
            <if test="suEno != null  and suEno != ''"> and su_eno = #{suEno}</if>
            <if test="suEnm != null  and suEnm != ''"> and su_enm = #{suEnm}</if>
            <if test="suLogno != null  and suLogno != ''"> and su_logno = #{suLogno}</if>
            <if test="suRnum != null "> and su_rnum = #{suRnum}</if>
            <if test="suPwd != null  and suPwd != ''"> and su_pwd = #{suPwd}</if>
            <if test="suLogip != null  and suLogip != ''"> and su_logip = #{suLogip}</if>
            <if test="suLogdt != null "> and su_logdt = #{suLogdt}</if>
            <if test="suLogflag != null  and suLogflag != ''"> and su_logflag = #{suLogflag}</if>
            <if test="suQywxEno != null  and suQywxEno != ''"> and su_qywx_eno = #{suQywxEno}</if>
            <if test="rCreKid != null  and rCreKid != ''"> and r_cre_kid = #{rCreKid}</if>
            <if test="rCreDt != null "> and r_cre_dt = #{rCreDt}</if>
            <if test="rUpdKid != null  and rUpdKid != ''"> and r_upd_kid = #{rUpdKid}</if>
            <if test="rUpdDt != null "> and r_upd_dt = #{rUpdDt}</if>
            <if test="rV != null "> and r_v = #{rV}</if>
            <if test="rDel != null "> and r_del = #{rDel}</if>
            <if test="suSort != null "> and su_sort = #{suSort}</if>
            <if test="suQywxCheckdt != null "> and su_qywx_checkdt = #{suQywxCheckdt}</if>
        </where>
    </select>
    
    <select id="selectSystemUserById" parameterType="Long" resultMap="SystemUserResult">
        <include refid="selectSystemUserVo"/>
        where id = #{id}
    </select>
    <select id="selectSystemUserByLogno" resultMap="SystemUserResult" parameterType="String">
        <include refid="selectSystemUserVo"/>
        where su_logno = #{logno}
    </select>
    <select id="selectSystemUserByEno" resultMap="SystemUserResult" parameterType="String">
        <include refid="selectSystemUserVo"/>
        where su_eno = #{eno}
    </select>

    <insert id="insertSystemUser" parameterType="SystemUser" useGeneratedKeys="true" keyProperty="id">
        insert into system_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="suTyp != null and suTyp != ''">su_typ,</if>
            <if test="suEno != null">su_eno,</if>
            <if test="suEnm != null">su_enm,</if>
            <if test="suLogno != null and suLogno != ''">su_logno,</if>
            <if test="suRnum != null">su_rnum,</if>
            <if test="suPwd != null and suPwd != ''">su_pwd,</if>
            <if test="suLogip != null">su_logip,</if>
            <if test="suLogdt != null">su_logdt,</if>
            <if test="suLogflag != null">su_logflag,</if>
            <if test="suQywxEno != null">su_qywx_eno,</if>
            <if test="rCreKid != null">r_cre_kid,</if>
            <if test="rCreDt != null">r_cre_dt,</if>
            <if test="rUpdKid != null">r_upd_kid,</if>
            <if test="rUpdDt != null">r_upd_dt,</if>
            <if test="rV != null">r_v,</if>
            <if test="rDel != null">r_del,</if>
            <if test="suSort != null">su_sort,</if>
            <if test="suQywxCheckdt != null">su_qywx_checkdt,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="suTyp != null and suTyp != ''">#{suTyp},</if>
            <if test="suEno != null">#{suEno},</if>
            <if test="suEnm != null">#{suEnm},</if>
            <if test="suLogno != null and suLogno != ''">#{suLogno},</if>
            <if test="suRnum != null">#{suRnum},</if>
            <if test="suPwd != null and suPwd != ''">#{suPwd},</if>
            <if test="suLogip != null">#{suLogip},</if>
            <if test="suLogdt != null">#{suLogdt},</if>
            <if test="suLogflag != null">#{suLogflag},</if>
            <if test="suQywxEno != null">#{suQywxEno},</if>
            <if test="rCreKid != null">#{rCreKid},</if>
            <if test="rCreDt != null">#{rCreDt},</if>
            <if test="rUpdKid != null">#{rUpdKid},</if>
            <if test="rUpdDt != null">#{rUpdDt},</if>
            <if test="rV != null">#{rV},</if>
            <if test="rDel != null">#{rDel},</if>
            <if test="suSort != null">#{suSort},</if>
            <if test="suQywxCheckdt != null">#{suQywxCheckdt},</if>
         </trim>
    </insert>

    <update id="updateSystemUser" parameterType="SystemUser">
        update system_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="suTyp != null and suTyp != ''">su_typ = #{suTyp},</if>
            <if test="suEno != null">su_eno = #{suEno},</if>
            <if test="suEnm != null">su_enm = #{suEnm},</if>
            <if test="suLogno != null and suLogno != ''">su_logno = #{suLogno},</if>
            <if test="suRnum != null">su_rnum = #{suRnum},</if>
            <if test="suPwd != null and suPwd != ''">su_pwd = #{suPwd},</if>
            <if test="suLogip != null">su_logip = #{suLogip},</if>
            <if test="suLogdt != null">su_logdt = #{suLogdt},</if>
            <if test="suLogflag != null">su_logflag = #{suLogflag},</if>
            <if test="suQywxEno != null">su_qywx_eno = #{suQywxEno},</if>
            <if test="rCreKid != null">r_cre_kid = #{rCreKid},</if>
            <if test="rCreDt != null">r_cre_dt = #{rCreDt},</if>
            <if test="rUpdKid != null">r_upd_kid = #{rUpdKid},</if>
            <if test="rUpdDt != null">r_upd_dt = #{rUpdDt},</if>
            <if test="rV != null">r_v = #{rV},</if>
            <if test="rDel != null">r_del = #{rDel},</if>
            <if test="suSort != null">su_sort = #{suSort},</if>
            <if test="suQywxCheckdt != null">su_qywx_checkdt = #{suQywxCheckdt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSystemUserById" parameterType="Long">
        delete from system_user where id = #{id}
    </delete>

    <delete id="deleteSystemUserByIds" parameterType="String">
        delete from system_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>