package com.ruoyi.repackage.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionCostConvolutionMapper;
import com.ruoyi.repackage.domain.ConversionCostConvolution;
import com.ruoyi.repackage.service.IConversionCostConvolutionService;

/**
 * 分装成本卷积Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionCostConvolutionServiceImpl implements IConversionCostConvolutionService 
{
    @Autowired
    private ConversionCostConvolutionMapper conversionCostConvolutionMapper;

    /**
     * 查询分装成本卷积
     * 
     * @param convolutionId 分装成本卷积主键
     * @return 分装成本卷积
     */
    @Override
    public ConversionCostConvolution selectConversionCostConvolutionByConvolutionId(Long convolutionId)
    {
        return conversionCostConvolutionMapper.selectConversionCostConvolutionByConvolutionId(convolutionId);
    }

    /**
     * 查询分装成本卷积列表
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 分装成本卷积
     */
    @Override
    public List<ConversionCostConvolution> selectConversionCostConvolutionList(ConversionCostConvolution conversionCostConvolution)
    {
        return conversionCostConvolutionMapper.selectConversionCostConvolutionList(conversionCostConvolution);
    }

    /**
     * 新增分装成本卷积
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 结果
     */
    @Override
    public int insertConversionCostConvolution(ConversionCostConvolution conversionCostConvolution)
    {
        return conversionCostConvolutionMapper.insertConversionCostConvolution(conversionCostConvolution);
    }

    /**
     * 修改分装成本卷积
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 结果
     */
    @Override
    public int updateConversionCostConvolution(ConversionCostConvolution conversionCostConvolution)
    {
        return conversionCostConvolutionMapper.updateConversionCostConvolution(conversionCostConvolution);
    }

    /**
     * 批量删除分装成本卷积
     * 
     * @param convolutionIds 需要删除的分装成本卷积主键
     * @return 结果
     */
    @Override
    public int deleteConversionCostConvolutionByConvolutionIds(Long[] convolutionIds)
    {
        return conversionCostConvolutionMapper.deleteConversionCostConvolutionByConvolutionIds(convolutionIds);
    }

    /**
     * 删除分装成本卷积信息
     * 
     * @param convolutionId 分装成本卷积主键
     * @return 结果
     */
    @Override
    public int deleteConversionCostConvolutionByConvolutionId(Long convolutionId)
    {
        return conversionCostConvolutionMapper.deleteConversionCostConvolutionByConvolutionId(convolutionId);
    }
}
