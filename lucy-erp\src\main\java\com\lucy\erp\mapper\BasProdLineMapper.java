package com.lucy.erp.mapper;

import java.util.List;
import com.lucy.erp.domain.BasProdLine;

/**
 * 产线数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface BasProdLineMapper 
{
    /**
     * 查询产线数据
     * 
     * @param id 产线数据主键
     * @return 产线数据
     */
    public BasProdLine selectBasProdLineById(Long id);

    /**
     * 查询产线数据列表
     * 
     * @param basProdLine 产线数据
     * @return 产线数据集合
     */
    public List<BasProdLine> selectBasProdLineList(BasProdLine basProdLine);

    /**
     * 新增产线数据
     * 
     * @param basProdLine 产线数据
     * @return 结果
     */
    public int insertBasProdLine(BasProdLine basProdLine);

    /**
     * 修改产线数据
     * 
     * @param basProdLine 产线数据
     * @return 结果
     */
    public int updateBasProdLine(BasProdLine basProdLine);

    /**
     * 删除产线数据
     * 
     * @param id 产线数据主键
     * @return 结果
     */
    public int deleteBasProdLineById(Long id);

    /**
     * 批量删除产线数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasProdLineByIds(Long[] ids);
}
