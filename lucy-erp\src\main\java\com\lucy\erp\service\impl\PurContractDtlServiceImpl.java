package com.lucy.erp.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.erp.mapper.PurContractDtlMapper;
import com.lucy.erp.domain.PurContractDtl;
import com.lucy.erp.service.IPurContractDtlService;

/**
 * 采购_合同_明细
Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@DataSource(DataSourceType.ERP)
public class PurContractDtlServiceImpl implements IPurContractDtlService
{
    @Autowired
    private PurContractDtlMapper purContractDtlMapper;

    /**
     * 查询采购_合同_明细

     * 
     * @param pcdCd 采购_合同_明细
主键
     * @return 采购_合同_明细

     */
    @Override
    public PurContractDtl selectPurContractDtlByPcdCd(String pcdCd)
    {
        return purContractDtlMapper.selectPurContractDtlByPcdCd(pcdCd);
    }

    /**
     * 查询采购_合同_明细
列表
     * 
     * @param purContractDtl 采购_合同_明细

     * @return 采购_合同_明细

     */
    @Override
    public List<PurContractDtl> selectPurContractDtlList(PurContractDtl purContractDtl)
    {
        return purContractDtlMapper.selectPurContractDtlList(purContractDtl);
    }

    /**
     * 新增采购_合同_明细

     * 
     * @param purContractDtl 采购_合同_明细

     * @return 结果
     */
    @Override
    public int insertPurContractDtl(PurContractDtl purContractDtl)
    {
        return purContractDtlMapper.insertPurContractDtl(purContractDtl);
    }

    /**
     * 修改采购_合同_明细

     * 
     * @param purContractDtl 采购_合同_明细

     * @return 结果
     */
    @Override
    public int updatePurContractDtl(PurContractDtl purContractDtl)
    {
        return purContractDtlMapper.updatePurContractDtl(purContractDtl);
    }

    /**
     * 批量删除采购_合同_明细

     * 
     * @param pcdCds 需要删除的采购_合同_明细
主键
     * @return 结果
     */
    @Override
    public int deletePurContractDtlByPcdCds(String[] pcdCds)
    {
        return purContractDtlMapper.deletePurContractDtlByPcdCds(pcdCds);
    }

    /**
     * 删除采购_合同_明细
信息
     * 
     * @param pcdCd 采购_合同_明细
主键
     * @return 结果
     */
    @Override
    public int deletePurContractDtlByPcdCd(String pcdCd)
    {
        return purContractDtlMapper.deletePurContractDtlByPcdCd(pcdCd);
    }
}
