package com.lucy.inventoryoe.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.inventoryoe.mapper.CostCompositionMapper;
import com.lucy.inventoryoe.domain.CostComposition;
import com.lucy.inventoryoe.service.ICostCompositionService;

/**
 * Cost composition structureService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Service
public class CostCompositionServiceImpl implements ICostCompositionService 
{
    @Autowired
    private CostCompositionMapper costCompositionMapper;

    /**
     * 查询Cost composition structure
     * 
     * @param id Cost composition structure主键
     * @return Cost composition structure
     */
    @Override
    public CostComposition selectCostCompositionById(Long id)
    {
        return costCompositionMapper.selectCostCompositionById(id);
    }

    /**
     * 查询Cost composition structure列表
     * 
     * @param costComposition Cost composition structure
     * @return Cost composition structure
     */
    @Override
    public List<CostComposition> selectCostCompositionList(CostComposition costComposition)
    {
        return costCompositionMapper.selectCostCompositionList(costComposition);
    }

    /**
     * 新增Cost composition structure
     * 
     * @param costComposition Cost composition structure
     * @return 结果
     */
    @Override
    public int insertCostComposition(CostComposition costComposition)
    {
        return costCompositionMapper.insertCostComposition(costComposition);
    }

    /**
     * 修改Cost composition structure
     * 
     * @param costComposition Cost composition structure
     * @return 结果
     */
    @Override
    public int updateCostComposition(CostComposition costComposition)
    {
        return costCompositionMapper.updateCostComposition(costComposition);
    }

    /**
     * 批量删除Cost composition structure
     * 
     * @param ids 需要删除的Cost composition structure主键
     * @return 结果
     */
    @Override
    public int deleteCostCompositionByIds(Long[] ids)
    {
        return costCompositionMapper.deleteCostCompositionByIds(ids);
    }

    /**
     * 删除Cost composition structure信息
     * 
     * @param id Cost composition structure主键
     * @return 结果
     */
    @Override
    public int deleteCostCompositionById(Long id)
    {
        return costCompositionMapper.deleteCostCompositionById(id);
    }
}
